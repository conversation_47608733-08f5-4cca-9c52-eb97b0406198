<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:mvc="http://www.springframework.org/schema/mvc"
       xmlns:task="http://www.springframework.org/schema/task"
       xsi:schemaLocation="http://www.springframework.org/schema/mvc http://www.springframework.org/schema/mvc/spring-mvc-3.0.xsd
		http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-3.0.xsd
		http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context-3.0.xsd
		http://www.springframework.org/schema/task http://www.springframework.org/schema/task/spring-task-3.0.xsd">

 <bean id="chatMongoDao" class="com.docMigration.dao.ChatMongoDao" init-method="init">
        <property name="host" value="${mongo.chat.host}"/>
        <property name="dbName" value="${mongo.chat.db}"/>
        <property name="portNo" value="${mongo.chat.port}"/>
        <property name="userName" value="${mongo.chat.userName}"/>
        <property name="password" value="${mongo.chat.password}"/>
        <property name="connectionsPerHost" value="${mongo.connectionsPerHost}"/>
        <property name="socketTimeOut" value="200000"/>
        <property name="connectionTimeOut" value="${mongo.connectiontimeout}"/>
        <property name="replicaSetName" value="${mongo.chat.replicaSetName}"/>
        <property name="replicaSetMode" value="${mongodb.chat.isReplica}"/>
        <property name="authEnable" value="${mongodb.chat.isAuth}"/>
    </bean>

    <bean id="uaeChatMongoDao" class="com.docMigration.dao.ChatMongoDao" init-method="init">
        <property name="host" value="${mongo.uaechat.host}"/>
        <property name="dbName" value="${mongo.uaechat.db}"/>
        <property name="portNo" value="${mongo.uaechat.port}"/>
        <property name="userName" value="${mongo.uaechat.userName}"/>
        <property name="password" value="${mongo.uaechat.password}"/>
        <property name="connectionsPerHost" value="${mongo.connectionsPerHost}"/>
        <property name="socketTimeOut" value="200000"/>
        <property name="connectionTimeOut" value="${mongo.connectiontimeout}"/>
        <property name="replicaSetName" value="${mongo.uaechat.replicaSetName}"/>
        <property name="replicaSetMode" value="${mongodb.uaechat.isReplica}"/>
        <property name="authEnable" value="${mongodb.uaechat.isAuth}"/>
    </bean>
    
    <bean id="coreMongoDao" class="com.docMigration.dao.ChatMongoDao" init-method="init">
        <property name="host" value="${mongo.core.host}"/>
        <property name="dbName" value="${mongo.core.db}"/>
        <property name="portNo" value="${mongo.core.port}"/>
        <property name="userName" value="${mongo.core.userName}"/>
        <property name="password" value="${mongo.core.password}"/>
        <property name="connectionsPerHost" value="${mongo.connectionsPerHost}"/>
        <property name="socketTimeOut" value="200000"/>
        <property name="connectionTimeOut" value="${mongo.connectiontimeout}"/>
        <property name="replicaSetName" value="${mongo.core.replicaSetName}"/>
        <property name="replicaSetMode" value="${mongodb.core.isReplica}"/>
        <property name="authEnable" value="${mongodb.core.isAuth}"/>
    </bean>
    
     <bean id="uaeJourMongoDao" class="com.docMigration.dao.ChatMongoDao" init-method="init">
        <property name="host" value="${mongo.uaejourney.host}"/>
        <property name="dbName" value="${mongo.uaejourney.db}"/>
        <property name="portNo" value="${mongo.uaejourney.port}"/>
        <property name="userName" value="${mongo.uaejourney.userName}"/>
        <property name="password" value="${mongo.uaejourney.password}"/>
        <property name="connectionsPerHost" value="${mongo.connectionsPerHost}"/>
        <property name="socketTimeOut" value="200000"/>
        <property name="connectionTimeOut" value="${mongo.connectiontimeout}"/>
        <property name="replicaSetName" value="${mongo.uaejourney.replicaSetName}"/>
        <property name="replicaSetMode" value="${mongodb.uaejourney.isReplica}"/>
        <property name="authEnable" value="${mongodb.uaejourney.isAuth}"/>
    </bean>
    <bean id="uaeArchivalDao" class="com.docMigration.dao.ChatMongoDao" init-method="init">
        <property name="host" value="${mongo.uaeArchival.host}"/>
        <property name="dbName" value="${mongo.uaeArchival.db}"/>
        <property name="portNo" value="${mongo.uaeArchival.port}"/>
        <property name="userName" value="${mongo.uaeArchival.userName}"/>
        <property name="password" value="${mongo.uaeArchival.password}"/>
        <property name="connectionsPerHost" value="${mongo.connectionsPerHost}"/>
        <property name="socketTimeOut" value="200000"/>
        <property name="connectionTimeOut" value="${mongo.connectiontimeout}"/>
        <property name="replicaSetName" value="${mongo.uaeArchival.replicaSetName}"/>
        <property name="replicaSetMode" value="${mongodb.uaeArchival.isReplica}"/>
        <property name="authEnable" value="${mongodb.uaeArchival.isAuth}"/>
    </bean>
     <bean id="commLoggerMongoDao" class="com.docMigration.dao.ChatMongoDao" init-method="init">
        <property name="host" value="${mongo.commlogger.host}"/>
        <property name="dbName" value="${mongo.commlogger.db}"/>
        <property name="portNo" value="${mongo.commlogger.port}"/>
        <property name="userName" value="${mongo.commlogger.userName}"/>
        <property name="password" value="${mongo.commlogger.password}"/>
        <property name="connectionsPerHost" value="${mongo.connectionsPerHost}"/>
        <property name="socketTimeOut" value="200000"/>
        <property name="connectionTimeOut" value="${mongo.connectiontimeout}"/>
        <property name="replicaSetName" value="${mongo.commlogger.replicaSetName}"/>
        <property name="replicaSetMode" value="${mongodb.commlogger.isReplica}"/>
        <property name="authEnable" value="${mongodb.commlogger.isAuth}"/>
    </bean>

    <bean id="commLoggerNewMongoDao" class="com.docMigration.dao.ChatMongoDao" init-method="init">
        <property name="host" value="${mongo.commloggerNew.host}"/>
        <property name="dbName" value="${mongo.commloggerNew.db}"/>
        <property name="portNo" value="${mongo.commloggerNew.port}"/>
        <property name="userName" value="${mongo.commloggerNew.userName}"/>
        <property name="password" value="${mongo.commloggerNew.password}"/>
        <property name="connectionsPerHost" value="${mongo.connectionsPerHost}"/>
        <property name="socketTimeOut" value="200000"/>
        <property name="connectionTimeOut" value="${mongo.connectiontimeout}"/>
        <property name="replicaSetName" value="${mongo.commloggerNew.replicaSetName}"/>
        <property name="replicaSetMode" value="${mongodb.commloggerNew.isReplica}"/>
        <property name="authEnable" value="${mongodb.commloggerNew.isAuth}"/>
    </bean>

    <bean id="pospLoggerMongoDao" class="com.docMigration.dao.ChatMongoDao" init-method="init">
        <property name="host" value="${mongo.posplogger.host}"/>
        <property name="dbName" value="${mongo.posplogger.db}"/>
        <property name="portNo" value="${mongo.posplogger.port}"/>
        <property name="userName" value="${mongo.posplogger.userName}"/>
        <property name="password" value="${mongo.posplogger.password}"/>
        <property name="connectionsPerHost" value="${mongo.connectionsPerHost}"/>
        <property name="socketTimeOut" value="200000"/>
        <property name="connectionTimeOut" value="${mongo.connectiontimeout}"/>
        <property name="replicaSetName" value="${mongo.posplogger.replicaSetName}"/>
        <property name="replicaSetMode" value="${mongodb.posplogger.isReplica}"/>
        <property name="authEnable" value="${mongodb.posplogger.isAuth}"/>
    </bean>
    <bean id="pospArchivalMongoDao" class="com.docMigration.dao.ChatMongoDao" init-method="init">
        <property name="host" value="${mongo.posparchival.host}"/>
        <property name="dbName" value="${mongo.posparchival.db}"/>
        <property name="portNo" value="${mongo.posparchival.port}"/>
        <property name="userName" value="${mongo.posparchival.userName}"/>
        <property name="password" value="${mongo.posparchival.password}"/>
        <property name="connectionsPerHost" value="${mongo.connectionsPerHost}"/>
        <property name="socketTimeOut" value="200000"/>
        <property name="connectionTimeOut" value="${mongo.connectiontimeout}"/>
        <property name="replicaSetName" value="${mongo.posparchival.replicaSetName}"/>
        <property name="replicaSetMode" value="${mongodb.posparchival.isReplica}"/>
        <property name="authEnable" value="${mongodb.posparchival.isAuth}"/>
    </bean>

    <bean id="commPrimaryArchivalMongoDao" class="com.docMigration.dao.ChatMongoDao" init-method="init">
        <property name="host" value="${mongo.commPrimaryArchivalMongoDao.host}"/>
        <property name="dbName" value="${mongo.commPrimaryArchivalMongoDao.db}"/>
        <property name="portNo" value="${mongo.commPrimaryArchivalMongoDao.port}"/>
        <property name="userName" value="${mongo.commPrimaryArchivalMongoDao.userName}"/>
        <property name="password" value="${mongo.commPrimaryArchivalMongoDao.password}"/>
        <property name="connectionsPerHost" value="${mongo.connectionsPerHost}"/>
        <property name="socketTimeOut" value="200000"/>
        <property name="connectionTimeOut" value="${mongo.connectiontimeout}"/>
        <property name="replicaSetName" value="${mongo.commPrimaryArchivalMongoDao.replicaSetName}"/>
        <property name="replicaSetMode" value="${mongodb.commPrimaryArchivalMongoDao.isReplica}"/>
        <property name="authEnable" value="${mongodb.commPrimaryArchivalMongoDao.isAuth}"/>
    </bean>
    
    <bean id="commMongoDao" class="com.docMigration.dao.ChatMongoDao" init-method="init">
        <property name="host" value="${mongo.comm.host}"/>
        <property name="dbName" value="${mongo.comm.db}"/>
        <property name="portNo" value="${mongo.comm.port}"/>
        <property name="userName" value="${mongo.comm.userName}"/>
        <property name="password" value="${mongo.comm.password}"/>
        <property name="connectionsPerHost" value="${mongo.connectionsPerHost}"/>
        <property name="socketTimeOut" value="200000"/>
        <property name="connectionTimeOut" value="${mongo.connectiontimeout}"/>
        <property name="replicaSetName" value="${mongo.comm.replicaSetName}"/>
        <property name="replicaSetMode" value="${mongodb.comm.isReplica}"/>
        <property name="authEnable" value="${mongodb.comm.isAuth}"/>
    </bean>
    
    
     <bean id="archMongoDao" class="com.docMigration.dao.ChatMongoDao" init-method="init">
        <property name="host" value="${mongo.archival.host}"/>
        <property name="dbName" value="${mongo.archival.db}"/>
        <property name="portNo" value="${mongo.archival.port}"/>
        <property name="userName" value="${mongo.archival.userName}"/>
        <property name="password" value="${mongo.archival.password}"/>
        <property name="connectionsPerHost" value="${mongo.connectionsPerHost}"/>
        <property name="socketTimeOut" value="200000"/>
        <property name="connectionTimeOut" value="${mongo.connectiontimeout}"/>
        <property name="replicaSetName" value="${mongo.archival.replicaSetName}"/>
        <property name="replicaSetMode" value="${mongodb.archival.isReplica}"/>
        <property name="authEnable" value="${mongodb.archival.isAuth}"/>
    </bean>
    
         <bean id="motorLoggerDao" class="com.docMigration.dao.ChatMongoDao" init-method="init">
        <property name="host" value="${mongo.motorLogger.host}"/>
        <property name="dbName" value="${mongo.motorLogger.db}"/>
        <property name="portNo" value="${mongo.motorLogger.port}"/>
        <property name="userName" value="${mongo.motorLogger.userName}"/>
        <property name="password" value="${mongo.motorLogger.password}"/>
        <property name="connectionsPerHost" value="${mongo.connectionsPerHost}"/>
        <property name="socketTimeOut" value="200000"/>
        <property name="connectionTimeOut" value="${mongo.connectiontimeout}"/>
        <property name="replicaSetName" value="${mongo.motorLogger.replicaSetName}"/>
        <property name="replicaSetMode" value="${mongodb.motorLogger.isReplica}"/>
        <property name="authEnable" value="${mongodb.motorLogger.isAuth}"/>
    </bean>
    <bean id="oldMongoDao" class="com.docMigration.dao.ChatMongoDao" init-method="init">
        <property name="host" value="${mongo.oldMongoDao.host}"/>
        <property name="dbName" value="${mongo.oldMongoDao.db}"/>
        <property name="portNo" value="${mongo.oldMongoDao.port}"/>
        <property name="userName" value="${mongo.oldMongoDao.userName}"/>
        <property name="password" value="${mongo.oldMongoDao.password}"/>
        <property name="connectionsPerHost" value="${mongo.connectionsPerHost}"/>
        <property name="socketTimeOut" value="200000"/>
        <property name="connectionTimeOut" value="${mongo.connectiontimeout}"/>
        <property name="replicaSetName" value="${mongo.oldMongoDao.replicaSetName}"/>
        <property name="replicaSetMode" value="${mongodb.oldMongoDao.isReplica}"/>
        <property name="authEnable" value="${mongodb.oldMongoDao.isAuth}"/>
    </bean>
    <bean id="e2eMongoLog" class="com.docMigration.dao.ChatMongoDao" init-method="init">
        <property name="host" value="${mongo.e2eMongologger.host}"/>
        <property name="dbName" value="${mongo.e2eMongologger.db}"/>
        <property name="portNo" value="${mongo.e2eMongologger.port}"/>
        <property name="userName" value="${mongo.e2eMongologger.userName}"/>
        <property name="password" value="${mongo.e2eMongologger.password}"/>
        <property name="connectionsPerHost" value="${mongo.e2eMongologger.connectionsPerHost}"/>
        <property name="socketTimeOut" value="200000"/>
        <property name="connectionTimeOut" value="${mongo.connectiontimeout}"/>
        <property name="replicaSetName" value="${mongo.e2eMongologger.replicaSetName}"/>
        <property name="replicaSetMode" value="${mongodb.e2eMongologger.isReplica}"/>
        <property name="authEnable" value="${mongodb.e2eMongologger.isAuth}"/>
    </bean>
    <bean id="genericMongoDao" class="com.docMigration.dao.GenericMongoDao" init-method="init">
        <property name="host" value="${mongo.genericMongoLogger.host}"/>
        <property name="dbName" value="${mongo.genericMongoLogger.db}"/>
        <property name="portNo" value="${mongo.genericMongoLogger.port}"/>
        <property name="userName" value="${mongo.genericMongoLogger.userName}"/>
        <property name="password" value="${mongo.genericMongoLogger.password}"/>
        <property name="connectionsPerHost" value="${mongo.genericMongoLogger.connectionsPerHost}"/>
        <property name="socketTimeOut" value="200000"/>
        <property name="connectionTimeOut" value="${mongo.connectiontimeout}"/>
        <property name="replicaSetName" value="${mongo.genericMongoLogger.replicaSetName}"/>
        <property name="replicaSetMode" value="${mongodb.genericMongoLogger.isReplica}"/>
        <property name="authEnable" value="${mongodb.genericMongoLogger.isAuth}"/>
    </bean>
    <bean id="pbpSupplierDao" class="com.docMigration.dao.GenericMongoDao" init-method="init">
        <property name="host" value="${mongo.PbpSupplierDao.host}"/>
        <property name="dbName" value="${mongo.PbpSupplierDao.db}"/>
        <property name="portNo" value="${mongo.PbpSupplierDao.port}"/>
        <property name="userName" value="${mongo.PbpSupplierDao.userName}"/>
        <property name="password" value="${mongo.PbpSupplierDao.password}"/>
        <property name="connectionsPerHost" value="${mongo.PbpSupplierDao.connectionsPerHost}"/>
        <property name="socketTimeOut" value="200000"/>
        <property name="connectionTimeOut" value="${mongo.connectiontimeout}"/>
        <property name="replicaSetName" value="${mongo.PbpSupplierDao.replicaSetName}"/>
        <property name="replicaSetMode" value="${mongodb.PbpSupplierDao.isReplica}"/>
        <property name="authEnable" value="${mongodb.PbpSupplierDao.isAuth}"/>
    </bean>
    <bean id="newArchivalMongoDao" class="com.docMigration.dao.ChatMongoDao" init-method="init">
        <property name="host" value="${mongo.newArchival.host}"/>
        <property name="dbName" value="${mongo.newArchival.db}"/>
        <property name="portNo" value="${mongo.newArchival.port}"/>
        <property name="userName" value="${mongo.newArchival.userName}"/>
        <property name="password" value="${mongo.newArchival.password}"/>
        <property name="connectionsPerHost" value="${mongo.connectionsPerHost}"/>
        <property name="socketTimeOut" value="200000"/>
        <property name="connectionTimeOut" value="${mongo.connectiontimeout}"/>
        <property name="replicaSetName" value="${mongo.newArchival.replicaSetName}"/>
        <property name="replicaSetMode" value="${mongodb.newArchival.isReplica}"/>
        <property name="authEnable" value="${mongodb.newArchival.isAuth}"/>
    </bean>
    <bean id="newCommBoxDao" class="com.docMigration.dao.ChatMongoDao" init-method="init">
        <property name="host" value="${mongo.newCommBox.host}"/>
        <property name="dbName" value="${mongo.newCommBox.db}"/>
        <property name="portNo" value="${mongo.newCommBox.port}"/>
        <property name="userName" value="${mongo.newCommBox.userName}"/>
        <property name="password" value="${mongo.newCommBox.password}"/>
        <property name="connectionsPerHost" value="${mongo.connectionsPerHost}"/>
        <property name="socketTimeOut" value="200000"/>
        <property name="connectionTimeOut" value="${mongo.connectiontimeout}"/>
        <property name="replicaSetName" value="${mongo.newCommBox.replicaSetName}"/>
        <property name="replicaSetMode" value="${mongodb.newCommBox.isReplica}"/>
        <property name="authEnable" value="${mongodb.newCommBox.isAuth}"/>
    </bean>

    <bean id="uaeJourMongoDao2" class="com.docMigration.dao.ChatMongoDao" init-method="init">
        <property name="host" value="${mongo.uaejourneyNew.host}"/>
        <property name="dbName" value="${mongo.uaejourneyNew.db}"/>
        <property name="portNo" value="${mongo.uaejourneyNew.port}"/>
        <property name="userName" value="${mongo.uaejourneyNew.userName}"/>
        <property name="password" value="${mongo.uaejourneyNew.password}"/>
        <property name="connectionsPerHost" value="${mongo.connectionsPerHost}"/>
        <property name="socketTimeOut" value="200000"/>
        <property name="connectionTimeOut" value="${mongo.connectiontimeout}"/>
        <property name="replicaSetName" value="${mongo.uaejourneyNew.replicaSetName}"/>
        <property name="replicaSetMode" value="${mongodb.uaejourneyNew.isReplica}"/>
        <property name="authEnable" value="${mongodb.uaejourneyNew.isAuth}"/>
    </bean>
    <bean id="newTempMongoDao" class="com.docMigration.dao.ChatMongoDao" init-method="init">
        <property name="host" value="${mongo.tempMotorLog.host}"/>
        <property name="dbName" value="${mongo.tempMotorLog.db}"/>
        <property name="portNo" value="${mongo.tempMotorLog.port}"/>
        <property name="userName" value="${mongo.tempMotorLog.userName}"/>
        <property name="password" value="${mongo.tempMotorLog.password}"/>
        <property name="connectionsPerHost" value="${mongo.connectionsPerHost}"/>
        <property name="socketTimeOut" value="200000"/>
        <property name="connectionTimeOut" value="${mongo.connectiontimeout}"/>
        <property name="replicaSetName" value="${mongo.tempMotorLog.replicaSetName}"/>
        <property name="replicaSetMode" value="${mongodb.tempMotorLog.isReplica}"/>
        <property name="authEnable" value="${mongodb.tempMotorLog.isAuth}"/>
    </bean>
    <bean id="newTempMongoDao2" class="com.docMigration.dao.ChatMongoDao" init-method="init">
        <property name="host" value="${mongo.tempMotorLog2.host}"/>
        <property name="dbName" value="${mongo.tempMotorLog2.db}"/>
        <property name="portNo" value="${mongo.tempMotorLog2.port}"/>
        <property name="userName" value="${mongo.tempMotorLog2.userName}"/>
        <property name="password" value="${mongo.tempMotorLog2.password}"/>
        <property name="connectionsPerHost" value="${mongo.connectionsPerHost}"/>
        <property name="socketTimeOut" value="200000"/>
        <property name="connectionTimeOut" value="${mongo.connectiontimeout}"/>
        <property name="replicaSetName" value="${mongo.tempMotorLog2.replicaSetName}"/>
        <property name="replicaSetMode" value="${mongodb.tempMotorLog2.isReplica}"/>
        <property name="authEnable" value="${mongodb.tempMotorLog2.isAuth}"/>
    </bean>
    <bean id="pcdMongoDao" class="com.docMigration.dao.ChatMongoDao" init-method="init">
        <property name="host" value="${mongo.pcdMongoDao.host}"/>
        <property name="dbName" value="${mongo.pcdMongoDao.db}"/>
        <property name="portNo" value="${mongo.pcdMongoDao.port}"/>
        <property name="userName" value="${mongo.pcdMongoDao.userName}"/>
        <property name="password" value="${mongo.pcdMongoDao.password}"/>
        <property name="connectionsPerHost" value="${mongo.connectionsPerHost}"/>
        <property name="socketTimeOut" value="200000"/>
        <property name="connectionTimeOut" value="${mongo.connectiontimeout}"/>
        <property name="replicaSetName" value="${mongo.pcdMongoDao.replicaSetName}"/>
        <property name="replicaSetMode" value="${mongodb.pcdMongoDao.isReplica}"/>
        <property name="authEnable" value="${mongodb.pcdMongoDao.isAuth}"/>
    </bean>
</beans>
