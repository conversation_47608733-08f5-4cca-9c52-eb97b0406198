#logging.file.path=/opt/logs/

# this is development properties
#mongo.host=*********
#mongo.host=***********,************,***********
#mongo.host=**************,**************,**************
mongo.host=***********,************,************
mongo.db=coreService
mongo.port=27017
mongo.userName=migrationMDB7U
mongo.password=migrationU8730x
mongo.threadsAllowedToBlockForConnectionMultiplier=1
mongo.connectiontimeout=600000
mongo.socketTimeout=600000
mongo.connectionsPerHost=500

mongo.replicaSetName=cs1

## replicaSetMode
mongodb.isReplica=1
mongodb.isAuth=1

#aws related properties
aws.bucket_name = policycopy
aws.access_key=********************
aws.secret_key=TKg/uRT0Kyfsl07XInb6C/Pq9suFw4xs1T9Xzu/o
aws.userName=pbcopy
aws.pwd=Secure@#pb@1234@
aws.pgi_s3Bucket_link = https://s3.ap-south-1.amazonaws.com/policycopy/

#document schedular get count
doc.schedular.count=10

#for role changesin aws
isLocalDev=1
dev.file.path.access.key=/home/<USER>/accessKeyFile.txt

#mongo.car.host=************
#mongo.car.host=***********
#mongo.car.host=***********,***********
mongo.car.host=************
#mongo.car.host=************
mongo.car.db=CarLoggerDB
mongo.car.port=27017
mongo.car.userName=bhaskarUserW
mongo.car.password=123456789
mongo.car.replicaSetName=rs2
mongo.car.isReplica=0
mongo.car.isAuth=1

mongo.carlead.host=***********
mongo.carlead.db=CarLoggerDB
mongo.carlead.port=27017
mongo.carlead.userName=loogerUpd
mongo.carlead.password=loogerUpd123
mongo.carlead.replicaSetName=rs2
mongo.carlead.isReplica=0
mongo.carlead.isAuth=1

#mongo.host=*********
mongo.chat.host=************,************,************
#mongo.host=**************,**************,**************
mongo.chat.db=admin
mongo.chat.port=27017
mongo.chat.userName=anjalim
mongo.chat.password=Anum541
mongo.chat.connectionsPerHost=500
mongo.chat.replicaSetName=rs2

## replicaSetMode
mongodb.chat.isReplica=1
mongodb.chat.isAuth=1


mongo.uaechat.host=***********,***********,***********
mongo.uaechat.db=admin
mongo.uaechat.port=27017
mongo.uaechat.userName=anjalim
mongo.uaechat.password=Anum541
mongo.uaechat.replicaSetName=rs2
mongodb.uaechat.isReplica=1
mongodb.uaechat.isAuth=1
mongo.uae.connectionsPerHost=500

mongo.uaejourney.host=***********,***********,**********
mongo.uaejourney.db=admin
mongo.uaejourney.port=27017
mongo.uaejourney.userName=anjalim
mongo.uaejourney.password=Anum541
mongo.uaejourney.replicaSetName=rs1
mongodb.uaejourney.isReplica=1
mongodb.uaejourney.isAuth=1
mongo.uaejourney.connectionsPerHost=500

mongo.uaeArchival.host=**********
mongo.uaeArchival.db=admin
mongo.uaeArchival.port=27017
mongo.uaeArchival.userName=archvialAppUsr
mongo.uaeArchival.password=Ar3val0hJlon0
mongo.uaeArchival.replicaSetName=rs1
mongodb.uaeArchival.isReplica=0
mongodb.uaeArchival.isAuth=1
mongo.uaeArchival.connectionsPerHost=50

mongo.core.host=***********,************,***********
mongo.core.db=admin
mongo.core.port=27017
mongo.core.userName=anjalim
mongo.core.password=Anum541
mongo.core.replicaSetName=cs1
mongodb.core.isReplica=1
mongodb.core.isAuth=1

spring.datasource.jdbcUrl=************************************************************************************************;
spring.datasource.username=Core_App
spring.datasource.password=o@7#J0@#$k@%6l

# this is development new coll properties
#mongo.host=*********
mongo.new.host=***********,************,***********
#mongo.host=**************,**************,**************
mongo.new.db=coreService
mongo.new.port=27017
mongo.new.userName=coreuser
mongo.new.password=coreuser
mongo.new.threadsAllowedToBlockForConnectionMultiplier=1
mongo.new.connectiontimeout=3000
mongo.new.socketTimeout=9000
mongo.new.connectionsPerHost=500

mongo.new.replicaSetName=rs5

## replicaSetMode
mongodb.new.isReplica=0
mongodb.new.isAuth=1

server.port=8091

#mongo.comm.host=************,************,************
mongo.comm.host=************
mongo.comm.db=admin
mongo.comm.port=27017
mongo.comm.userName=archivalUser
mongo.comm.password=ti0AlJWcA9AE
mongo.comm.replicaSetName=rs3
mongodb.comm.isReplica=0
mongodb.comm.isAuth=1

mongo.archival.host=**********
mongo.archival.db=admin
mongo.archival.port=27017
mongo.archival.userName=anjalim
mongo.archival.password=Anum541
mongo.archival.replicaSetName=rs6
mongodb.archival.isReplica=0
mongodb.archival.isAuth=1

#mongo.e2e.host=***********,************,************
#mongo.e2e.host=************,************,***********
## mservice and mobile app cluster IP
mongo.e2e.host=***********,***********,************
mongo.e2e.db=admin
mongo.e2e.port=27017
mongo.e2e.userName=archUser
mongo.e2e.password=ar49j4nch7jk4dcx8
mongo.e2e.replicaSetName=rs1
mongo.e2e.isReplica=1
mongo.e2e.isAuth=1

#spring.datasource.jdbcUrl=*******************************************************************************************************************************;
#spring.datasource.username=core
#spring.datasource.password=c@re@#2021!$$live

spring.repldatasource.jdbcUrl=************************************************************************************************;
spring.repldatasource.username=Core_App
spring.repldatasource.password=o@7#J0@#$k@%6l

#spring.repldatasource.jdbcUrl=******************************************************************************************;
#spring.repldatasource.username=Core
#spring.repldatasource.password=Core@43@QA@PrDB

# this is development cache properties
#mongocust.host=************,************,************
#mongocust.host=***********,************,************
#mongocust.db=coreService
#mongocust.port=27017
#mongocust.userName=coreuser
#mongocust.password=coreuser123
#mongocust.password=coreuser

#mongocust.connectiontimeout=6000
#mongocust.socketTimeout=6000
#mongocust.connectionsPerHost=50
#mongocust.replicaSetName=rs2
#mongocust.replicaSetName=rs6
## replicaSetMode
#mongodbcust.isReplica=1
#mongodbcust.isAuth=1

mongo.commlogger.host=************
mongo.commlogger.db=admin
mongo.commlogger.port=27017
mongo.commlogger.userName=migrationMDB7U
mongo.commlogger.password=migrationU8730x
mongo.commlogger.replicaSetName=rs3
mongodb.commlogger.isReplica=0
mongodb.commlogger.isAuth=1

mongo.posplogger.host=**********,***********,**********
mongo.posplogger.db=admin
mongo.posplogger.port=27017
mongo.posplogger.userName=archivalUser
mongo.posplogger.password=ti0AlJWcA9AE
mongo.posplogger.replicaSetName=rs5
mongodb.posplogger.isReplica=1
mongodb.posplogger.isAuth=1

mongo.posparchival.host=***********
mongo.posparchival.db=admin
mongo.posparchival.port=27017
mongo.posparchival.userName=migrationMDB7U
mongo.posparchival.password=migrationU8730x
mongo.posparchival.replicaSetName=rs6
mongodb.posparchival.isReplica=0
mongodb.posparchival.isAuth=1

mongo.commPrimaryArchivalMongoDao.host=***********
mongo.commPrimaryArchivalMongoDao.db=admin
mongo.commPrimaryArchivalMongoDao.port=27017
mongo.commPrimaryArchivalMongoDao.userName=archivalUser
mongo.commPrimaryArchivalMongoDao.password=sSDrUpJbcW342@
mongo.commPrimaryArchivalMongoDao.replicaSetName=cs1
mongodb.commPrimaryArchivalMongoDao.isReplica=0
mongodb.commPrimaryArchivalMongoDao.isAuth=1

#mongo.motorLogger.host=***********9
mongo.motorLogger.host=***********
mongo.motorLogger.db=admin
mongo.motorLogger.port=27017
mongo.motorLogger.userName=archivalUser
mongo.motorLogger.password=ti0AlJWcA9AE
mongo.motorLogger.replicaSetName=rs5
mongodb.motorLogger.isReplica=0
mongodb.motorLogger.isAuth=1

mongo.oldMongoDao.host=************
mongo.oldMongoDao.db=admin
mongo.oldMongoDao.port=27017
mongo.oldMongoDao.userName=migrationMDB7U
mongo.oldMongoDao.password=migrationU8730x
mongo.oldMongoDao.replicaSetName=rs6
mongodb.oldMongoDao.isReplica=0
mongodb.oldMongoDao.isAuth=1
mongo.oldMongoDao.connectionsPerHost=50

aws.mongo_archive_s3Bucket_link=dfghjk

mongo.e2eMongologger.host=***********
mongo.e2eMongologger.db=admin
mongo.e2eMongologger.port=27017
mongo.e2eMongologger.userName=migrationMDB7U
mongo.e2eMongologger.password=migrationU8730x
mongo.e2eMongologger.replicaSetName=rs6
mongodb.e2eMongologger.isReplica=0
mongodb.e2eMongologger.isAuth=1
mongo.e2eMongologger.connectionsPerHost=50

mongo.genericMongoLogger.host=***********
mongo.genericMongoLogger.db=admin
mongo.genericMongoLogger.port=27017
mongo.genericMongoLogger.userName=migrationMDB7U
mongo.genericMongoLogger.password=migrationU8730x
mongo.genericMongoLogger.replicaSetName=rs6
mongodb.genericMongoLogger.isReplica=0
mongodb.genericMongoLogger.isAuth=1
mongo.genericMongoLogger.connectionsPerHost=500

mongo.PbpSupplierDao.host=***********,***********,***********
mongo.PbpSupplierDao.db=admin
mongo.PbpSupplierDao.port=27017
mongo.PbpSupplierDao.userName=migrationMDB7U
mongo.PbpSupplierDao.password=migrationU8730x
mongo.PbpSupplierDao.replicaSetName=rs6
mongodb.PbpSupplierDao.isReplica=0
mongodb.PbpSupplierDao.isAuth=1
mongo.PbpSupplierDao.connectionsPerHost=50

mongo.newArchival.host=***********
mongo.newArchival.db=admin
mongo.newArchival.port=27017
mongo.newArchival.userName=migrationMDB7U
mongo.newArchival.password=migrationU8730x
mongo.newArchival.replicaSetName=rs6
mongodb.newArchival.isReplica=0
mongodb.newArchival.isAuth=1
mongo.newArchival.connectionsPerHost=50

mongo.newCommBox.host=***********,************,************
mongo.newCommBox.db=admin
mongo.newCommBox.port=27017
mongo.newCommBox.userName=migrationMDB7U
mongo.newCommBox.password=migrationU8730x
mongo.newCommBox.replicaSetName=rs6
mongodb.newCommBox.isReplica=0
mongodb.newCommBox.isAuth=1
mongo.newCommBox.connectionsPerHost=50

mongo.uaejourneyNew.host=**********
mongo.uaejourneyNew.db=admin
mongo.uaejourneyNew.port=27017
mongo.uaejourneyNew.userName=migrationMDB7U
mongo.uaejourneyNew.password=migrationU8730x
mongo.uaejourneyNew.replicaSetName=rs1
mongodb.uaejourneyNew.isReplica=1
mongodb.uaejourneyNew.isAuth=1
mongo.uaejourneyNew.connectionsPerHost=50

mongo.tempMotorLog.host=************
mongo.tempMotorLog.db=Recovery
#mongo.tempMotorLog.db=CarLoggerDB
mongo.tempMotorLog.port=27017
mongo.tempMotorLog.userName=
mongo.tempMotorLog.password=
mongo.tempMotorLog.replicaSetName=rs1
mongodb.tempMotorLog.isReplica=0
mongodb.tempMotorLog.isAuth=0
mongo.tempMotorLog.connectionsPerHost=50

mongo.tempMotorLog2.host=************
mongo.tempMotorLog2.db=CarLoggerDB
mongo.tempMotorLog2.port=27017
mongo.tempMotorLog2.userName=
mongo.tempMotorLog2.password=
mongo.tempMotorLog2.replicaSetName=rs1
mongodb.tempMotorLog2.isReplica=0
mongodb.tempMotorLog2.isAuth=0
mongo.tempMotorLog2.connectionsPerHost=50

mongo.pcdMongoDao.host=***********,************,************
mongo.pcdMongoDao.db=admin
mongo.pcdMongoDao.port=27017
mongo.pcdMongoDao.userName=migrationMDB7U
mongo.pcdMongoDao.password=migrationU8730x
mongo.pcdMongoDao.replicaSetName=rs1
mongodb.pcdMongoDao.isReplica=0
mongodb.pcdMongoDao.isAuth=1
mongo.pcdMongoDao.connectionsPerHost=50

mongo.commloggerNew.host=************
mongo.commloggerNew.db=admin
mongo.commloggerNew.port=27017
mongo.commloggerNew.userName=migrationMDB7U
mongo.commloggerNew.password=migrationU8730x
mongo.commloggerNew.replicaSetName=rs3
mongodb.commloggerNew.isReplica=0
mongodb.commloggerNew.isAuth=1