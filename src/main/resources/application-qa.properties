#logging.file.path=/opt/logs/

# this is development properties
#mongo.host=*********
mongo.host=***********,************,***********
#mongo.host=**************,**************,**************
mongo.db=coreService
mongo.port=27017
mongo.userName=coreuser
mongo.password=coreuser123
mongo.threadsAllowedToBlockForConnectionMultiplier=1
mongo.connectiontimeout=600000
mongo.socketTimeout=600000
mongo.connectionsPerHost=50

mongo.replicaSetName=cs1

## replicaSetMode
mongodb.isReplica=1
mongodb.isAuth=1

#aws related properties      
aws.bucket_name = policycopy
aws.access_key=********************
aws.secret_key=TKg/uRT0Kyfsl07XInb6C/Pq9suFw4xs1T9Xzu/o
aws.userName=pbcopy
aws.pwd=Secure@#pb@1234@
aws.pgi_s3Bucket_link = https://s3.ap-south-1.amazonaws.com/policycopy/

#document schedular get count
doc.schedular.count=10

#for role changesin aws
isLocalDev=1
dev.file.path.access.key=/home/<USER>/accessKeyFile.txt

#mongo.car.host=************
#mongo.car.host=**********9
#mongo.car.host=***********,***********
mongo.car.host=************
#mongo.car.host=************
mongo.car.db=CarLoggerDB
mongo.car.port=27017
mongo.car.userName=bhaskarUserW
mongo.car.password=123456789
mongo.car.replicaSetName=rs2
mongo.car.isReplica=0
mongo.car.isAuth=1

mongo.carlead.host=***********
mongo.carlead.db=CarLoggerDB
mongo.carlead.port=27017
mongo.carlead.userName=loogerUpd
mongo.carlead.password=loogerUpd123
mongo.carlead.replicaSetName=rs2
mongo.carlead.isReplica=0
mongo.carlead.isAuth=1

#mongo.host=*********
mongo.chat.host=************,************,************
#mongo.host=**************,**************,**************
mongo.chat.db=admin
mongo.chat.port=27017
mongo.chat.userName=anjalim
mongo.chat.password=Anum541
mongo.chat.connectionsPerHost=50
mongo.chat.replicaSetName=rs2

## replicaSetMode
mongodb.chat.isReplica=1
mongodb.chat.isAuth=1


mongo.uaechat.host=***********,***********,***********
mongo.uaechat.db=admin
mongo.uaechat.port=27017
mongo.uaechat.userName=anjalim
mongo.uaechat.password=Anum541
mongo.uaechat.replicaSetName=rs2
mongodb.uaechat.isReplica=1
mongodb.uaechat.isAuth=1
mongo.uae.connectionsPerHost=50

mongo.uaejourney.host=***********,***********,**********
mongo.uaejourney.db=DubaiMotorDB
mongo.uaejourney.port=27017
mongo.uaejourney.userName=anjalim
mongo.uaejourney.password=Anum541
mongo.uaejourney.replicaSetName=rs1
mongodb.uaejourney.isReplica=1
mongodb.uaejourney.isAuth=1
mongo.uaejourney.connectionsPerHost=50

mongo.core.host=***********,************,***********
mongo.core.db=admin
mongo.core.port=27017
mongo.core.userName=anjalim
mongo.core.password=Anum541
mongo.core.replicaSetName=cs1
mongodb.core.isReplica=1
mongodb.core.isAuth=1

spring.datasource.jdbcUrl=************************************************************************************************;
spring.datasource.username=Core_App
spring.datasource.password=o@7#J0@#$k@%6l

# this is development new coll properties
#mongo.host=*********
mongo.new.host=***********,************,***********
#mongo.host=**************,**************,**************
mongo.new.db=coreService
mongo.new.port=27017
mongo.new.userName=coreuser
mongo.new.password=coreuser
mongo.new.threadsAllowedToBlockForConnectionMultiplier=1
mongo.new.connectiontimeout=3000
mongo.new.socketTimeout=3000
mongo.new.connectionsPerHost=50

mongo.new.replicaSetName=rs5

## replicaSetMode
mongodb.new.isReplica=0
mongodb.new.isAuth=1

server.port=8091

#mongo.comm.host=************,************,************
mongo.comm.host=************
mongo.comm.db=admin
mongo.comm.port=27017
mongo.comm.userName=anjalim
mongo.comm.password=Anum541
mongo.comm.replicaSetName=rs3
mongodb.comm.isReplica=0
mongodb.comm.isAuth=1

mongo.archival.host=**********
mongo.archival.db=admin
mongo.archival.port=27017
mongo.archival.userName=anjalim
mongo.archival.password=Anum541
mongo.archival.replicaSetName=rs6
mongodb.archival.isReplica=0
mongodb.archival.isAuth=1

#mongo.e2e.host=***********,************,************
mongo.e2e.host=************,************,***********
mongo.e2e.db=admin
mongo.e2e.port=27017
mongo.e2e.userName=archUser
mongo.e2e.password=archUser123
mongo.e2e.replicaSetName=rs1
mongo.e2e.isReplica=1
mongo.e2e.isAuth=1

#spring.datasource.jdbcUrl=*******************************************************************************************************************************;
#spring.datasource.username=core
#spring.datasource.password=c@re@#2021!$$live

spring.repldatasource.jdbcUrl=************************************************************************************************;
spring.repldatasource.username=Core_App
spring.repldatasource.password=o@7#J0@#$k@%6l

#spring.repldatasource.jdbcUrl=******************************************************************************************;
#spring.repldatasource.username=Core
#spring.repldatasource.password=Core@43@QA@PrDB

# this is development cache properties
mongocust.host=************,************,************
#mongocust.host=***********,************,************
mongocust.db=coreService
mongocust.port=27017
mongocust.userName=coreuser
mongocust.password=coreuser123
#mongocust.password=coreuser

mongocust.connectiontimeout=6000
mongocust.socketTimeout=6000
mongocust.connectionsPerHost=50
mongocust.replicaSetName=rs2
#mongocust.replicaSetName=rs6
## replicaSetMode
mongodbcust.isReplica=1
mongodbcust.isAuth=1

mongo.commlogger.host=************
mongo.commlogger.db=admin
mongo.commlogger.port=27017
mongo.commlogger.userName=anjalim
mongo.commlogger.password=Anum541
mongo.commlogger.replicaSetName=rs3
mongodb.commlogger.isReplica=0
mongodb.commlogger.isAuth=1

mongo.commloggerNew.host=************
mongo.commloggerNew.db=admin
mongo.commloggerNew.port=27017
mongo.commloggerNew.userName=migrationMDB7U
mongo.commloggerNew.password=migrationU8730x
mongo.commloggerNew.replicaSetName=rs3
mongodb.commloggerNew.isReplica=0
mongodb.commloggerNew.isAuth=1