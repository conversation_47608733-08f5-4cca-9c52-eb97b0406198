/**
 * 
 */
package com.docMigration.dao.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

import javax.annotation.PostConstruct;
import javax.sql.DataSource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.namedparam.MapSqlParameterSource;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.jdbc.core.namedparam.SqlParameterSource;
import org.springframework.stereotype.Repository;

import com.docMigration.dao.MigrationDao;
import com.docMigration.util.QueryMigration;


/**
 * <AUTHOR>
 *
 */
@Repository
public class MigrationDaoImpl implements MigrationDao {

	@Qualifier("jdbcTemplateRepl")
	private JdbcTemplate jdbcTemplateRepl;
	
	@Qualifier("jdbcTemplate")
	private JdbcTemplate jdbcTemplate;
	
	private NamedParameterJdbcTemplate namedParameterJdbcTemplate;
	
	@Autowired
	private DataSource dataSourceRepl;
	
	@Autowired
	private DataSource dataSource;
	
	private final Logger logger = LoggerFactory.getLogger(MigrationDaoImpl.class);

	@PostConstruct
	public void init() {
		this.jdbcTemplateRepl = new JdbcTemplate(dataSourceRepl);
		namedParameterJdbcTemplate = new NamedParameterJdbcTemplate(dataSource);
		jdbcTemplate = new JdbcTemplate(dataSource);
	}
	
	@Override
	public List<Map<String, Object>> getCustEmailDetail(int size, long maxCustId) {
		return this.jdbcTemplateRepl.queryForList(QueryMigration.GET_EMAIL_CUST_DETAIL , new Object[] { size, maxCustId});
	}
	
	@Override
	public List<Map<String, Object>> getEmailNetworkDetail(int size, long maxCustId) {
		return this.jdbcTemplateRepl.queryForList(QueryMigration.GET_EMAIL_NETWORK_DETAIL , new Object[] { size, maxCustId});
	}

	@Override
	public List<Map<String, Object>> getCustMobileDetail(int size, long maxMobileNo) {
		return this.jdbcTemplateRepl.queryForList(QueryMigration.GET_MOBILE_CUST_DETAIL , new Object[] {size, maxMobileNo});
	}
	
	@Override
	public List<Map<String, Object>> getMobilePbNetworkDetail(int size, long maxMobileNo) {
		return this.jdbcTemplateRepl.queryForList(QueryMigration.GET_MOBILE_NETWORK_DETAIL , new Object[] {size, maxMobileNo});
	}

	@Override
	public List<Map<String, Object>> getCustDetail(int size, long maxCustId) {
		return this.jdbcTemplateRepl.queryForList(QueryMigration.GET_CUST_DETAIL , new Object[] { size, maxCustId,size, maxCustId});
	}

	@Override
	public List<Map<String, Object>> getEncMobileDetailsByCustMob(int size, long maxCustMobNo) {
		return this.jdbcTemplateRepl.queryForList(QueryMigration.GET_CUST_MOB_IDS_DETAIL , new Object[] { size, maxCustMobNo});
	}
	
	@Override
	public List<Map<String, Object>> getEncMobileNetworkDetailsByCustMob(int size, long maxCustMobNo) {
		return this.jdbcTemplateRepl.queryForList(QueryMigration.GET_CUST_NETWORK_MOB_IDS_DETAIL , new Object[] { size, maxCustMobNo});
	}

	@Override
	public List<Map<String, Object>> getmobileDetailsByEnc(String encMobile) {
		return this.jdbcTemplateRepl.queryForList(QueryMigration.GET_CUST_MOB_ID_ENC_MOBILE_DETAIL , encMobile);
	}

	@Override
	public void updateCustEncMobile(long mobileNo, String encMobile, int clientId) {
		MapSqlParameterSource namedParameters = new MapSqlParameterSource();
		namedParameters.addValue("mobileNo", mobileNo);
		namedParameters.addValue("encMobile", encMobile);
		namedParameters.addValue("updatedOn", new Date());
		namedParameters.addValue("updatedBy", clientId);
		namedParameterJdbcTemplate.update(QueryMigration.UPDATE_CUST_ENC_MOBILE_ALL_FOR_MOBILE, namedParameters);
	}

	@Override
	public List<Map<String, Object>> getEncEmailDetailsByCustEmail(int size, long maxCustEmailId) {
		return this.jdbcTemplateRepl.queryForList(QueryMigration.GET_CUST_EMAIL_IDS_DETAIL , new Object[] { size, maxCustEmailId});
	}
	
	@Override
	public List<Map<String, Object>> getEncEmailNetwrkDetailsById(int size, long maxCustEmailId) {
		return this.jdbcTemplateRepl.queryForList(QueryMigration.GET_CUST_EMAIL_PB_NETWORK_IDS_DETAIL , new Object[] { size, maxCustEmailId});
	}

	@Override
	public List<Map<String, Object>> getEmailDetailsByEnc(String encEmail) {
		return this.jdbcTemplateRepl.queryForList(QueryMigration.GET_CUST_EMAIL_ID_ENC_EMAIL_DETAIL , encEmail);
		}

	@Override
	public void updateCustEncEmail(String email, String encEmail, int clientId) {
		MapSqlParameterSource namedParameters = new MapSqlParameterSource();
		namedParameters.addValue("email", email);
		namedParameters.addValue("encEmail", encEmail);
		namedParameters.addValue("updatedOn", new Date());
		namedParameters.addValue("updatedBy", clientId);
		namedParameterJdbcTemplate.update(QueryMigration.UPDATE_CUST_ENC_EMAIL_ALL_FOR_EMAIL, namedParameters);
	}
	
	@Override
	public void bulkUpdateMobData(List<Map<String, Object>> updateList) {
        try {
        	List<SqlParameterSource> sqlParameterSources = new ArrayList<>();
        	
        	for (Map<String, Object> map : updateList) {
			MapSqlParameterSource mapSqlParameterSource = new MapSqlParameterSource();
			mapSqlParameterSource.addValue("encMobile", map.get("nEn"));
			mapSqlParameterSource.addValue("updatedOn", new Date());
			mapSqlParameterSource.addValue("updatedBy", 99);
			mapSqlParameterSource.addValue("mobileNo", map.get("m"));
			sqlParameterSources.add(mapSqlParameterSource);
		}
		namedParameterJdbcTemplate.batchUpdate(QueryMigration.UPDATE_CUST_ENC_MOBILE_ALL_FOR_MOBILE, sqlParameterSources.toArray(new SqlParameterSource[0]));} catch (Exception e) {
            logger.error("Error occurred while bulkUpdateMobData {}, error:{}",updateList, e.getMessage());
        }
		
	}
	
	@Override
	public void bulkUpdateMobNetworkData(List<Map<String, Object>> updateList) {
        try {
        	List<SqlParameterSource> sqlParameterSources = new ArrayList<>();
        	
        	for (Map<String, Object> map : updateList) {
			MapSqlParameterSource mapSqlParameterSource = new MapSqlParameterSource();
			mapSqlParameterSource.addValue("encMobile", map.get("nEn"));
			mapSqlParameterSource.addValue("updatedOn", new Date());
			mapSqlParameterSource.addValue("updatedBy", 99);
			mapSqlParameterSource.addValue("mobileNo", map.get("m"));
			sqlParameterSources.add(mapSqlParameterSource);
		}
		namedParameterJdbcTemplate.batchUpdate(QueryMigration.UPDATE_PB_NETWORK_ENC_MOBILE_ALL_FOR_MOBILE, sqlParameterSources.toArray(new SqlParameterSource[0]));} catch (Exception e) {
            logger.error("Error occurred while bulkUpdateMobData {}, error:{}",updateList, e.getMessage());
        }
		
	}
	
	@Override
	public void bulkUpdateEmailData(List<Map<String, Object>> updateList) {
		try {
			List<SqlParameterSource> sqlParameterSources = new ArrayList<>();

			for (Map<String, Object> map : updateList) {
				MapSqlParameterSource mapSqlParameterSource = new MapSqlParameterSource();
				mapSqlParameterSource.addValue("encEmail", map.get("nEn"));
				mapSqlParameterSource.addValue("updatedOn", new Date());
				mapSqlParameterSource.addValue("updatedBy", 99);
				mapSqlParameterSource.addValue("email", map.get("e"));
				sqlParameterSources.add(mapSqlParameterSource);
			}
			namedParameterJdbcTemplate.batchUpdate(QueryMigration.UPDATE_CUST_ENC_EMAIL_ALL_FOR_EMAIL,
					sqlParameterSources.toArray(new SqlParameterSource[0]));
		} catch (Exception e) {
            logger.error("Error occurred while bulkUpdateEmailData {}, error:{}",updateList, e.getMessage());
        }
		
	}
	
	@Override
	public void bulkUpdateEmailNtwrkData(List<Map<String, Object>> updateList) {
        try {
        	List<SqlParameterSource> sqlParameterSources = new ArrayList<>();
        	
        	for (Map<String, Object> map : updateList) {
			MapSqlParameterSource mapSqlParameterSource = new MapSqlParameterSource();
			mapSqlParameterSource.addValue("encEmail", map.get("nEn"));
			mapSqlParameterSource.addValue("updatedOn", new Date());
			mapSqlParameterSource.addValue("updatedBy", 99);
			mapSqlParameterSource.addValue("email", map.get("e"));
			sqlParameterSources.add(mapSqlParameterSource);
		}
		namedParameterJdbcTemplate.batchUpdate(QueryMigration.UPDATE_PB_NETWORK_ENC_EMAIL_ALL_FOR_EMAIL, sqlParameterSources.toArray(new SqlParameterSource[0]));} catch (Exception e) {
            logger.error("Error occurred while bulkUpdateEmailData {}, error:{}",updateList, e.getMessage());
        }
		
	}
	
	@Override
	public void bulkUpdateMblData(List<Map<String, Object>> updateList) {
		try {
			List<SqlParameterSource> sqlParameterSources = new ArrayList<>();

			for (Map<String, Object> map : updateList) {
				MapSqlParameterSource mapSqlParameterSource = new MapSqlParameterSource();
				mapSqlParameterSource.addValue("encMobile", map.get("nEn"));
				mapSqlParameterSource.addValue("updatedOn", new Date());
				mapSqlParameterSource.addValue("updatedBy", 99);
				mapSqlParameterSource.addValue("MobileNo", map.get("m"));
				sqlParameterSources.add(mapSqlParameterSource);
			}
			namedParameterJdbcTemplate.batchUpdate(QueryMigration.UPDATE_CUST_ENC_MBL_ALL_FOR_MBL,
					sqlParameterSources.toArray(new SqlParameterSource[0]));
		} catch (Exception e) {
            logger.error("Error occurred while bulkUpdateEmailData {}, error:{}",updateList, e.getMessage());
        }
		
	}
	
	@Override
	public void bulkUpdateMblNtwrkData(List<Map<String, Object>> updateList) {
        try {
        	List<SqlParameterSource> sqlParameterSources = new ArrayList<>();
        	
        	for (Map<String, Object> map : updateList) {
			MapSqlParameterSource mapSqlParameterSource = new MapSqlParameterSource();
			mapSqlParameterSource.addValue("encMobile", map.get("nEn"));
			mapSqlParameterSource.addValue("updatedOn", new Date());
			mapSqlParameterSource.addValue("updatedBy", 99);
			mapSqlParameterSource.addValue("mobileNo", map.get("m"));
			sqlParameterSources.add(mapSqlParameterSource);
		}
		namedParameterJdbcTemplate.batchUpdate(QueryMigration.UPDATE_PB_NETWORK_ENC_MBL_ALL_FOR_MBL, sqlParameterSources.toArray(new SqlParameterSource[0]));} catch (Exception e) {
            logger.error("Error occurred while bulkUpdateEmailData {}, error:{}",updateList, e.getMessage());
        }
		
	}
	
	@Override
	public List<Map<String, Object>> getCommResponse(long batchStartId, long size, String eventType) {
		List<Map<String, Object>> responseData = new ArrayList<>();
		if (eventType.equalsIgnoreCase("email")) {
			responseData = jdbcTemplateRepl.queryForList(QueryMigration.GET_COMM_RESPONSE_BY_RANGE, batchStartId, size);
		} else if (eventType.equalsIgnoreCase("mobile")) {
			// for future implementation
			responseData = jdbcTemplateRepl.queryForList(QueryMigration.GET_MOB_COMM_RESPONSE_BY_RANGE, batchStartId, size);
		}
		return responseData;
	}

	@Override
	public long getMaxCustId() {
		return jdbcTemplateRepl.queryForObject(QueryMigration.GET_MAX_CUST_ID,Long.class);
	}
	
	@Override
	public void updateEmailResponse(List<Map<String, Object>> data) {
        try {
        	List<SqlParameterSource> parameterSources = new ArrayList<>();
        	data.forEach(i -> {
				MapSqlParameterSource namedParam = new MapSqlParameterSource();
				namedParam.addValue("CustEmailId", i.get("CustEmailId"));
				namedParam.addValue("sentTime", i.get("timeStampDt"));
				namedParam.addValue("eventId", i.get("eventId"));
				parameterSources.add(namedParam);
			});
			
			if (!data.isEmpty()) {
				namedParameterJdbcTemplate.batchUpdate(QueryMigration.UPDATE_COMM_RESPONSE_FRM_MONGO_BY_EMAIL, parameterSources.toArray(new SqlParameterSource[0]));
			}
        	
        } catch (Exception e) {
            logger.error("Error occurred while updateEmailResponse data. {}, error:{}",data, e.getMessage());
        }
	}
	
	@Override
	public List<Map<String, Object>> getEmailDetail(long startId, Integer size, String type) {
		List<Map<String, Object>> responseData = new ArrayList<>();
		if (type.equalsIgnoreCase("email")) {
			responseData = jdbcTemplateRepl.queryForList(QueryMigration.GET_EMAIL_DETAIL_BY_RANGE, startId, size);
		} else if (type.equalsIgnoreCase("mobile")) {
			
		}
		return responseData;
	}

	@Override
	public List<Map<String, Object>> getEncMblDetailsByCustEmail(Integer size, long maxCustMobNo) {
		return this.jdbcTemplateRepl.queryForList(QueryMigration.GET_CUST_MBL_IDS_DETAIL , new Object[] { size, maxCustMobNo});
		}

	@Override
	public List<Map<String, Object>> getEncMblNetwrkDetailsById(Integer size, long maxCustMobNo) {
		return this.jdbcTemplateRepl.queryForList(QueryMigration.GET_CUST_MBL_PB_NETWORK_IDS_DETAIL , new Object[] { size, maxCustMobNo});
		}
	
}
