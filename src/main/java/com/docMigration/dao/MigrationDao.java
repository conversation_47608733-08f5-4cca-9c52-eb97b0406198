/**
 * 
 */
package com.docMigration.dao;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 *
 */
public interface MigrationDao {

	List<Map<String, Object>> getCustEmailDetail(int size, long maxCustId);

	List<Map<String, Object>> getCustMobileDetail(int size, long maxMobileNo);

	List<Map<String, Object>> getCustDetail(int size, long maxCustId);

	List<Map<String, Object>> getEncMobileDetailsByCustMob(int size, long maxCustMobNo);
	
	List<Map<String, Object>> getEncMobileNetworkDetailsByCustMob(int size, long maxCustMobNo);

	List<Map<String, Object>> getmobileDetailsByEnc(String encMobile);

	void updateCustEncMobile(long mobileNo, String encMobile, int clientId);
	
	List<Map<String, Object>> getEncEmailDetailsByCustEmail(int size, long maxCustMobNo);
	
	List<Map<String, Object>> getCommResponse(long batchStartId, long size, String eventType);

	List<Map<String, Object>> getEmailDetailsByEnc(String encEmail);

	void updateCustEncEmail(String email, String encEmail, int clientId);

	void bulkUpdateMobData(List<Map<String, Object>> updateList);
	
	void bulkUpdateMobNetworkData(List<Map<String, Object>> updateList);
	
	void bulkUpdateEmailData(List<Map<String, Object>> updateList);
	
	void bulkUpdateEmailNtwrkData(List<Map<String, Object>> updateList);
	
	void bulkUpdateMblData(List<Map<String, Object>> updateList);
	
	void bulkUpdateMblNtwrkData(List<Map<String, Object>> updateList);

	List<Map<String, Object>> getMobilePbNetworkDetail(int size, long maxMobileNo);

	List<Map<String, Object>> getEncEmailNetwrkDetailsById(int size, long maxEmailId);

	List<Map<String, Object>> getEmailNetworkDetail(int size, long maxCustId);

	long getMaxCustId();

	void updateEmailResponse(List<Map<String, Object>> data);

	List<Map<String, Object>> getEmailDetail(long startId, Integer size, String type);

	List<Map<String, Object>> getEncMblDetailsByCustEmail(Integer size, long maxCustEmailId);

	List<Map<String, Object>> getEncMblNetwrkDetailsById(Integer size, long maxCustEmailId);

}
