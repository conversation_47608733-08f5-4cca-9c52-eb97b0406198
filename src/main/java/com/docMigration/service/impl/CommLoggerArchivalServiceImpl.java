package com.docMigration.service.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import com.docMigration.util.NStoS3util;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import com.docMigration.cleanup.entity.StaticMaster;
import com.docMigration.dao.ChatMongoDao;
import com.docMigration.service.CommLoggerArchivalService;
import com.docMigration.service.S3BucketService;
import com.docMigration.util.DateUtil;
import com.mongodb.BasicDBObject;
import com.mongodb.DBObject;

@Service
public class CommLoggerArchivalServiceImpl implements CommLoggerArchivalService {
	
	private final Logger logger = LoggerFactory.getLogger(CommLoggerArchivalServiceImpl.class);

	@Autowired
	S3BucketService s3BucketService;
	
	@Autowired
	@Qualifier("commLoggerMongoDao")
	ChatMongoDao commLoggerMongoDao;

	@Autowired
	@Qualifier("newCommBoxDao")
	ChatMongoDao newCommBoxDao;
	@Autowired
	@Qualifier("newArchivalMongoDao")
	ChatMongoDao newArchMongoDao;

	public static boolean moveWhatsappIsStop = false;

	@Override
	public void moveBookLeads(String coll, Integer size, Boolean timeCheck, String db, int days , boolean isStop , String lastIdLogger) throws Exception {

		logger.info("Inside moveLead");
		
		Date startDate = new Date();
		startDate = DateUtil.setHours(startDate, 7);
		startDate = DateUtil.setMinute(startDate, 0);
		startDate = DateUtil.setSeconds(startDate, 0);

		Date endDate = new Date();
		endDate = DateUtil.setHours(endDate, 20);
		endDate = DateUtil.setMinute(endDate, 0);
		endDate = DateUtil.setSeconds(endDate, 0);
		
		DBObject queryObject = new BasicDBObject();
		
		DBObject orderObject = new BasicDBObject("_id", 1);
		
		List<Object> listOfIds = new ArrayList<>();
		
		int offset = 0;
		int len = size;
		//movedIdsLogger
		DBObject queryForDeletId = new BasicDBObject();
		queryForDeletId.put("coll", coll);
		BasicDBObject integrationDeleteId = commLoggerMongoDao.getDBObjectByDbName(lastIdLogger,queryForDeletId,db);
		
		DBObject queryOnId = new BasicDBObject();
		
		//only provide startId at once we are starting the batch.
		if(integrationDeleteId!=null && integrationDeleteId.size()>0){
			queryOnId.put("$gte", integrationDeleteId.get("movedId"));
		}
		
		queryOnId.put("$lt", new ObjectId(DateUtil.getStringToDate(DateUtil.getFormatedDate(days, DateUtil.DATE_FORMAT_EE_MMM_dd_hh_mm_ss_z_yyyy),DateUtil.DATE_FORMAT_EE_MMM_dd_hh_mm_ss_z_yyyy)));
		//queryOnId.put("$lt", twoWheelerDeletedId.get("endId"));
		
		queryObject.put("_id", queryOnId);
		List<DBObject> dbObjectsList = commLoggerMongoDao.getDBObjects(coll, queryObject, null, offset, len, orderObject,db);
		logger.info("StaticMaster.isDeleteCromaLoggerStop() {}", isStop);
		while (dbObjectsList != null && dbObjectsList.size() > 0 && !StaticMaster.isDeleteCommDbLoggerStop()) {
			Date todayDate = new Date();
			startDate = DateUtil.setHours(todayDate, 7);
			startDate = DateUtil.setMinute(startDate, 0);
			startDate = DateUtil.setSeconds(startDate, 0);
			
			endDate = DateUtil.setHours(todayDate, 20);
			endDate = DateUtil.setMinute(endDate, 0);
			endDate = DateUtil.setSeconds(endDate, 0);
			logger.info("Today date in service: {}, start date {} , end date {}", todayDate, startDate, endDate);
			if (todayDate.before(startDate) || todayDate.after(endDate) || !timeCheck) {
				
				Date date = null;
				for (DBObject res : dbObjectsList) {
					//logger.info("Id found , {} ", res.getObjectId("_id"));
					listOfIds.add(res.get("_id"));
					date = ((ObjectId)res.get("_id")).getDate();
				}
				
				DBObject queryFormovedIds = new BasicDBObject();
				queryFormovedIds.put("_id", new BasicDBObject().append("$in", listOfIds));
				
				try {
					newArchMongoDao.addRows(coll+"_"+DateUtil.parseDate(date, DateUtil.DATE_FORMAT_YYYY_MM_DD), dbObjectsList, db);
				}catch(Exception e) {
					logger.error("Error while adding in moveBookLeads , {}", e.getMessage());
					if(!e.getMessage().contains("timeout")) {
						throw e;
					}
				}
				
				commLoggerMongoDao.deleteRow(coll, queryFormovedIds, true,db);
				Thread.sleep(StaticMaster.getWaitForNextDeleteBatchInCar()*1000);
				
				listOfIds = new ArrayList<>();
				logger.info(" Last Id deleted : {}", dbObjectsList.get(dbObjectsList.size()-1).get("_id"));
				DBObject queryJson;
				if(integrationDeleteId!=null && integrationDeleteId.size()> 0 && integrationDeleteId.getObjectId("_id")!=null) {
					DBObject setquery = new BasicDBObject("movedId", dbObjectsList.get(dbObjectsList.size()-1).get("_id"));
					queryJson = new BasicDBObject("_id", integrationDeleteId.getObjectId("_id"));
					DBObject rowJson = new BasicDBObject("$set", setquery);
					commLoggerMongoDao.updateRow(lastIdLogger, queryJson, rowJson, false,false,db);
				}
				
					queryOnId.put("$gt", dbObjectsList.get(dbObjectsList.size()-1).get("_id"));
				
				queryObject.put("_id", queryOnId);
				
				dbObjectsList = commLoggerMongoDao.getDBObjects(coll, queryObject, null, offset, len, orderObject,db);
				
			}else {
				dbObjectsList = null;
			}
		}
		
	
	}

	@Override
	public void moveWhatsappChat(String dbColl, Integer size, Boolean timeCheck, Integer days, boolean isStop, String lastIdLogger) throws Exception{

		logger.info("Inside moveWhatsappChat");
		String[] nsArray = dbColl.split("\\.");
		String db = nsArray[0];
		String coll = nsArray[1];;
		Date startDate = new Date();
		startDate = DateUtil.setHours(startDate, 7);
		startDate = DateUtil.setMinute(startDate, 0);
		startDate = DateUtil.setSeconds(startDate, 0);

		Date endDate = new Date();
		endDate = DateUtil.setHours(endDate, 20);
		endDate = DateUtil.setMinute(endDate, 0);
		endDate = DateUtil.setSeconds(endDate, 0);

		DBObject queryObject = new BasicDBObject();

		DBObject orderObject = new BasicDBObject("_id", 1);

		List<Object> listOfIds = new ArrayList<>();

		int offset = 0;
		int len = size;
		long totalMigrated = 0;
		//movedIdsLogger
		DBObject queryForDeletId = new BasicDBObject();
		queryForDeletId.put("coll", dbColl);
		BasicDBObject whatsappBkpDeleteId = newArchMongoDao.getDBObjectByDbName(NStoS3util.lastUpdatedIdColl, queryForDeletId, NStoS3util.lastUpdatedIdDB);

		DBObject queryOnId = new BasicDBObject();

		//only provide startId at once we are starting the batch.
		if(whatsappBkpDeleteId !=null && whatsappBkpDeleteId.size()>0){
			queryOnId.put("$gte", whatsappBkpDeleteId.get("movedId"));
			totalMigrated = (long)whatsappBkpDeleteId.get("totalMigrated");
		}

		queryOnId.put("$lt", new ObjectId("6666f09c0000000000000000"));
		//queryOnId.put("$lt", twoWheelerDeletedId.get("endId"));

		queryObject.put("_id", queryOnId);
		List<DBObject> dbObjectsList = newCommBoxDao.getDBObjects(coll, queryObject, null, offset, len, orderObject,db);
		logger.info("moveWhatsappIsStop {}", moveWhatsappIsStop);
		while (dbObjectsList != null && dbObjectsList.size() > 0 && !moveWhatsappIsStop) {
			Date todayDate = new Date();
			startDate = DateUtil.setHours(todayDate, 7);
			startDate = DateUtil.setMinute(startDate, 0);
			startDate = DateUtil.setSeconds(startDate, 0);

			endDate = DateUtil.setHours(todayDate, 20);
			endDate = DateUtil.setMinute(endDate, 0);
			endDate = DateUtil.setSeconds(endDate, 0);
			logger.info("Today date in service: {}, start date {} , end date {}", todayDate, startDate, endDate);
			if (todayDate.before(startDate) || todayDate.after(endDate) || !timeCheck) {

				Date date = null;
//				for (DBObject res : dbObjectsList) {
//					//logger.info("Id found , {} ", res.getObjectId("_id"));
//					listOfIds.add(res.get("_id"));
//				}

				DBObject queryFormovedIds = new BasicDBObject();
				queryFormovedIds.put("_id", new BasicDBObject().append("$in", listOfIds));

				try {
					newCommBoxDao.addRows("WhatsappChat", dbObjectsList, db);
					logger.info("moved succesfully in Whatsapp , {}", dbObjectsList.get(dbObjectsList.size()-1).get("_id"));
				}catch(Exception e) {
					logger.error("Error while adding in Whatsapp , {}", e.getMessage());
					if(!e.getMessage().contains("duplicate")) {
						throw e;
					}
				}

//				newCommBoxDao.deleteRow(coll, queryFormovedIds, true,db);
				Thread.sleep(StaticMaster.getWaitForNextDeleteBatchInCar()*1000);

				listOfIds = new ArrayList<>();
				logger.info("Whatsapp Last Id deleted : {}", dbObjectsList.get(dbObjectsList.size()-1).get("_id"));
				DBObject queryJson;
				if(whatsappBkpDeleteId !=null && whatsappBkpDeleteId.size()> 0 && whatsappBkpDeleteId.getObjectId("_id")!=null) {
					totalMigrated = totalMigrated + dbObjectsList.size();
					DBObject setquery = new BasicDBObject("movedId", dbObjectsList.get(dbObjectsList.size() - 1).get("_id"))
							.append("totalMigrated", totalMigrated)
							.append("updatedOn", new Date());
					queryJson = new BasicDBObject("_id", whatsappBkpDeleteId.getObjectId("_id"));
					DBObject rowJson = new BasicDBObject("$set", setquery);
					newArchMongoDao.updateRow(NStoS3util.lastUpdatedIdColl, queryJson, rowJson, false, false, NStoS3util.lastUpdatedIdDB);
				}

				queryOnId.put("$gt", dbObjectsList.get(dbObjectsList.size()-1).get("_id"));

				queryObject.put("_id", queryOnId);

				dbObjectsList = newCommBoxDao.getDBObjects(coll, queryObject, null, offset, len, orderObject,db);

			}else {
				dbObjectsList = null;
			}
		}


	}

}
