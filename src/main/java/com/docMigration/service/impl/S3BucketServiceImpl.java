package com.docMigration.service.impl;

import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLConnection;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.zip.GZIPInputStream;

import javax.annotation.PostConstruct;

import org.apache.commons.io.FilenameUtils;
import org.apache.commons.io.output.ByteArrayOutputStream;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import com.amazonaws.AmazonServiceException;
import com.amazonaws.SdkClientException;
import com.amazonaws.auth.AWSStaticCredentialsProvider;
import com.amazonaws.auth.BasicSessionCredentials;
import com.amazonaws.auth.InstanceProfileCredentialsProvider;
import com.amazonaws.regions.Regions;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.AmazonS3ClientBuilder;
import com.amazonaws.services.s3.model.AmazonS3Exception;
import com.amazonaws.services.s3.model.ObjectMetadata;
import com.amazonaws.services.s3.model.PutObjectRequest;
import com.amazonaws.services.s3.model.S3Object;
import com.amazonaws.services.s3.model.S3ObjectInputStream;
import com.docMigration.service.S3BucketService;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;

import java.io.*;


@Service
public class S3BucketServiceImpl implements S3BucketService {

	private final Logger logger = LoggerFactory.getLogger(S3BucketServiceImpl.class);

	@Value("${aws.access_key}")
	public String accessKey;

	@Value("${aws.secret_key}")
	public String secretKey;

	@Value("${aws.pgi_s3Bucket_link}")
	private String awslinkId;
	
	@Value("#{'${dev.file.path.access.key:/opt/props/accessKeyFile.txt}'}")
	String cache;

	@Value("${isLocalDev}")
	boolean isLocalDev;

	AmazonS3 s3;

	private static BasicSessionCredentials cachedCredentials = null;
	private static long credentialsExpiryTime = 0;

	private static final String LOCAL_CREDENTIALS_API = "https://pblabsapi.policybazaar.com/api/getawscredentials/";
	private static final String BASIC_AUTH_HEADER = "Basic ZGlhbGVyOjkyOHpPcmg2b3E1Sg==";
	private static final File CREDENTIAL_CACHE_FILE = new File(System.getProperty("user.home") + "/.aws_cached_credentials.json");

	
	private static final Map<String, Object> mimeTypeMap = new HashMap();

	@PostConstruct
    public void init() {
        try {
					System.out.println("--------------isLocalDev " + isLocalDev);
            if (!isLocalDev) {
                // AWS EC2/ECS environment
                s3 = AmazonS3ClientBuilder.standard()
                        .withCredentials(new InstanceProfileCredentialsProvider(false))
                        .withRegion(Regions.AP_SOUTH_1)
                        .build();
                System.out.println("S3 initialized using instance role.");
            } else {
                // Local Dev: Fetch credentials from secured API
                BasicSessionCredentials credentials = getCachedCredentials();
                if (credentials != null) {
                    s3 = AmazonS3ClientBuilder.standard()
                            .withCredentials(new AWSStaticCredentialsProvider(credentials))
                            .withRegion(Regions.AP_SOUTH_1)
                            .build();
                    System.out.println("S3 initialized using local dev credentials.");
                } else {
                    System.err.println("Failed to initialize S3 for local dev.");
                }
            }

            loadMimeTypes();

        } catch (Exception e) {
            System.err.println("S3 Initialization Error: " + e.getMessage());
            e.printStackTrace();
        }
    }

    private BasicSessionCredentials getCachedCredentials() {
    long now = System.currentTimeMillis();

    // 1. Check in-memory cache
    if (cachedCredentials != null && now < credentialsExpiryTime) {
        return cachedCredentials;
    }

    // 2. Check file cache
    if (CREDENTIAL_CACHE_FILE.exists()) {
        try (InputStream is = new FileInputStream(CREDENTIAL_CACHE_FILE)) {
            ObjectMapper mapper = new ObjectMapper();
            JsonNode node = mapper.readTree(is);

            long expiry = node.path("expiresAt").asLong();
            if (now < expiry) {
                cachedCredentials = new BasicSessionCredentials(
                        node.path("AccessKeyId").asText(),
                        node.path("SecretAccessKey").asText(),
                        node.path("Token").asText()
                );
                credentialsExpiryTime = expiry;
                return cachedCredentials;
            }
        } catch (IOException e) {
            System.err.println("Failed to read cached credential file: " + e.getMessage());
        }
    }

    // 3. Fetch from API
    try {
        URL url = new URL(LOCAL_CREDENTIALS_API);
        HttpURLConnection conn = (HttpURLConnection) url.openConnection();
        conn.setConnectTimeout(5000);
        conn.setReadTimeout(5000);
        conn.setRequestMethod("GET");
        conn.setRequestProperty("Accept", "application/json");
        conn.setRequestProperty("Authorization", BASIC_AUTH_HEADER);

        if (conn.getResponseCode() == 200) {
            String jsonResponse = new BufferedReader(new InputStreamReader(conn.getInputStream()))
                    .lines().collect(Collectors.joining("\n"));

            ObjectMapper mapper = new ObjectMapper();
            JsonNode root = mapper.readTree(jsonResponse);

            if (root.path("status").asBoolean(false)) {
                JsonNode creds = root.path("credentials");

                cachedCredentials = new BasicSessionCredentials(
                        creds.path("AccessKeyId").asText(),
                        creds.path("SecretAccessKey").asText(),
                        creds.path("Token").asText()
                );

                // Cache expiry: 7.5 hours from now
                long expiry = now + (7 * 60 * 60 * 1000 + 30 * 60 * 1000);
                credentialsExpiryTime = expiry;

                // Save to file
                ObjectNode saveNode = mapper.createObjectNode();
                saveNode.put("AccessKeyId", creds.path("AccessKeyId").asText());
                saveNode.put("SecretAccessKey", creds.path("SecretAccessKey").asText());
                saveNode.put("Token", creds.path("Token").asText());
                saveNode.put("expiresAt", expiry);

                try (FileWriter fw = new FileWriter(CREDENTIAL_CACHE_FILE)) {
                    mapper.writerWithDefaultPrettyPrinter().writeValue(fw, saveNode);
                }

                return cachedCredentials;
            }
        } else {
            System.err.println("Credential API error: " + conn.getResponseCode());
        }
    } catch (Exception e) {
        System.err.println("Error fetching credentials: " + e.getMessage());
        e.printStackTrace();
    }

    return null;
}


    private void loadMimeTypes() {
        mimeTypeMap.put("application/pdf", "pdf");
        mimeTypeMap.put("text/plain", "txt");
        mimeTypeMap.put("application/msword", "doc");
        mimeTypeMap.put("application/vnd.openxmlformats-officedocument.wordprocessingml.document", "docx");
        mimeTypeMap.put("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "xlsx");
        mimeTypeMap.put("application/vnd.ms-excel", "xls");
        mimeTypeMap.put("image/jpeg", "jpeg");
    }

	@Override
	public String createBucketData(File file, String bucketName, String fileName, String folderName) {
		String url = null;
		try {

			logger.info("AWS S3 connection build--- createBucketData method");

			logger.info(folderName + " -- ___ -- " + fileName);
			if (folderName != null && !folderName.equals("")) {
				folderName = folderName + "/";
			} else
				folderName = "";
			boolean exist = s3.doesObjectExist(bucketName, folderName + fileName);
			if (exist) {
				return "File Already Exist";
			}

			byte[] b = readBytesFromFile(file);
			ByteArrayInputStream byteArrayInputStream = new ByteArrayInputStream(b);
			// s3.putObject("policycopy", "Bytes", "Uploaded String
			// Object");
			ObjectMetadata metadata = new ObjectMetadata();
			metadata.setContentType("plain/text");
			metadata.setContentLength(b.length);
			// metadata.addUserMetadata("x-amz-meta-title", "docprime" );
			PutObjectRequest request = new PutObjectRequest(bucketName, folderName + fileName, byteArrayInputStream,
					metadata);

			request.setMetadata(metadata);
			s3.putObject(request);
			url = awslinkId + folderName + fileName;
		} catch (AmazonS3Exception e) {
			System.err.println(e.getErrorMessage());
			logger.error("S3BucketImpl createBucket  ---" + e.getMessage());
		}
		return url;
	}

	private static byte[] readBytesFromFile(File file) {
		FileInputStream fileInputStream = null;
		byte[] bytesArray = null;
		try {
			bytesArray = new byte[(int) file.length()];
			fileInputStream = new FileInputStream(file);
			fileInputStream.read(bytesArray);

		} catch (IOException e) {
			e.printStackTrace();
		} finally {
			if (fileInputStream != null) {
				try {
					fileInputStream.close();
				} catch (IOException e) {
					e.printStackTrace();
				}
			}
		}
		return bytesArray;
	}

	@Override
	public String deleteBucketData(String fileName, String bucketName, String folderName) {
		try {
			// BasicAWSCredentials awsCreds = new BasicAWSCredentials(accessKey,
			// secretKey);
			// AmazonS3 s3 = AmazonS3Client.builder().withCredentials(new
			// AWSStaticCredentialsProvider(awsCreds))
			// .withRegion(Regions.AP_SOUTH_1).build();
			logger.info("AWS S3 connection build--- deleteBucket method");
			if (folderName != null && !folderName.equals("")) {
				folderName = folderName + "/";
			} else
				folderName = "";
			try {
				logger.info(folderName + "  ___ " + fileName);
				// ObjectListing objectListing = s3.listObjects(bucketName);
				boolean exist = s3.doesObjectExist(bucketName, folderName + fileName);
				if (!exist) {
					return "File Not Found";
				}
				s3.deleteObject(bucketName, folderName + fileName);
			} catch (AmazonS3Exception e) {
				System.err.println(e.getErrorMessage());
				logger.error("S3BucketImpl createBucket  ---" + e.getMessage());
				return e.getMessage();

			}
			// }
		} catch (Exception e) {
			logger.error("S3BucketImpl createBucket  ---" + e.getMessage());
			return e.getMessage();
		}
		return "Deleted Successfully";
	}

	/**
	 * This function will rename the file name as :- orginalFileName + objId'. File
	 * = abc.txt eg:- orgFileName = abc , param given :- 561qfs7621f Then FileName
	 * will be :- abc_561qfs7621f.txt
	 * 
	 * It has been done to get the unq Id we generated may be for dynamo or mongo or
	 * ofr other purpose at any time using filename.
	 * 
	 */
	@Override
	public String getfileOrgName(MultipartFile file, String objid, String mimeType, String fileNameSentByBu) {
		String orgfilename;
		//for default naming
		if (fileNameSentByBu != null) {
			fileNameSentByBu = fileNameSentByBu.replace(" ", "_").replace("#", "_").replace("$", "_").replace("(", "_")
					.replace(")", "_").replace("*", "_").replace("%", "_");
			orgfilename = objid+"_"+fileNameSentByBu;
		}else {
			orgfilename = objid+"_"+file.getName().replace(" ", "_");
		}
		if (file != null) {
			logger.info("file found.. in S3BucketServiceimpl");
			String ext = "";
			String fileName = file.getOriginalFilename().replace(" ", "_").replace("#", "_").replace("$", "_").replace("(", "_")
					.replace(")", "_").replace("*", "_").replace("%", "_"); // replacing '#' due to error occured while opening file using url formed.
			ext = FilenameUtils.getExtension(fileName);
			if (!StringUtils.isEmpty(ext)) {
				logger.info("extension found : " + ext);
				if (!StringUtils.isEmpty(objid)) {
					if (fileNameSentByBu != null) {
						orgfilename = fileNameSentByBu + "_" + objid + "." + ext;
					} else {
						orgfilename = FilenameUtils.removeExtension(fileName) + "_" + objid + "." + ext;
					}
				} else {
					orgfilename = fileName + ext;
				}
			}
			else if(!StringUtils.isEmpty(mimeType) && !StringUtils.isEmpty(mimeTypeMap.get(mimeType)))
			{
				logger.info("Extension not found , adding externally");
				orgfilename = orgfilename + "." + mimeTypeMap.get(mimeType);
			}

		}
		return orgfilename;
	}

	@Override
	public String addMultipartContent(MultipartFile multipartFile, String bucketName, String fileName, String folderName) throws AmazonServiceException, SdkClientException, IOException {
		logger.info("AWS S3 connection build--- addMultipartContent method");
        String url;
		logger.info(folderName + " - " + fileName);
		if (folderName != null && !folderName.equals("")) {
			folderName = folderName + "/";
		} else
			folderName = "";
		boolean exist = s3.doesObjectExist(bucketName, folderName + fileName);
		if (exist) {
			return "File Already Exist";
		}
		ObjectMetadata data = new ObjectMetadata();
		data.setContentType(multipartFile.getContentType());
		data.setContentLength(multipartFile.getSize());
		s3.putObject(bucketName, folderName + fileName, multipartFile.getInputStream(), data);
		url = awslinkId + folderName + fileName;
		return url;
	}

	@Override
	public String addByteContent(byte[] fileByte, String bucketName, String folderName, String fileName, String mimeType) throws IOException {
		logger.info("AWS S3 connection build--- addMultipartContent method");
		String url;
		logger.info("fileName : " + fileName);
		ObjectMetadata metaData = new ObjectMetadata();
		InputStream is = new ByteArrayInputStream(fileByte);
		try {
			if (StringUtils.isEmpty(mimeType))
				mimeType = URLConnection.guessContentTypeFromStream(is);
			logger.info("mime type get for file : {}", mimeType);
			String objectId = new ObjectId().toHexString();
			fileName = getfileOrgNameofByte(is, objectId, mimeType, fileName);
			boolean exist = s3.doesObjectExist(bucketName, folderName + fileName);
			if (exist) {
				return "File Already Exist";
			}

			if (!StringUtils.isEmpty(mimeType))
				metaData.setContentType(mimeType);
			// else we will not set the contentType.
			metaData.setContentLength(fileByte.length);
			s3.putObject(bucketName, folderName + fileName, is, metaData);
			url = awslinkId + folderName + fileName;
			return url;
		} finally {
			is.close();
		}
	}
	
	public String getfileOrgNameofByte(InputStream is, String objid, String mimeType, String fileName) {
		String orgfilename = objid+"_"+fileName.replace(" ", "_");
		if (is != null) {
			logger.info("file found.. in S3BucketServiceimpl");
			String ext = "";
			String name = fileName.replace(" ", "_").replace("#", "_").replace("$", "_").replace("(", "_")
					.replace(")", "_").replace("*", "_").replace("%", "_"); // replacing '#' due to error occured while opening file using url formed.
			ext = FilenameUtils.getExtension(name);
			if (!StringUtils.isEmpty(ext)) {
				logger.info("extension found : " + ext);
					if (!StringUtils.isEmpty(objid))
						orgfilename = FilenameUtils.removeExtension(name)+ "_" + objid + "."+ext;
					else
						orgfilename = name + ext;
			}
			else if(!StringUtils.isEmpty(mimeType) && !StringUtils.isEmpty(mimeTypeMap.get(mimeType)))
			{
				logger.info("Extension not found , adding externally");
				orgfilename = orgfilename + "." + mimeTypeMap.get(mimeType);
			}

		}
		
		logger.info("Orginial file name get :- {} ", orgfilename);
		return orgfilename;
	}

	@Override
	public String addByteContentWithGzip(byte[] fileByte, String bucketName, String folderName, String fileName, String mimeType) throws IOException {
		String url = null;
		ObjectMetadata metaData = new ObjectMetadata();
		InputStream is = new ByteArrayInputStream(fileByte);
		try {
			if (StringUtils.isEmpty(mimeType))
				mimeType = URLConnection.guessContentTypeFromStream(is);
//			fileName = getfileOrgNameofByte(is, objectId, mimeType, fileName);
			boolean exist = s3.doesObjectExist(bucketName, folderName + fileName);
			if (exist) {
				return "File Already Exist";
			}

			if (!StringUtils.isEmpty(mimeType))
				metaData.setContentType(mimeType);
			// else we will not set the contentType.
			metaData.setContentLength(fileByte.length);
			s3.putObject(bucketName, folderName + fileName, is, metaData);
			url = awslinkId + folderName + fileName;
			return url;
		}catch (Exception e) {
			logger.error("Exception caught in addByteContentWithGzip, msg: {}", e.getMessage());
			throw e;
		}
		finally {
			is.close();
		}
	}

	@Override
	public List<Map<String, Object>> getByteContentWithGzip(String bucketName, String folderName, String fileName) throws IOException {
		List<Map<String, Object>> result = null;
		GZIPInputStream gzipInputStream = null;
		ByteArrayOutputStream outputStream = null;
		try {
			// Download .gz file from S3
			String key = folderName + fileName;
			S3Object s3Object = s3.getObject(bucketName, key);
			S3ObjectInputStream s3InputStream = s3Object.getObjectContent();
			gzipInputStream = new GZIPInputStream(s3InputStream);

			// Read decompressed data into a byte array output stream
			outputStream = new ByteArrayOutputStream();
			byte[] buffer = new byte[1024];
			int bytesRead;
			while ((bytesRead = gzipInputStream.read(buffer)) != -1) {
				outputStream.write(buffer, 0, bytesRead);
			}
			ObjectMapper objectMapper = new ObjectMapper();
			objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
			result = objectMapper.readValue(outputStream.toString("UTF-8"), List.class);
			// Decompress .gz file
			System.out.println("File downloaded, decompressed, and converted to JSON successfully!");
		} catch (Exception e) {
			System.err.println("Error downloading, decompressing, or converting file to JSON: " + e);
		}
		finally {
			// Close streams
			if(gzipInputStream!=null)
				gzipInputStream.close();
			if(outputStream!=null)
				outputStream.close();
		}
		return result;
	}

}
