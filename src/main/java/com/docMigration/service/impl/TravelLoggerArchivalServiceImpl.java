package com.docMigration.service.impl;

import com.docMigration.dao.ArchivalMongoDao;
import com.docMigration.dao.ChatMongoDao;
import com.docMigration.dao.GenericMongoDao;
import com.docMigration.service.S3BucketService;
import com.docMigration.util.AppConstants;
import com.docMigration.util.DateUtil;
import com.docMigration.util.NStoS3util;
import com.mongodb.BasicDBObject;
import com.mongodb.DBObject;
import com.mongodb.util.JSON;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashSet;
import java.util.List;

import static com.docMigration.util.CompressionUtil.compressData;

@Service
public class TravelLoggerArchivalServiceImpl {
    private final Logger logger = LoggerFactory.getLogger(TravelLoggerArchivalServiceImpl.class);

    public static boolean isTravelLogS3JobRunning = false;
    public static boolean isTravelLogS3JobStop = false;
    public static boolean isOldTravelLogS3JobRunning = false;
    public static boolean isOldTravelLogS3JobStop = false;
    @Autowired
    @Qualifier("genericMongoDao")
    private GenericMongoDao genericMongoDao;


    @Autowired
    @Qualifier("oldMongoDao")
    private ChatMongoDao oldLoggerDao;
    @Autowired
    @Qualifier("newArchivalMongoDao")
    ChatMongoDao newArchMongoDao;
    @Autowired
    private S3BucketService s3service;
    public void moveTravelBookLeads(String dbColl, Integer size, Boolean timeCheck, int days, int chunkSize) {
        logger.info("Inside moveBookLeads , ns: {}, size: {}, timeCheck: {}, days", dbColl, size, timeCheck, days, chunkSize);
        List<DBObject> dbObjectsList = null;

        try {
            String[] nsArray = dbColl.split("\\.");
            String db = nsArray[0];
            String coll = nsArray[1];;
            int len = size;
            long totalMigrated = 0;
            DBObject queryForDeletId = new BasicDBObject();
            queryForDeletId.put("coll", dbColl);
            BasicDBObject motorDeletedId = newArchMongoDao.getDBObjectByDbName(NStoS3util.lastUpdatedIdColl, queryForDeletId, NStoS3util.lastUpdatedIdDB);

            DBObject queryOnId = new BasicDBObject();
            //only provide startId at once we are starting the batch.
            if (motorDeletedId != null && motorDeletedId.size() > 0) {
                queryOnId.put("$gte", motorDeletedId.get("movedId"));
                totalMigrated = (long) motorDeletedId.get("totalMigrated");
            }
            // days should be three months  -- 180 days
            queryOnId.put("$lt", new ObjectId(DateUtil.getStringToDate(DateUtil.getFormatedDate(days, DateUtil.DATE_FORMAT_EE_MMM_dd_hh_mm_ss_z_yyyy), DateUtil.DATE_FORMAT_EE_MMM_dd_hh_mm_ss_z_yyyy)));
            DBObject queryObject = new BasicDBObject();
            queryObject.put("_id", queryOnId);
            BasicDBObject projection = new BasicDBObject("_id", 1).append("MatrixLeadID", 1);
            DBObject orderObject = new BasicDBObject("_id", 1);
            dbObjectsList = genericMongoDao.getDBObjects(coll, queryObject, projection, 0, len, orderObject, db);
            logger.info("isTravelLogS3JobStop {}, isTravelLogS3JobRunning: {} ", isTravelLogS3JobStop, isTravelLogS3JobRunning);
            while (dbObjectsList != null && dbObjectsList.size() > 0 && !isTravelLogS3JobStop) {

                if (!timeCheck) {
                    List<Object> listOfIds = new ArrayList<>();
                    HashSet<Integer> matrixLeadIds = new HashSet<>();
                    for (DBObject jsonDocument : dbObjectsList) {
                        listOfIds.add(jsonDocument.get("_id"));
                        Integer matrixLeadID = (Integer) jsonDocument.get("MatrixLeadID");
                        if (matrixLeadID != null && matrixLeadID > 0 && !matrixLeadIds.contains(matrixLeadID)) {
                            matrixLeadIds.add(matrixLeadID);
                            DBObject queryOnrequestId = new BasicDBObject();
                            queryOnrequestId.put("MatrixLeadID", matrixLeadID);
                            int offset = 0;
                            List<DBObject> dbList = genericMongoDao.getDBObjects(coll, queryOnrequestId, offset, chunkSize, orderObject, db);
                            if(dbList.size() == chunkSize) {
                                offset = offset + chunkSize;
                                dbList = getList(coll, queryOnrequestId, offset, orderObject, db, dbList, chunkSize);

                            }
                            List<DBObject> dbListToAdd = new ArrayList<>();
                            for(DBObject dbDocs : dbList) {
                                // check condition to move for travel
                                boolean haveToMove =  matchesCriteria(dbDocs);
                                if(haveToMove){
                                    dbListToAdd.add(dbDocs);
                                }
                            }
                            String jsonString;
                            if (dbList != null) {
                                try {
                                    if(dbListToAdd!=null && dbListToAdd.size()>0) {
                                        jsonString = JSON.serialize(dbListToAdd);
                                        // Compress JSON data
                                        byte[] compressedData = compressData(jsonString);
                                        //String deCompressedData = decompressData(compressedData);
                                        String fileName = matrixLeadID + ".json.gz";
                                        logger.info("Travel fileName : " + fileName);
                                        String folderName = NStoS3util.MAP_NS_TO_S3_PATH.get(dbColl);
                                        String url = s3service.addByteContentWithGzip(compressedData, NStoS3util.BUCKET_NAME_LOGGER_ARCHIVAL, folderName, fileName, "application/gzip");
                                        if (url.equals(AppConstants.FILE_ALREADY_EXIST)) {
                                            try {
                                                newArchMongoDao.addRows(coll, dbList, db);
                                            } catch (Exception e) {
                                                logger.error("Error while adding in moveBookLeads , {}", e.getMessage());
                                            }
                                        }
                                    }
                                    for (DBObject reqIdDoc : dbList) {
                                        listOfIds.add(reqIdDoc.get("_id"));
                                    }
                                } catch (Exception e) {
                                    logger.error("Error serializing DBObject: " + e.getMessage());
                                    return;
                                }
                            }
                        }
                    }
                    matrixLeadIds = null;
                    List<Object> subList = new ArrayList<>();
                    int startIndx = 0;
                    int lastIndx = 100;
                    if(listOfIds.size()>100){
                        while(startIndx < listOfIds.size()){
                            if(startIndx + 100 >= listOfIds.size()){
                                lastIndx =  listOfIds.size();
                            }
                            subList = listOfIds.subList(startIndx, lastIndx);
                            DBObject queryFormovedIds = new BasicDBObject();
                            queryFormovedIds.put("_id", new BasicDBObject().append("$in", subList));
                            genericMongoDao.deleteRow(coll, queryFormovedIds, true, db);
                            logger.info("Travel Last Id deleted : {}", subList.get(subList.size() - 1));
                            startIndx = lastIndx;
                            lastIndx = lastIndx+ 100;
                            subList = null;
                        }
                    }
                    else{
                        DBObject queryFormovedIds = new BasicDBObject();
                        queryFormovedIds.put("_id", new BasicDBObject().append("$in", listOfIds));
                        genericMongoDao.deleteRow(coll, queryFormovedIds, true, db);
                        logger.info("Travel Last Id deleted : {}", dbObjectsList.get(dbObjectsList.size() - 1).get("_id"));
                    }
                    listOfIds = null;
                    DBObject queryJson;
                    if (motorDeletedId != null && motorDeletedId.size() > 0 && motorDeletedId.getObjectId("_id") != null) {
                        totalMigrated = totalMigrated + dbObjectsList.size();
                        DBObject setquery = new BasicDBObject("movedId", dbObjectsList.get(dbObjectsList.size() - 1).get("_id"))
                                .append("totalMigrated", totalMigrated)
                                .append("updatedOn", new Date());
                        queryJson = new BasicDBObject("_id", motorDeletedId.getObjectId("_id"));
                        DBObject rowJson = new BasicDBObject("$set", setquery);
                        newArchMongoDao.updateRow(NStoS3util.lastUpdatedIdColl, queryJson, rowJson, false, false, NStoS3util.lastUpdatedIdDB);
                    }
                    queryOnId.put("$gt", dbObjectsList.get(dbObjectsList.size() - 1).get("_id"));
                    queryObject.put("_id", queryOnId);
                    dbObjectsList = null;
                    dbObjectsList = genericMongoDao.getDBObjects(coll, queryObject, projection, 0, len, orderObject, db);
                } else {
                    dbObjectsList = null;
                }
            }
        }catch (Exception e) {
            logger.error("Exception caught into moveMotorBookLeads, msg: {}", e.getMessage());
        } finally {
            isTravelLogS3JobRunning = false;
            isTravelLogS3JobStop = false;
        }
    }
    private List<DBObject> getList(String coll, DBObject queryOnrequestId, int offset, DBObject orderObject, String db, List<DBObject> resultList, int chunkSize) {

        try {
            List<DBObject> dataList = genericMongoDao.getDBObjects(coll, queryOnrequestId, offset, chunkSize, null, db);
            while (dataList.size() > 0){
                resultList.addAll(dataList);
                offset += chunkSize;
                dataList = genericMongoDao.getDBObjects(coll, queryOnrequestId, offset, chunkSize, null, db);
            }
        } catch (Exception e) {
            logger.error("Exception while getList");
        }
        return resultList;
    }
    private List<DBObject> getOldList(String coll, DBObject queryOnrequestId, int offset, DBObject orderObject, String db, List<DBObject> resultList, int chunkSize) {

        try {
            List<DBObject> dataList = oldLoggerDao.getDBObjects(coll, queryOnrequestId, offset, chunkSize, orderObject, db);
            while (dataList.size() > 0){
                resultList.addAll(dataList);
                offset += chunkSize;
                dataList = oldLoggerDao.getDBObjects(coll, queryOnrequestId, offset, chunkSize, orderObject, db);
            }
        } catch (Exception e) {
            logger.error("Exception while getOldList");
        }
        return resultList;
    }
    private static boolean matchesCriteria(DBObject document) {
        String logName = (String) document.get("LogName");
        Integer logType = (Integer) document.get("LogType");
        String methodName = (String) document.get("MethodName");
        // Check criteria
        if ("ProposalLogNew".equals(logName)) {
            return true;
        }
        if (!"CoreAPILog".equals(logName)) {
            return false;
        }
        if (logType !=null && (logType == 2 || logType == 3)) {
            return false;
        }
        if ("ValidateToken".equals(methodName) ||
                "SaveSeoDataV2".equals(methodName) ||
                "SavePreQuoteV3".equals(methodName) ||
                "SaveQuoteV2".equals(methodName) ||
                "ModifyProposalDataV1".equals(methodName) ||
                "PayfirstProposalBL->ModifyProposalDataV1".equals(methodName) ||
                "PayfirstProposalBL->ModifyProposalDataV1PostPayment".equals(methodName) ||
                "GetPaymentData".equals(methodName) ||
                "PushLeadProductDetails".equals(methodName)) {
            return false;
        }

        return true;
    }

    public void moveOldTravelBookLeads(String dbColl, Integer size, int days, int chunkSize) {
        logger.info("Inside moveBookLeads , ns: {}, size: {}, days:{}, chunkcsize:{}", dbColl, size, days, chunkSize);
        List<DBObject> dbObjectsList = null;

        try {
            String[] nsArray = dbColl.split("\\.");
            String db = nsArray[0];
            String coll = nsArray[1];;
            int len = size;
            long totalMigrated = 0;
            DBObject queryForDeletId = new BasicDBObject();
            queryForDeletId.put("coll", dbColl);
            BasicDBObject motorDeletedId = newArchMongoDao.getDBObjectByDbName(NStoS3util.lastUpdatedIdColl, queryForDeletId, NStoS3util.lastUpdatedIdDB);

            DBObject queryOnId = new BasicDBObject();
            //only provide startId at once we are starting the batch.
            if (motorDeletedId != null && motorDeletedId.size() > 0) {
                queryOnId.put("$gte", motorDeletedId.get("movedId"));
                totalMigrated = (long)motorDeletedId.get("totalMigrated");
            }
            // days should be three months  -- 180 days
            queryOnId.put("$lt", new ObjectId(DateUtil.getStringToDate(DateUtil.getFormatedDate(days, DateUtil.DATE_FORMAT_EE_MMM_dd_hh_mm_ss_z_yyyy), DateUtil.DATE_FORMAT_EE_MMM_dd_hh_mm_ss_z_yyyy)));
            DBObject queryObject = new BasicDBObject();
            queryObject.put("_id", queryOnId);
            BasicDBObject projection = new BasicDBObject("_id", 1).append("MatrixLeadID", 1);
            DBObject orderObject = new BasicDBObject("_id", 1);
            dbObjectsList = oldLoggerDao.getDBObjects(coll, queryObject, projection, 0, len, orderObject, db);
            logger.info("isOldTravelLogS3JobStop {}, isOldTravelLogS3JobRunning: {} ", isOldTravelLogS3JobStop, isOldTravelLogS3JobRunning);
            while (dbObjectsList != null && dbObjectsList.size() > 0 && !isOldTravelLogS3JobStop) {

                List<Object> listOfIds = new ArrayList<>();
                List<Integer> matrixLeadIds = new ArrayList<>();
                for (DBObject jsonDocument : dbObjectsList) {
                    listOfIds.add(jsonDocument.get("_id"));
                    Integer matrixLeadID = (Integer) jsonDocument.get("MatrixLeadID");
                    if (matrixLeadID != null && matrixLeadID > 0 && !matrixLeadIds.contains(matrixLeadID)) {
                        matrixLeadIds.add(matrixLeadID);
                        DBObject queryOnrequestId = new BasicDBObject();
                        queryOnrequestId.put("MatrixLeadID", matrixLeadID);
                        int offset = 0;
                        List<DBObject> dbList = oldLoggerDao.getDBObjects(coll, queryOnrequestId, offset, chunkSize, orderObject, db);
                        if (dbList.size() == chunkSize) {
                            offset = offset + chunkSize;
                            dbList = getOldList(coll, queryOnrequestId, offset, orderObject, db, dbList, chunkSize);

                        }
                        List<DBObject> dbListToAdd = new ArrayList<>();
                        for (DBObject dbDocs : dbList) {
                            // check condition to move for travel
                            boolean haveToMove = matchesCriteria(dbDocs);
                            if (haveToMove) {
                                dbListToAdd.add(dbDocs);
                            }
                        }
                        String jsonString;
                        if (dbList != null) {
                            try {
                                if (dbListToAdd != null && dbListToAdd.size() > 0) {
                                    jsonString = JSON.serialize(dbListToAdd);
                                    // Compress JSON data
                                    byte[] compressedData = compressData(jsonString);
                                    //String deCompressedData = decompressData(compressedData);
                                    String fileName = matrixLeadID + ".json.gz";
                                    logger.info("Travel Old fileName : " + fileName);
                                    String folderName = NStoS3util.MAP_NS_TO_S3_PATH.get(dbColl);
                                    String url = s3service.addByteContentWithGzip(compressedData, NStoS3util.BUCKET_NAME_LOGGER_ARCHIVAL, folderName, fileName, "application/gzip");
                                    if (url.equals(AppConstants.FILE_ALREADY_EXIST)) {
                                        try {
                                            newArchMongoDao.addRows(coll, dbList, db);
                                        } catch (Exception e) {
                                            logger.error("Error while adding in moveOldBookLeadsTravel , {}", e.getMessage());
                                        }
                                    }
                                }
                                for (DBObject reqIdDoc : dbList) {
                                    listOfIds.add(reqIdDoc.get("_id"));
                                }
                            } catch (Exception e) {
                                logger.error("Error serializing DBObject: " + e.getMessage());
                                return;
                            }
                        }
                    }
                }
                matrixLeadIds = null;
                List<Object> subList = new ArrayList<>();
                int startIndx = 0;
                int lastIndx = 100;
                if (listOfIds.size() > 100) {
                    while (startIndx < listOfIds.size()) {
                        if (startIndx + 100 >= listOfIds.size()) {
                            lastIndx = listOfIds.size();
                        }
                        subList = listOfIds.subList(startIndx, lastIndx);
                        DBObject queryFormovedIds = new BasicDBObject();
                        queryFormovedIds.put("_id", new BasicDBObject().append("$in", subList));
                        oldLoggerDao.deleteRow(coll, queryFormovedIds, true, db);
                        logger.info("Travel Last Id deleted : {}", subList.get(subList.size() - 1));
                        startIndx = lastIndx;
                        lastIndx = lastIndx + 100;
                        subList = null;
                    }
                } else {
                    DBObject queryFormovedIds = new BasicDBObject();
                    queryFormovedIds.put("_id", new BasicDBObject().append("$in", listOfIds));
                    oldLoggerDao.deleteRow(coll, queryFormovedIds, true, db);
                    logger.info("Travel Last Id deleted : {}", dbObjectsList.get(dbObjectsList.size() - 1).get("_id"));
                }
                DBObject queryJson;
                if (motorDeletedId != null && motorDeletedId.size() > 0 && motorDeletedId.getObjectId("_id") != null) {
                    totalMigrated = totalMigrated + dbObjectsList.size();
                    DBObject setquery = new BasicDBObject("movedId", dbObjectsList.get(dbObjectsList.size() - 1).get("_id"))
                            .append("totalMigrated", totalMigrated)
                            .append("updatedOn", new Date());
                    queryJson = new BasicDBObject("_id", motorDeletedId.getObjectId("_id"));
                    DBObject rowJson = new BasicDBObject("$set", setquery);
                    newArchMongoDao.updateRow(NStoS3util.lastUpdatedIdColl, queryJson, rowJson, false, false, NStoS3util.lastUpdatedIdDB);
                }
                queryOnId.put("$gt", dbObjectsList.get(dbObjectsList.size() - 1).get("_id"));
                queryObject.put("_id", queryOnId);
                dbObjectsList = oldLoggerDao.getDBObjects(coll, queryObject, projection, 0, len, orderObject, db);
            }
        }catch (Exception e) {
            logger.error("Exception caught into moveMotorBookLeads, msg: {}", e.getMessage());
        } finally {
            isOldTravelLogS3JobRunning = false;
            isOldTravelLogS3JobStop = false;
        }
    }
}
