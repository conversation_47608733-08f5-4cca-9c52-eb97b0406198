package com.docMigration.service.impl;

import com.docMigration.dao.ChatMongoDao;
import com.docMigration.dao.GenericMongoDao;
import com.docMigration.service.S3BucketService;
import com.docMigration.util.AppConstants;
import com.docMigration.util.AppUtil;
import com.docMigration.util.DateUtil;
import com.docMigration.util.NStoS3util;
import com.mongodb.BasicDBObject;
import com.mongodb.DBObject;
import com.mongodb.util.JSON;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Service
public class SmeServiceImpl {
    private final Logger logger = LoggerFactory.getLogger(SmeServiceImpl.class);
    @Autowired
    GenericMongoDao genericMongoDao;

    @Autowired
    @Qualifier("newArchivalMongoDao")
    ChatMongoDao newArchMongoDao;
    @Autowired
    private S3BucketService s3service;
    public static boolean isMoveSMELogModelJobStop = false;
    public static boolean isMoveSMELogModelJobRunning = false;

    public static boolean isMoveSMELogEntryJobStop = false;
    public static boolean isMoveSMELogEntryJobRunning = false;
    public static boolean isMoveSMENonEBLogEntryJobStop = false;
    public static boolean isMoveSMENonEBLogEntryJobRunning = false;
    public static boolean isMoveSMECorpAppLogEntryJobStop = false;
    public static boolean isMoveSMECorpAppLogEntryJobRunning = false;
    public static boolean isMoveSMEMongoLogEntryJobStop = false;
    public static boolean isMoveSMEMongoLogEntryJobRunning = false;
    public void moveSMELogModelToS3(String dbColl, Integer size, int days, int chunkSize) {

        logger.info("Inside moveSMELogModelToS3");
        try {
            boolean isNotUpdate = false;
            String[] nsArray = dbColl.split("\\.");
            String db = nsArray[0];
            String coll = nsArray[1];;
            int len = size;
            long totalMigrated = 0;
            DBObject queryForDeletId = new BasicDBObject();
            queryForDeletId.put("coll", dbColl);
            BasicDBObject commDeletedId = newArchMongoDao.getDBObjectByDbName(NStoS3util.lastUpdatedIdColl, queryForDeletId, NStoS3util.lastUpdatedIdDB);
            DBObject queryOnId = new BasicDBObject();
            //only provide startId at once we are starting the batch.
            if (commDeletedId != null && commDeletedId.size() > 0) {
                queryOnId.put("$gte", commDeletedId.get("movedId"));
                totalMigrated = (long)commDeletedId.get("totalMigrated");
            }
            queryOnId.put("$lt", new ObjectId(DateUtil.getStringToDate(DateUtil.getFormatedDate(days, DateUtil.DATE_FORMAT_EE_MMM_dd_hh_mm_ss_z_yyyy), DateUtil.DATE_FORMAT_EE_MMM_dd_hh_mm_ss_z_yyyy)));
            DBObject queryObject = new BasicDBObject();
            queryObject.put("_id", queryOnId);
            BasicDBObject projection = new BasicDBObject("_id", 1).append("EnquiryId", 1).append("LeadId", 1).append("ProposerId", 1)
                    .append("VisitId", 1);
            DBObject orderObject = new BasicDBObject("_id", 1);
            // get list from mongoDB
            List<DBObject> dbObjectsList = genericMongoDao.getDBObjects(coll, queryObject, projection, 0, len, orderObject, db);
            logger.info("isMoveSMELogModelJobStop: {}, isMoveSMELogModelJobRunning: {}", isMoveSMELogModelJobStop, isMoveSMELogModelJobRunning);

            // if list have data then proceed
            while (dbObjectsList != null && dbObjectsList.size() > 0 && !isMoveSMELogModelJobStop) {
                List<Object> listOfIds = new ArrayList<>();
                List<String> enquiryIdList= new ArrayList<>();
                List<String> leadIdLogList = new ArrayList<>();
                List<String> proposerIdLogList = new ArrayList<>();
                List<String> visitIdLogList = new ArrayList<>();
                for (DBObject jsonDocument : dbObjectsList) {
                    listOfIds.add(jsonDocument.get("_id"));
                    Long enquiryId = jsonDocument.get("EnquiryId") instanceof Long ? ((Long) jsonDocument.get("EnquiryId")) : null;
                    Long leadId = jsonDocument.get("LeadId") instanceof Long ? ((Long) jsonDocument.get("LeadId")) : null;
                    Long proposerId = jsonDocument.get("ProposerId") instanceof Long ? ((Long) jsonDocument.get("ProposerId")) : null;
                    Long visitId = jsonDocument.get("VisitId") instanceof Long ? ((Long) jsonDocument.get("VisitId")) : null;
                    Integer ienquiryId = enquiryId == null && jsonDocument.get("EnquiryId") instanceof Integer ? (Integer) jsonDocument.get("EnquiryId") : null;
                    Integer ileadId = leadId == null && jsonDocument.get("LeadId") instanceof Integer ? (Integer) jsonDocument.get("LeadId") : null;
                    Integer iproposerId = proposerId == null && jsonDocument.get("ProposerId") instanceof Integer ? (Integer) jsonDocument.get("ProposerId") : null;
                    Integer ivisitId = visitId == null && jsonDocument.get("VisitId") instanceof Integer ? (Integer) jsonDocument.get("VisitId") : null;
                    StringBuilder fileName = new StringBuilder();
                    List<DBObject> dbList = null;
                    if ((enquiryId != null && enquiryId > 0) || (ienquiryId != null && ienquiryId > 0)) {
                        dbList = getObjectList(chunkSize, enquiryIdList, enquiryId, ienquiryId, coll, db, "EnquiryId", fileName);
                    } else if ((leadId != null && leadId > 0) || (ileadId != null && ileadId > 0)) {
                        dbList = getObjectList(chunkSize, leadIdLogList, leadId, ileadId, coll, db, "LeadId", fileName);
                    } else if ((proposerId != null && proposerId > 0) || (iproposerId != null && iproposerId > 0)){
                        dbList = getObjectList(chunkSize, proposerIdLogList, proposerId, iproposerId, coll, db, "ProposerId", fileName);
                    } else if ((visitId != null && visitId > 0) || (ivisitId != null && ivisitId > 0)) {
                        dbList = getObjectList(chunkSize, visitIdLogList, visitId, ivisitId, coll, db, "VisitId", fileName);
                    }else{
                        continue;
                    }
                    String jsonString;
                    if (dbList != null) {
                        try {
                            jsonString = JSON.serialize(dbList);

                            // Compress JSON data
                            byte[] compressedData = AppUtil.compressData(jsonString);
                            //String deCompressedData = decompressData(compressedData);
                            logger.info("SME LogModel fileName : " + fileName);
                            String folderName = NStoS3util.MAP_NS_TO_S3_PATH.get(dbColl);
                            String url = s3service.addByteContentWithGzip(compressedData, NStoS3util.BUCKET_NAME_SME_LOG, folderName, fileName.toString(), "application/gzip");
                            if (url.equals(AppConstants.FILE_ALREADY_EXIST)) {
                                try {
                                    genericMongoDao.addRows(coll + "_failOver", dbList, db);
                                } catch (Exception e) {
                                    logger.error("Error while adding in moveSMELogModelToS3 , {}", e.getMessage());
                                }
                            }else if (url !=null){
                                logger.info("File Has uploaded on S3 name: {}, url: {}", fileName, url);
                                isNotUpdate = true;
                                break;
                            }else{
                                logger.info("url is name: {}, url: {}", fileName, url);
                                isNotUpdate = true;
                                break;
                            }
                            for (DBObject reqIdDoc : dbList) {
                                listOfIds.add(reqIdDoc.get("_id"));
                            }
                        } catch (Exception e) {
                            System.err.println("Error serializing DBObject: " + e.getMessage());
                            return;
                        }
                    }
                }
                // to delete from source by list of _id
                try {
                    List<Object> subList = new ArrayList<>();
                    int startIndx = 0;
                    int lastIndx = 100;
                    if (listOfIds.size() > 100) {
                        while (startIndx < listOfIds.size()) {
                            if (startIndx + 100 >= listOfIds.size()) {
                                lastIndx = listOfIds.size();
                            }
                            subList = listOfIds.subList(startIndx, lastIndx);
                            DBObject queryFormovedIds = new BasicDBObject();
                            queryFormovedIds.put("_id", new BasicDBObject().append("$in", subList));
                            genericMongoDao.deleteRow(coll, queryFormovedIds, true, db);
                            logger.info("LogModel Last Id deleted : {}", subList.get(subList.size() - 1));
                            startIndx = lastIndx;
                            lastIndx = lastIndx + 100;
                            subList = null;
                        }
                    } else if(listOfIds!=null && listOfIds.size() > 0) {
                        DBObject queryFormovedIds = new BasicDBObject();
                        queryFormovedIds.put("_id", new BasicDBObject().append("$in", listOfIds));
                        genericMongoDao.deleteRow(coll, queryFormovedIds, true, db);
                        logger.info("LogModel Last Id deleted : {}", listOfIds.get(listOfIds.size() - 1));
                    }

                } catch (Exception e) {
                    logger.error("Error while adding in moveSMELogModelToS3 , {}", e.getMessage());
                    if (!e.getMessage().contains("timeout") && !e.getMessage().contains("duplicate")) {
                        throw e;
                    }
                }

                // updating last _id in metaData master mongoDB collection: movedIdsLogger
                DBObject queryJson;
                if (commDeletedId != null && commDeletedId.size() > 0 && commDeletedId.getObjectId("_id") != null) {
                    totalMigrated = totalMigrated + dbObjectsList.size();
                    DBObject setquery = new BasicDBObject("movedId", dbObjectsList.get(dbObjectsList.size() - 1).get("_id"))
                            .append("totalMigrated", totalMigrated)
                            .append("updatedOn", new Date());
                    queryJson = new BasicDBObject("_id", commDeletedId.getObjectId("_id"));
                    DBObject rowJson = new BasicDBObject("$set", setquery);
                    newArchMongoDao.updateRow(NStoS3util.lastUpdatedIdColl, queryJson, rowJson, false, false, NStoS3util.lastUpdatedIdDB);
                }
                queryOnId.put("$gt", dbObjectsList.get(dbObjectsList.size() - 1).get("_id"));
                queryObject.put("_id", queryOnId);
                dbObjectsList = null; // explicitly nullify
                if(isNotUpdate) {
                    break;
                }
                dbObjectsList = genericMongoDao.getDBObjects(coll, queryObject, projection, 0, len, orderObject, db);
            }

        }catch(Exception e) {
            logger.error("Exception caught in moveSMELogModelToS3, ns:{} , msg: {}", e.getMessage());
        }finally {
            isMoveSMELogModelJobRunning = false;
            isMoveSMELogModelJobStop = false;
        }
    }

    private List<DBObject> getObjectList(int chunkSize, List<String> idList, Long uniqueId, Integer iuniqueId, String coll, String db, String key, StringBuilder fileName) {
        List<DBObject> dbList = null;
        String suniqueId = uniqueId !=null ?String.valueOf(uniqueId) : String.valueOf(iuniqueId);
        if(!idList.contains(suniqueId)) {
            idList.add(suniqueId);
            fileName.append(key+"_"+suniqueId+".json.gz");
            DBObject queryOnrequestId = new BasicDBObject();
            DBObject orderObject = new BasicDBObject("_id", 1);
            int offset = 0;
            if(uniqueId!= null && uniqueId > 0){
                queryOnrequestId.put(key, uniqueId);
            }
            else if(iuniqueId!= null && iuniqueId > 0){
                queryOnrequestId.put(key, iuniqueId);
            }
            else if(suniqueId!= null && suniqueId.length() > 2){
                queryOnrequestId.put(key, suniqueId);
            }
            logger.info("key: {}, uniqueId: {}, uniqueId: {},", key, uniqueId, iuniqueId);
            dbList = genericMongoDao.getDBObjects(coll, queryOnrequestId, offset, chunkSize, orderObject, db);
            if (dbList.size() == chunkSize) {
                offset = offset + chunkSize;
                dbList = getList(coll, queryOnrequestId, offset, orderObject, db, dbList, chunkSize);
            }
        }
        return dbList;
    }

    private List<DBObject> getList(String coll, DBObject queryOnrequestId, int offset, DBObject orderObject, String db, List<DBObject> resultList, int chunkSize) {

        try {
            List<DBObject> dataList = genericMongoDao.getDBObjects(coll, queryOnrequestId, offset, chunkSize, orderObject, db);
            while (dataList.size() > 0){
                resultList.addAll(dataList);
                offset += chunkSize;
                dataList = genericMongoDao.getDBObjects(coll, queryOnrequestId, offset, chunkSize, orderObject, db);
            }
        } catch (Exception e) {
            logger.error("Exception while getList");
        }
        return resultList;
    }

    public void moveSMELogEntryToS3(String dbColl, Integer size, int days, int chunkSize) {

        logger.info("Inside moveSMELogEntryToS3");
        try {
            boolean isNotUpdate = false;
            String[] nsArray = dbColl.split("\\.");
            String db = nsArray[0];
            String coll = nsArray[1];;
            int len = size;
            long totalMigrated = 0;
            DBObject queryForDeletId = new BasicDBObject();
            queryForDeletId.put("coll", dbColl);
            BasicDBObject commDeletedId = newArchMongoDao.getDBObjectByDbName(NStoS3util.lastUpdatedIdColl, queryForDeletId, NStoS3util.lastUpdatedIdDB);
            DBObject queryOnId = new BasicDBObject();
            //only provide startId at once we are starting the batch.
            if (commDeletedId != null && commDeletedId.size() > 0) {
                queryOnId.put("$gte", commDeletedId.get("movedId"));
                totalMigrated = (long)commDeletedId.get("totalMigrated");
            }
            queryOnId.put("$lt", new ObjectId(DateUtil.getStringToDate(DateUtil.getFormatedDate(days, DateUtil.DATE_FORMAT_EE_MMM_dd_hh_mm_ss_z_yyyy), DateUtil.DATE_FORMAT_EE_MMM_dd_hh_mm_ss_z_yyyy)));
            DBObject queryObject = new BasicDBObject();
            queryObject.put("_id", queryOnId);
            BasicDBObject projection = new BasicDBObject("_id", 1).append("EnquiryID", 1).append("MatrixLeadID", 1).append("VisitID", 1);
            DBObject orderObject = new BasicDBObject("_id", 1);
            // get list from mongoDB
            List<DBObject> dbObjectsList = genericMongoDao.getDBObjects(coll, queryObject, projection, 0, len, orderObject, db);
            logger.info("isMoveSMELogEntryJobStop: {}, isMoveSMELogEntryJobRunning: {}", isMoveSMELogEntryJobStop, isMoveSMELogEntryJobRunning);

            // if list have data then proceed
            while (dbObjectsList != null && dbObjectsList.size() > 0 && !isMoveSMELogEntryJobStop) {
                List<Object> listOfIds = new ArrayList<>();
                List<String> enquiryIdList= new ArrayList<>();
                List<String> leadIdLogList = new ArrayList<>();
                List<String> visitIdLogList = new ArrayList<>();
                for (DBObject jsonDocument : dbObjectsList) {
                    listOfIds.add(jsonDocument.get("_id"));
                    Long enquiryId = jsonDocument.get("EnquiryID") instanceof Long ? ((Long) jsonDocument.get("EnquiryID")) : null;
                    Long leadId = jsonDocument.get("MatrixLeadID") instanceof Long ? ((Long) jsonDocument.get("MatrixLeadID")) : null;
                    Long visitId = jsonDocument.get("VisitID") instanceof Long ? ((Long) jsonDocument.get("VisitID")) : null;
                    Integer ienquiryId = enquiryId == null && jsonDocument.get("EnquiryID") instanceof Integer ? (Integer) jsonDocument.get("EnquiryID") : null;
                    Integer ileadId = leadId == null && jsonDocument.get("MatrixLeadID") instanceof Integer ? (Integer) jsonDocument.get("MatrixLeadID") : null;
                    Integer ivisitId = visitId == null && jsonDocument.get("VisitID") instanceof Integer ? (Integer) jsonDocument.get("VisitID") : null;
                    StringBuilder fileName = new StringBuilder();
                    List<DBObject> dbList = null;
                    if ((enquiryId != null && enquiryId > 0) || (ienquiryId != null && ienquiryId > 0)) {
                        dbList = getObjectList(chunkSize, enquiryIdList, enquiryId, ienquiryId, coll, db, "EnquiryID", fileName);
                    } else if ((leadId != null && leadId > 0) || (ileadId != null && ileadId > 0)) {
                        dbList = getObjectList(chunkSize, leadIdLogList, leadId, ileadId, coll, db, "MatrixLeadID", fileName);
                    } else if ((visitId != null && visitId > 0) || (ivisitId != null && ivisitId > 0)) {
                        dbList = getObjectList(chunkSize, visitIdLogList, visitId, ivisitId, coll, db, "VisitID", fileName);
                    }else{
                        continue;
                    }
                    String jsonString;
                    if (dbList != null) {
                        try {
                            jsonString = JSON.serialize(dbList);

                            // Compress JSON data
                            byte[] compressedData = AppUtil.compressData(jsonString);
                            //String deCompressedData = decompressData(compressedData);
                            logger.info("SME LogEntry fileName : " + fileName);
                            String folderName = NStoS3util.MAP_NS_TO_S3_PATH.get(dbColl);
                            String url = s3service.addByteContentWithGzip(compressedData, NStoS3util.BUCKET_NAME_SME_LOG, folderName, fileName.toString(), "application/gzip");
                            if (url.equals(AppConstants.FILE_ALREADY_EXIST)) {
                                try {
                                    genericMongoDao.addRows(coll + "_failOver", dbList, db);
                                } catch (Exception e) {
                                    logger.error("Error while adding in moveSMELogEntryToS3 , {}", e.getMessage());
                                }
                            }else if (url !=null){
                                logger.info("File Has uploaded on S3 name: {}, url: {}", fileName, url);
                                isNotUpdate = true;
                                break;
                            }else{
                                logger.info("url is name: {}, url: {}", fileName, url);
                                isNotUpdate = true;
                                break;
                            }
                            for (DBObject reqIdDoc : dbList) {
                                listOfIds.add(reqIdDoc.get("_id"));
                            }
                        } catch (Exception e) {
                            System.err.println("Error serializing DBObject: " + e.getMessage());
                            return;
                        }
                    }
                }
                // to delete from source by list of _id
                try {
                    List<Object> subList = new ArrayList<>();
                    int startIndx = 0;
                    int lastIndx = 100;
                    if (listOfIds.size() > 100) {
                        while (startIndx < listOfIds.size()) {
                            if (startIndx + 100 >= listOfIds.size()) {
                                lastIndx = listOfIds.size();
                            }
                            subList = listOfIds.subList(startIndx, lastIndx);
                            DBObject queryFormovedIds = new BasicDBObject();
                            queryFormovedIds.put("_id", new BasicDBObject().append("$in", subList));
//                            genericMongoDao.deleteRow(coll, queryFormovedIds, true, db);
                            logger.info("SMELogEntry Last Id deleted : {}", subList.get(subList.size() - 1));
                            startIndx = lastIndx;
                            lastIndx = lastIndx + 100;
                            subList = null;
                        }
                    } else if(listOfIds!=null && listOfIds.size() > 0) {
                        DBObject queryFormovedIds = new BasicDBObject();
                        queryFormovedIds.put("_id", new BasicDBObject().append("$in", listOfIds));
//                        genericMongoDao.deleteRow(coll, queryFormovedIds, true, db);
                        logger.info("SMELogEntry Last Id deleted : {}", listOfIds.get(listOfIds.size() - 1));
                    }

                } catch (Exception e) {
                    logger.error("Error while adding in moveSMELogEntryToS3 , {}", e.getMessage());
                    if (!e.getMessage().contains("timeout") && !e.getMessage().contains("duplicate")) {
                        throw e;
                    }
                }

                // updating last _id in metaData master mongoDB collection: movedIdsLogger
                DBObject queryJson;
                if (commDeletedId != null && commDeletedId.size() > 0 && commDeletedId.getObjectId("_id") != null) {
                    totalMigrated = totalMigrated + dbObjectsList.size();
                    DBObject setquery = new BasicDBObject("movedId", dbObjectsList.get(dbObjectsList.size() - 1).get("_id"))
                            .append("totalMigrated", totalMigrated)
                            .append("updatedOn", new Date());
                    queryJson = new BasicDBObject("_id", commDeletedId.getObjectId("_id"));
                    DBObject rowJson = new BasicDBObject("$set", setquery);
                    newArchMongoDao.updateRow(NStoS3util.lastUpdatedIdColl, queryJson, rowJson, false, false, NStoS3util.lastUpdatedIdDB);
                }
                queryOnId.put("$gt", dbObjectsList.get(dbObjectsList.size() - 1).get("_id"));
                queryObject.put("_id", queryOnId);
                dbObjectsList = null; // explicitly nullify
                if(isNotUpdate) {
                    break;
                }
                dbObjectsList = genericMongoDao.getDBObjects(coll, queryObject, projection, 0, len, orderObject, db);
            }

        }catch(Exception e) {
            logger.error("Exception caught in moveSMELogEntryToS3, ns:{} , msg: {}", e.getMessage());
        }finally {
            isMoveSMELogEntryJobRunning = false;
            isMoveSMELogEntryJobStop = false;
        }
    }

    public void moveSMENonEBLogEntryToS3(String dbColl, Integer size, int days, int chunkSize) {

        logger.info("Inside moveSMENonEBLogEntryToS3");
        try {
            String[] nsArray = dbColl.split("\\.");
            String db = nsArray[0];
            String coll = nsArray[1];;
            int len = size;
            long totalMigrated = 0;
            DBObject queryForDeletId = new BasicDBObject();
            queryForDeletId.put("coll", dbColl);
            BasicDBObject commDeletedId = newArchMongoDao.getDBObjectByDbName(NStoS3util.lastUpdatedIdColl, queryForDeletId, NStoS3util.lastUpdatedIdDB);
            DBObject queryOnId = new BasicDBObject();
            //only provide startId at once we are starting the batch.
            if (commDeletedId != null && commDeletedId.size() > 0) {
                queryOnId.put("$gte", commDeletedId.get("movedId"));
                totalMigrated = (long)commDeletedId.get("totalMigrated");
            }
            queryOnId.put("$lt", new ObjectId(DateUtil.getStringToDate(DateUtil.getFormatedDate(days, DateUtil.DATE_FORMAT_EE_MMM_dd_hh_mm_ss_z_yyyy), DateUtil.DATE_FORMAT_EE_MMM_dd_hh_mm_ss_z_yyyy)));
            DBObject queryObject = new BasicDBObject();
            queryObject.put("_id", queryOnId);
            BasicDBObject projection = new BasicDBObject("_id", 1).append("EnquiryID", 1).append("MatrixLeadID", 1).append("VisitID", 1);
            DBObject orderObject = new BasicDBObject("_id", 1);
            // get list from mongoDB
            List<DBObject> dbObjectsList = genericMongoDao.getDBObjects(coll, queryObject, projection, 0, len, orderObject, db);
            logger.info("isMoveSMENonEBLogEntryJobStop: {}, isMoveSMENonEBLogEntryJobRunning: {}", isMoveSMENonEBLogEntryJobStop, isMoveSMENonEBLogEntryJobRunning);

            // if list have data then proceed
            while (dbObjectsList != null && dbObjectsList.size() > 0 && !isMoveSMENonEBLogEntryJobStop) {
                List<Object> listOfIds = new ArrayList<>();
                List<String> enquiryIdList= new ArrayList<>();
                List<String> leadIdLogList = new ArrayList<>();
                List<String> visitIdLogList = new ArrayList<>();
                for (DBObject jsonDocument : dbObjectsList) {
                    listOfIds.add(jsonDocument.get("_id"));
                    Long enquiryId = jsonDocument.get("EnquiryID") instanceof Long ? ((Long) jsonDocument.get("EnquiryID")) : null;
                    Long leadId = jsonDocument.get("MatrixLeadID") instanceof Long ? ((Long) jsonDocument.get("MatrixLeadID")) : null;
                    Long visitId = jsonDocument.get("VisitID") instanceof Long ? ((Long) jsonDocument.get("VisitID")) : null;
                    Integer ienquiryId = enquiryId == null && jsonDocument.get("EnquiryID") instanceof Integer ? (Integer) jsonDocument.get("EnquiryID") : null;
                    Integer ileadId = leadId == null && jsonDocument.get("MatrixLeadID") instanceof Integer ? (Integer) jsonDocument.get("MatrixLeadID") : null;
                    Integer ivisitId = visitId == null && jsonDocument.get("VisitID") instanceof Integer ? (Integer) jsonDocument.get("VisitID") : null;
                    StringBuilder fileName = new StringBuilder();
                    List<DBObject> dbList = null;
                    // checked BookedEnquiry or Not
                    if ((enquiryId != null && enquiryId > 0) || (ienquiryId != null && ienquiryId > 0)) {
                        dbList = getObjectList(chunkSize, enquiryIdList, enquiryId, ienquiryId, coll, db, "EnquiryID", fileName);
                    } else if ((leadId != null && leadId > 0) || (ileadId != null && ileadId > 0)) {
                        dbList = getObjectList(chunkSize, leadIdLogList, leadId, ileadId, coll, db, "MatrixLeadID", fileName);
                    } else if ((visitId != null && visitId > 0) || (ivisitId != null && ivisitId > 0)) {
                        dbList = getObjectList(chunkSize, visitIdLogList, visitId, ivisitId, coll, db, "VisitID", fileName);
                    }else{
                        continue;
                    }
                    String jsonString;
                    if (dbList != null) {
                        try {
                            jsonString = JSON.serialize(dbList);

                            // Compress JSON data
                            byte[] compressedData = AppUtil.compressData(jsonString);
                            //String deCompressedData = decompressData(compressedData);
                            logger.info("SMENonEB LogEntry fileName : " + fileName);
                            String folderName = NStoS3util.MAP_NS_TO_S3_PATH.get(dbColl);
                            String url = s3service.addByteContentWithGzip(compressedData, NStoS3util.BUCKET_NAME_SME_LOG, folderName, fileName.toString(), "application/gzip");
                            if (url.equals(AppConstants.FILE_ALREADY_EXIST)) {
                                try {
//                                    genericMongoDao.addRows(coll + "_failOver", dbList, db);
                                } catch (Exception e) {
                                    logger.error("Error while adding in moveSMENonEBLogEntryToS3 , {}", e.getMessage());
                                }
                            }
                            for (DBObject reqIdDoc : dbList) {
                                listOfIds.add(reqIdDoc.get("_id"));
                            }
                        } catch (Exception e) {
                            System.err.println("Error serializing DBObject: " + e.getMessage());
                            return;
                        }
                    }
                }
                // to delete from source by list of _id
                try {
                    List<Object> subList = new ArrayList<>();
                    int startIndx = 0;
                    int lastIndx = 100;
                    if (listOfIds.size() > 100) {
                        while (startIndx < listOfIds.size()) {
                            if (startIndx + 100 >= listOfIds.size()) {
                                lastIndx = listOfIds.size();
                            }
                            subList = listOfIds.subList(startIndx, lastIndx);
                            DBObject queryFormovedIds = new BasicDBObject();
                            queryFormovedIds.put("_id", new BasicDBObject().append("$in", subList));
//                            genericMongoDao.deleteRow(coll, queryFormovedIds, true, db);
                            logger.info("SMENonEB LogEntry Last Id deleted : {}", subList.get(subList.size() - 1));
                            startIndx = lastIndx;
                            lastIndx = lastIndx + 100;
                            subList = null;
                        }
                    } else if(listOfIds!=null && listOfIds.size() > 0) {
                        DBObject queryFormovedIds = new BasicDBObject();
                        queryFormovedIds.put("_id", new BasicDBObject().append("$in", listOfIds));
//                        genericMongoDao.deleteRow(coll, queryFormovedIds, true, db);
                        logger.info("SMENonEB LogEntry Last Id deleted : {}", listOfIds.get(listOfIds.size() - 1));
                    }

                } catch (Exception e) {
                    logger.error("Error while adding in moveSMENonEBLogEntryToS3 , {}", e.getMessage());
                    if (!e.getMessage().contains("timeout") && !e.getMessage().contains("duplicate")) {
                        throw e;
                    }
                }

                // updating last _id in metaData master mongoDB collection: movedIdsLogger
                DBObject queryJson;
                if (commDeletedId != null && commDeletedId.size() > 0 && commDeletedId.getObjectId("_id") != null) {
                    totalMigrated = totalMigrated + dbObjectsList.size();
                    DBObject setquery = new BasicDBObject("movedId", dbObjectsList.get(dbObjectsList.size() - 1).get("_id"))
                            .append("totalMigrated", totalMigrated)
                            .append("updatedOn", new Date());
                    queryJson = new BasicDBObject("_id", commDeletedId.getObjectId("_id"));
                    DBObject rowJson = new BasicDBObject("$set", setquery);
                    newArchMongoDao.updateRow(NStoS3util.lastUpdatedIdColl, queryJson, rowJson, false, false, NStoS3util.lastUpdatedIdDB);
                }
                queryOnId.put("$gt", dbObjectsList.get(dbObjectsList.size() - 1).get("_id"));
                queryObject.put("_id", queryOnId);
                dbObjectsList = null; // explicitly nullify
                dbObjectsList = genericMongoDao.getDBObjects(coll, queryObject, projection, 0, len, orderObject, db);
            }

        }catch(Exception e) {
            logger.error("Exception caught in moveSMENonEBLogEntryToS3, ns:{} , msg: {}", e.getMessage());
        }finally {
            isMoveSMENonEBLogEntryJobRunning = false;
            isMoveSMENonEBLogEntryJobStop = false;
        }
    }

    public void moveCorpApplicationLogsEntryToS3(String dbColl, Integer size, int days, int chunkSize) {

        logger.info("Inside moveCorpApplicationLogsEntryToS3");
        try {
            String[] nsArray = dbColl.split("\\.");
            String db = nsArray[0];
            String coll = nsArray[1];;
            int len = size;
            long totalMigrated = 0;
            DBObject queryForDeletId = new BasicDBObject();
            queryForDeletId.put("coll", dbColl);
            BasicDBObject commDeletedId = newArchMongoDao.getDBObjectByDbName(NStoS3util.lastUpdatedIdColl, queryForDeletId, NStoS3util.lastUpdatedIdDB);
            DBObject queryOnId = new BasicDBObject();
            //only provide startId at once we are starting the batch.
            if (commDeletedId != null && commDeletedId.size() > 0) {
                queryOnId.put("$gte", commDeletedId.get("movedId"));
                totalMigrated = (long)commDeletedId.get("totalMigrated");
            }
            queryOnId.put("$lt", new ObjectId(DateUtil.getStringToDate(DateUtil.getFormatedDate(days, DateUtil.DATE_FORMAT_EE_MMM_dd_hh_mm_ss_z_yyyy), DateUtil.DATE_FORMAT_EE_MMM_dd_hh_mm_ss_z_yyyy)));
            DBObject queryObject = new BasicDBObject();
            queryObject.put("_id", queryOnId);
            BasicDBObject projection = new BasicDBObject("_id", 1).append("ModuleName", 1).append("LeadId", 1)
                    .append("SupplierId", 1).append("UniqueId", 1).append("UniqueType", 1).append("LogId", 1).append("ProcessId", 1);
            DBObject orderObject = new BasicDBObject("_id", 1);
            // get list from mongoDB
            List<DBObject> dbObjectsList = genericMongoDao.getDBObjects(coll, queryObject, projection, 0, len, orderObject, db);
            logger.info("isMoveSMECorpAppLogEntryJobStop: {}, isMoveSMECorpAppLogEntryJobRunning: {}", isMoveSMECorpAppLogEntryJobStop, isMoveSMECorpAppLogEntryJobRunning);

            // if list have data then proceed
            while (dbObjectsList != null && dbObjectsList.size() > 0 && !isMoveSMECorpAppLogEntryJobStop) {
                List<Object> listOfIds = new ArrayList<>();
                List<String> enquiryIdList= new ArrayList<>();
                List<String> leadIdLogList = new ArrayList<>();
                List<String> visitIdLogList = new ArrayList<>();
                for (DBObject jsonDocument : dbObjectsList) {
                    listOfIds.add(jsonDocument.get("_id"));
                    String moduleName = (String) jsonDocument.get("ModuleName");
                    Long leadId = jsonDocument.get("LeadId") instanceof Long ? ((Long) jsonDocument.get("LeadId")) : null;
                    Long supplierId = jsonDocument.get("SupplierId") instanceof Long ? ((Long) jsonDocument.get("SupplierId")) : null;
                    String uniqueId = (String) jsonDocument.get("UniqueId");
                    String uniqueType = (String) jsonDocument.get("UniqueType");
                    String logId = (String) jsonDocument.get("LogId");
                    String processId = (String) jsonDocument.get("ProcessId");
                    Integer ileadId = logId == null && jsonDocument.get("LeadId") instanceof Integer ? (Integer) jsonDocument.get("LeadId") : null;
                    Integer iSupplierId = leadId == null && jsonDocument.get("SupplierId") instanceof Integer ? (Integer) jsonDocument.get("SupplierId") : null;
                    StringBuilder fileName = new StringBuilder();
                    List<DBObject> dbList = null;
                    // checked BookedEnquiry or Not
                    if ((leadId != null && leadId > 0) || (ileadId != null && ileadId > 0)) {
                        dbList = getObjectList(chunkSize, enquiryIdList, leadId, ileadId, coll, db, "LeadId", fileName);
                    } else if ((supplierId != null && supplierId > 0) || (iSupplierId != null && iSupplierId > 0)) {
                        dbList = getObjectList(chunkSize, leadIdLogList, leadId, ileadId, coll, db, "SupplierId", fileName);
                    } else if (uniqueId != null && StringUtils.hasText(uniqueId)){
//                        dbList = getObjectList(chunkSize, visitIdLogList, uniqueId, uniq, coll, db, "VisitID", fileName);
                    }else{
                        continue;
                    }
                    String jsonString;
                    if (dbList != null) {
                        try {
                            jsonString = JSON.serialize(dbList);

                            // Compress JSON data
                            byte[] compressedData = AppUtil.compressData(jsonString);
                            //String deCompressedData = decompressData(compressedData);
                            logger.info("SME CorpConnect fileName : " + fileName);
                            String folderName = NStoS3util.MAP_NS_TO_S3_PATH.get(dbColl);
                            String url = s3service.addByteContentWithGzip(compressedData, NStoS3util.BUCKET_NAME_SME_LOG, folderName, fileName.toString(), "application/gzip");
                            if (url.equals(AppConstants.FILE_ALREADY_EXIST)) {
                                try {
//                                    genericMongoDao.addRows(coll + "_failOver", dbList, db);
                                } catch (Exception e) {
                                    logger.error("Error while adding in moveSMENonEBLogEntryToS3 , {}", e.getMessage());
                                }
                            }
                            for (DBObject reqIdDoc : dbList) {
                                listOfIds.add(reqIdDoc.get("_id"));
                            }
                        } catch (Exception e) {
                            System.err.println("Error serializing DBObject: " + e.getMessage());
                            return;
                        }
                    }
                }
                // to delete from source by list of _id
                try {
                    List<Object> subList = new ArrayList<>();
                    int startIndx = 0;
                    int lastIndx = 100;
                    if (listOfIds.size() > 100) {
                        while (startIndx < listOfIds.size()) {
                            if (startIndx + 100 >= listOfIds.size()) {
                                lastIndx = listOfIds.size();
                            }
                            subList = listOfIds.subList(startIndx, lastIndx);
                            DBObject queryFormovedIds = new BasicDBObject();
                            queryFormovedIds.put("_id", new BasicDBObject().append("$in", subList));
//                            genericMongoDao.deleteRow(coll, queryFormovedIds, true, db);
                            logger.info("SME CorpConnect LogEntry Last Id deleted : {}", subList.get(subList.size() - 1));
                            startIndx = lastIndx;
                            lastIndx = lastIndx + 100;
                            subList = null;
                        }
                    } else if(listOfIds!=null && listOfIds.size() > 0) {
                        DBObject queryFormovedIds = new BasicDBObject();
                        queryFormovedIds.put("_id", new BasicDBObject().append("$in", listOfIds));
//                        genericMongoDao.deleteRow(coll, queryFormovedIds, true, db);
                        logger.info("SME CorpConnect Last Id deleted : {}", listOfIds.get(listOfIds.size() - 1));
                    }

                } catch (Exception e) {
                    logger.error("Error while adding in moveSMENonEBLogEntryToS3 , {}", e.getMessage());
                    if (!e.getMessage().contains("timeout") && !e.getMessage().contains("duplicate")) {
                        throw e;
                    }
                }

                // updating last _id in metaData master mongoDB collection: movedIdsLogger
                DBObject queryJson;
                if (commDeletedId != null && commDeletedId.size() > 0 && commDeletedId.getObjectId("_id") != null) {
                    totalMigrated = totalMigrated + dbObjectsList.size();
                    DBObject setquery = new BasicDBObject("movedId", dbObjectsList.get(dbObjectsList.size() - 1).get("_id"))
                            .append("totalMigrated", totalMigrated)
                            .append("updatedOn", new Date());
                    queryJson = new BasicDBObject("_id", commDeletedId.getObjectId("_id"));
                    DBObject rowJson = new BasicDBObject("$set", setquery);
                    newArchMongoDao.updateRow(NStoS3util.lastUpdatedIdColl, queryJson, rowJson, false, false, NStoS3util.lastUpdatedIdDB);
                }
                queryOnId.put("$gt", dbObjectsList.get(dbObjectsList.size() - 1).get("_id"));
                queryObject.put("_id", queryOnId);
                dbObjectsList = null; // explicitly nullify
                dbObjectsList = genericMongoDao.getDBObjects(coll, queryObject, projection, 0, len, orderObject, db);
            }

        }catch(Exception e) {
            logger.error("Exception caught in moveCorpApplicationLogsEntryToS3, ns:{} , msg: {}", e.getMessage());
        }finally {
            isMoveSMECorpAppLogEntryJobRunning = false;
            isMoveSMECorpAppLogEntryJobStop = false;
        }
    }

    public void moveMongoLogEntryToS3(String dbColl, Integer size, int days, int chunkSize) {

        logger.info("Inside moveMongoLogEntryToS3");
        try {
            String[] nsArray = dbColl.split("\\.");
            String db = nsArray[0];
            String coll = nsArray[1];;
            int len = size;
            long totalMigrated = 0;
            DBObject queryForDeletId = new BasicDBObject();
            queryForDeletId.put("coll", dbColl);
            BasicDBObject commDeletedId = newArchMongoDao.getDBObjectByDbName(NStoS3util.lastUpdatedIdColl, queryForDeletId, NStoS3util.lastUpdatedIdDB);
            DBObject queryOnId = new BasicDBObject();
            //only provide startId at once we are starting the batch.
            if (commDeletedId != null && commDeletedId.size() > 0) {
                queryOnId.put("$gte", commDeletedId.get("movedId"));
                totalMigrated = (long)commDeletedId.get("totalMigrated");
            }
            queryOnId.put("$lt", new ObjectId(DateUtil.getStringToDate(DateUtil.getFormatedDate(days, DateUtil.DATE_FORMAT_EE_MMM_dd_hh_mm_ss_z_yyyy), DateUtil.DATE_FORMAT_EE_MMM_dd_hh_mm_ss_z_yyyy)));
            DBObject queryObject = new BasicDBObject();
            queryObject.put("_id", queryOnId);
            BasicDBObject projection = new BasicDBObject("_id", 1).append("EnquiryId", 1).append("LogType", 1).append("ApiLogType", 1)
                    .append("LogLevel", 1).append("Request", 1).append("CreatedOn", 1);
            DBObject orderObject = new BasicDBObject("_id", 1);
            // get list from mongoDB
            List<DBObject> dbObjectsList = genericMongoDao.getDBObjects(coll, queryObject, projection, 0, len, orderObject, db);
            logger.info("isMoveSMEMongoLogEntryJobStop: {}, isMoveSMEMongoLogEntryJobRunning: {}", isMoveSMEMongoLogEntryJobStop, isMoveSMEMongoLogEntryJobRunning);

            // if list have data then proceed
            while (dbObjectsList != null && dbObjectsList.size() > 0 && !isMoveSMEMongoLogEntryJobStop) {
                List<Object> listOfIds = new ArrayList<>();
                List<String> enquiryIdList= new ArrayList<>();
                List<String> leadIdLogList = new ArrayList<>();
                List<String> visitIdLogList = new ArrayList<>();
                for (DBObject jsonDocument : dbObjectsList) {
                    listOfIds.add(jsonDocument.get("_id"));
                    Long enquiryId = jsonDocument.get("EnquiryId") instanceof Long ? ((Long) jsonDocument.get("EnquiryId    ")) : null;
                    String apiLogType = (String) jsonDocument.get("ApiLogType");
                    String logType = (String) jsonDocument.get("LogType");
                    String logLevel = (String) jsonDocument.get("LogLevel");
                    String request = (String) jsonDocument.get("Request");
                    String createdOn = (String) jsonDocument.get("CreatedOn");
                    Integer ienquiryId = enquiryId == null && jsonDocument.get("EnquiryID") instanceof Integer ? (Integer) jsonDocument.get("EnquiryID") : null;
                    StringBuilder fileName = new StringBuilder();
                    List<DBObject> dbList = null;
                    // checked BookedEnquiry or Not
                    if ((enquiryId != null && enquiryId > 0) || (ienquiryId != null && ienquiryId > 0)) {
                        dbList = getObjectList(chunkSize, enquiryIdList, enquiryId, ienquiryId, coll, db, "EnquiryID", fileName);
                    } else if ((apiLogType != null && StringUtils.hasText(apiLogType)) || (logType != null && StringUtils.hasText(logType))) {
//                        dbList = getObjectList(chunkSize, leadIdLogList, logType, apiLogType, coll, db, "MatrixLeadID", fileName);
                    } else if (logLevel != null && StringUtils.hasText(logLevel)){
//                        dbList = getObjectList(chunkSize, visitIdLogList, visitId, ivisitId, coll, db, "VisitID", fileName);
                    }else{
                        continue;
                    }
                    String jsonString;
                    if (dbList != null) {
                        try {
                            jsonString = JSON.serialize(dbList);

                            // Compress JSON data
                            byte[] compressedData = AppUtil.compressData(jsonString);
                            //String deCompressedData = decompressData(compressedData);
                            logger.info("SME MongoLogEntry fileName : " + fileName);
                            String folderName = NStoS3util.MAP_NS_TO_S3_PATH.get(dbColl);
                            String url = s3service.addByteContentWithGzip(compressedData, NStoS3util.BUCKET_NAME_SME_LOG, folderName, fileName.toString(), "application/gzip");
                            if (url.equals(AppConstants.FILE_ALREADY_EXIST)) {
                                try {
//                                    genericMongoDao.addRows(coll + "_failOver", dbList, db);
                                } catch (Exception e) {
                                    logger.error("Error while adding in moveSMENonEBLogEntryToS3 , {}", e.getMessage());
                                }
                            }
                            for (DBObject reqIdDoc : dbList) {
                                listOfIds.add(reqIdDoc.get("_id"));
                            }
                        } catch (Exception e) {
                            System.err.println("Error serializing DBObject: " + e.getMessage());
                            return;
                        }
                    }
                }
                // to delete from source by list of _id
                try {
                    List<Object> subList = new ArrayList<>();
                    int startIndx = 0;
                    int lastIndx = 100;
                    if (listOfIds.size() > 100) {
                        while (startIndx < listOfIds.size()) {
                            if (startIndx + 100 >= listOfIds.size()) {
                                lastIndx = listOfIds.size();
                            }
                            subList = listOfIds.subList(startIndx, lastIndx);
                            DBObject queryFormovedIds = new BasicDBObject();
                            queryFormovedIds.put("_id", new BasicDBObject().append("$in", subList));
                            genericMongoDao.deleteRow(coll, queryFormovedIds, true, db);
                            logger.info("SME MongoLogEntry LogEntry Last Id deleted : {}", subList.get(subList.size() - 1));
                            startIndx = lastIndx;
                            lastIndx = lastIndx + 100;
                            subList = null;
                        }
                    } else if(listOfIds!=null && listOfIds.size() > 0) {
                        DBObject queryFormovedIds = new BasicDBObject();
                        queryFormovedIds.put("_id", new BasicDBObject().append("$in", listOfIds));
                        genericMongoDao.deleteRow(coll, queryFormovedIds, true, db);
                        logger.info("SME MongoLogEntry Last Id deleted : {}", listOfIds.get(listOfIds.size() - 1));
                    }

                } catch (Exception e) {
                    logger.error("Error while adding in moveMongoLogEntryToS3 , {}", e.getMessage());
                    if (!e.getMessage().contains("timeout") && !e.getMessage().contains("duplicate")) {
                        throw e;
                    }
                }

                // updating last _id in metaData master mongoDB collection: movedIdsLogger
                DBObject queryJson;
                if (commDeletedId != null && commDeletedId.size() > 0 && commDeletedId.getObjectId("_id") != null) {
                    totalMigrated = totalMigrated + dbObjectsList.size();
                    DBObject setquery = new BasicDBObject("movedId", dbObjectsList.get(dbObjectsList.size() - 1).get("_id"))
                            .append("totalMigrated", totalMigrated)
                            .append("updatedOn", new Date());
                    queryJson = new BasicDBObject("_id", commDeletedId.getObjectId("_id"));
                    DBObject rowJson = new BasicDBObject("$set", setquery);
                    newArchMongoDao.updateRow(NStoS3util.lastUpdatedIdColl, queryJson, rowJson, false, false, NStoS3util.lastUpdatedIdDB);
                }
                queryOnId.put("$gt", dbObjectsList.get(dbObjectsList.size() - 1).get("_id"));
                queryObject.put("_id", queryOnId);
                dbObjectsList = null; // explicitly nullify
                dbObjectsList = genericMongoDao.getDBObjects(coll, queryObject, projection, 0, len, orderObject, db);
            }

        }catch(Exception e) {
            logger.error("Exception caught in moveMongoLogEntryToS3, ns:{} , msg: {}", e.getMessage());
        }finally {
            isMoveSMEMongoLogEntryJobRunning = false;
            isMoveSMEMongoLogEntryJobStop = false;
        }
    }
}
