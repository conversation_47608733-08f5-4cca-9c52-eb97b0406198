package com.docMigration.service.impl;
import com.docMigration.dao.ChatMongoDao;
import com.docMigration.service.LifeLoggerService;
import com.docMigration.service.S3BucketService;
import com.docMigration.util.*;
import com.mongodb.BasicDBObject;
import com.mongodb.DBObject;
import com.mongodb.util.JSON;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.*;

import static com.docMigration.util.CompressionUtil.compressData;

@Service
public class LifeLoggerServiceImpl implements LifeLoggerService {
    private final Logger logger = LoggerFactory.getLogger(LifeLoggerServiceImpl.class);
    @Autowired
    @Qualifier("e2eMongoLog")
    private ChatMongoDao lifeLoggerDao;
    @Autowired
    @Qualifier("newArchivalMongoDao")
    ChatMongoDao newArchMongoDao;
    @Autowired
    private S3BucketService s3service;
    @Autowired
    private MongoUtil mongoUtil;

    public static boolean isE2ELoggerS3JobRunning = false;
    public static boolean isE2ELoggerS3JobStop = false;
    public static boolean isE2ELoggerS3JobRunningV1 = false;
    public static boolean isE2ELoggerS3JobStopV1 = false;
    public static boolean isE2ELoggerS3JobRunningV2 = false;
    public static boolean isE2ELoggerS3JobStopV2 = false;
    public static boolean isE2ELoggerDeleteJobRunningV2 = false;
    public static boolean isE2ELoggerDeleteJobStopV2 = false;

    @Override
    public void moveLifeDocs(String dbColl, Integer size, boolean timeCheck, int days, int chunkSize) {
        logger.info("Inside moveLifeDocs , ns: {}, size: {}, timeCheck: {}, days", dbColl, size, timeCheck, days, chunkSize);
        List<DBObject> dbObjectsList = null;
        List<String> unwantedPolicyNoList = Arrays.asList("BMSKafka", "nslookup", "windows", "passwd");
        try {
            String[] nsArray = dbColl.split("\\.");
            String db = nsArray[0];
            String coll = nsArray[1];
            int len = size;
            long totalMigrated = 0;
            DBObject queryForDeletId = new BasicDBObject();
            queryForDeletId.put("coll", dbColl);
            BasicDBObject motorDeletedId = newArchMongoDao.getDBObjectByDbName(NStoS3util.lastUpdatedIdColl, queryForDeletId, NStoS3util.lastUpdatedIdDB);
            DBObject queryOnId = new BasicDBObject();
            //only provide startId at once we are starting the batch.
            if (motorDeletedId != null && motorDeletedId.size() > 0) {
                queryOnId.put("$gte", motorDeletedId.get("movedId"));
                totalMigrated = (long)motorDeletedId.get("totalMigrated");
            }
            queryOnId.put("$lt", new ObjectId(DateUtil.getStringToDate(DateUtil.getFormatedDate(days, DateUtil.DATE_FORMAT_EE_MMM_dd_hh_mm_ss_z_yyyy), DateUtil.DATE_FORMAT_EE_MMM_dd_hh_mm_ss_z_yyyy)));
            DBObject queryObject = new BasicDBObject();
            queryObject.put("_id", queryOnId);
            BasicDBObject projection = new BasicDBObject("_id", 1).append("PolicyNumber", 1);
            DBObject orderObject = new BasicDBObject("_id", 1);
            dbObjectsList = lifeLoggerDao.getDBObjects(coll, queryObject, projection, 0, len, null, db);
            logger.info("E2E isE2ELoggerS3JobStop: {}, isE2ELoggerS3JobRunning:{}", isE2ELoggerS3JobStop, isE2ELoggerS3JobRunning);
            while (dbObjectsList != null && dbObjectsList.size() > 0 && !isE2ELoggerS3JobStop) {
                Date currentDate = new Date();
                // Calculate the date 30 days ago
                Calendar calendar = Calendar.getInstance();
                calendar.setTime(currentDate);
                calendar.add(Calendar.DAY_OF_YEAR, -30);
                Date thirtyDaysAgo = calendar.getTime();
                List<Object> listOfIds = new ArrayList<>();
                HashSet<String> policyNumbers = new HashSet<>();
                for (DBObject jsonDocument : dbObjectsList) {
                    listOfIds.add(jsonDocument.get("_id"));
                    String policyNumber = (String) jsonDocument.get("PolicyNumber");
                    if (StringUtils.hasText(policyNumber) && policyNumber.length() < 50 && !(policyNumber.contains("System") ||policyNumber.contains("passwd")||policyNumber.contains("etc")) && !policyNumbers.contains(policyNumber)) {
                        policyNumbers.add(policyNumber);
                        // query to find booked EnquiryId
                        DBObject queryOnPolicyId = new BasicDBObject();
                        queryOnPolicyId.put("PolicyNumber", policyNumber);
                        int offset = 0;
                        List<DBObject> dbList = lifeLoggerDao.getDBObjects(coll, queryOnPolicyId, offset, chunkSize, null, db);
                        if (dbList.size() == chunkSize) {
                            offset = offset + chunkSize;
                            dbList = getList(coll, queryOnPolicyId, offset, db, dbList, chunkSize);

                        }
                        String jsonString;
                        if (dbList != null && dbList.size() > 0) {
                            try {
                                List<DBObject>listToAddInS3 = new ArrayList<>();
                                for(DBObject obj : dbList) {
                                    ObjectId objectId = new ObjectId(String.valueOf(obj.get("_id")));
                                    Date objectIdDate = objectId.getDate();
                                    if (!objectIdDate.after(thirtyDaysAgo)) {
                                        listToAddInS3.add(obj);
                                    }
                                }
                                if(listToAddInS3 != null && listToAddInS3.size() > 0) {
                                    jsonString = JSON.serialize(listToAddInS3);
                                    // Compress JSON data
                                    byte[] compressedData = compressData(jsonString);
                                    //String deCompressedData = decompressData(compressedData);
                                    String fileName = policyNumber + "_1.json.gz";
                                    // check This fileName exist or Not
                                    DBObject orderQuery = new BasicDBObject("_id", -1);
                                    List<DBObject> fileExistList = lifeLoggerDao.getDBObjects("lifeS3FilenameLogs", queryOnPolicyId, null, 0, 1, orderQuery, db);
                                    if (fileExistList != null && fileExistList.size() > 0) {
                                        String lastFileName = (String) fileExistList.get(0).get("FileName");
                                        fileName = AppUtil.incrementFilenameNumber(lastFileName);
                                    }
                                    logger.info("E2E FileName: {}", fileName);
                                    // add in lifeS3FilenameLogs collection for url map
                                    DBObject lifeFileObject = new BasicDBObject().append("PolicyNumber", policyNumber).append("FileName", fileName).append("C_AT", new Date());
                                    lifeLoggerDao.addRow("lifeS3FilenameLogs", lifeFileObject, db);
                                    String folderName = NStoS3util.MAP_NS_TO_S3_PATH.get(dbColl);
                                    String url = s3service.addByteContentWithGzip(compressedData, NStoS3util.BUCKET_NAME_LOGGER_ARCHIVAL, folderName, fileName, "application/gzip");
                                    if (url.equals(AppConstants.FILE_ALREADY_EXIST)) {
                                        newArchMongoDao.addRows(coll, dbList, db);
                                    }
                                    for (DBObject reqIdDoc : listToAddInS3) {
                                        listOfIds.add(reqIdDoc.get("_id"));
                                    }
                                }
                                listToAddInS3 = null;
                            } catch (Exception e) {
                                System.err.println("Error serializing DBObject: " + e.getMessage());
                                throw e;
                            }
                        }
                    }
                }
                policyNumbers = null;
                List<Object> subList = new ArrayList<>();
                int startIndx = 0;
                int lastIndx = 100;
                if (listOfIds.size() > 100) {
                    while (startIndx < listOfIds.size()) {
                        if (startIndx + 100 >= listOfIds.size()) {
                            lastIndx = listOfIds.size();
                        }
                        subList = listOfIds.subList(startIndx, lastIndx);
                        DBObject queryFormovedIds = new BasicDBObject();
                        queryFormovedIds.put("_id", new BasicDBObject().append("$in", subList));
                        lifeLoggerDao.deleteRow(coll, queryFormovedIds, true, db);
                        logger.info("E2E Last Id deleted : {}", subList.get(subList.size() - 1));
                        startIndx = lastIndx;
                        lastIndx = lastIndx + 100;
                        subList = null;
                    }
                } else {
                    DBObject queryFormovedIds = new BasicDBObject();
                    queryFormovedIds.put("_id", new BasicDBObject().append("$in", listOfIds));
                    lifeLoggerDao.deleteRow(coll, queryFormovedIds, true, db);
                    logger.info("E2E Last Id deleted : {}", listOfIds.get(listOfIds.size() - 1));
                }
                listOfIds = null;

                DBObject queryJson;
                if (motorDeletedId != null && motorDeletedId.size() > 0 && motorDeletedId.getObjectId("_id") != null) {
                    totalMigrated = totalMigrated + dbObjectsList.size();
                    DBObject setquery = new BasicDBObject("movedId", dbObjectsList.get(dbObjectsList.size() - 1).get("_id"))
                            .append("totalMigrated", totalMigrated)
                            .append("updatedOn", new Date());
                    queryJson = new BasicDBObject("_id", motorDeletedId.getObjectId("_id"));
                    DBObject rowJson = new BasicDBObject("$set", setquery);
                    newArchMongoDao.updateRow(NStoS3util.lastUpdatedIdColl, queryJson, rowJson, false, false, NStoS3util.lastUpdatedIdDB);
                }
                queryOnId.put("$gt", dbObjectsList.get(dbObjectsList.size() - 1).get("_id"));
                queryObject.put("_id", queryOnId);
                dbObjectsList = null;
                dbObjectsList = lifeLoggerDao.getDBObjects(coll, queryObject, projection, 0, len, null, db);
            }
            dbObjectsList = null;
        }catch (Exception e) {
            logger.error("Exception caught into E2E moveLifeDocs, msg: {}", e.getMessage());
        }finally {
            LifeLoggerServiceImpl.isE2ELoggerS3JobStop = false;
            LifeLoggerServiceImpl.isE2ELoggerS3JobRunning = false;
        }
    }
    @Override
    public void moveLifeDocs(String dbColl, Integer size, boolean timeCheck, int days, int chunkSize, String lte_id) {
        logger.info("Inside moveLifeDocs , ns: {}, size: {}, timeCheck: {}, days", dbColl, size, timeCheck, days, chunkSize);
        List<DBObject> dbObjectsList = null;

        try {
            String[] nsArray = dbColl.split("\\.");
            String db = nsArray[0];
            String coll = nsArray[1];;
            int len = size;
            long totalMigrated = 0;
            DBObject queryForDeletId = new BasicDBObject();
            queryForDeletId.put("coll", "LifeRenewalLog.LogDataRenewal_V1");
            BasicDBObject motorDeletedId = newArchMongoDao.getDBObjectByDbName(NStoS3util.lastUpdatedIdColl, queryForDeletId, NStoS3util.lastUpdatedIdDB);
            DBObject queryOnId = new BasicDBObject();
            //only provide startId at once we are starting the batch.
            if (motorDeletedId != null && motorDeletedId.size() > 0) {
                queryOnId.put("$gte", motorDeletedId.get("movedId"));
                totalMigrated = (long)motorDeletedId.get("totalMigrated");
            }
            queryOnId.put("$lt",  new ObjectId(lte_id));// new ObjectId(DateUtil.getStringToDate(DateUtil.getFormatedDate(days, DateUtil.DATE_FORMAT_EE_MMM_dd_hh_mm_ss_z_yyyy), DateUtil.DATE_FORMAT_EE_MMM_dd_hh_mm_ss_z_yyyy)));
            DBObject queryObject = new BasicDBObject();
            queryObject.put("_id", queryOnId);
            BasicDBObject projection = new BasicDBObject("_id", 1).append("PolicyNumber", 1);
            DBObject orderObject = new BasicDBObject("_id", 1);
            dbObjectsList = lifeLoggerDao.getDBObjects(coll, queryObject, projection, 0, len, null, db);
            logger.info("E2E isE2ELoggerS3JobStopV1: {}, isE2ELoggerS3JobRunningV1:{}", isE2ELoggerS3JobStopV1, isE2ELoggerS3JobRunningV1);
            while (dbObjectsList != null && dbObjectsList.size() > 0 && !isE2ELoggerS3JobStopV1) {
                Date currentDate = new Date();
                // Calculate the date 30 days ago
                Calendar calendar = Calendar.getInstance();
                calendar.setTime(currentDate);
                calendar.add(Calendar.DAY_OF_YEAR, -30);
                Date thirtyDaysAgo = calendar.getTime();
                List<Object> listOfIds = new ArrayList<>();
                HashSet<String> policyNumbers = new HashSet<>();
                for (DBObject jsonDocument : dbObjectsList) {
                    listOfIds.add(jsonDocument.get("_id"));
                    String policyNumber = (String) jsonDocument.get("PolicyNumber");
                    if (StringUtils.hasText(policyNumber) && policyNumber.length() < 50 && !(policyNumber.contains("System") ||policyNumber.contains("passwd")||policyNumber.contains("etc")) && !policyNumbers.contains(policyNumber)) {
                        policyNumbers.add(policyNumber);
                        // query to find booked EnquiryId
                        DBObject queryOnPolicyId = new BasicDBObject();
                        queryOnPolicyId.put("PolicyNumber", policyNumber);
                        int offset = 0;
                        List<DBObject> dbList = lifeLoggerDao.getDBObjects(coll, queryOnPolicyId, offset, chunkSize, null, db);
                        if (dbList.size() == chunkSize) {
                            offset = offset + chunkSize;
                            dbList = getList(coll, queryOnPolicyId, offset, db, dbList, chunkSize);

                        }
                        String jsonString;
                        if (dbList != null) {
                            try {
                                List<DBObject>listToAddInS3 = new ArrayList<>();
                                for(DBObject obj : dbList) {
                                    ObjectId objectId = new ObjectId(String.valueOf(obj.get("_id")));
                                    Date objectIdDate = objectId.getDate();
                                    if (!objectIdDate.after(thirtyDaysAgo)) {
                                        listToAddInS3.add(obj);
                                    }
                                }
                                if(listToAddInS3 != null && listToAddInS3.size() > 0) {
                                    jsonString = JSON.serialize(listToAddInS3);
                                    // Compress JSON data
                                    byte[] compressedData = compressData(jsonString);
                                    //String deCompressedData = decompressData(compressedData);
                                    String fileName = policyNumber + "_1.json.gz";
                                    // check This fileName exist or Not
                                    DBObject orderQuery = new BasicDBObject("_id", -1);
                                    List<DBObject> fileExistList = lifeLoggerDao.getDBObjects("lifeS3FilenameLogs", queryOnPolicyId, null, 0, 1, orderQuery, db);
                                    if (fileExistList != null && fileExistList.size() > 0) {
                                        String lastFileName = (String) fileExistList.get(0).get("FileName");
                                        fileName = AppUtil.incrementFilenameNumber(lastFileName);
                                    }
                                    logger.info("E2EV1 FileName: {}", fileName);
                                    // add in lifeS3FilenameLogs collection for url map
                                    DBObject lifeFileObject = new BasicDBObject().append("PolicyNumber", policyNumber).append("FileName", fileName).append("C_AT", new Date());
                                    lifeLoggerDao.addRow("lifeS3FilenameLogs", lifeFileObject, db);
                                    String folderName = NStoS3util.MAP_NS_TO_S3_PATH.get(dbColl);
                                    String url = s3service.addByteContentWithGzip(compressedData, NStoS3util.BUCKET_NAME_LOGGER_ARCHIVAL, folderName, fileName, "application/gzip");
                                    if (url.equals(AppConstants.FILE_ALREADY_EXIST)) {
                                        try {
                                            newArchMongoDao.addRows(coll, dbList, db);
                                        } catch (Exception e) {
                                            logger.error("Error while adding in moveBookLeads , {}", e.getMessage());
                                        }
                                    }
                                    for (DBObject reqIdDoc : listToAddInS3) {
                                        listOfIds.add(reqIdDoc.get("_id"));
                                    }
                                }
                                listToAddInS3 = null;
                            } catch (Exception e) {
                                System.err.println("Error serializing DBObject: " + e.getMessage());
                                throw e;
                            }
                        }
                    }
                }
                policyNumbers = null;
                List<Object> subList = new ArrayList<>();
                int startIndx = 0;
                int lastIndx = 100;
                if (listOfIds.size() > 100) {
                    while (startIndx < listOfIds.size()) {
                        if (startIndx + 100 >= listOfIds.size()) {
                            lastIndx = listOfIds.size();
                        }
                        subList = listOfIds.subList(startIndx, lastIndx);
                        DBObject queryFormovedIds = new BasicDBObject();
                        queryFormovedIds.put("_id", new BasicDBObject().append("$in", subList));
                        lifeLoggerDao.deleteRow(coll, queryFormovedIds, true, db);
                        logger.info("E2E V1 Last Id deleted : {}", subList.get(subList.size() - 1));
                        startIndx = lastIndx;
                        lastIndx = lastIndx + 100;
                        subList = null;
                    }
                } else {
                    DBObject queryFormovedIds = new BasicDBObject();
                    queryFormovedIds.put("_id", new BasicDBObject().append("$in", listOfIds));
                    lifeLoggerDao.deleteRow(coll, queryFormovedIds, true, db);
                    logger.info("E2E V1 Last Id deleted : {}", listOfIds.get(listOfIds.size() - 1));
                }
                listOfIds = null;

                DBObject queryJson;
                if (motorDeletedId != null && motorDeletedId.size() > 0 && motorDeletedId.getObjectId("_id") != null) {
                    totalMigrated = totalMigrated + dbObjectsList.size();
                    DBObject setquery = new BasicDBObject("movedId", dbObjectsList.get(dbObjectsList.size() - 1).get("_id"))
                            .append("totalMigrated", totalMigrated)
                            .append("updatedOn", new Date());
                    queryJson = new BasicDBObject("_id", motorDeletedId.getObjectId("_id"));
                    DBObject rowJson = new BasicDBObject("$set", setquery);
                    newArchMongoDao.updateRow(NStoS3util.lastUpdatedIdColl, queryJson, rowJson, false, false, NStoS3util.lastUpdatedIdDB);
                }
                queryOnId.put("$gt", dbObjectsList.get(dbObjectsList.size() - 1).get("_id"));
                queryObject.put("_id", queryOnId);
                dbObjectsList = null;
                dbObjectsList = lifeLoggerDao.getDBObjects(coll, queryObject, projection, 0, len, null, db);
            }
            dbObjectsList = null;
        }catch (Exception e) {
            logger.error("Exception caught into E2E V1 moveLifeDocs, msg: {}", e.getMessage());
        }finally {
            LifeLoggerServiceImpl.isE2ELoggerS3JobStopV1 = false;
            LifeLoggerServiceImpl.isE2ELoggerS3JobRunningV1 = false;
        }
    }

    @Override
    public void moveLifeDocsV2(String dbColl, Integer size, boolean timeCheck, int days, int chunkSize, String lte_id) {
        logger.info("Inside moveLifeDocsV2 , ns: {}, size: {}, timeCheck: {}, days", dbColl, size, timeCheck, days, chunkSize);
        List<DBObject> dbObjectsList = null;

        try {
            String[] nsArray = dbColl.split("\\.");
            String db = nsArray[0];
            String coll = nsArray[1];;
            int len = size;
            long totalMigrated = 0;
            DBObject queryForDeletId = new BasicDBObject();
            queryForDeletId.put("coll", "LifeRenewalLog.LogDataRenewal_V2");
            BasicDBObject motorDeletedId = newArchMongoDao.getDBObjectByDbName(NStoS3util.lastUpdatedIdColl, queryForDeletId, NStoS3util.lastUpdatedIdDB);
            DBObject queryOnId = new BasicDBObject();
            //only provide startId at once we are starting the batch.
            if (motorDeletedId != null && motorDeletedId.size() > 0) {
                queryOnId.put("$gte", motorDeletedId.get("movedId"));
                totalMigrated = (long)motorDeletedId.get("totalMigrated");
            }
            queryOnId.put("$lt",  new ObjectId(lte_id));// new ObjectId(DateUtil.getStringToDate(DateUtil.getFormatedDate(days, DateUtil.DATE_FORMAT_EE_MMM_dd_hh_mm_ss_z_yyyy), DateUtil.DATE_FORMAT_EE_MMM_dd_hh_mm_ss_z_yyyy)));
            DBObject queryObject = new BasicDBObject();
            queryObject.put("_id", queryOnId);
            BasicDBObject projection = new BasicDBObject("_id", 1).append("PolicyNumber", 1);
            DBObject orderObject = new BasicDBObject("_id", 1);
            dbObjectsList = lifeLoggerDao.getDBObjects(coll, queryObject, projection, 0, len, null, db);
            logger.info("E2E isE2ELoggerS3JobStopV2: {}, isE2ELoggerS3JobRunningV2:{}", isE2ELoggerS3JobStopV2, isE2ELoggerS3JobRunningV2);
            while (dbObjectsList != null && dbObjectsList.size() > 0 && !isE2ELoggerS3JobStopV2) {
                Date currentDate = new Date();
                // Calculate the date 30 days ago
                Calendar calendar = Calendar.getInstance();
                calendar.setTime(currentDate);
                calendar.add(Calendar.DAY_OF_YEAR, -30);
                Date thirtyDaysAgo = calendar.getTime();
                List<Object> listOfIds = new ArrayList<>();
                HashSet<String> policyNumbers = new HashSet<>();
                for (DBObject jsonDocument : dbObjectsList) {
                    listOfIds.add(jsonDocument.get("_id"));
                    String policyNumber = (String) jsonDocument.get("PolicyNumber");
                    if (StringUtils.hasText(policyNumber) && policyNumber.length() < 50 && !(policyNumber.contains("System") ||policyNumber.contains("passwd")||policyNumber.contains("etc")) && !policyNumbers.contains(policyNumber)) {
                        policyNumbers.add(policyNumber);
                        // query to find booked EnquiryId
                        DBObject queryOnPolicyId = new BasicDBObject();
                        queryOnPolicyId.put("PolicyNumber", policyNumber);
                        int offset = 0;
                        List<DBObject> dbList = lifeLoggerDao.getDBObjects(coll, queryOnPolicyId, offset, chunkSize, null, db);
                        if (dbList.size() == chunkSize) {
                            offset = offset + chunkSize;
                            dbList = getList(coll, queryOnPolicyId, offset, db, dbList, chunkSize);

                        }
                        String jsonString;
                        if (dbList != null) {
                            try {
                                List<DBObject>listToAddInS3 = new ArrayList<>();
                                for(DBObject obj : dbList) {
                                    ObjectId objectId = new ObjectId(String.valueOf(obj.get("_id")));
                                    Date objectIdDate = objectId.getDate();
                                    if (!objectIdDate.after(thirtyDaysAgo)) {
                                        listToAddInS3.add(obj);
                                    }
                                }
                                if(listToAddInS3 != null && listToAddInS3.size() > 0) {
                                    jsonString = JSON.serialize(listToAddInS3);
                                    // Compress JSON data
                                    byte[] compressedData = compressData(jsonString);
                                    //String deCompressedData = decompressData(compressedData);
                                    String fileName = policyNumber + "_1.json.gz";
                                    // check This fileName exist or Not
                                    DBObject orderQuery = new BasicDBObject("_id", -1);
                                    List<DBObject> fileExistList = lifeLoggerDao.getDBObjects("lifeS3FilenameLogs", queryOnPolicyId, null, 0, 1, orderQuery, db);
                                    if (fileExistList != null && fileExistList.size() > 0) {
                                        String lastFileName = (String) fileExistList.get(0).get("FileName");
                                        fileName = AppUtil.incrementFilenameNumber(lastFileName);
                                    }
                                    logger.info("E2EV2 FileName: {}", fileName);
                                    // add in lifeS3FilenameLogs collection for url map
                                    DBObject lifeFileObject = new BasicDBObject().append("PolicyNumber", policyNumber).append("FileName", fileName).append("C_AT", new Date());
                                    lifeLoggerDao.addRow("lifeS3FilenameLogs", lifeFileObject, db);
                                    String folderName = NStoS3util.MAP_NS_TO_S3_PATH.get(dbColl);
                                    String url = s3service.addByteContentWithGzip(compressedData, NStoS3util.BUCKET_NAME_LOGGER_ARCHIVAL, folderName, fileName, "application/gzip");
                                    if (url.equals(AppConstants.FILE_ALREADY_EXIST)) {
                                        try {
                                            newArchMongoDao.addRows(coll, dbList, db);
                                        } catch (Exception e) {
                                            logger.error("Error while adding in moveBookLeads , {}", e.getMessage());
                                        }
                                    }
                                    for (DBObject reqIdDoc : listToAddInS3) {
                                        listOfIds.add(reqIdDoc.get("_id"));
                                    }
                                }
                                listToAddInS3 = null;
                            } catch (Exception e) {
                                logger.error("Error serializing DBObject:{} " , e.getMessage());
                                throw e;
                            }
                        }
                    }
                }
                policyNumbers = null;
                List<Object> subList = new ArrayList<>();
                int startIndx = 0;
                int lastIndx = 100;
                if (listOfIds.size() > 100) {
                    while (startIndx < listOfIds.size()) {
                        if (startIndx + 100 >= listOfIds.size()) {
                            lastIndx = listOfIds.size();
                        }
                        subList = listOfIds.subList(startIndx, lastIndx);
                        DBObject queryFormovedIds = new BasicDBObject();
                        queryFormovedIds.put("_id", new BasicDBObject().append("$in", subList));
                        lifeLoggerDao.deleteRow(coll, queryFormovedIds, true, db);
                        logger.info("E2E V2 Last Id deleted : {}", subList.get(subList.size() - 1));
                        startIndx = lastIndx;
                        lastIndx = lastIndx + 100;
                        subList = null;
                    }
                } else {
                    DBObject queryFormovedIds = new BasicDBObject();
                    queryFormovedIds.put("_id", new BasicDBObject().append("$in", listOfIds));
                    lifeLoggerDao.deleteRow(coll, queryFormovedIds, true, db);
                    logger.info("E2E V2 Last Id deleted : {}", listOfIds.get(listOfIds.size() - 1));
                }
                listOfIds = null;

                DBObject queryJson;
                if (motorDeletedId != null && motorDeletedId.size() > 0 && motorDeletedId.getObjectId("_id") != null) {
                    totalMigrated = totalMigrated + dbObjectsList.size();
                    DBObject setquery = new BasicDBObject("movedId", dbObjectsList.get(dbObjectsList.size() - 1).get("_id"))
                            .append("totalMigrated", totalMigrated)
                            .append("updatedOn", new Date());
                    queryJson = new BasicDBObject("_id", motorDeletedId.getObjectId("_id"));
                    DBObject rowJson = new BasicDBObject("$set", setquery);
                    newArchMongoDao.updateRow(NStoS3util.lastUpdatedIdColl, queryJson, rowJson, false, false, NStoS3util.lastUpdatedIdDB);
                }
                queryOnId.put("$gt", dbObjectsList.get(dbObjectsList.size() - 1).get("_id"));
                queryObject.put("_id", queryOnId);
                dbObjectsList = null;
                dbObjectsList = lifeLoggerDao.getDBObjects(coll, queryObject, projection, 0, len, null, db);
            }
            dbObjectsList = null;
        }catch (Exception e) {
            logger.error("Exception caught into E2E V2 moveLifeDocs, msg: {}", e.getMessage());
        }finally {
            LifeLoggerServiceImpl.isE2ELoggerS3JobStopV2 = false;
            LifeLoggerServiceImpl.isE2ELoggerS3JobRunningV2 = false;
        }
    }

    private List<DBObject> getList(String coll, DBObject queryOnrequestId, int offset, String db, List<DBObject> resultList, int chunkSize) {

        try {
            List<DBObject> dataList = lifeLoggerDao.getDBObjects(coll, queryOnrequestId, offset, chunkSize, null, db);
            while (dataList.size() > 0){
                resultList.addAll(dataList);
                offset += chunkSize;
                dataList = lifeLoggerDao.getDBObjects(coll, queryOnrequestId, offset, chunkSize, null, db);
                if((db.equals("e2eLog") || db.equals("LifeRenewalLog")) && coll.equals("LogDataRenewal") && resultList.size() >= 500){
                    break;
                }
            }
        } catch (Exception e) {
            logger.error("Exception while getList");
        }
        return resultList;
    }

    @Override
    public void deleteLifeDocs(String db, Integer size, Boolean timeCheck, int days, int chunkSize){
        logger.info("Inside deleteLifeDocs , ns: {}, size: {}, timeCheck: {}, days", db, size, timeCheck, days, chunkSize);
        List<DBObject> dbObjectsList = null;
        try {
            int len = size;
            List<String> collList = lifeLoggerDao.getAllCollectionBYDB(db);
            for(String coll : collList) {
                DBObject queryOnId = new BasicDBObject();
                queryOnId.put("$gte", new ObjectId("54a440a80000000000000000"));
                queryOnId.put("$lt", new ObjectId(DateUtil.getStringToDate(DateUtil.getFormatedDate(days, DateUtil.DATE_FORMAT_EE_MMM_dd_hh_mm_ss_z_yyyy), DateUtil.DATE_FORMAT_EE_MMM_dd_hh_mm_ss_z_yyyy)));
                DBObject queryObject = new BasicDBObject();
                queryObject.put("_id", queryOnId);
                BasicDBObject projection = new BasicDBObject("_id", 1);
                DBObject orderObject = new BasicDBObject("_id", 1);
                dbObjectsList = lifeLoggerDao.getDBObjects(coll, queryObject, projection, 0, len, orderObject, db);
                logger.info("E2E isE2ELoggerDeleteJobStopV2: {}, isE2ELoggerDeleteJobRunningV2:{}", isE2ELoggerDeleteJobStopV2, isE2ELoggerDeleteJobRunningV2);
                while (dbObjectsList != null && dbObjectsList.size() > 0 && !isE2ELoggerDeleteJobStopV2) {
                    Date currentDate = new Date();
                    // Calculate the date 30 days ago
                    Calendar calendar = Calendar.getInstance();
                    calendar.setTime(currentDate);
                    calendar.add(Calendar.DAY_OF_YEAR, -180);
                    Date sixMonthsAgo = calendar.getTime();
                    List<Object> listOfIds = new ArrayList<>();
                    ObjectId startObjectId = new ObjectId(String.valueOf(dbObjectsList.get(0).get("_id")));
                    ObjectId lastObjectId = new ObjectId(String.valueOf(dbObjectsList.get(dbObjectsList.size() - 1).get("_id")));
                    Date startObjectIdDate = startObjectId.getDate();
                    Date lastObjectIdDate = lastObjectId.getDate();
                    if (!startObjectIdDate.after(sixMonthsAgo) && !lastObjectIdDate.after(sixMonthsAgo)) {
                        for (DBObject reqIdDoc : dbObjectsList) {
                            listOfIds.add(reqIdDoc.get("_id"));
                        }
                    }else{
                        break;
                    }
                    List<Object> subList = new ArrayList<>();
                    int startIndx = 0;
                    int lastIndx = 100;
                    if (listOfIds.size() > 100) {
                        while (startIndx < listOfIds.size()) {
                            if (startIndx + 100 >= listOfIds.size()) {
                                lastIndx = listOfIds.size();
                            }
                            subList = listOfIds.subList(startIndx, lastIndx);
                            DBObject queryFormovedIds = new BasicDBObject();
                            queryFormovedIds.put("_id", new BasicDBObject().append("$in", subList));
                            lifeLoggerDao.deleteRow(coll, queryFormovedIds, true, db);
                            logger.info("E2E V1 Last Id deleted : {}", subList.get(subList.size() - 1));
                            startIndx = lastIndx;
                            lastIndx = lastIndx + 100;
                            subList = null;
                        }
                    } else {
                        DBObject queryFormovedIds = new BasicDBObject();
                        queryFormovedIds.put("_id", new BasicDBObject().append("$in", listOfIds));
                        lifeLoggerDao.deleteRow(coll, queryFormovedIds, true, db);
                        logger.info("E2E V1 Last Id deleted : {}", listOfIds.get(listOfIds.size() - 1));
                    }
                    listOfIds = null;
                    queryOnId.put("$gt", dbObjectsList.get(dbObjectsList.size() - 1).get("_id"));
                    queryObject.put("_id", queryOnId);
                    dbObjectsList = lifeLoggerDao.getDBObjects(coll, queryObject, projection, 0, len, orderObject, db);
                }
                dbObjectsList = null;
            }
        }catch (Exception e) {
            logger.error("Exception caught into E2E V2 moveLifeDocs, msg: {}", e.getMessage());
        }finally {
            LifeLoggerServiceImpl.isE2ELoggerDeleteJobStopV2 = false;
            LifeLoggerServiceImpl.isE2ELoggerDeleteJobRunningV2 = false;
        }
    }
}
