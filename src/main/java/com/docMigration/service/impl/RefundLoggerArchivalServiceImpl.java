package com.docMigration.service.impl;

import com.docMigration.dao.ArchivalMongoDao;
import com.docMigration.dao.ChatMongoDao;
import com.docMigration.dao.GenericMongoDao;
import com.docMigration.service.S3BucketService;
import com.docMigration.util.AppConstants;
import com.docMigration.util.DateUtil;
import com.docMigration.util.NStoS3util;
import com.google.gson.Gson;
import com.mongodb.BasicDBObject;
import com.mongodb.DBObject;
import com.mongodb.util.JSON;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashSet;
import java.util.List;

import static com.docMigration.util.CompressionUtil.compressData;

@Service
public class RefundLoggerArchivalServiceImpl{
    private final Logger logger = LoggerFactory.getLogger(RefundLoggerArchivalServiceImpl.class);

    public  static boolean isRefundS3JobRunning = false;
    public  static boolean isRefundS3JobStop = false;

    public  static boolean isOldRefundS3JobRunning = false;
    public  static boolean isOldRefundS3JobStop = false;

    @Autowired
    @Qualifier("genericMongoDao")
    private GenericMongoDao genericMongoDao;
    @Autowired
    @Qualifier("newArchivalMongoDao")
    ChatMongoDao newArchMongoDao;

    @Autowired
    @Qualifier("oldMongoDao")
    private ChatMongoDao oldLoggerDao;
    @Autowired
    private S3BucketService s3service;

    public void moveRefundBookLeads(String dbColl, Integer size, int days, int chunkSize) throws Exception{
        logger.info("Inside moveRefundBookLeads , ns: {}, size: {}, days:{}", dbColl, size, days);
        List<DBObject> dbObjectsList = null;

        try {
            String[] nsArray = dbColl.split("\\.");
            long totalMigrated = 0;
            String db = nsArray[0];
            String coll = nsArray[1];
            int len = size;
            Gson gson = null;
            DBObject queryForDeletId = new BasicDBObject();
            queryForDeletId.put("coll", dbColl);
            BasicDBObject motorDeletedId = newArchMongoDao.getDBObjectByDbName(NStoS3util.lastUpdatedIdColl, queryForDeletId, NStoS3util.lastUpdatedIdDB);

            DBObject queryOnId = new BasicDBObject();
            //only provide startId at once we are starting the batch.
            if (motorDeletedId != null && motorDeletedId.size() > 0) {
                queryOnId.put("$gte", motorDeletedId.get("movedId"));
                totalMigrated = (long)motorDeletedId.get("totalMigrated");
            }
            queryOnId.put("$lt", new ObjectId(DateUtil.getStringToDate(DateUtil.getFormatedDate(days, DateUtil.DATE_FORMAT_EE_MMM_dd_hh_mm_ss_z_yyyy), DateUtil.DATE_FORMAT_EE_MMM_dd_hh_mm_ss_z_yyyy)));
            DBObject queryObject = new BasicDBObject();
            queryObject.put("_id", queryOnId);
            BasicDBObject projection = new BasicDBObject("_id", 1).append("LeadId", 1);
            DBObject orderObject = new BasicDBObject("_id", 1);
            dbObjectsList = genericMongoDao.getDBObjects(coll, queryObject, projection, 0, len, orderObject, db);
            logger.info("isRefundS3JobStop: {}, isRefundS3JobRunning:{}", isRefundS3JobStop, isRefundS3JobRunning);
            while (dbObjectsList != null && dbObjectsList.size() > 0 && !isRefundS3JobStop) {
                List<Object> listOfIds = new ArrayList<>();
                HashSet<String> leadIds = new HashSet<>();
                for (DBObject jsonDocument : dbObjectsList) {
                    listOfIds.add(jsonDocument.get("_id"));

                    String fileName = null;
                    String sleadId = null;
                    Long leadId = null;
                    Integer iLeadId = null;
                    if (jsonDocument.get("LeadId") instanceof String) {
                        sleadId = (String) jsonDocument.get("LeadId");
                    }
                    else if (jsonDocument.get("LeadId") instanceof Long) {
                        leadId = (Long) jsonDocument.get("LeadId");
                        sleadId = String.valueOf(jsonDocument.get("LeadId"));
                    }
                    else if(jsonDocument.get("LeadId") instanceof Integer){
                        iLeadId = (Integer) jsonDocument.get("LeadId");
                        sleadId = String.valueOf(jsonDocument.get("LeadId"));

                    }else{
                        continue;
                    }
                    if ((sleadId != null && StringUtils.hasText(sleadId) && sleadId.length()>3 && !leadIds.contains(sleadId))) {
                        leadIds.add(sleadId);
                        int offset = 0;
                        List<DBObject> dbList = null;
                        if(iLeadId !=null && iLeadId >0){
                            DBObject queryOniLeadId = new BasicDBObject();
                            queryOniLeadId.put("LeadId", iLeadId);
                            dbList = genericMongoDao.getDBObjects(coll, queryOniLeadId, offset, chunkSize, null, db);
                            if (dbList != null && dbList.size() == chunkSize) {
                                offset = offset + chunkSize;
                                dbList = getList(coll, queryOniLeadId, offset, db, dbList, chunkSize);
                            }
                        }

                        else if(leadId !=null && leadId >0){
                            DBObject queryOnLeadId = new BasicDBObject();
                            queryOnLeadId.put("LeadId", leadId);
                            dbList = genericMongoDao.getDBObjects(coll, queryOnLeadId, offset, chunkSize, null, db);
                            if (dbList != null && dbList.size() == chunkSize) {
                                offset = offset + chunkSize;
                                dbList = getList(coll, queryOnLeadId, offset, db, dbList, chunkSize);
                            }
                        }

                        else if(sleadId !=null && StringUtils.hasText(sleadId) && sleadId.length()>3){
                            DBObject queryOnSLeadId = new BasicDBObject();
                            queryOnSLeadId.put("LeadId", sleadId);
                            dbList = genericMongoDao.getDBObjects(coll, queryOnSLeadId, offset, chunkSize, null, db);
                            if (dbList != null && dbList.size() == chunkSize) {
                                offset = offset + chunkSize;
                                dbList = getList(coll, queryOnSLeadId, offset, db, dbList, chunkSize);
                            }
                        }

                        // compress and move to s3 and failOver applied
                        String jsonString;
                        if (dbList != null && dbList.size() > 0) {
                            try {
                                jsonString = JSON.serialize(dbList);
                                // Compress JSON data
                                byte[] compressedData = compressData(jsonString);
                                String folderName = NStoS3util.MAP_NS_TO_S3_PATH.get(dbColl);
                                fileName = "lid_" + sleadId + ".json.gz";
                                logger.info("Refund fileName : {} ", fileName);
                                String url = s3service.addByteContentWithGzip(compressedData, NStoS3util.BUCKET_NAME_LOGGER_ARCHIVAL, folderName, fileName, "application/gzip");
                                if (url.equals(AppConstants.FILE_ALREADY_EXIST)) {
                                    try {
                                        newArchMongoDao.addRows(coll, dbList, db);
                                    } catch (Exception e) {
                                        // do not throw exception from here
                                        logger.error("Error while adding in moveRefundBookLeads , {}", e.getMessage());
                                    }
                                }
                                for (DBObject reqIdDoc : dbList) {
                                    listOfIds.add(reqIdDoc.get("_id"));
                                }
                            } catch (Exception e) {
                                logger.error("Error serializing DBObject: " + e.getMessage());
                                throw e;
                            }
                        }
                    }
                }
                leadIds = null;
                List<Object> subList = new ArrayList<>();
                int startIndx = 0;
                int lastIndx = 100;
                if (listOfIds.size() > 100) {
                    while (startIndx < listOfIds.size()) {
                        if (startIndx + 100 >= listOfIds.size()) {
                            lastIndx = listOfIds.size();
                        }
                        subList = listOfIds.subList(startIndx, lastIndx);
                        DBObject queryFormovedIds = new BasicDBObject();
                        queryFormovedIds.put("_id", new BasicDBObject().append("$in", subList));
                        genericMongoDao.deleteRow(coll, queryFormovedIds, true, db);
                        logger.info(" deleted Refund: Last _id : {}", subList.get(subList.size() - 1));
                        startIndx = lastIndx;
                        lastIndx = lastIndx + 100;
                        subList = null;
                    }
                } else if (listOfIds != null && listOfIds.size() > 0) {
                    DBObject queryFormovedIds = new BasicDBObject();
                    queryFormovedIds.put("_id", new BasicDBObject().append("$in", listOfIds));
                    genericMongoDao.deleteRow(coll, queryFormovedIds, true, db);
                    logger.info("deleted Refund: Last _id : {}", listOfIds.get(listOfIds.size() - 1));
                }
                listOfIds = null;

                DBObject queryJson;
                if (motorDeletedId != null && motorDeletedId.size() > 0 && motorDeletedId.getObjectId("_id") != null) {
                    totalMigrated = totalMigrated + dbObjectsList.size();
                    DBObject setquery = new BasicDBObject("movedId", dbObjectsList.get(dbObjectsList.size() - 1).get("_id"))
                            .append("totalMigrated", totalMigrated)
                            .append("updatedOn", new Date());                      queryJson = new BasicDBObject("_id", motorDeletedId.getObjectId("_id"));
                    DBObject rowJson = new BasicDBObject("$set", setquery);
                    newArchMongoDao.updateRow(NStoS3util.lastUpdatedIdColl, queryJson, rowJson, false, false, NStoS3util.lastUpdatedIdDB);
                }
                queryOnId.put("$gt", dbObjectsList.get(dbObjectsList.size() - 1).get("_id"));
                queryObject.put("_id", queryOnId);
                dbObjectsList = genericMongoDao.getDBObjects(coll, queryObject, projection, 0, len, orderObject, db);
            }
            dbObjectsList = null;
        }
        catch (Exception e) {
            logger.error("Refund :  Exception caught into moveRefundBookLeads, msg: {}", e.getMessage());
            throw e;
        } finally {
            isRefundS3JobStop = false;
            isRefundS3JobRunning = false;
        }
    }
    private List<DBObject> getList(String coll, DBObject queryOnrequestId, int offset, String db, List<DBObject> resultList, int chunkSize) {

        try {
            List<DBObject> dataList = genericMongoDao.getDBObjects(coll, queryOnrequestId, offset, chunkSize, null, db);
            while (dataList.size() > 0){
                resultList.addAll(dataList);
                offset += chunkSize;
                dataList = genericMongoDao.getDBObjects(coll, queryOnrequestId, offset, chunkSize, null, db);
            }
        } catch (Exception e) {
            logger.error("Exception while getList");
        }
        return resultList;
    }
    private List<DBObject> getOldList(String coll, DBObject queryOnrequestId, int offset, DBObject orderObject, String db, List<DBObject> resultList, int chunkSize) {

        try {
            List<DBObject> dataList = oldLoggerDao.getDBObjects(coll, queryOnrequestId, offset, chunkSize, orderObject, db);
            while (dataList.size() > 0){
                resultList.addAll(dataList);
                offset += chunkSize;
                dataList = oldLoggerDao.getDBObjects(coll, queryOnrequestId, offset, chunkSize, orderObject, db);
            }
        } catch (Exception e) {
            logger.error("Exception while getList");
        }
        return resultList;
    }

    public void moveOldRefundBookLeads(String dbColl, Integer size, int days, int chunkSize) throws Exception {
        logger.info("Inside moveOldRefundBookLeads , ns: {}, size: {}, days:{}", dbColl, size, days);
        List<DBObject> dbObjectsList = null;

        try {
            String[] nsArray = dbColl.split("\\.");
            String db = nsArray[0];
            String coll = nsArray[1];
            int len = size;
            long totalMigrated = 0;
            DBObject queryForDeletId = new BasicDBObject();
            queryForDeletId.put("coll", dbColl);
            BasicDBObject motorDeletedId = newArchMongoDao.getDBObjectByDbName(NStoS3util.lastUpdatedIdColl, queryForDeletId, NStoS3util.lastUpdatedIdDB);

            DBObject queryOnId = new BasicDBObject();
            //only provide startId at once we are starting the batch.
            if (motorDeletedId != null && motorDeletedId.size() > 0) {
                queryOnId.put("$gte", motorDeletedId.get("movedId"));
                totalMigrated = (long)motorDeletedId.get("totalMigrated");
            }
            queryOnId.put("$lt", new ObjectId(DateUtil.getStringToDate(DateUtil.getFormatedDate(days, DateUtil.DATE_FORMAT_EE_MMM_dd_hh_mm_ss_z_yyyy), DateUtil.DATE_FORMAT_EE_MMM_dd_hh_mm_ss_z_yyyy)));
            DBObject queryObject = new BasicDBObject();
            queryObject.put("_id", queryOnId);
            BasicDBObject projection = new BasicDBObject("_id", 1).append("LeadId", 1);
            DBObject orderObject = new BasicDBObject("_id", 1);
            dbObjectsList = oldLoggerDao.getDBObjects(coll, queryObject, projection, 0, len, orderObject, db);
            logger.info("isOldRefundS3JobStop: {}, isOldRefundS3JobRunning:{}", isOldRefundS3JobStop, isOldRefundS3JobRunning);
            while (dbObjectsList != null && dbObjectsList.size() > 0 && !isOldRefundS3JobStop) {

                logger.info("Refund Old migration running");
                List<Object> listOfIds = new ArrayList<>();
                HashSet<String> leadIds = new HashSet<>();
                for (DBObject jsonDocument : dbObjectsList) {
                    listOfIds.add(jsonDocument.get("_id"));

                    String fileName = null;
                    String sleadId = null;
                    Long leadId = null;
                    Integer ileadId = null;
                    if (jsonDocument.get("LeadId") instanceof String) {
                        sleadId = (String) jsonDocument.get("LeadId");
                    }
                    if (jsonDocument.get("LeadId") instanceof Long) {
                        sleadId = String.valueOf(jsonDocument.get("LeadId"));
                        leadId = (Long) jsonDocument.get("LeadId");
                    }
                    if (jsonDocument.get("LeadId") instanceof Integer) {
                        sleadId = String.valueOf(jsonDocument.get("LeadId"));
                        ileadId = (Integer) jsonDocument.get("LeadId");
                    }
                    if (sleadId != null && StringUtils.hasText(sleadId) && sleadId.length()>3 && !leadIds.contains(sleadId)){
                        leadIds.add(sleadId);
                        int offset = 0;
                        List<DBObject> dbList = null;
                        if(ileadId !=null && ileadId >0){
                            DBObject queryOniLeadId = new BasicDBObject();
                            queryOniLeadId.put("LeadId", ileadId);
                            dbList = oldLoggerDao.getDBObjects(coll, queryOniLeadId, offset, chunkSize, orderObject, db);
                            if (dbList != null && dbList.size() == chunkSize) {
                                offset = offset + chunkSize;
                                dbList = getOldList(coll, queryOniLeadId, offset, orderObject, db, dbList, chunkSize);
                            }
                        }

                        else if(leadId !=null && leadId >0){
                            DBObject queryOnLeadId = new BasicDBObject();
                            queryOnLeadId.put("LeadId", leadId);
                            dbList = oldLoggerDao.getDBObjects(coll, queryOnLeadId, offset, chunkSize, orderObject, db);
                            if (dbList != null && dbList.size() == chunkSize) {
                                offset = offset + chunkSize;
                                dbList = getOldList(coll, queryOnLeadId, offset, orderObject, db, dbList, chunkSize);
                            }
                        }

                        else if(sleadId !=null && StringUtils.hasText(sleadId) && sleadId.length()>3){
                            DBObject queryOnSLeadId = new BasicDBObject();
                            queryOnSLeadId.put("LeadId", sleadId);
                            dbList = oldLoggerDao.getDBObjects(coll, queryOnSLeadId, offset, chunkSize, orderObject, db);
                            if (dbList != null && dbList.size() == chunkSize) {
                                offset = offset + chunkSize;
                                dbList = getOldList(coll, queryOnSLeadId, offset, orderObject, db, dbList, chunkSize);
                            }
                        }
                        // compress and move to s3 and failOver applied
                        String jsonString;
                        if (dbList != null && dbList.size() > 0) {
                            try {
                                jsonString = JSON.serialize(dbList);
                                // Compress JSON data
                                byte[] compressedData = compressData(jsonString);

                                fileName = "lid_" + sleadId + ".json.gz";
                                String folderName = NStoS3util.MAP_NS_TO_S3_PATH.get(dbColl);
                                logger.info("Refund Old fileName : {} ", fileName);
                                String url = s3service.addByteContentWithGzip(compressedData, NStoS3util.BUCKET_NAME_LOGGER_ARCHIVAL, folderName, fileName, "application/gzip");
                                if (url.equals(AppConstants.FILE_ALREADY_EXIST)) {
                                    try {
                                        newArchMongoDao.addRows(coll, dbList, db);
                                    } catch (Exception e) {
                                        // do not throw exception from here
                                        logger.error("Error while adding in moveRefundBookLeads , {}", e.getMessage());
                                    }
                                }
                                for (DBObject reqIdDoc : dbList) {
                                    listOfIds.add(reqIdDoc.get("_id"));
                                }
                            } catch (Exception e) {
                                logger.error("Error serializing DBObject: " + e.getMessage());
                                throw e;
                            }
                        }
                    }
                }
                leadIds = null;
                List<Object> subList = new ArrayList<>();
                int startIndx = 0;
                int lastIndx = 100;
                if (listOfIds.size() > 100) {
                    while (startIndx < listOfIds.size()) {
                        if (startIndx + 100 >= listOfIds.size()) {
                            lastIndx = listOfIds.size();
                        }
                        subList = listOfIds.subList(startIndx, lastIndx);
                        DBObject queryFormovedIds = new BasicDBObject();
                        queryFormovedIds.put("_id", new BasicDBObject().append("$in", subList));
                        oldLoggerDao.deleteRow(coll, queryFormovedIds, true, db);
                        logger.info(" deleted old Refund: Last _id : {}", subList.get(subList.size() - 1));
                        startIndx = lastIndx;
                        lastIndx = lastIndx + 100;
                        subList = null;
                    }
                } else if (listOfIds != null && listOfIds.size() > 0) {
                    DBObject queryFormovedIds = new BasicDBObject();
                    queryFormovedIds.put("_id", new BasicDBObject().append("$in", listOfIds));
                    oldLoggerDao.deleteRow(coll, queryFormovedIds, true, db);
                    logger.info("deleted old Refund: Last _id : {}", listOfIds.get(listOfIds.size() - 1));
                }
                listOfIds = null;

                DBObject queryJson;
                if (motorDeletedId != null && motorDeletedId.size() > 0 && motorDeletedId.getObjectId("_id") != null) {
                    totalMigrated = totalMigrated + dbObjectsList.size();
                    DBObject setquery = new BasicDBObject("movedId", dbObjectsList.get(dbObjectsList.size() - 1).get("_id"))
                            .append("totalMigrated", totalMigrated)
                            .append("updatedOn", new Date());                    queryJson = new BasicDBObject("_id", motorDeletedId.getObjectId("_id"));
                    DBObject rowJson = new BasicDBObject("$set", setquery);
                    newArchMongoDao.updateRow(NStoS3util.lastUpdatedIdColl, queryJson, rowJson, false, false, NStoS3util.lastUpdatedIdDB);
                }
                queryOnId.put("$gt", dbObjectsList.get(dbObjectsList.size() - 1).get("_id"));
                queryObject.put("_id", queryOnId);
                dbObjectsList = oldLoggerDao.getDBObjects(coll, queryObject, projection, 0, len, orderObject, db);
            }
            dbObjectsList = null;
        }
        catch (Exception e) {
            logger.error("Refund :  Exception caught into moveOldRefundBookLeads, msg: {}", e.getMessage());
            throw e;
        } finally {
            isOldRefundS3JobStop = false;
            isOldRefundS3JobRunning = false;
        }
    }
}
