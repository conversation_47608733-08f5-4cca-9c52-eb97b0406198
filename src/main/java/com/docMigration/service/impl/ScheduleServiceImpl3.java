/**
 * 
 */
package com.docMigration.service.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import com.docMigration.cleanup.entity.StaticMaster;
import com.docMigration.dao.AppMongoDao;
import com.docMigration.dao.AppNewMongoDao;
import com.docMigration.dao.ArchivalMongoDao;
import com.docMigration.dao.ChatMongoDao;
import com.docMigration.dao.CommBoxDao;
import com.docMigration.dao.E2eMongoDao;
import com.docMigration.dao.LoggerMongoDao;
import com.docMigration.service.S3BucketService;
import com.docMigration.service.ScheduleService;
import com.docMigration.service.ScheduleService3;
import com.docMigration.util.DateUtil;
import com.mongodb.BasicDBObject;
import com.mongodb.DBObject;

/**
 * <AUTHOR>
 *
 */
@Service
public class ScheduleServiceImpl3 implements ScheduleService3{

	private final Logger logger = LoggerFactory.getLogger(ScheduleServiceImpl3.class);
	
	private static Date date = DateUtil.getStringToDate("31/03/2019", DateUtil.DATE_FORMAT_DD_MM_YYYY_SLASH);

	public static Boolean stopScheduleTw1 = false;

	public static Integer waitScheduleTw1 = 1;
	
	public static Boolean scheduleTw1Running = false;
	
	public static Boolean stopScheduleCar1 = false;

	public static Integer waitScheduleCar1 = 1;
	
	public static Boolean scheduleCar1Running = false;
	
	@Autowired
	S3BucketService s3BucketService;
	
	@Autowired
	AppMongoDao appMongoDao;
	
	@Autowired
	AppNewMongoDao appNewMongoDao;
	
	@Autowired
	ChatMongoDao chatMongoDao;
	
	@Autowired
	CommBoxDao commBoxMongoDao;
	
	@Autowired
	LoggerMongoDao loggerMongoDao;
	
	@Autowired
	E2eMongoDao e2eMongoDao;

	@Value("${aws.bucket_name}")
	private String bucketName;
	
	@Value("${doc.schedular.count}")
	private int count;
	
	@Autowired
	private MongoTemplate mongoTemplate;
	

	@Override
	public void deleteNotBookLeadInCar(String startId, String coll, int size, Boolean timeCheck) throws Exception {
		logger.info("Inside deleteNotBookLeadInCarLogger : {}", startId);
		
		Date startDate = new Date();
		startDate = DateUtil.setHours(startDate, 7);
		startDate = DateUtil.setMinute(startDate, 0);
		startDate = DateUtil.setSeconds(startDate, 0);

		Date endDate = new Date();
		endDate = DateUtil.setHours(endDate, 20);
		endDate = DateUtil.setMinute(endDate, 0);
		endDate = DateUtil.setSeconds(endDate, 0);
		
		DBObject queryObject = new BasicDBObject();
		
		DBObject orderObject = new BasicDBObject("_id", 1);
		DBObject projectObj = new BasicDBObject("_id", 1);
		
		List<ObjectId> listOfIds = new ArrayList<>();
		
		int offset = 0;
		int len = size;
		
		DBObject queryForDeletId = new BasicDBObject();
		queryForDeletId.put("coll", coll);
		BasicDBObject twoWheelerDeletedId = loggerMongoDao.getDBObject("deletedIdsLogger3",queryForDeletId);
		
		DBObject queryOnId = new BasicDBObject();
		
		//only provide startId at once we are starting the batch.
		if(!StringUtils.isEmpty(startId)) {
			queryOnId.put("$gte", new ObjectId(startId));
		}else if(twoWheelerDeletedId!=null && twoWheelerDeletedId.size()>0){
			queryOnId.put("$gte", twoWheelerDeletedId.get("deletedId"));
		}
		
		queryOnId.put("$lt", twoWheelerDeletedId.get("endId"));
		
		queryObject.put("_id", queryOnId);
		queryObject.put("IsLeadBooked", new BasicDBObject().append("$ne", true));
		List<BasicDBObject> dbObjectsList = loggerMongoDao.getDBObjects(coll, queryObject, projectObj, offset, len, orderObject);
		logger.info("StaticMaster.isDeleteCarLoggerStop() {}", stopScheduleCar1);
		while (dbObjectsList != null && dbObjectsList.size() > 0 && !stopScheduleCar1) {
			Date todayDate = new Date();
			startDate = DateUtil.setHours(todayDate, 7);
			startDate = DateUtil.setMinute(startDate, 0);
			startDate = DateUtil.setSeconds(startDate, 0);
			
			endDate = DateUtil.setHours(todayDate, 20);
			endDate = DateUtil.setMinute(endDate, 0);
			endDate = DateUtil.setSeconds(endDate, 0);
			logger.info("Today date in service: {}, start date {} , end date {}", todayDate, startDate, endDate);
			if (todayDate.before(startDate) || todayDate.after(endDate) || !timeCheck) {
				
				for (BasicDBObject res : dbObjectsList) {
					//logger.info("Id found , {} ", res.getObjectId("_id"));
					listOfIds.add(res.getObjectId("_id"));
				}
				
				DBObject queryForDeleteIds = new BasicDBObject();
				queryForDeleteIds.put("_id", new BasicDBObject().append("$in", listOfIds));
				loggerMongoDao.deleteRow(coll, queryForDeleteIds, true);
				Thread.sleep(waitScheduleCar1*1000);
				
				listOfIds = new ArrayList<>();
				logger.info(" Last Id deleted : {}", dbObjectsList.get(dbObjectsList.size()-1).getObjectId("_id"));
				DBObject queryJson;
				if(twoWheelerDeletedId!=null && twoWheelerDeletedId.size()> 0 && twoWheelerDeletedId.getObjectId("_id")!=null) {
					DBObject setquery = new BasicDBObject("deletedId", dbObjectsList.get(dbObjectsList.size()-1).getObjectId("_id"));
					queryJson = new BasicDBObject("_id", twoWheelerDeletedId.getObjectId("_id"));
					DBObject rowJson = new BasicDBObject("$set", setquery);
					loggerMongoDao.updateRow("deletedIdsLogger3", queryJson, rowJson, false,false);
				}
					queryOnId.put("$gte", dbObjectsList.get(dbObjectsList.size()-1).getObjectId("_id"));
				
				queryObject.put("_id", queryOnId);
				queryObject.put("IsLeadBooked", new BasicDBObject().append("$ne", true));
				
				dbObjectsList = loggerMongoDao.getDBObjects(coll, queryObject, projectObj, offset, len, orderObject);
				
			}else {
				dbObjectsList = null;
			}
		}
		
	}

	
}
