/**
 * 
 */
package com.docMigration.service.impl;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.zip.GZIPInputStream;
import java.util.zip.GZIPOutputStream;
import org.apache.commons.io.output.ByteArrayOutputStream;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import com.docMigration.cleanup.entity.StaticMaster;
import com.docMigration.dao.ArchivalMongoDao;
import com.docMigration.dao.ChatMongoDao;
import com.docMigration.dao.LoggerMongoDao;
import com.docMigration.service.LoggerArchivalService;
import com.docMigration.service.S3BucketService;
import com.docMigration.util.DateUtil;
import com.mongodb.BasicDBObject;
import com.mongodb.DBObject;

/**
 * <AUTHOR>
 *
 */
@Service
public class LoggerArchivalServiceImpl implements LoggerArchivalService{

	private final Logger logger = LoggerFactory.getLogger(LoggerArchivalServiceImpl.class);
	
	@Autowired
	S3BucketService s3BucketService;
	
	@Autowired
	LoggerMongoDao loggerMongoDao;
	
	@Autowired
	@Qualifier("motorLoggerDao")
	ChatMongoDao motorLoggerDao;

	@Autowired
	@Qualifier("newArchivalMongoDao")
	ChatMongoDao newArchMongoDao;

	@Autowired
	@Qualifier("commLoggerMongoDao")
	ChatMongoDao commLoggerMongoDao;

	@Autowired
	@Qualifier("commPrimaryArchivalMongoDao")
	ChatMongoDao commPrimaryArchivalMongoDao;

	@Value("${aws.bucket_name}")
	private String bucketName;
	
	@Value("${doc.schedular.count}")
	private int count;
	
	@Autowired
	@Qualifier("oldMongoDao")
	ChatMongoDao oldMongoDao;
	@Autowired
	private S3BucketService s3service;

	@Override
	public void moveBookLeads(String coll, Integer size, Boolean timeCheck, String db, int days) throws Exception {

		logger.info("Inside moveBookLeads");
		Date startDate = new Date();
		startDate = DateUtil.setHours(startDate, 7);
		startDate = DateUtil.setMinute(startDate, 0);
		startDate = DateUtil.setSeconds(startDate, 0);
		Date endDate = new Date();
		endDate = DateUtil.setHours(endDate, 20);
		endDate = DateUtil.setMinute(endDate, 0);
		endDate = DateUtil.setSeconds(endDate, 0);
		DBObject queryObject = new BasicDBObject();
		
		DBObject orderObject = new BasicDBObject("_id", 1);
		
		List<Object> listOfIds = new ArrayList<>();
		
		int offset = 0;
		int len = size;
		
		DBObject queryForDeletId = new BasicDBObject();
		queryForDeletId.put("coll", coll);
		BasicDBObject twoWheelerDeletedId = motorLoggerDao.getDBObjectByDbName("movedIdsLogger",queryForDeletId,db);
		
		DBObject queryOnId = new BasicDBObject();
		
		//only provide startId at once we are starting the batch.
		if(twoWheelerDeletedId!=null && twoWheelerDeletedId.size()>0){
			queryOnId.put("$gte", twoWheelerDeletedId.get("movedId"));
		}
		
		queryOnId.put("$lt", new ObjectId(DateUtil.getStringToDate(DateUtil.getFormatedDate(days, DateUtil.DATE_FORMAT_EE_MMM_dd_hh_mm_ss_z_yyyy),DateUtil.DATE_FORMAT_EE_MMM_dd_hh_mm_ss_z_yyyy)));
		//queryOnId.put("$lt", twoWheelerDeletedId.get("endId"));
		
		queryObject.put("_id", queryOnId);
		List<DBObject> dbObjectsList = motorLoggerDao.getDBObjects(coll, queryObject, null, offset, len, orderObject,db);
		logger.info("StaticMaster.isMoveCarLoggerDataStop() {}", StaticMaster.isMoveCarLoggerDataStop());
		while (dbObjectsList != null && dbObjectsList.size() > 0 && !StaticMaster.isMoveCarLoggerDataStop()) {
			Date todayDate = new Date();
			startDate = DateUtil.setHours(todayDate, 7);
			startDate = DateUtil.setMinute(startDate, 0);
			startDate = DateUtil.setSeconds(startDate, 0);

			endDate = DateUtil.setHours(todayDate, 20);
			endDate = DateUtil.setMinute(endDate, 0);
			endDate = DateUtil.setSeconds(endDate, 0);
			logger.info("Today date in service: {}, start date {} , end date {}", todayDate, startDate, endDate);
			if (todayDate.before(startDate) || todayDate.after(endDate) || !timeCheck) {
				
				Date date = null;
				for (DBObject res : dbObjectsList) {
					//logger.info("Id found , {} ", res.getObjectId("_id"));
					listOfIds.add(res.get("_id"));
					date = ((ObjectId)res.get("_id")).getDate();
				}
				
				DBObject queryFormovedIds = new BasicDBObject();
				queryFormovedIds.put("_id", new BasicDBObject().append("$in", listOfIds));
				
				try {
					newArchMongoDao.addRows(coll+"_"+DateUtil.parseDate(date, DateUtil.DATE_FORMAT_YYYY_MM_DD), dbObjectsList, db);
				}catch(Exception e) {
					logger.error("Error while adding in moveBookLeads , {}", e.getMessage());
					if(!e.getMessage().contains("timeout")&&!e.getMessage().contains("duplicate")) {
						throw e;
					}
				}
				
				motorLoggerDao.deleteRow(coll, queryFormovedIds, true,db);
				Thread.sleep(StaticMaster.getWaitForNextDeleteBatchInCar()*1000);
				
				listOfIds = new ArrayList<>();
				logger.info(" Last Id deleted : {}", dbObjectsList.get(dbObjectsList.size()-1).get("_id"));
				DBObject queryJson;
				if(twoWheelerDeletedId!=null && twoWheelerDeletedId.size()> 0 && twoWheelerDeletedId.getObjectId("_id")!=null) {
					DBObject setquery = new BasicDBObject("movedId", dbObjectsList.get(dbObjectsList.size()-1).get("_id"));
					queryJson = new BasicDBObject("_id", twoWheelerDeletedId.getObjectId("_id"));
					DBObject rowJson = new BasicDBObject("$set", setquery);
					motorLoggerDao.updateRow("movedIdsLogger", queryJson, rowJson, false,false,db);
				}
					queryOnId.put("$gt", dbObjectsList.get(dbObjectsList.size()-1).get("_id"));
				//}
				
				queryObject.put("_id", queryOnId);
				//queryObject.put("IsLeadBooked", new BasicDBObject().append("$ne", true));
				
				dbObjectsList = motorLoggerDao.getDBObjects(coll, queryObject, null, offset, len, orderObject,db);
				
			}else {
				dbObjectsList = null;
			}
		}
		
	
	}

	@Override
	public void twMarkBookedLeads(String startId, String endId, String coll, Boolean timeCheck, String db, int days, int len) throws Exception {
		

		logger.info("Inside deleteNotBookLeadInTwLogger : {}", startId);
		
		Date startDate = new Date();
		startDate = DateUtil.setHours(startDate, 7);
		startDate = DateUtil.setMinute(startDate, 0);
		startDate = DateUtil.setSeconds(startDate, 0);

		Date endDate = new Date();
		endDate = DateUtil.setHours(endDate, 20);
		endDate = DateUtil.setMinute(endDate, 0);
		endDate = DateUtil.setSeconds(endDate, 0);
		
		DBObject queryObject = new BasicDBObject();
		
		DBObject orderObject = new BasicDBObject("_id", 1);
		DBObject projectObj = new BasicDBObject("_id", 1).append("VehicleDetailId", 1);
		
		int offset = 0;
		
		DBObject queryForDeletId = new BasicDBObject();
		BasicDBObject twoWheelerDeletedId = motorLoggerDao.getBasicDBObject("CheckedIdForBookedLeads",queryForDeletId,db);
		
		DBObject queryOnId = new BasicDBObject();
		
		//only provide startId at once we are starting the batch.
		if(!StringUtils.isEmpty(startId)) {
			queryOnId.put("$gte", new ObjectId(startId));
		}else if(twoWheelerDeletedId!=null && twoWheelerDeletedId.size()>0){
			queryOnId.put("$gte", twoWheelerDeletedId.get("lastId"));
		}
		
		queryOnId.put("$lt", new ObjectId(DateUtil.getStringToDate(DateUtil.getFormatedDate(-days, DateUtil.DATE_FORMAT_EE_MMM_dd_hh_mm_ss_z_yyyy),DateUtil.DATE_FORMAT_EE_MMM_dd_hh_mm_ss_z_yyyy)));
		
		queryObject.put("_id", queryOnId);
		 queryObject.put("IsLeadBooked", new BasicDBObject().append("$ne", true));
		List<DBObject> dbObjectsList = motorLoggerDao.getDBObjects(coll, queryObject, projectObj, offset, len, orderObject,db);
		logger.info("StaticMaster.isDeleteCarLoggerStop() {}", StaticMaster.isTwMarkBookedLeadsStop());
		while (dbObjectsList != null && dbObjectsList.size() > 0 && !StaticMaster.isTwMarkBookedLeadsStop()) {
			Date todayDate = new Date();
			startDate = DateUtil.setHours(todayDate, 7);
			startDate = DateUtil.setMinute(startDate, 0);
			startDate = DateUtil.setSeconds(startDate, 0);
			
			endDate = DateUtil.setHours(todayDate, 20);
			endDate = DateUtil.setMinute(endDate, 0);
			endDate = DateUtil.setSeconds(endDate, 0);
			logger.info("Today date in service: {}, start date {} , end date {}", todayDate, startDate, endDate);
			if (todayDate.before(startDate) || todayDate.after(endDate) || !timeCheck) {
				
				for (DBObject res : dbObjectsList) {
					//logger.info("Id found , {} ", res.getObjectId("_id"));
					DBObject queryOnSearch = new BasicDBObject();
					queryOnSearch.put("VehicleDetailId", res.get("VehicleDetailId"));
					BasicDBObject result = motorLoggerDao.getDBObjectByDbName("BookedVehicleDetailsIds", queryOnSearch,db);
					if(result != null && result.size()>0) {
						DBObject setquery = new BasicDBObject("IsLeadBooked", true);
						DBObject queryJson = new BasicDBObject("_id", res.get("_id"));
						DBObject rowJson = new BasicDBObject("$set", setquery);
						motorLoggerDao.updateRow(coll, queryJson, rowJson, false,false,db);
					}
					
				}
				
				
				logger.info(" Last Id deleted : {}", dbObjectsList.get(dbObjectsList.size()-1).get("_id"));
				DBObject queryJson;
				if(twoWheelerDeletedId!=null && twoWheelerDeletedId.size()> 0 && twoWheelerDeletedId.getObjectId("_id")!=null) {
					DBObject setquery = new BasicDBObject("lastId", dbObjectsList.get(dbObjectsList.size()-1).get("_id"));
					queryJson = new BasicDBObject("_id", twoWheelerDeletedId.getObjectId("_id"));
					DBObject rowJson = new BasicDBObject("$set", setquery);
					motorLoggerDao.updateRow("CheckedIdForBookedLeads", queryJson, rowJson, false,false,db);
				}
				
				queryOnId.put("$gt", dbObjectsList.get(dbObjectsList.size()-1).get("_id"));
				queryObject.put("_id", queryOnId);
				queryObject.put("IsLeadBooked", new BasicDBObject().append("$ne", true));
				
				dbObjectsList = motorLoggerDao.getDBObjects(coll, queryObject, projectObj, offset, len, orderObject,db);
			}else {
				dbObjectsList = null;
			}
		}
		
	
	}


	@Override
	  public void cleanReqResLogs(String coll, Integer size, Boolean timeCheck,String db) throws Exception  {
	    logger.info("Inside cleanReqResLogs : {}");
	    
	    Date startDate = new Date();
	    startDate = DateUtil.setHours(startDate, 7);
	    startDate = DateUtil.setMinute(startDate, 0);
	    startDate = DateUtil.setSeconds(startDate, 0);

	    Date endDate = new Date();
	    endDate = DateUtil.setHours(endDate, 20);
	    endDate = DateUtil.setMinute(endDate, 0);
	    endDate = DateUtil.setSeconds(endDate, 0);
	    
	    DBObject queryObject = new BasicDBObject();
	    
	    DBObject orderObject = new BasicDBObject("_id", 1);
	    
	    
	    List<ObjectId> listOfIds = new ArrayList<>();
	    
	    int offset = 0;
	    int len = size;
	    
	    //one entry should be present in this.
	    DBObject queryForDeletId = new BasicDBObject();
	    //queryForDeletId.put("coll", coll);
	    List<BasicDBObject> deletedIds = oldMongoDao.getDBObjectsByDbName("deletedIdsFrom"+coll, queryForDeletId, null, 0,1,new BasicDBObject("_id", -1),db);
	    
	    BasicDBObject newObject = new BasicDBObject(deletedIds.get(0));
	    newObject.remove("_id");
	    newObject.put("cnt", 0);
	    newObject.put("cAt", new Date());
	    
	    oldMongoDao.addRow("deletedIdsFrom"+coll, newObject);
	    
	    deletedIds = oldMongoDao.getDBObjectsByDbName("deletedIdsFrom"+coll, queryForDeletId, null, 0,1,new BasicDBObject("_id", -1),db);
	    int count = deletedIds.get(0).getInt("cnt");
	    DBObject queryOnId = new BasicDBObject();
	    
	    //only provide startId at once we are starting the batch.
	    if(deletedIds!=null && deletedIds.size()>0){
	   	  queryOnId.put("$gte", deletedIds.get(0).get("deletedId"));
	    }	    
	    queryObject.put("_id", queryOnId);
	    DBObject projectObj = new BasicDBObject("_id", 1).append("EnquiryID", 1);
        DBObject query = new BasicDBObject();
	    List<BasicDBObject> dbObjectsList = oldMongoDao.getDBObjectsByDbNameObj(coll, queryObject, projectObj, offset, len, orderObject,db);
	    while (dbObjectsList != null && dbObjectsList.size() > 0 && !StaticMaster.isDeleteCarLoggerV1Stop()) {
	      long startTime = System.currentTimeMillis();
	      logger.info("while entry : {}", new Date());
	      Date todayDate = new Date();
	      startDate = DateUtil.setHours(todayDate, 8);
	      startDate = DateUtil.setMinute(startDate, 30);
	      startDate = DateUtil.setSeconds(startDate, 0);
	      
	      endDate = DateUtil.setHours(todayDate, 20);
	      endDate = DateUtil.setMinute(endDate, 0);
	      endDate = DateUtil.setSeconds(endDate, 0);
	      logger.info("Today date in service: {}, start date {} , end date {}", todayDate, startDate, endDate);
	      if (todayDate.before(startDate) || todayDate.after(endDate) || !timeCheck) {
	        logger.info("check data : {}", new Date());
	        
	        // threads
	        
	    	int threadLimit = 5;
			ExecutorService exec = Executors.newFixedThreadPool(threadLimit);
			int startSize = 0;
			int threadRecordSize = 100;
			List<Future<?>> futures = new ArrayList<>();
			
		
			int length = dbObjectsList.size();
			while (startSize < length && dbObjectsList.size()>0) {
				int endSize = startSize + threadRecordSize;
				if (endSize > length) {
					endSize = length;
				}

				futures.add(exec.submit(new cleanReqResLogs(dbObjectsList.subList(startSize, endSize),oldMongoDao,db,coll)));
				startSize += threadRecordSize;
			}
	        
	        /*
	        for (BasicDBObject res : dbObjectsList) {
	          query.put("EnquiryId", "EnquiryID");  
	          Set<String> queryKeys =  query.keySet();
	          for(String key : queryKeys) {
	            query.put(key, res.get(query.get(key)));
	          }
	          BasicDBObject result = oldMongoDao.getDBObjectByDbName("checkReqResLog", query,db);
	          if(result==null|| result.isEmpty()){
	           listOfIds.add(res.getObjectId("_id"));
	          }
	        }
	        logger.info("delete data : {} sec", (startTime - System.currentTimeMillis())/1000 );
	        
	        if(listOfIds!=null && listOfIds.size()>0){
	          DBObject queryForDeleteIds = new BasicDBObject();
	          queryForDeleteIds.put("_id", new BasicDBObject().append("$in", listOfIds));
	          oldMongoDao.deleteRow(coll, queryForDeleteIds, true,db);
	        }
	        
	       */
	        
	        
	        logger.info("delete data : {} sec", (startTime - System.currentTimeMillis())/1000 );
	        logger.info(" Last Id deleted : {}", dbObjectsList.get(dbObjectsList.size()-1).getObjectId("_id"));
	        
	        if (deletedIds != null && deletedIds.size() > 0 && deletedIds.get(0).getObjectId("_id") != null) {
	          count += listOfIds.size();
	          DBObject setquery = new BasicDBObject("deletedId",dbObjectsList.get(dbObjectsList.size() - 1).getObjectId("_id")).append("cnt", count);
	          DBObject queryJson = new BasicDBObject("_id", deletedIds.get(0).getObjectId("_id"));
	          DBObject rowJson = new BasicDBObject("$set", setquery);
	          oldMongoDao.updateRow("deletedIdsFrom"+coll, queryJson, rowJson, false, false,db);
	        }
	        
	        logger.info("record update: {} sec", (startTime - System.currentTimeMillis())/1000 );
	        listOfIds.clear();
	        
	        queryOnId.put("$gte", dbObjectsList.get(dbObjectsList.size()-1).getObjectId("_id"));
	        
	        queryObject.put("_id", queryOnId);
	        dbObjectsList = oldMongoDao.getDBObjectsByDbNameObj(coll, queryObject, projectObj, offset, len, orderObject,db);
	        
	        logger.info("new data : {} sec", (startTime - System.currentTimeMillis())/1000 );
	        
	      }else {
	        dbObjectsList = null;
	      }
	    }
	    
	  }

	@Override
	public void moveTravelApiLogs(String coll, Integer size, Boolean timeCheck, String db, int days) throws Exception {

		logger.info("Inside moveTravelApiLogs");

		Date startDate = new Date();
		startDate = DateUtil.setHours(startDate, 7);
		startDate = DateUtil.setMinute(startDate, 0);
		startDate = DateUtil.setSeconds(startDate, 0);

		Date endDate = new Date();
		endDate = DateUtil.setHours(endDate, 20);
		endDate = DateUtil.setMinute(endDate, 0);
		endDate = DateUtil.setSeconds(endDate, 0);

		DBObject queryObject = new BasicDBObject();

		DBObject orderObject = new BasicDBObject("_id", 1);
		List<Object> listOfIds = null;
		int offset = 0;
		int len = size;
		DBObject queryForDeletId = new BasicDBObject();
		queryForDeletId.put("coll", "apiLog");
		BasicDBObject travelDeletedId = loggerMongoDao.getDBObjectByDbName("movedIdsLogger", queryForDeletId, db);
		DBObject queryOnId = new BasicDBObject();

		//only provide startId at once we are starting the batch.
		if(travelDeletedId !=null && travelDeletedId.size()>0){
			queryOnId.put("$gte", travelDeletedId.get("movedId"));
		}

		queryOnId.put("$lt", new ObjectId(DateUtil.getStringToDate(DateUtil.getFormatedDate(days, DateUtil.DATE_FORMAT_EE_MMM_dd_hh_mm_ss_z_yyyy),DateUtil.DATE_FORMAT_EE_MMM_dd_hh_mm_ss_z_yyyy)));
		//queryOnId.put("$lt", travelDeletedId.get("endId"));
		queryObject.put("_id", queryOnId);
		List<DBObject> dbObjectsList = loggerMongoDao.getDBObjects(coll, queryObject, null, offset, len, orderObject,db);
		logger.info("StaticMaster.isMoveTravelLoggerDataStop() {}", StaticMaster.isMoveTravelLoggerDataStop());
		while (dbObjectsList != null && dbObjectsList.size() > 0 && !StaticMaster.isMoveTravelLoggerDataStop()) {
			Date todayDate = new Date();
			startDate = DateUtil.setHours(todayDate, 7);
			startDate = DateUtil.setMinute(startDate, 0);
			startDate = DateUtil.setSeconds(startDate, 0);

			endDate = DateUtil.setHours(todayDate, 20);
			endDate = DateUtil.setMinute(endDate, 0);
			endDate = DateUtil.setSeconds(endDate, 0);
			logger.info("Today date in service: {}, start date {} , end date {}", todayDate, startDate, endDate);
			if (todayDate.before(startDate) || todayDate.after(endDate) || !timeCheck) {
				List<DBObject> documents = new ArrayList<>();
				Date idDate = null;
				listOfIds = new ArrayList<>();
				for (DBObject res : dbObjectsList) {
					//logger.info("Id found , {} ", res.getObjectId("_id"));
					idDate = ((ObjectId) res.get("_id")).getDate();
					// if (get this date is older than 180 days) // delete from existing
					Calendar calendar = Calendar.getInstance();
					calendar.setTime(new Date());
					// Subtract the specified number of days
					calendar.add(Calendar.DAY_OF_MONTH, -90);
					// Get the date 'daysBefore' days before the current date
					Date dateBefore = calendar.getTime();
					listOfIds.add(res.get("_id"));
					boolean haveToMove = matchesCriteria(res);
					if (haveToMove) {
						// insert in new collection
						documents.add(res);
					}
				}
				try {
					if(!CollectionUtils.isEmpty(documents)) {
						newArchMongoDao.addRows(coll+"_New", documents, db);
					}
					DBObject queryFormovedIds = new BasicDBObject();
					queryFormovedIds.put("_id", new BasicDBObject().append("$in", listOfIds));
//					delete from old collection
					loggerMongoDao.deleteRow(coll, queryFormovedIds, true,db);
					Thread.sleep(StaticMaster.getWaitFormoveTravelLoggerData()*1000);
				}catch(Exception e) {
					logger.error("Error while adding in moveTravelApiLogs , {}", e.getMessage());
					if(!e.getMessage().contains("timeout")&&!e.getMessage().contains("duplicate")) {
						throw e;
					}
				}
				logger.info(" Last Id deleted : {}", dbObjectsList.get(dbObjectsList.size()-1).get("_id"));
				DBObject queryJson;
				if(travelDeletedId !=null && travelDeletedId.size()> 0 && travelDeletedId.getObjectId("_id")!=null) {
					DBObject setquery = new BasicDBObject("movedId", dbObjectsList.get(dbObjectsList.size()-1).get("_id"));
					queryJson = new BasicDBObject("_id", travelDeletedId.getObjectId("_id"));
					DBObject rowJson = new BasicDBObject("$set", setquery);
					loggerMongoDao.updateRow("movedIdsLogger", queryJson, rowJson, false,false,db);
				}
				queryOnId.put("$gt", dbObjectsList.get(dbObjectsList.size()-1).get("_id"));
				queryObject.put("_id", queryOnId);
				dbObjectsList = loggerMongoDao.getDBObjects(coll, queryObject, null, offset, len, orderObject,db);

			}else {
				dbObjectsList = null;
			}
		}


	}

	/*
	 Please retain the logs data of these queries permanently
	1) {LogName:"CoreAPILog",LogType:{$nin:[2,3]},"MethodName":{ $nin : ["ValidateToken","SaveSeoDataV2","SavePreQuoteV3","SaveQuoteV2","ModifyProposalDataV1","PayfirstProposalBL->ModifyProposalDataV1","PayfirstProposalBL->ModifyProposalDataV1PostPayment","GetPaymentData","PushLeadProductDetails"]}}
	2) {LogName:"ProposalLogNew"}
	 */

	private static boolean matchesCriteria(DBObject document) {
		String logName = (String) document.get("LogName");
		Integer logType = (Integer) document.get("LogType");
		String methodName = (String) document.get("MethodName");
		// Check criteria
		if ("ProposalLogNew".equals(logName)) {
			return true;
		}
		if (!"CoreAPILog".equals(logName)) {
			return false;
		}
		if (logType == 2 || logType == 3) {
			return false;
		}
		if ("ValidateToken".equals(methodName) ||
				"SaveSeoDataV2".equals(methodName) ||
				"SavePreQuoteV3".equals(methodName) ||
				"SaveQuoteV2".equals(methodName) ||
				"ModifyProposalDataV1".equals(methodName) ||
				"PayfirstProposalBL->ModifyProposalDataV1".equals(methodName) ||
				"PayfirstProposalBL->ModifyProposalDataV1PostPayment".equals(methodName) ||
				"GetPaymentData".equals(methodName) ||
				"PushLeadProductDetails".equals(methodName)) {
			return false;
		}

		return true;
	}

	@Override
	public void removeCommV2FilesV2Logs(String coll, Integer size, Boolean timeCheck, String db, int days) throws Exception {

		logger.info("Inside removeCommV2FilesV2Logs");

		Date startDate = new Date();
		startDate = DateUtil.setHours(startDate, 7);
		startDate = DateUtil.setMinute(startDate, 0);
		startDate = DateUtil.setSeconds(startDate, 0);

		Date endDate = new Date();
		endDate = DateUtil.setHours(endDate, 20);
		endDate = DateUtil.setMinute(endDate, 0);
		endDate = DateUtil.setSeconds(endDate, 0);

		DBObject queryObject = new BasicDBObject();

		DBObject orderObject = new BasicDBObject("_id", 1);
		BasicDBObject projection = new BasicDBObject("_id", 1).append("FileName", 1);
		projection.append("FileName", 1);
		List<Object> listOfIds = null;
		int offset = 0;
		int len = size;
		DBObject queryForDeletId = new BasicDBObject();
		queryForDeletId.put("coll", "CommV2FileLogs");
		BasicDBObject commDeletedId = commLoggerMongoDao.getDBObjectByDbName("movedIdsLogger", queryForDeletId, db);
		DBObject queryOnId = new BasicDBObject();

		//only provide startId at once we are starting the batch.
		if(commDeletedId !=null && commDeletedId.size()>0){
			queryOnId.put("$gt", commDeletedId.get("movedId"));
		}

		queryOnId.put("$lt", new ObjectId(DateUtil.getStringToDate(DateUtil.getFormatedDate(days, DateUtil.DATE_FORMAT_EE_MMM_dd_hh_mm_ss_z_yyyy),DateUtil.DATE_FORMAT_EE_MMM_dd_hh_mm_ss_z_yyyy)));
		//queryOnId.put("$lt", commDeletedId.get("endId"));
		queryObject.put("_id", queryOnId);
		List<DBObject> dbObjectsList = commLoggerMongoDao.getDBObjects(coll, queryObject, projection, offset, len, orderObject,db);
		logger.info("StaticMaster.isMoveCommV2FileLogsLoggerDataStop() {}", StaticMaster.isMoveCommV2FileLogsLoggerDataStop());
		while (dbObjectsList != null && dbObjectsList.size() > 0 && !StaticMaster.isMoveCommV2FileLogsLoggerDataStop()) {
			Date todayDate = new Date();
			startDate = DateUtil.setHours(todayDate, 7);
			startDate = DateUtil.setMinute(startDate, 0);
			startDate = DateUtil.setSeconds(startDate, 0);

			endDate = DateUtil.setHours(todayDate, 20);
			endDate = DateUtil.setMinute(endDate, 0);
			endDate = DateUtil.setSeconds(endDate, 0);
			logger.info("Today date in service: {}, start date {} , end date {}", todayDate, startDate, endDate);
			if (todayDate.before(startDate) || todayDate.after(endDate) || !timeCheck) {
				List<DBObject> documents = new ArrayList<>();
				Date idDate = null;
				listOfIds = new ArrayList<>();
				for (DBObject res : dbObjectsList) {
					String fileName = (String) res.get("FileName");
					if(fileName != null && fileName.contains("MailBody.html")) {
						documents.add(res);
						listOfIds.add(res.get("_id"));
					}
				}
				try {
//					if(!CollectionUtils.isEmpty(documents)) {
//						commPrimaryArchivalMongoDao.addRows(coll+"_New", documents, db);
//					}
					DBObject queryFormovedIds = new BasicDBObject();
					queryFormovedIds.put("_id", new BasicDBObject().append("$in", listOfIds));
//					delete from old collection
					commLoggerMongoDao.deleteRow(coll, queryFormovedIds, true,db);
					Thread.sleep(StaticMaster.getWaitCommV2FileLogsData()*1000);
				}catch(Exception e) {
					logger.error("Error while adding in removeCommV2FilesV2Logs , {}", e.getMessage());
					if(!e.getMessage().contains("timeout")&&!e.getMessage().contains("duplicate")) {
						throw e;
					}
				}
				logger.info(" Last Id deleted : {}", dbObjectsList.get(dbObjectsList.size()-1).get("_id"));
				DBObject queryJson;
				if(commDeletedId !=null && commDeletedId.size()> 0 && commDeletedId.getObjectId("_id")!=null) {
					DBObject setquery = new BasicDBObject("movedId", dbObjectsList.get(dbObjectsList.size()-1).get("_id"));
					queryJson = new BasicDBObject("_id", commDeletedId.getObjectId("_id"));
					DBObject rowJson = new BasicDBObject("$set", setquery);
					commLoggerMongoDao.updateRow("movedIdsLogger", queryJson, rowJson, false,false,db);
				}
				queryOnId.put("$gt", dbObjectsList.get(dbObjectsList.size()-1).get("_id"));
				queryObject.put("_id", queryOnId);
				dbObjectsList = commLoggerMongoDao.getDBObjects(coll, queryObject, projection, offset, len, orderObject,db);

			}else {
				dbObjectsList = null;
			}
		}


	}

	@Override
	public void movePBCromaV2ToS3(String coll, Integer size, Boolean timeCheck, String db, int days) throws Exception {

		logger.info("Inside PBCromaV2");

		Date startDate = new Date();
		startDate = DateUtil.setHours(startDate, 7);
		startDate = DateUtil.setMinute(startDate, 0);
		startDate = DateUtil.setSeconds(startDate, 0);

		Date endDate = new Date();
		endDate = DateUtil.setHours(endDate, 20);
		endDate = DateUtil.setMinute(endDate, 0);
		endDate = DateUtil.setSeconds(endDate, 0);

		DBObject queryObject = new BasicDBObject();

		DBObject orderObject = new BasicDBObject("_id", 1);
		List<Object> listOfIds = null;
		int offset = 0;
		int len = size;
		DBObject queryForDeletId = new BasicDBObject();
		queryForDeletId.put("coll", "Car_Collection");
		BasicDBObject commDeletedId = loggerMongoDao.getDBObjectByDbName("movedIdsLogger", queryForDeletId, db);
		DBObject queryOnId = new BasicDBObject();

		//only provide startId at once we are starting the batch.
		if(commDeletedId !=null && commDeletedId.size()>0){
			queryOnId.put("$gt", commDeletedId.get("movedId"));
		}

		queryOnId.put("$lt", new ObjectId(DateUtil.getStringToDate(DateUtil.getFormatedDate(days, DateUtil.DATE_FORMAT_EE_MMM_dd_hh_mm_ss_z_yyyy),DateUtil.DATE_FORMAT_EE_MMM_dd_hh_mm_ss_z_yyyy)));
		//queryOnId.put("$lt", commDeletedId.get("endId"));
		queryObject.put("_id", queryOnId);
		List<DBObject> dbObjectsList = loggerMongoDao.getDBObjects(coll, queryObject, null, offset, len, orderObject,db);
		logger.info("StaticMaster.isMoveCommV2FileLogsLoggerDataStop() {}", StaticMaster.isMoveCommV2FileLogsLoggerDataStop());
		while (dbObjectsList != null && dbObjectsList.size() > 0 && !StaticMaster.isMoveCommV2FileLogsLoggerDataStop()) {
			Date todayDate = new Date();
			startDate = DateUtil.setHours(todayDate, 7);
			startDate = DateUtil.setMinute(startDate, 0);
			startDate = DateUtil.setSeconds(startDate, 0);

			endDate = DateUtil.setHours(todayDate, 20);
			endDate = DateUtil.setMinute(endDate, 0);
			endDate = DateUtil.setSeconds(endDate, 0);
			logger.info("Today date in service: {}, start date {} , end date {}", todayDate, startDate, endDate);
			if (todayDate.before(startDate) || todayDate.after(endDate) || !timeCheck) {
				List<DBObject> documents = new ArrayList<>();
				Date idDate = null;
				listOfIds = new ArrayList<>();
				for (DBObject jsonDocument : dbObjectsList) {
					String trackingID = (String) jsonDocument.get("TrackingID");
					// Compress JSON data
					byte[] compressedData = compressData(jsonDocument.toString());
//					String deCompressedData = decompressData(compressedData);
					String fileName = trackingID+"_"+"json.gz";
					s3service.addByteContentWithGzip(compressedData, "qapolicycopy", "mg/", fileName, "application/gzip");
				}
				try {
					DBObject queryFormovedIds = new BasicDBObject();
					queryFormovedIds.put("_id", new BasicDBObject().append("$in", listOfIds));
//					delete from old collection
					loggerMongoDao.deleteRow(coll, queryFormovedIds, true,db);
					Thread.sleep(StaticMaster.getWaitCommV2FileLogsData()*1000);
				}catch(Exception e) {
					logger.error("Error while adding in removeCommV2FilesV2Logs , {}", e.getMessage());
					if(!e.getMessage().contains("timeout")&&!e.getMessage().contains("duplicate")) {
						throw e;
					}
				}
				logger.info(" Last Id deleted : {}", dbObjectsList.get(dbObjectsList.size()-1).get("_id"));
				DBObject queryJson;
				if(commDeletedId !=null && commDeletedId.size()> 0 && commDeletedId.getObjectId("_id")!=null) {
					DBObject setquery = new BasicDBObject("movedId", dbObjectsList.get(dbObjectsList.size()-1).get("_id"));
					queryJson = new BasicDBObject("_id", commDeletedId.getObjectId("_id"));
					DBObject rowJson = new BasicDBObject("$set", setquery);
					loggerMongoDao.updateRow("movedIdsLogger", queryJson, rowJson, false,false,db);
				}
				queryOnId.put("$gt", dbObjectsList.get(dbObjectsList.size()-1).get("_id"));
				queryObject.put("_id", queryOnId);
				dbObjectsList = loggerMongoDao.getDBObjects(coll, queryObject, null, offset, len, orderObject,db);

			}else {
				dbObjectsList = null;
			}
		}
	}

	@Override
	public List<Map<String, Object>> getLogFromS3(String folderPath, String fileName) throws IOException {
		List<Map<String, Object>> resp = null;
		try{
			resp = s3service.getByteContentWithGzip("pbmongoarchive", folderPath, fileName);
		}catch (Exception e){
			logger.error("Exception caught in getCommV2FileLogsFromS3 , msg: {}", e.getMessage());
		}
		return resp;
	}
	private List<DBObject> getList(String coll, DBObject queryOnrequestId, int offset, DBObject orderObject, String db, List<DBObject> resultList, int chunkSize) {

		try {
			List<DBObject> dataList = commLoggerMongoDao.getDBObjects(coll, queryOnrequestId, offset, chunkSize, orderObject, db);
			while (dataList.size() > 0){
				resultList.addAll(dataList);
				offset += chunkSize;
				dataList = commLoggerMongoDao.getDBObjects(coll, queryOnrequestId, offset, chunkSize, orderObject, db);
			}
		} catch (Exception e) {
			logger.error("Exception while getList");
		}
		return resultList;
	}

	// Method to compress data using GZIP
	private static byte[] compressData(String data) {
		ByteArrayOutputStream outputStream = null;
		GZIPOutputStream gzipOutputStream = null;
		try {
			outputStream = new ByteArrayOutputStream();
			gzipOutputStream = new GZIPOutputStream(outputStream);
			gzipOutputStream.write(data.getBytes());
			gzipOutputStream.close();
			return outputStream.toByteArray();
		} catch (Exception e) {
			e.printStackTrace();
			return null;
		} finally {
			if(outputStream != null) {
				try {
					outputStream.close();
				} catch (IOException e) {
				}
			}
			if(gzipOutputStream != null) {
				try {
					gzipOutputStream.close();
				} catch (IOException e) {
				}
			}
		}
	}
	private static String decompressData(byte[] compressedData) {
		try {
			// Create a GZIP input stream to decompress the data
			ByteArrayInputStream inputStream = new ByteArrayInputStream(compressedData);
			GZIPInputStream gzipInputStream = new GZIPInputStream(inputStream);

			// Read decompressed data into a byte array output stream
			ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
			byte[] buffer = new byte[1024];
			int bytesRead;
			while ((bytesRead = gzipInputStream.read(buffer)) != -1) {
				outputStream.write(buffer, 0, bytesRead);
			}

			// Close streams
			gzipInputStream.close();
			outputStream.close();

			// Convert decompressed byte array to string
			return outputStream.toString("UTF-8");
		} catch (IOException e) {
			e.printStackTrace();
			return null;
		}
	}
}



class cleanReqResLogs implements Runnable {

	Logger logger = LoggerFactory.getLogger(cleanReqResLogs.class);
	private List<BasicDBObject> basicDBObjects;
	private ChatMongoDao oldMongoDao;
	private String db ;
	private String coll;
	public cleanReqResLogs() {
	}

	public cleanReqResLogs(  List<BasicDBObject> basicDBObjects,ChatMongoDao oldMongoDao,String db,String coll) {
		this.basicDBObjects = basicDBObjects;
		this.oldMongoDao = oldMongoDao;
		this.db = db;
		this.coll= coll;
	}

	@Override
	public void run() {
		if ((this.basicDBObjects != null) && !this.basicDBObjects.isEmpty()) {
			logger.info("{} - {} started", this.basicDBObjects.get(0),this.basicDBObjects.get(this.basicDBObjects.size() - 1));
			List<ObjectId> listOfIds = new ArrayList<>();
			DBObject query = new BasicDBObject();
			try {
				 for (BasicDBObject res : basicDBObjects) {
			          query.put("EnquiryId", "EnquiryID");  
			          Set<String> queryKeys =  query.keySet();
			          for(String key : queryKeys) {
			            query.put(key, res.get(query.get(key)));
			          }
			          BasicDBObject result = oldMongoDao.getDBObjectByDbName("checkReqResLog", query,db);
			          if(result==null|| result.isEmpty()){
			           listOfIds.add(res.getObjectId("_id"));
			          }
			     }
				 
				 if(listOfIds!=null && listOfIds.size()>0){
			          DBObject queryForDeleteIds = new BasicDBObject();
			          queryForDeleteIds.put("_id", new BasicDBObject().append("$in", listOfIds));
			          oldMongoDao.deleteRow(coll, queryForDeleteIds, true,db);
			      }   
				 
				 this.logger.info("{} - {} ended", this.basicDBObjects.get(0),this.basicDBObjects.get(this.basicDBObjects.size() - 1));
			} catch (Exception e) {
				logger.error("Error while run in cleanReqResLogs: {}", e.getMessage());
			}finally {
				basicDBObjects = null;
			}
		} else {
			basicDBObjects = null;
			this.logger.info("Empty list for thread {}", Thread.currentThread().getName());
		}

	}
}

