package com.docMigration.service.impl;

import com.docMigration.dao.ChatMongoDao;
import com.docMigration.service.S3BucketService;
import com.docMigration.util.*;
import com.mongodb.*;
import com.mongodb.util.JSON;
import org.apache.commons.io.output.ByteArrayOutputStream;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;
import java.util.zip.GZIPOutputStream;

@Service
public class BmsLoggerServiceImplNew {
    private final Logger logger = LoggerFactory.getLogger(BmsLoggerServiceImplNew.class);
    @Autowired
    @Qualifier("newArchivalMongoDao")
    ChatMongoDao newArchMongoDao;

    @Autowired
    private S3BucketService s3service;

    @Autowired
    @Qualifier("commLoggerNewMongoDao")
    ChatMongoDao commLoggerNewMongoDao;

    public static volatile boolean isMoveDocPushLogsJobStop = false;
    public static volatile boolean isDeleteOldCommV2LogsJobStop = false;
    public static volatile boolean isMoveIntegrationExternalAPILogsJobStop = false;
    public static volatile boolean isMovePIVCLogsJobStop = false;

    public void moveNewDocPushLogsToS3V1(String dbColl, Integer size, int days, int chunkSize, String movedIdsCollection) {

        logger.info("Inside moveDocPushLogsToS3");
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        try {
            String[] nsArray = dbColl.split("\\.");
            String db = nsArray[0];
            String coll = nsArray[1];
            int len = size;
            long totalMigrated = 0;

            List<String> excludedMethods = Arrays.asList(
                    "DocService_GetttlUrl_Manual",
                    "BMSDocStatusStamping",
                    "BMSeventTracking",
                    "GetTTLURL",
                    "MergeRequestDocs",
                    "PushDocumentsToInspection",
                    "DocToTextNew",
                    "GetCustomerDetails",
                    "GetPIVCDetails",
                    "GetAnalyserOCRAData",
                    "GetCorePolicyVehicleDetails",
                    "UpdateQCDetails",
                    "PushAgentUpdateEvents",
                    "BMSbookingUpdate",
                    "GetInsuredMemberDetails",
                    "PushWAEventTicket",
                    "UpdateBookingE2E",
                    "System UW Update",
                    "PIVCAPI",
                    "GetPolicyStatusByLeadID",
                    "HealthDocPush",
                    "NEWStatementAnalyse"
            );

            DBObject queryForDeletId = new BasicDBObject();
            queryForDeletId.put("coll", dbColl);
            BasicDBObject commDeletedId = newArchMongoDao.getDBObjectByDbName(movedIdsCollection, queryForDeletId, NStoS3util.lastUpdatedIdDB);
            DBObject queryOnId = new BasicDBObject();
            //only provide startId at once we are starting the batch.
            if (commDeletedId != null && commDeletedId.size() > 0) {
                queryOnId.put("$gte", commDeletedId.get("movedId"));
                totalMigrated = (long) commDeletedId.get("totalMigrated");
            }
            queryOnId.put("$lt", new ObjectId(DateUtil.getStringToDate(DateUtil.getFormatedDate(days, DateUtil.DATE_FORMAT_EE_MMM_dd_hh_mm_ss_z_yyyy), DateUtil.DATE_FORMAT_EE_MMM_dd_hh_mm_ss_z_yyyy)));
            DBObject queryObject = new BasicDBObject();
            queryObject.put("_id", queryOnId);

            queryObject.put("MethodName", new BasicDBObject("$nin", excludedMethods));

            DBObject orderObject = new BasicDBObject("_id", 1);
            // get list from mongoDB
            List<DBObject> dbObjectsList = commLoggerNewMongoDao.getDBObjects(coll, queryObject, null, 0, len, orderObject, db);

            // if list have data then proceed
            while (dbObjectsList != null && dbObjectsList.size() > 0 && !isMoveDocPushLogsJobStop) {
                long start = System.currentTimeMillis();
                List<Object> listOfIds = new ArrayList<>();
                List<DBObject> addFileNameObjList = new ArrayList<>();
                for (DBObject jsonDocument : dbObjectsList) {
                    try {
                        listOfIds.add(jsonDocument.get("_id"));
                        String jsonString = JSON.serialize(jsonDocument);
                        // Compress JSON data
                        byte[] compressedData = compressData(jsonString);
                        //String deCompressedData = decompressData(compressedData);
                        ObjectId docObjId = (ObjectId) jsonDocument.get("_id");
                        Integer leadId = (Integer) jsonDocument.get("Leadid");
                        String createdOn = null;
                        if( jsonDocument.get("CreatedOn") instanceof Date){
                            createdOn = formatter.format(jsonDocument.get("CreatedOn"));
                        }
                        else createdOn = (String) jsonDocument.get("CreatedOn");
                        String methodName = (String) jsonDocument.get("MethodName");
                        String fileName = docObjId + "_json.gz";
                        // check This fileName exist or Not
                        DBObject docPushMetaFileObject = new BasicDBObject().append("_id", docObjId).append("Leadid", leadId).append("MethodName", methodName).append("fileName", fileName).append("CreatedOn", createdOn);
                        // add in docPushLogS3FilenameLogs collection for url map
                        addFileNameObjList.add(docPushMetaFileObject);
                        logger.info("BMS DocPushLog: " + movedIdsCollection + " " + leadId + " " + fileName);
                        String folderName = NStoS3util.MAP_NS_TO_S3_PATH.get("DocsPoint.DocPushLog");
                        String url = s3service.addByteContentWithGzip(compressedData, NStoS3util.BUCKET_NAME_LOGGER_ARCHIVAL, folderName, fileName, "application/gzip");
                        if (url.equals(AppConstants.FILE_ALREADY_EXIST)) {
                            try {
                                commLoggerNewMongoDao.addRow("DocPushLog_failOver", jsonDocument, db);
                            } catch (Exception e) {
                                logger.error("Error while adding in moveDocPushLogsToS3 , {}", e.getMessage());
                            }
                        }
                    } catch (Exception e) {
                        System.err.println("Error serializing DBObject: " + e.getMessage());
                        return;
                    }
                }
                // add FileName Object list in docPushLogS3FilenameLog
                if(!CollectionUtils.isEmpty(addFileNameObjList)){
                    try {
                        commLoggerNewMongoDao.addRows("DocPushLog_S3Archive", addFileNameObjList, "DocsPoint");
                    }catch (Exception e){
                        logger.error("Error while adding in DocPushLog_S3Archive , {}", e.getMessage());
                    }
                    addFileNameObjList = null;
                }

                // to delete from source by list of _id
                try {
                    List<Object> subList = new ArrayList<>();
                    int startIndx = 0;
                    int lastIndx = 100;
                    if (listOfIds.size() > 100) {
                        while (startIndx < listOfIds.size()) {
                            if (startIndx + 100 >= listOfIds.size()) {
                                lastIndx = listOfIds.size();
                            }
                            subList = listOfIds.subList(startIndx, lastIndx);
                            DBObject queryFormovedIds = new BasicDBObject();
                            queryFormovedIds.put("_id", new BasicDBObject().append("$in", subList));
                            commLoggerNewMongoDao.deleteRow(coll, queryFormovedIds, true, db);
                            logger.info("BMS DocPushLog Last Id deleted : {}", subList.get(subList.size() - 1));
                            startIndx = lastIndx;
                            lastIndx = lastIndx + 100;
                            subList = null;
                        }
                    } else if (listOfIds != null && listOfIds.size() > 0) {
                        DBObject queryFormovedIds = new BasicDBObject();
                        queryFormovedIds.put("_id", new BasicDBObject().append("$in", listOfIds));
                        commLoggerNewMongoDao.deleteRow(coll, queryFormovedIds, true, db);
                        logger.info("BMS DocPushLog  Last Id deleted : {}", listOfIds.get(listOfIds.size() - 1));
                    }

                } catch (Exception e) {
                    logger.error("Error while adding in moveDocPushLogsToS3 , {}", e.getMessage());
                    if (!e.getMessage().contains("timeout") && !e.getMessage().contains("duplicate")) {
                        throw e;
                    }
                }
                listOfIds = null;
                // updating last _id in metaData master mongoDB collection: movedIdsLogger
                DBObject queryJson;
                if (commDeletedId != null && commDeletedId.size() > 0 && commDeletedId.getObjectId("_id") != null) {
                    totalMigrated = totalMigrated + dbObjectsList.size();
                    DBObject setquery = new BasicDBObject("movedId", dbObjectsList.get(dbObjectsList.size() - 1).get("_id"))
                            .append("totalMigrated", totalMigrated)
                            .append("updatedOn", new Date());
                    queryJson = new BasicDBObject("_id", commDeletedId.getObjectId("_id"));
                    DBObject rowJson = new BasicDBObject("$set", setquery);
                    newArchMongoDao.updateRow(movedIdsCollection, queryJson, rowJson, false, false, NStoS3util.lastUpdatedIdDB);
                }
                queryOnId.put("$gt", dbObjectsList.get(dbObjectsList.size() - 1).get("_id"));
                queryObject.put("_id", queryOnId);
                queryObject.put("MethodName", new BasicDBObject("$nin", excludedMethods));

                dbObjectsList = null; // explicitly nullify
                dbObjectsList = commLoggerNewMongoDao.getDBObjects(coll, queryObject, null, 0, len, orderObject, db);

                long end = System.currentTimeMillis();
                long diff = end - start;
                logger.info("Time for {} records in docPushLogs: {} ms", len, diff);
            }

            if (isMoveDocPushLogsJobStop) {
                logger.warn("Deletion stopped by stop request.");
            } else {
                logger.info("Job completed successfully.");
            }

        } catch (Exception e) {
            logger.error("Exception caught in moveDocPushLogsToS3, ns:{} , msg: {}", e.getMessage());
        }
    }

    public void deleteOldCommV2FileLogs(String dbColl, int days, int batchSize) {
        logger.info("Starting deletion of CommV2FileLogs older than {} days", days);

        try {
            String[] nsArray = dbColl.split("\\.");
            String db = nsArray[0];
            String coll = nsArray[1];

            // Create ObjectId based on date threshold
            ObjectId cutoffId = new ObjectId(DateUtil.getStringToDate(
                    DateUtil.getFormatedDate(-days, DateUtil.DATE_FORMAT_EE_MMM_dd_hh_mm_ss_z_yyyy),
                    DateUtil.DATE_FORMAT_EE_MMM_dd_hh_mm_ss_z_yyyy));

            DBObject queryObject = new BasicDBObject("_id", new BasicDBObject("$lt", cutoffId));
            BasicDBObject projection = new BasicDBObject("_id", 1);
            DBObject sortObject = new BasicDBObject("_id", 1);

            List<DBObject> toDeleteList = commLoggerNewMongoDao.getDBObjects(coll, queryObject, projection, 0, batchSize, sortObject, db);

            while (toDeleteList != null && !toDeleteList.isEmpty() && !isDeleteOldCommV2LogsJobStop) {
                
                List<Object> idsToDelete = toDeleteList.stream()
                        .map(doc -> doc.get("_id"))
                        .collect(Collectors.toList());

                // Delete in smaller chunks (e.g., 100 at a time)
                for (int i = 0; i < idsToDelete.size(); i += 100) {
                    int end = Math.min(i + 100, idsToDelete.size());
                    List<Object> subList = idsToDelete.subList(i, end);

                    DBObject deleteQuery = new BasicDBObject("_id", new BasicDBObject("$in", subList));
                    commLoggerNewMongoDao.deleteRow(coll, deleteQuery, true, db);
                    logger.info("Deleted {} documents from {}", subList.size(), coll);
                }

                // Fetch next batch
                ObjectId lastId = (ObjectId) toDeleteList.get(toDeleteList.size() - 1).get("_id");
                queryObject.put("_id", new BasicDBObject("$gt", lastId).append("$lt", cutoffId));
                toDeleteList = commLoggerNewMongoDao.getDBObjects(coll, queryObject, projection, 0, batchSize, sortObject, db);
            }

            if (isDeleteOldCommV2LogsJobStop) {
                logger.warn("Deletion stopped by stop request.");
            } else {
                logger.info("Deletion job completed successfully.");
            }

        } catch (Exception e) {
            logger.error("Exception in deleteOldCommV2FileLogs: {}", e.getMessage(), e);
        }
    }   
    
    public void moveNewIntegrationExternalAPILogsToS3(String dbColl, Integer size, int days, int chunkSize, boolean timeCheck, String movedIdsCollection) {

        logger.info("Inside moveIntegrationExternalAPILogsToS3");
        try {
            String[] nsArray = dbColl.split("\\.");
            String db = nsArray[0];
            String coll = nsArray[1];
            int len = size;
            long totalMigrated = 0;
            DBObject queryForDeletId = new BasicDBObject();
            queryForDeletId.put("coll", dbColl);
            BasicDBObject commDeletedId = newArchMongoDao.getDBObjectByDbName(movedIdsCollection, queryForDeletId, NStoS3util.lastUpdatedIdDB);
            DBObject queryOnId = new BasicDBObject();
            //only provide startId at once we are starting the batch.
            if (commDeletedId != null && commDeletedId.size() > 0) {
                queryOnId.put("$gte", commDeletedId.get("movedId"));
                totalMigrated = (long) commDeletedId.get("totalMigrated");
            }
            queryOnId.put("$lt", new ObjectId(DateUtil.getStringToDate(DateUtil.getFormatedDate(days, DateUtil.DATE_FORMAT_EE_MMM_dd_hh_mm_ss_z_yyyy), DateUtil.DATE_FORMAT_EE_MMM_dd_hh_mm_ss_z_yyyy)));
            DBObject queryObject = new BasicDBObject();
            queryObject.put("_id", queryOnId);
            BasicDBObject projection = new BasicDBObject("_id", 1).append("BookingId", 1);
            DBObject orderObject = new BasicDBObject("_id", 1);
            // get list from mongoDB
            List<DBObject> dbObjectsList = commLoggerNewMongoDao.getDBObjects(coll, queryObject, projection, 0, len, orderObject, db);

            // if list have data then proceed
            while (dbObjectsList != null && dbObjectsList.size() > 0 && !isMoveIntegrationExternalAPILogsJobStop) {

                Date todayDate = new Date();
                /* Date endDate = DateUtil.addDays(todayDate, 1); */
                Date startDate = new Date();
                startDate = DateUtil.setHours(startDate, 7);
                startDate = DateUtil.setMinute(startDate, 0);
                startDate = DateUtil.setSeconds(startDate, 0);

                Date endDate = new Date();
                endDate = DateUtil.setHours(endDate, 20);
                endDate = DateUtil.setMinute(endDate, 0);
                endDate = DateUtil.setSeconds(endDate, 0);
                logger.info("Today date in uaeJourMongoDao service: {}, start date {} , end date {}", todayDate, startDate, endDate);
                List<Object> listOfIds = new ArrayList<>();
                if (todayDate.before(startDate) || todayDate.after(endDate) || !timeCheck) {

                    HashSet<Integer> bookingIds = new HashSet<>();
                    for (DBObject jsonDocument : dbObjectsList) {
                        listOfIds.add(jsonDocument.get("_id"));
                        Integer bookingId = (Integer) jsonDocument.get("BookingId");
                        DBObject queryOnrequestId = new BasicDBObject();
                        queryOnrequestId.put("BookingId", bookingId);
                        if (bookingId != null && bookingId > 0 && !bookingIds.contains(bookingId)) {
                            bookingIds.add(bookingId);
                            int offset = 0;
                            List<DBObject> dbList = commLoggerNewMongoDao.getDBObjects(coll, queryOnrequestId, offset, chunkSize, null, db);
                            if (dbList.size() == chunkSize) {
                                offset = offset + chunkSize;
                                dbList = getList(coll, queryOnrequestId, offset, db, dbList, chunkSize);
                            }
                            String jsonString;
                            if (dbList != null && dbList.size() > 0) {
                                try {
                                    jsonString = JSON.serialize(dbList);

                                    // Compress JSON data
                                    byte[] compressedData = compressData(jsonString);
                                    //String deCompressedData = decompressData(compressedData);
                                    String fileName = bookingId + ".json.gz";
                                    logger.info("BMS Integration.ExternalAPILogs fileName : " + fileName + " " + movedIdsCollection);
                                    String folderName = NStoS3util.MAP_NS_TO_S3_PATH.get(dbColl);
                                    String url = s3service.addByteContentWithGzip(compressedData, NStoS3util.BUCKET_NAME_LOGGER_ARCHIVAL, folderName, fileName, "application/gzip");
                                    if (url.equals(AppConstants.FILE_ALREADY_EXIST)) {
                                        try {
                                            commLoggerNewMongoDao.addRows(coll + "_failOver", dbList, db);
                                        } catch (Exception e) {
                                            logger.error("Error while adding in moveRequestLogsLogsToS3 , {}", e.getMessage());
                                        }
                                    }

                                    for (DBObject reqIdDoc : dbList) {
                                        listOfIds.add(reqIdDoc.get("_id"));
                                    }
                                } catch (Exception e) {
                                    System.err.println("Error serializing DBObject: " + e.getMessage());
                                    return;
                                }
                            }
                        }
                    }
                    bookingIds = null;
                    // to delete from source by list of _id
                    try {
                        List<Object> subList = new ArrayList<>();
                        int startIndx = 0;
                        int lastIndx = 100;
                        if (listOfIds.size() > 100) {
                            while (startIndx < listOfIds.size()) {
                                if (startIndx + 100 >= listOfIds.size()) {
                                    lastIndx = listOfIds.size();
                                }
                                subList = listOfIds.subList(startIndx, lastIndx);
                                DBObject queryFormovedIds = new BasicDBObject();
                                queryFormovedIds.put("_id", new BasicDBObject().append("$in", subList));
                                commLoggerNewMongoDao.deleteRow(coll, queryFormovedIds, true, db);
                                logger.info("BMS Integration.ExternalAPILogs Last Id deleted : {}", subList.get(subList.size() - 1));
                                startIndx = lastIndx;
                                lastIndx = lastIndx + 100;
                                subList = null;
                            }
                        } else if (listOfIds != null && listOfIds.size() > 0) {
                            DBObject queryFormovedIds = new BasicDBObject();
                            queryFormovedIds.put("_id", new BasicDBObject().append("$in", listOfIds));
                            commLoggerNewMongoDao.deleteRow(coll, queryFormovedIds, true, db);
                            logger.info("BMS Integration.ExternalAPILogs  Last Id deleted : {}", listOfIds.get(listOfIds.size() - 1));
                        }

                    } catch (Exception e) {
                        logger.error("Error while adding in moveIntegrationExternalAPILogsToS3 , {}", e.getMessage());
                        if (!e.getMessage().contains("timeout") && !e.getMessage().contains("duplicate")) {
                            throw e;
                        }
                    }

                    // updating last _id in metaData master mongoDB collection: movedIdsLogger
                    DBObject queryJson;
                    if (commDeletedId != null && commDeletedId.size() > 0 && commDeletedId.getObjectId("_id") != null) {
                        totalMigrated = totalMigrated + dbObjectsList.size();
                        DBObject setquery = new BasicDBObject("movedId", dbObjectsList.get(dbObjectsList.size() - 1).get("_id"))
                                .append("totalMigrated", totalMigrated)
                                .append("updatedOn", new Date());
                        queryJson = new BasicDBObject("_id", commDeletedId.getObjectId("_id"));
                        DBObject rowJson = new BasicDBObject("$set", setquery);
                        newArchMongoDao.updateRow(movedIdsCollection, queryJson, rowJson, false, false, NStoS3util.lastUpdatedIdDB);
                    }
                    queryOnId.put("$gt", dbObjectsList.get(dbObjectsList.size() - 1).get("_id"));
                    queryObject.put("_id", queryOnId);
                    dbObjectsList = null; // explicitly nullify
                    dbObjectsList = commLoggerNewMongoDao.getDBObjects(coll, queryObject, projection, 0, len, orderObject, db);
                }else {
                    dbObjectsList = null;
                }
            }

            if (isMoveIntegrationExternalAPILogsJobStop) {
                logger.warn("Deletion stopped by stop request.");
            } else {
                logger.info("Deletion job completed successfully.");
            }

        } catch (Exception e) {
            logger.error("Exception caught in moveRequestLogsLogsToS3, ns:{} , msg: {}", e.getMessage());
        }
    }

    public void moveNewPIVCLogsToS3(String dbColl, Integer size, int days, int chunkSize, String movedIdsCollection) {

        logger.info("Inside movePIVCLogsToS3");
        try {
            String[] nsArray = dbColl.split("\\.");
            String db = nsArray[0];
            String coll = nsArray[1];
            ;
            int len = size;
            long totalMigrated = 0;
            DBObject queryForDeletId = new BasicDBObject();
            queryForDeletId.put("coll", dbColl);
            BasicDBObject commDeletedId = newArchMongoDao.getDBObjectByDbName(movedIdsCollection, queryForDeletId, NStoS3util.lastUpdatedIdDB);
            DBObject queryOnId = new BasicDBObject();
            //only provide startId at once we are starting the batch.
            if (commDeletedId != null && commDeletedId.size() > 0) {
                queryOnId.put("$gte", commDeletedId.get("movedId"));
                totalMigrated = (long) commDeletedId.get("totalMigrated");
            }
            queryOnId.put("$lt", new ObjectId(DateUtil.getStringToDate(DateUtil.getFormatedDate(days, DateUtil.DATE_FORMAT_EE_MMM_dd_hh_mm_ss_z_yyyy), DateUtil.DATE_FORMAT_EE_MMM_dd_hh_mm_ss_z_yyyy)));
            DBObject queryObject = new BasicDBObject();
            queryObject.put("_id", queryOnId);
            BasicDBObject projection = new BasicDBObject("_id", 1).append("LeadId", 1);
            DBObject orderObject = new BasicDBObject("_id", 1);
            // get list from mongoDB
            List<DBObject> dbObjectsList = commLoggerNewMongoDao.getDBObjects(coll, queryObject, projection, 0, len, orderObject, db);

            // if list have data then proceed
            while (dbObjectsList != null && dbObjectsList.size() > 0 && !isMovePIVCLogsJobStop) {
                List<Object> listOfIds = new ArrayList<>();
                HashSet<Integer> requestIdListCommV2FileLogs = new HashSet<>();

                for (DBObject jsonDocument : dbObjectsList) {
                    listOfIds.add(jsonDocument.get("_id"));
                    Integer leadId = (Integer) jsonDocument.get("LeadId");
                    DBObject queryOnrequestId = new BasicDBObject();
                    queryOnrequestId.put("LeadId", leadId);
                    if (leadId != null && leadId > 0 && !requestIdListCommV2FileLogs.contains(leadId)) {
                        requestIdListCommV2FileLogs.add(leadId);
                        int offset = 0;
                        List<DBObject> dbList = commLoggerNewMongoDao.getDBObjects(coll, queryOnrequestId, offset, chunkSize, null, db);
                        if (dbList.size() == chunkSize) {
                            offset = offset + chunkSize;
                            dbList = getList(coll, queryOnrequestId, offset, db, dbList, chunkSize);
                        }
                        String jsonString;
                        if (dbList != null && dbList.size() > 0) {
                            try {
                                jsonString = JSON.serialize(dbList);

                                // Compress JSON data
                                byte[] compressedData = compressData(jsonString);
                                //String deCompressedData = decompressData(compressedData);
                                String fileName = leadId + "_" + "json.gz";
                                logger.info("PIVCLogs fileName : " + fileName + " " + movedIdsCollection);
                                String folderName = NStoS3util.MAP_NS_TO_S3_PATH.get(dbColl);
                                String url = s3service.addByteContentWithGzip(compressedData, NStoS3util.BUCKET_NAME_LOGGER_ARCHIVAL, folderName, fileName, "application/gzip");
                                if (url.equals(AppConstants.FILE_ALREADY_EXIST)) {
                                    try {
                                        commLoggerNewMongoDao.addRows("PIVCLogs_failOver", dbList, db);
                                    } catch (Exception e) {
                                        logger.error("Error while adding in movePIVCLogsToS3 , {}", e.getMessage());
                                    }
                                }

                                for (DBObject reqIdDoc : dbList) {
                                    listOfIds.add(reqIdDoc.get("_id"));
                                }
                            } catch (Exception e) {
                                System.err.println("Error serializing DBObject: " + e.getMessage());
                                return;
                            }
                        }
                    }
                }
                requestIdListCommV2FileLogs = null;

                // to delete from source by list of _id
                try {
                    List<Object> subList = new ArrayList<>();
                    int startIndx = 0;
                    int lastIndx = 100;
                    if (listOfIds.size() > 100) {
                        while (startIndx < listOfIds.size()) {
                            if (startIndx + 100 >= listOfIds.size()) {
                                lastIndx = listOfIds.size();
                            }
                            subList = listOfIds.subList(startIndx, lastIndx);
                            DBObject queryFormovedIds = new BasicDBObject();
                            queryFormovedIds.put("_id", new BasicDBObject().append("$in", subList));
                            commLoggerNewMongoDao.deleteRow(coll, queryFormovedIds, true, db);
                            logger.info("PIVCLogs Last Id deleted : {}", subList.get(subList.size() - 1));
                            startIndx = lastIndx;
                            lastIndx = lastIndx + 100;
                            subList = null;
                        }
                    } else if (listOfIds != null && listOfIds.size() > 0) {
                        DBObject queryFormovedIds = new BasicDBObject();
                        queryFormovedIds.put("_id", new BasicDBObject().append("$in", listOfIds));
                        commLoggerNewMongoDao.deleteRow(coll, queryFormovedIds, true, db);
                        logger.info("PIVCLogs Last Id deleted : {}", listOfIds.get(listOfIds.size() - 1));
                    }

                } catch (Exception e) {
                    logger.error("Error while adding in movePIVCLogsToS3 , {}", e.getMessage());
                    if (!e.getMessage().contains("timeout") && !e.getMessage().contains("duplicate")) {
                        throw e;
                    }
                }

                // updating last _id in metaData master mongoDB collection: movedIdsLogger
                DBObject queryJson;
                if (commDeletedId != null && commDeletedId.size() > 0 && commDeletedId.getObjectId("_id") != null) {
                    totalMigrated = totalMigrated + dbObjectsList.size();
                    DBObject setquery = new BasicDBObject("movedId", dbObjectsList.get(dbObjectsList.size() - 1).get("_id"))
                            .append("totalMigrated", totalMigrated)
                            .append("updatedOn", new Date());
                    queryJson = new BasicDBObject("_id", commDeletedId.getObjectId("_id"));
                    DBObject rowJson = new BasicDBObject("$set", setquery);
                    newArchMongoDao.updateRow(movedIdsCollection, queryJson, rowJson, false, false, NStoS3util.lastUpdatedIdDB);
                }
                queryOnId.put("$gt", dbObjectsList.get(dbObjectsList.size() - 1).get("_id"));
                queryObject.put("_id", queryOnId);
                dbObjectsList = null; // explicitly nullify
                dbObjectsList = commLoggerNewMongoDao.getDBObjects(coll, queryObject, projection, 0, len, orderObject, db);
            }

            if (isMovePIVCLogsJobStop) {
                logger.warn("Deletion stopped by stop request.");
            } else {
                logger.info("Deletion job completed successfully.");
            }

        } catch (Exception e) {
            logger.error("Exception caught in movePIVCLogsToS3, ns:{} , msg: {}", dbColl, e.getMessage());
        }
    }


    private List<DBObject> getList(String coll, DBObject queryOnrequestId, int offset, String db, List<DBObject> resultList, int chunkSize) {

        try {
            List<DBObject> dataList = commLoggerNewMongoDao.getDBObjects(coll, queryOnrequestId, offset, chunkSize, null, db);
            while (dataList.size() > 0) {
                resultList.addAll(dataList);
                offset += chunkSize;
                dataList = commLoggerNewMongoDao.getDBObjects(coll, queryOnrequestId, offset, chunkSize, null, db);
                if(db.equalsIgnoreCase("DocsPoint") && coll.equalsIgnoreCase("DocPushLog") && resultList.size()>3000){
                    break;
                }
                else if(db.equalsIgnoreCase("DocsPoint") && coll.equalsIgnoreCase("RequestLogs") && resultList.size()>=3000){
                    break;
                }
                else if(db.equalsIgnoreCase("Integration") && coll.equalsIgnoreCase("RequestLogs") && resultList.size()>=3000){
                    break;
                }
                else if(db.equalsIgnoreCase("Integration") && coll.equalsIgnoreCase("ExternalAPILogs") && resultList.size()>=1000){
                    offset = 0;
                    while (dataList.size() > 0) {
                        resultList.addAll(dataList);
                        offset += chunkSize;
                        deleteForExcessDocs(coll, db, resultList);
                        dataList = commLoggerNewMongoDao.getDBObjects(coll, queryOnrequestId, offset, chunkSize, null, db);
                        resultList = new ArrayList<>();
                    }
                }
            }
        } catch (Exception e) {
            logger.error("Exception while getList");
        }
        return resultList;
    }

    private void deleteForExcessDocs(String coll, String db, List<DBObject> resultList) throws Exception {
        try {
            List<Object>listOfIds = new ArrayList<>();
            for (DBObject reqIdDoc : resultList) {
                listOfIds.add(reqIdDoc.get("_id"));
            }
            List<Object> subList = new ArrayList<>();
            int startIndx = 0;
            int lastIndx = 100;
            if (listOfIds.size() > 100) {
                while (startIndx < listOfIds.size()) {
                    if (startIndx + 100 >= listOfIds.size()) {
                        lastIndx = listOfIds.size();
                    }
                    subList = listOfIds.subList(startIndx, lastIndx);
                    DBObject queryFormovedIds = new BasicDBObject();
                    queryFormovedIds.put("_id", new BasicDBObject().append("$in", subList));
                    commLoggerNewMongoDao.deleteRow(coll, queryFormovedIds, true, db);
                    logger.info("BMS Integration.ExternalAPILogs Last Id deleted : {}", subList.get(subList.size() - 1));
                    startIndx = lastIndx;
                    lastIndx = lastIndx + 100;
                    subList = null;
                }
            } else if (listOfIds != null && listOfIds.size() > 0) {
                DBObject queryFormovedIds = new BasicDBObject();
                queryFormovedIds.put("_id", new BasicDBObject().append("$in", listOfIds));
                commLoggerNewMongoDao.deleteRow(coll, queryFormovedIds, true, db);
                logger.info("BMS Integration.ExternalAPILogs  Last Id deleted : {}", listOfIds.get(listOfIds.size() - 1));
            }

        } catch (Exception e) {
            logger.error("Error while adding in moveIntegrationExternalAPILogsToS3 , {}", e.getMessage());
            if (!e.getMessage().contains("timeout") && !e.getMessage().contains("duplicate")) {
                throw e;
            }
        }
    }

  // Method to compress data using GZIP
    private static byte[] compressData(String data) {
        ByteArrayOutputStream outputStream = null;
        GZIPOutputStream gzipOutputStream = null;
        try {
            outputStream = new ByteArrayOutputStream();
            gzipOutputStream = new GZIPOutputStream(outputStream);
            gzipOutputStream.write(data.getBytes());
            gzipOutputStream.close();
            return outputStream.toByteArray();
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        } finally {
            if (outputStream != null) {
                try {
                    outputStream.close();
                } catch (IOException e) {
                }
            }
            if (gzipOutputStream != null) {
                try {
                    gzipOutputStream.close();
                } catch (IOException e) {
                }
            }
        }
    }
}
