/**
 * 
 */
package com.docMigration.service.impl;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

import javax.annotation.PostConstruct;
import javax.sql.DataSource;

import com.docMigration.dao.*;
import com.docMigration.util.NStoS3util;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import com.docMigration.cleanup.entity.LogIdWorked;
import com.docMigration.cleanup.entity.ReqResLog;
import com.docMigration.cleanup.entity.ReqResTWowheelerLog;
import com.docMigration.cleanup.entity.StaticMaster;
import com.docMigration.service.DocumentStoreScheduleService;
import com.docMigration.service.S3BucketService;
import com.docMigration.util.AppUtil;
import com.docMigration.util.DateUtil;
import com.mongodb.BasicDBObject;
import com.mongodb.DB;
import com.mongodb.DBObject;
import com.mongodb.MongoClient;
import com.mongodb.MongoClientURI;

/**
 * <AUTHOR>
 *
 */
@Service
public class DocumentStoreScheduleServiceImpl implements DocumentStoreScheduleService{

	private final Logger logger = LoggerFactory.getLogger(DocumentStoreScheduleServiceImpl.class);
	
	private static Date date = DateUtil.getStringToDate("31/03/2019", DateUtil.DATE_FORMAT_DD_MM_YYYY_SLASH);
	
	@Autowired
	S3BucketService s3BucketService;
	
	@Autowired
	AppMongoDao appMongoDao;
	
	@Autowired
	AppNewMongoDao appNewMongoDao;
	
	@Autowired
	ChatMongoDao chatMongoDao;
	
	@Autowired
	@Qualifier("uaeChatMongoDao")
	ChatMongoDao uaeChatMongoDao;
	
	@Autowired
	CommBoxDao commBoxMongoDao;
	
	@Autowired
	@Qualifier("uaeJourMongoDao")
	ChatMongoDao uaeJourMongoDao;

	@Autowired
	@Qualifier("uaeJourMongoDao2")
	ChatMongoDao uaeJourMongoDaoNew;

	@Autowired
	@Qualifier("newArchivalMongoDao")
	ChatMongoDao newArchMongoDao;
	
	@Autowired
	@Qualifier("commPrimaryArchivalMongoDao")
	ChatMongoDao commPrimaryArchMongoDao;
	
	@Autowired
	@Qualifier("commMongoDao")
	ChatMongoDao commMongoDao;

	@Autowired
	@Qualifier("uaeArchivalDao")
	ChatMongoDao uaeArchivalDao;
	
	@Autowired
	LoggerMongoDao loggerMongoDao;
	
	@Autowired
	@Qualifier("motorLoggerDao")
	ChatMongoDao motorLoggerDao;
	
	@Autowired
	E2eMongoDao e2eMongoDao;
	@Value("${aws.bucket_name}")
	private String bucketName;
	
	@Value("${doc.schedular.count}")
	private int count;
	
	@Qualifier("jdbcTemplate")
	@Autowired
    private JdbcTemplate jdbcTemplate;
	
	@Autowired
	private NamedParameterJdbcTemplate namedParameterJdbcTemplate;
	
	@Autowired
	private MongoTemplate mongoTemplate;
	
	@Autowired
	private DataSource dataSource;
	
	@PostConstruct
	public void init() {
		namedParameterJdbcTemplate = new NamedParameterJdbcTemplate(dataSource);
	}
	/**
	 * This method will be used to migrate file to S3 from  mongo docBytes.
	 * 
	 * @param coll
	 * @param idCount
	 * @param queryObject
	 * @param queryOnId
	 * @param orderObject
	 * @param projectObj
	 * @throws IOException
	 * @throws Exception
	 */
	@Override
	public void migrateFIlesToS3(String coll, String idCount, String startId, String endId, String server) throws IOException, Exception {
		logger.info("Inside migrateFIlesToS3 : {} ", idCount);
		DBObject queryObject = new BasicDBObject();
		DBObject queryOnId = new BasicDBObject("$lt", new ObjectId(date));
		DBObject orderObject = new BasicDBObject("_id", 1);
		DBObject projectObj = new BasicDBObject("_id", 1);
		DBObject lastSeen = null;
		DBObject getOneObjQuery = null;
		int offset = 0;
		int len = 5000;
		
		if(!StringUtils.isEmpty(startId) && !StringUtils.isEmpty(endId)) {
			queryOnId.put("$gt", new ObjectId(startId));
			queryOnId.put("$lt", new ObjectId(endId));
		}
		else {
			/*List<BasicDBObject> lastSeenList = appMongoDao.getDBObjects("lastSeenDoc", new BasicDBObject("coll", coll));
			lastSeen = new BasicDBObject();
			if (lastSeenList != null && lastSeenList.size() > 0) {
				logger.info("Last seen Obj found , {}", lastSeenList.get(0).get(coll));
				lastSeen = lastSeenList.get(0);
				queryOnId.put("$gt", lastSeen.get("lastSeenId"));

			} else {
				lastSeen.put("_id", new ObjectId());
				lastSeen.put("coll", coll);
				lastSeen.put("docCount", 0);
				lastSeen.put("U_AT", new Date());
			}*/
		}
		queryObject.put("_id", queryOnId);
		List<BasicDBObject> dbObjectsList = appMongoDao.getDBObjects(coll, queryObject, projectObj, offset, len, orderObject);
		boolean isDataFound = false;
		
		if (dbObjectsList != null && dbObjectsList.size() > 0) {
		isDataFound = true;
		}
		
		while(isDataFound) {
		
		if (dbObjectsList != null && dbObjectsList.size() > 0) {
			for (BasicDBObject dbObject : dbObjectsList) {
				if(lastSeen != null)
				{/*
					lastSeen.put("lastSeenId" , dbObject.get("_id"));
					lastSeen.put("docCount", AppUtil.getInt(String.valueOf(lastSeen.get("docCount"))) + 1);
					getOneObjQuery = new BasicDBObject("_id", lastSeen.get("lastSeenId"));*/
				}else {
					getOneObjQuery = new BasicDBObject("_id", dbObject.get("_id"));
				}
				List<BasicDBObject> objList = appMongoDao.getDBObjects(coll, getOneObjQuery);
				logger.info("Object got for update {} ", dbObject.getObjectId("_id").toHexString());

				for (BasicDBObject dbObj : objList) {
					if (!StringUtils.isEmpty(dbObj.getString("docBytes"))) {
						byte[] sd = (byte[]) dbObj.get("docBytes");
						String folderName = "mg/";

						if (!StringUtils.isEmpty(dbObj.getString("mimeType"))) {
							String mimeType = dbObj.getString("mimeType");
							String url = s3BucketService.addByteContent(sd, bucketName, folderName,
									dbObj.getString("fileName"), mimeType);
							dbObj.put("docUrl", url);
							dbObj.remove("docBytes");

							logger.info("document inserted in s3 with url, {}", url);
							DBObject updateQuery = new BasicDBObject("_id", dbObj.get("_id"));
							appMongoDao.updateRow(coll, updateQuery, dbObj, false, false);
							
								try {
									DBObject insertQue = new BasicDBObject("insertId", dbObj.get("_id"))
											.append("server", server).append("coll", coll);
									appMongoDao.insertRow("movedId"+coll, insertQue);
								}catch(Exception e) {
								logger.error("not inserted in movedId , {}" , e.getMessage());
							}
						}

					}else {
						logger.info("no docBytes , {} ", dbObj.get("_id"));
						try {
							DBObject insertQue = new BasicDBObject("scanId", dbObj.get("_id"))
									.append("server", server).append("coll", coll);
							appMongoDao.insertRow("NoDocBytes", insertQue);
						}catch(Exception e) {
						logger.error("not inserted in NoDocBytes , {}" , e.getMessage());
					}
					}
					/*if(lastSeen != null)
					lastSeen.put("U_AT", new Date());*/
				}

			}
		}
			/*if(lastSeen != null)
			{
			appMongoDao.updateRow("lastSeenDoc", new BasicDBObject("_id", lastSeen.get("_id")), lastSeen, true,
					false);
			}*/

		offset = offset + len;
		// get Data;
		dbObjectsList = appMongoDao.getDBObjects(coll, queryObject, projectObj, offset, len, orderObject);
		
		if (dbObjectsList != null && dbObjectsList.size() > 0) {
			isDataFound = true;
		} else {
			isDataFound = false;
		}
		
		}
	}

	@Override
	public void migrateCollection(String coll,  String startId, String endId) throws Exception{
		List<BasicDBObject> dataList = null;
		DBObject queryOnId = new BasicDBObject();
		DBObject queryObject = new BasicDBObject();
		int offset = 0;
		int len = 1000;
		try {
			queryOnId.put("$gte", new ObjectId(startId));
			queryOnId.put("$lt", new ObjectId(endId));

			queryObject.put("_id", queryOnId);
			dataList = appMongoDao.getDBObjects(coll, queryObject, null, offset, len);
			boolean isDataFound = false;

			if (dataList != null && dataList.size() > 0) {
				isDataFound = true;
			}

			while (isDataFound) {
				appNewMongoDao.insert(dataList, coll, true);
				logger.info("Inserted Documents ");
				
				// get Data;
				offset = offset + len;
				dataList = appMongoDao.getDBObjects(coll, queryObject, null, offset, len);

				if (dataList != null && dataList.size() > 0) {
					isDataFound = true;
				} else {
					isDataFound = false;
				}

			}
		}catch(Exception e) {
			throw e;
		}
	}

	@Override
	public void migrateCollectionForChat(String srcColl, String targetColl, String db, String tsStart, String tsEnd, String source,boolean timeCheck, int type) {
		DBObject queryOnDate = new BasicDBObject();
		DBObject queryObject = new BasicDBObject();
		int offset = 0;
		int len = 1000;
		try {
			Date startDate = null;
			Date endDate = DateUtil.getStringToDate(
					DateUtil.parseDate(DateUtil.addDays(new Date(), -2), DateUtil.DATE_FORMAT_yyyy_MM_dd_T_HH_mm_ss_Z),
					DateUtil.DATE_FORMAT_yyyy_MM_dd_T_HH_mm_ss_Z);
			
			if(srcColl.equals("rocketchat_room")) {
				
				if(tsStart!=null && tsEnd!=null ) {
					startDate = DateUtil.getStringToDate(DateUtil.parseDate(DateUtil.getStringToDate(tsStart, DateUtil.DATE_FORMAT_YYYY_MM_DD),
							DateUtil.DATE_FORMAT_yyyy_MM_dd_T_HH_mm_ss_Z),DateUtil.DATE_FORMAT_yyyy_MM_dd_T_HH_mm_ss_Z);
					
					endDate = DateUtil.getStringToDate(DateUtil.parseDate(DateUtil.getStringToDate(tsEnd, DateUtil.DATE_FORMAT_YYYY_MM_DD),
							DateUtil.DATE_FORMAT_yyyy_MM_dd_T_HH_mm_ss_Z),DateUtil.DATE_FORMAT_yyyy_MM_dd_T_HH_mm_ss_Z);
				}else {
					startDate = DateUtil.getStringToDate(
							DateUtil.parseDate(DateUtil.addDays(new Date(), -60), DateUtil.DATE_FORMAT_yyyy_MM_dd_T_HH_mm_ss_Z),
							DateUtil.DATE_FORMAT_yyyy_MM_dd_T_HH_mm_ss_Z);
					
					endDate = DateUtil.getStringToDate(
							DateUtil.parseDate(DateUtil.addDays(new Date(), -30), DateUtil.DATE_FORMAT_yyyy_MM_dd_T_HH_mm_ss_Z),
							DateUtil.DATE_FORMAT_yyyy_MM_dd_T_HH_mm_ss_Z);
				}
				
				queryObject.put("t", "l");
				if (source.equals("uae")) {
					queryObject.put("open", null);
				}
				queryOnDate.put("$gte", startDate);
			} else if (srcColl.equals("rocketchat_subscription"))  {

				if(source.equals("uae")) {
					endDate = DateUtil.getStringToDate(
							DateUtil.parseDate(DateUtil.addDays(new Date(), -10),
									DateUtil.DATE_FORMAT_yyyy_MM_dd_T_HH_mm_ss_Z),
							DateUtil.DATE_FORMAT_yyyy_MM_dd_T_HH_mm_ss_Z);
				}else {
				endDate = DateUtil.getStringToDate(
						DateUtil.parseDate(DateUtil.addDays(new Date(), -3),
								DateUtil.DATE_FORMAT_yyyy_MM_dd_T_HH_mm_ss_Z),
						DateUtil.DATE_FORMAT_yyyy_MM_dd_T_HH_mm_ss_Z);
				}

				queryObject.put("open", false);
				queryObject.put("t", "l");
				
			}else if (srcColl.equals("rocketchat_livechat_inquiry")) {

				if(source.equals("uae")) {
					endDate = DateUtil.getStringToDate(
							DateUtil.parseDate(DateUtil.addDays(new Date(), -30),
									DateUtil.DATE_FORMAT_yyyy_MM_dd_T_HH_mm_ss_Z),
							DateUtil.DATE_FORMAT_yyyy_MM_dd_T_HH_mm_ss_Z);
				}else {
				endDate = DateUtil.getStringToDate(
						DateUtil.parseDate(DateUtil.addDays(new Date(), -3),
								DateUtil.DATE_FORMAT_yyyy_MM_dd_T_HH_mm_ss_Z),
						DateUtil.DATE_FORMAT_yyyy_MM_dd_T_HH_mm_ss_Z);
				}

				queryObject.put("status", "taken");
				
			}
			
			if (srcColl.equals("users")) {

				if(source.equals("uae")) {
					endDate = DateUtil.getStringToDate(
							DateUtil.parseDate(DateUtil.addDays(new Date(), -30),
									DateUtil.DATE_FORMAT_yyyy_MM_dd_T_HH_mm_ss_Z),
							DateUtil.DATE_FORMAT_yyyy_MM_dd_T_HH_mm_ss_Z);
				}else {
					endDate = DateUtil.getStringToDate(
							DateUtil.parseDate(DateUtil.addDays(new Date(), -3),
									DateUtil.DATE_FORMAT_yyyy_MM_dd_T_HH_mm_ss_Z),
							DateUtil.DATE_FORMAT_yyyy_MM_dd_T_HH_mm_ss_Z);
				}
				

				queryObject.put("type", "visitor");
				queryOnDate.put("$lte", endDate);
				queryObject.put("_updatedAt", queryOnDate);

			}else if(srcColl.equals("rocketchat_subscription")) {
				queryOnDate.put("$lte", endDate);
				if(type == 1) {
					queryObject.put("_updatedAt", queryOnDate);
				}else {
					queryObject.put("ls", queryOnDate);
				}
				
			}else {
				queryOnDate.put("$lte", endDate);
				queryObject.put("ts", queryOnDate);
			}
			
			if(source.equals("pb"))
			{
				migrateForPBChat(srcColl, targetColl, db, queryObject, offset,timeCheck, len);
			}else if(source.equals("uae")) {
				migrateForUaeChat(srcColl, targetColl, db, queryObject, offset, timeCheck,len);
			}

		}catch(Exception e) {
			logger.error("Error inside migrateCollectionForChat : {} ", e.getMessage());
		}
		
	}

	private void migrateForPBChat(String srcColl, String targetColl, String db, DBObject queryObject, int offset,boolean timeCheck,
			int len) throws Exception {
		List<DBObject> dbObjectsList;
		Date startcheckDate = new Date();
		Date endCheckDate = new Date();
		dbObjectsList = chatMongoDao.getDBObjects(srcColl, queryObject, null, offset, len, null, db);
		boolean isDataFound = false;

		if (dbObjectsList != null && dbObjectsList.size() > 0) {
			isDataFound = true;
		}

		while (isDataFound) {
			Date todayDate = new Date();
			startcheckDate = DateUtil.setHours(todayDate, 7);
			startcheckDate = DateUtil.setMinute(startcheckDate, 0);
			startcheckDate = DateUtil.setSeconds(startcheckDate, 0);

			endCheckDate = DateUtil.setHours(todayDate, 20);
			endCheckDate = DateUtil.setMinute(endCheckDate, 0);
			endCheckDate = DateUtil.setSeconds(endCheckDate, 0);
			if (todayDate.before(startcheckDate) || todayDate.after(endCheckDate) || !timeCheck) {
				List<String> listOfIds = new ArrayList<>();
				if (!srcColl.equals("rocketchat_livechat_inquiry") && !srcColl.equals("rocketchat_subscription")
						&& !srcColl.equals("users")) {
					try {
						chatMongoDao.addRows(targetColl, dbObjectsList, db);

						logger.info("Inserted Documents...");

					} catch (Exception e) {
						if (!e.getMessage().contains("duplicate key error collection")) {
							logger.info("Error while inserting :- {}", e.getMessage());
							throw e;
						} else {
							logger.info("Error while inserting :- {}", e.getMessage());
						}
					}

					logger.info("Inserted Documents ");
				}

				for (DBObject res : dbObjectsList) {
					listOfIds.add(res.get("_id").toString());
				}

				DBObject deleteQueryObject = new BasicDBObject();
				DBObject deleteQueryInObject = new BasicDBObject();
				deleteQueryInObject.put("$in", listOfIds);
				deleteQueryObject.put("_id", deleteQueryInObject);
				chatMongoDao.deleteRow(srcColl, deleteQueryObject, db);

				logger.info("Deleted Documents ");
				// get Data;
				// offset = offset + len; commented because old data deleted and no need o skip.
				dbObjectsList = chatMongoDao.getDBObjects(srcColl, queryObject, null, offset, len, null, db);

				if (dbObjectsList != null && dbObjectsList.size() > 0) {
					isDataFound = true;
				} else {
					isDataFound = false;
					// chatMongoDao.deleteRow(srcColl, queryObject, db);
					logger.info("No Documents found ");
				}
			}

		}
	}
	
	private void migrateForUaeChat(String srcColl, String targetColl, String db, DBObject queryObject, int offset,boolean timeCheck,
			int len) throws Exception {
		List<DBObject> dbObjectsList;
		Date startcheckDate = new Date();
		Date endCheckDate = new Date();
		dbObjectsList = uaeChatMongoDao.getDBObjects(srcColl, queryObject, null, offset, len, null, db);
		boolean isDataFound = false;

		if (dbObjectsList != null && dbObjectsList.size() > 0) {
			isDataFound = true;
		}

		while (isDataFound) {
			Date todayDate = new Date();
			startcheckDate = DateUtil.setHours(todayDate, 7);
			startcheckDate = DateUtil.setMinute(startcheckDate, 0);
			startcheckDate = DateUtil.setSeconds(startcheckDate, 0);

			endCheckDate = DateUtil.setHours(todayDate, 20);
			endCheckDate = DateUtil.setMinute(endCheckDate, 0);
			endCheckDate = DateUtil.setSeconds(endCheckDate, 0);
			if (todayDate.before(startcheckDate) || todayDate.after(endCheckDate) || !timeCheck) {
				List<String> listOfIds = new ArrayList<>();
				if (!srcColl.equals("rocketchat_livechat_inquiry") && !srcColl.equals("rocketchat_subscription")
						&& !srcColl.equals("users")) {
					try {
						uaeChatMongoDao.addRows(targetColl, dbObjectsList, db);

						logger.info("Inserted Documents...");

					} catch (Exception e) {
						if (!e.getMessage().contains("duplicate key error collection")) {
							logger.info("Error while inserting :- {}", e.getMessage());
							throw e;
						} else {
							logger.info("Error while inserting :- {}", e.getMessage());
						}
					}

					logger.info("Inserted Documents ");
				}
				for (DBObject res : dbObjectsList) {
					listOfIds.add(res.get("_id").toString());
				}

				DBObject deleteQueryObject = new BasicDBObject();
				DBObject deleteQueryInObject = new BasicDBObject();
				deleteQueryInObject.put("$in", listOfIds);
				deleteQueryObject.put("_id", deleteQueryInObject);
				uaeChatMongoDao.deleteRow(srcColl, deleteQueryObject, db);

				logger.info("Deleted Documents ");
				// get Data;
				// offset = offset + len; commented because old data deleted and no need o skip.
				dbObjectsList = uaeChatMongoDao.getDBObjects(srcColl, queryObject, null, offset, len, null, db);

				if (dbObjectsList != null && dbObjectsList.size() > 0) {
					isDataFound = true;
				} else {
					isDataFound = false;
					// chatMongoDao.deleteRow(srcColl, queryObject, db);
					logger.info("No Documents found ");
				}
			}
		}
	}

	@Override
	public void cleanUpForTwoWheeler(Long startingSnNo, Long lastSnNo) {
		try {
			while(startingSnNo<=lastSnNo) {
				List<Map<String,Object>> result = populateVehicleIdsToDelete(startingSnNo,lastSnNo);
				if(result != null && result.size() > 0 )
				{
					startingSnNo = AppUtil.getLong(result.get(result.size() - 1).get("SNO").toString());
					List<Long> listOfVehicleIds = new ArrayList<>();
					for (Map<String,Object> res : result) {
						listOfVehicleIds.add(AppUtil.getLong(res.get("VehicleDetailId").toString()));
					}
					List<List<Long>> subSets = new ArrayList();//ListUtils.partition(listOfVehicleIds, 100);
					for (List<Long> subSet : subSets) {
						Query query = new Query();
						query.addCriteria(Criteria.where("VehicleDetailId").in(subSet));
						Update update = new Update();
						update.set("IsLeadBooked", true);
						mongoTemplate.updateMulti(query, update, "ReqResTWowheelerLog");
					}
					logger.info("SN NO Processed " + startingSnNo);
				}else {
					startingSnNo = lastSnNo+1;
				}
			}
			
		}catch (Exception e) {
			System.out.println("Exception caught in chunk starting SN NO - " + startingSnNo +" : "+e.getMessage());
		}
		
	}
	
	@Override
	public void cleanUpForTwoWheeler2(String startId, String endId, Integer limit,boolean timeCheck,boolean isNew) {
		Date startDate = new Date();
		startDate = DateUtil.setHours(startDate, 7);
		startDate = DateUtil.setMinute(startDate, 0);
		startDate = DateUtil.setSeconds(startDate, 0);

		Date endDate = new Date();
		endDate = DateUtil.setHours(endDate, 20);
		endDate = DateUtil.setMinute(endDate, 0);
		endDate = DateUtil.setSeconds(endDate, 0);
		try {
			
			LogIdWorked logIdWorked = null;
			
			Query query = new Query();
			Criteria criteria = null;
			if(startId !=null) {
				criteria = Criteria.where("_id").gt(new ObjectId(startId));
				//query.addCriteria();
			}else {
				Query startQuery = new Query();
				startQuery.addCriteria(Criteria.where("coll").is("ReqResTWowheelerLog"));
				logIdWorked = mongoTemplate.findOne(startQuery, LogIdWorked.class);
				criteria = Criteria.where("_id").gt(new ObjectId(logIdWorked.getUpdatedId()));
				//criteria = Criteria.where("_id").gte(new ObjectId(DateUtil.getStringToDate(DateUtil.getFormatedDate(-90, DateUtil.DATE_FORMAT_EE_MMM_dd_hh_mm_ss_z_yyyy),DateUtil.DATE_FORMAT_EE_MMM_dd_hh_mm_ss_z_yyyy)));
				//query.addCriteria();
			}
			
			if(endId != null) {
				criteria.lte(new ObjectId(endId));
				//query.addCriteria(Criteria.where("_id").lte(new ObjectId(endId)));
			}else {
				criteria.lte(new ObjectId(DateUtil.getStringToDate(DateUtil.getFormatedDate(-45, DateUtil.DATE_FORMAT_EE_MMM_dd_hh_mm_ss_z_yyyy),DateUtil.DATE_FORMAT_EE_MMM_dd_hh_mm_ss_z_yyyy)));
				//query.addCriteria(Criteria.where("_id").lte(new ObjectId(DateUtil.getStringToDate(DateUtil.getFormatedDate(-45, DateUtil.DATE_FORMAT_EE_MMM_dd_hh_mm_ss_z_yyyy),DateUtil.DATE_FORMAT_EE_MMM_dd_hh_mm_ss_z_yyyy))));
			}
			query.addCriteria(criteria);
			query.addCriteria(Criteria.where("IsLeadBooked").ne(true));
			query.limit(limit);
			query.fields().include("VehicleDetailId");
			query.with(Sort.by(Sort.Direction.ASC, "_id"));
			List<ReqResTWowheelerLog> queryResList = mongoTemplate.find(query, ReqResTWowheelerLog.class);
			while (queryResList.size() > 0) {
				try {
					Date todayDate = new Date();
					startDate = DateUtil.setHours(todayDate, 7);
					startDate = DateUtil.setMinute(startDate, 0);
					startDate = DateUtil.setSeconds(startDate, 0);

					endDate = DateUtil.setHours(todayDate, 20);
					endDate = DateUtil.setMinute(endDate, 0);
					endDate = DateUtil.setSeconds(endDate, 0);
					logger.info("Today date in service: {}, start date {} , end date {}", todayDate, startDate,
							endDate);
					if (todayDate.before(startDate) || todayDate.after(endDate) || !timeCheck) {
						logger.info("got first : {}", queryResList.get(0).getId());
						List<Long> listOfVehicleIds = new ArrayList<>();

						for (ReqResTWowheelerLog res : queryResList) {
							listOfVehicleIds.add(res.getVehicleDetailId());
						}

						List<Map<String, Object>> result = isNew ? populateVehicleIdsNewToDelete(listOfVehicleIds)
								: populateVehicleIdsToDelete(listOfVehicleIds);
						List<Long> listOfVehicleIds2 = new ArrayList<>();
						for (Map<String, Object> res : result) {
							listOfVehicleIds2.add(AppUtil.getLong(res.get("VehicleDetailId").toString()));
						}
						List<List<Long>> subSets = new ArrayList<>();// ListUtils.partition(listOfVehicleIds2, 100);
						for (List<Long> subSet : subSets) {
							Query query1 = new Query();
							query1.addCriteria(Criteria.where("VehicleDetailId").in(subSet));
							Update update = new Update();
							update.set("IsLeadBooked", true);
							mongoTemplate.updateMulti(query1, update, "ReqResTWowheelerLog");
						}

						if (logIdWorked != null && logIdWorked.getId() != null) {
							Query updateQuery = new Query();
							updateQuery.addCriteria(Criteria.where("_id").is(new ObjectId(logIdWorked.getId())));
							Update update = new Update();
							update.set("updatedId", queryResList.get(queryResList.size() - 1).getId());
							mongoTemplate.updateFirst(updateQuery, update, "logIdWorked");
						}

						logger.info("got lastId : {}", queryResList.get(queryResList.size() - 1).getId());
						criteria.gt(new ObjectId(queryResList.get(queryResList.size() - 1).getId()));
						queryResList = mongoTemplate.find(query, ReqResTWowheelerLog.class);
					}
				}catch(Exception e) {
					logger.error("Exception caught in cleanUpForTwoWheeler2 starting end NO - {} , error {} ",queryResList.get(queryResList.size() - 1).getId(), e.getMessage());
				}
			}
			
		}catch (Exception e) {
			logger.error("Exception caught in cleanUpForTwoWheeler2 starting SN NO - {} , error {} ",startId, e.getMessage());
		}
			
	}
	
	@Override
	public void markBookedCarLead(String startId, String endId, Integer limit,boolean timeCheck,boolean isNew) {
		Date startDate = new Date();
		startDate = DateUtil.setHours(startDate, 7);
		startDate = DateUtil.setMinute(startDate, 0);
		startDate = DateUtil.setSeconds(startDate, 0);

		Date endDate = new Date();
		endDate = DateUtil.setHours(endDate, 20);
		endDate = DateUtil.setMinute(endDate, 0);
		endDate = DateUtil.setSeconds(endDate, 0);
		try {
			LogIdWorked logIdWorked = null;
			
			Query query = new Query();
			Criteria criteria = null;
			if(startId !=null) {
				criteria = Criteria.where("_id").gt(new ObjectId(startId));
				//query.addCriteria();
			}else {
				Query startQuery = new Query();
				startQuery.addCriteria(Criteria.where("coll").is("ReqResLog"));
				logIdWorked = mongoTemplate.findOne(startQuery, LogIdWorked.class);
				criteria = Criteria.where("_id").gt(new ObjectId(logIdWorked.getUpdatedId()));
				//criteria = Criteria.where("_id").gte(new ObjectId(DateUtil.getStringToDate(DateUtil.getFormatedDate(-90, DateUtil.DATE_FORMAT_EE_MMM_dd_hh_mm_ss_z_yyyy),DateUtil.DATE_FORMAT_EE_MMM_dd_hh_mm_ss_z_yyyy)));
				//query.addCriteria();
			}
			
			
			if(endId != null) {
				criteria.lte(new ObjectId(endId));
				//query.addCriteria(Criteria.where("_id").lte(new ObjectId(endId)));
			}else {
				criteria.lte(new ObjectId(DateUtil.getStringToDate(DateUtil.getFormatedDate(-45, DateUtil.DATE_FORMAT_EE_MMM_dd_hh_mm_ss_z_yyyy),DateUtil.DATE_FORMAT_EE_MMM_dd_hh_mm_ss_z_yyyy)));
				//query.addCriteria(Criteria.where("_id").lte(new ObjectId(DateUtil.getStringToDate(DateUtil.getFormatedDate(-45, DateUtil.DATE_FORMAT_EE_MMM_dd_hh_mm_ss_z_yyyy),DateUtil.DATE_FORMAT_EE_MMM_dd_hh_mm_ss_z_yyyy))));
			}
			query.addCriteria(criteria);
			query.addCriteria(Criteria.where("IsLeadBooked").ne(true));
			query.limit(limit);
			query.fields().include("EnquiryID");
			query.with(Sort.by(Sort.Direction.ASC, "_id"));
			List<ReqResLog> queryResList = mongoTemplate.find(query, ReqResLog.class);
			
			while (queryResList.size() > 0) {
				try {
					Date todayDate = new Date();
					startDate = DateUtil.setHours(todayDate, 7);
					startDate = DateUtil.setMinute(startDate, 0);
					startDate = DateUtil.setSeconds(startDate, 0);

					endDate = DateUtil.setHours(todayDate, 20);
					endDate = DateUtil.setMinute(endDate, 0);
					endDate = DateUtil.setSeconds(endDate, 0);
					logger.info("Today date in service: {}, start date {} , end date {}", todayDate, startDate,
							endDate);
					if (todayDate.before(startDate) || todayDate.after(endDate) || !timeCheck) {
						logger.info("got first : {}", queryResList.get(0).getId());
						List<Long> listOfVehicleIds = new ArrayList<>();

						for (ReqResLog res : queryResList) {
							listOfVehicleIds.add(res.getEnquiryID());
						}

						List<Map<String, Object>> result = isNew ? populateNewCarEnqIdToMark(listOfVehicleIds)
								: populateCarEnqIdToMark(listOfVehicleIds);
						List<Long> listOfVehicleIds2 = new ArrayList<>();
						for (Map<String, Object> res : result) {
							listOfVehicleIds2.add(AppUtil.getLong(res.get("EnquiryID").toString()));
						}
						List<List<Long>> subSets = new ArrayList<>();// ListUtils.partition(listOfVehicleIds2, 100);
						try {
							for (List<Long> subSet : subSets) {
								Query query1 = new Query();
								query1.addCriteria(Criteria.where("EnquiryID").in(subSet));
								Update update = new Update();
								update.set("IsLeadBooked", true);
								mongoTemplate.updateMulti(query1, update, "ReqResLog");
							}
						} catch (Exception e) {
							logger.error("Exception caught in update  markBookedCarLead starting end");
						}

						if (logIdWorked != null && logIdWorked.getId() != null) {
							Query updateQuery = new Query();
							updateQuery.addCriteria(Criteria.where("_id").is(new ObjectId(logIdWorked.getId())));
							Update update = new Update();
							update.set("updatedId", queryResList.get(queryResList.size() - 1).getId());
							mongoTemplate.updateFirst(updateQuery, update, "logIdWorked");
						}

						logger.info("got lastId : {}", queryResList.get(queryResList.size() - 1).getId());
						criteria.gt(new ObjectId(queryResList.get(queryResList.size() - 1).getId()));
						queryResList = mongoTemplate.find(query, ReqResLog.class);
					}
				}catch(Exception e) {
					logger.error("Exception caught in markBookedCarLead starting end NO - {} , error {} ",queryResList.get(queryResList.size() - 1).getId(), e.getMessage());
				}
			}
			
		}catch (Exception e) {
			logger.error("Exception caught in markBookedCarLead starting SN NO - {} , error {} ",startId, e.getMessage());
		}
			
	}
	
	@SuppressWarnings("unchecked")
	public List<Map<String,Object>> populateVehicleIdsToDelete(long startingSnNo, long lastSnNo) {
		List<Map<String,Object>> dataFromDb = new ArrayList<Map<String,Object>>();
		String sql = "Select top 1000 V.VehicleDetailId,BD.SNO from PBCROMA.CRM.BookingDetails BD(nolock)" 
			     + " Inner Join ProductDB.Core.Enquiry E(nolock) on BD.LEADID=E.MatrixLeadID" 
			     + " Inner Join ProductDB.TW.T_TblVehicleDetails V(nolock) on V.EnquiryId=E.EnquiryId " 
			     + " where BD.SNO > ? and BD.SNO < ? and BD.ProductID=114 and PaymentSTATUS in ('300','0300','3002','4002','5002','6002')";
		dataFromDb = (List<Map<String, Object>>) jdbcTemplate.queryForList(sql,startingSnNo,lastSnNo);
//		ObjectMapper mapper = new ObjectMapper();
//		List<ReqResTWowheelerLog> reqResLogs = mapper.convertValue(dataFromDb, List.class);
		return dataFromDb;
		
	}
	
	@SuppressWarnings("unchecked")
	public List<Map<String,Object>> populateVehicleIdsToDelete(List<Long> vehicleDetailIdList ) {
		List<Map<String,Object>> dataFromDb = new ArrayList<Map<String,Object>>();
		String sql = "Select top 1000 V.VehicleDetailId,BD.SNO from PBCROMA.CRM.BookingDetails BD(nolock)" 
			     + " Inner Join ProductDB.Core.Enquiry E(nolock) on BD.LEADID=E.MatrixLeadID" 
			     + " Inner Join ProductDB.TW.T_TblVehicleDetails V(nolock) on V.EnquiryId=E.EnquiryId " 
			     + " where V.VehicleDetailId in (:vehicleDetails) AND BD.ProductID=114 and PaymentSTATUS in ('300','0300','3002','4002','5002','6002')";
		Map<String, Object> namedParameters = new HashMap<>();
		namedParameters.put("vehicleDetails", vehicleDetailIdList);
		dataFromDb = (List<Map<String, Object>>) namedParameterJdbcTemplate.queryForList(sql,namedParameters);
		return dataFromDb;
		
	}
	
	@SuppressWarnings("unchecked")
	public List<Map<String,Object>> populateVehicleIdsNewToDelete(List<Long> vehicleDetailIdList ) {
		List<Map<String,Object>> dataFromDb = new ArrayList<Map<String,Object>>();
		String sql = "Select top 1000 V.VehicleDetailId,BD.SNO from PBCROMA.CRM.BookingDetails BD(nolock)" 
			     + " Inner Join ProductDB.Core.Enquiry E(nolock) on BD.LEADID=E.MatrixLeadID" 
			     + " Inner Join TwoWheelerDB.TW.T_TblVehicleDetails V(nolock) on V.EnquiryId=E.EnquiryId " 
			     + " where V.VehicleDetailId in (:vehicleDetails) AND BD.ProductID=114 and PaymentSTATUS in ('300','0300','3002','4002','5002','6002')";
		Map<String, Object> namedParameters = new HashMap<>();
		namedParameters.put("vehicleDetails", vehicleDetailIdList);
		dataFromDb = (List<Map<String, Object>>) namedParameterJdbcTemplate.queryForList(sql,namedParameters);
		return dataFromDb;
		
	}
	
	@SuppressWarnings("unchecked")
	public List<Map<String,Object>> populateCarEnqIdToMark(List<Long> enquiryIdList ) {
		List<Map<String,Object>> dataFromDb = new ArrayList<Map<String,Object>>();
		String sql = "Select top 1000 V.EnquiryId from PBCROMA.CRM.BookingDetails BD(nolock) "
				+ " Inner Join ProductDB.Core.Enquiry E(nolock) on BD.LEADID=E.MatrixLeadID "
				+ " Inner Join ProductDB.CI.T_VehicleDetails V(nolock) on V.EnquiryId=E.EnquiryId "
				+ " where V.EnquiryId in (:EnquiryIds) and BD.ProductID=117 and PaymentSTATUS in ('300','0300','3002','4002','5002','6002')";
		Map<String, Object> namedParameters = new HashMap<>();
		namedParameters.put("EnquiryIds", enquiryIdList);
		dataFromDb = (List<Map<String, Object>>) namedParameterJdbcTemplate.queryForList(sql,namedParameters);
		return dataFromDb;
		
	}
	
	@SuppressWarnings("unchecked")
	public List<Map<String,Object>> populateNewCarEnqIdToMark(List<Long> enquiryIdList ) {
		List<Map<String,Object>> dataFromDb = new ArrayList<Map<String,Object>>();
		String sql = "Select top 1000 V.EnquiryId from PBCROMA.CRM.BookingDetails BD(nolock) "
				+ " Inner Join ProductDB.Core.Enquiry E(nolock) on BD.LEADID=E.MatrixLeadID "
				+ " Inner Join CARDB.CI.T_VehicleDetails V(nolock) on V.EnquiryId=E.EnquiryId "
				+ " where V.EnquiryId in (:EnquiryIds) and BD.ProductID=117 and PaymentSTATUS in ('300','0300','3002','4002','5002','6002')";
		Map<String, Object> namedParameters = new HashMap<>();
		namedParameters.put("EnquiryIds", enquiryIdList);
		dataFromDb = (List<Map<String, Object>>) namedParameterJdbcTemplate.queryForList(sql,namedParameters);
		return dataFromDb;
		
	}

	@Override
	public void cleanUpForCommbox(String dbColl, boolean timeCheck, int years, int type) {
		logger.info("Inside cleanUpForCommbox : {} , {}", dbColl,years);
		ChatMongoDao destination ;
		ChatMongoDao source ;
		String[] nsArray = dbColl.split("\\.");
		String dbName = nsArray[0];
		String coll = nsArray[1];
		long totalMigrated = 0;
		if(type == 1) {
			source = commMongoDao; 
			destination = commPrimaryArchMongoDao;
		}else {
			source = commPrimaryArchMongoDao;
			destination = newArchMongoDao;
		}
		
		Date startDate = new Date();
		startDate = DateUtil.setHours(startDate, 7);
		startDate = DateUtil.setMinute(startDate, 0);
		startDate = DateUtil.setSeconds(startDate, 0);

		Date endDate = new Date();
		endDate = DateUtil.setHours(endDate, 20);
		endDate = DateUtil.setMinute(endDate, 0);
		endDate = DateUtil.setSeconds(endDate, 0);
		
		DBObject queryObject = new BasicDBObject();
		
		DBObject orderObject = new BasicDBObject("_id", 1);
		DBObject projectObj = new BasicDBObject("_id", 1);
		
		int offset = 0;
		int len = 5000;
		
		DBObject queryForDeletId = new BasicDBObject();
 		queryForDeletId.put("coll", dbColl);
//		BasicDBObject emailIsertedIdList = (BasicDBObject) destination.getDBObject("cleanUpIds",queryForDeletId,destinationDb);
		BasicDBObject emailIsertedIdList = newArchMongoDao.getDBObjectByDbName(NStoS3util.lastUpdatedIdColl ,queryForDeletId, NStoS3util.lastUpdatedIdDB);

		Date datefOr3YrAgo = DateUtil.getDateByAddingYear(new  Date(), -years);
		DBObject queryOnId = new BasicDBObject("$lt", new ObjectId(datefOr3YrAgo));
		if(emailIsertedIdList!=null && emailIsertedIdList.size()>0) {
			queryOnId.put("$gt", new ObjectId(String.valueOf(emailIsertedIdList.get("movedId"))));
			totalMigrated = (long)emailIsertedIdList.get("totalMigrated");
			//queryOnId.put("$lt", new ObjectId(endId));
		}
		
		queryObject.put("_id", queryOnId);
		List<DBObject> dbObjectsList = source.getDBObjects(coll, queryObject, projectObj, offset, len, orderObject, dbName);
		
		while (dbObjectsList != null && dbObjectsList.size() > 0) {

			Date todayDate = new Date();
			startDate = DateUtil.setHours(todayDate, 7);
			startDate = DateUtil.setMinute(startDate, 0);
			startDate = DateUtil.setSeconds(startDate, 0);
			
			endDate = DateUtil.setHours(todayDate, 20);
			endDate = DateUtil.setMinute(endDate, 0);
			endDate = DateUtil.setSeconds(endDate, 0);
			
			if ((todayDate.before(startDate) || todayDate.after(endDate) || !timeCheck ) && (dbObjectsList != null && dbObjectsList.size() > 0) && !StaticMaster.isCleanUpCommBoxrStop()) {
				for (DBObject dbObject : dbObjectsList) {
					
					DBObject getOneObjQuery = new BasicDBObject("_id", dbObject.get("_id"));
						
					List<BasicDBObject> objList = source.getDBObjects(coll, getOneObjQuery, dbName);
					logger.info("Object got for remove in coll {} ", dbObject.get("_id"));

					for (BasicDBObject dbObj : objList) {
						if(dbObj.get("Conversations")!=null) {
							List<Map<String,Object>> conveList = (List<Map<String, Object>>) dbObj.get("Conversations");
							if(conveList.size()>0) {
								conveList.forEach((conveMap)->{
									if(conveMap.get("MailAttachments")!=null) {
										List<Map<String,Object>> mailList = (List<Map<String, Object>>) conveMap.get("MailAttachments");
										if(mailList.size()>0) {
											mailList.forEach((mailMap)->{
												if(mailMap.get("FileID")!=null&& !String.valueOf(mailMap.get("FileID")).equals("")) {
													String fileId = String.valueOf(mailMap.get("FileID"));
													if(ObjectId.isValid(fileId)) {
														DBObject queryOnFile = new BasicDBObject("_id", new ObjectId(fileId));
														List<DBObject> fsFileList = source.getDBObjects("fs.files",queryOnFile,null, 0,0,null, dbName);
														List<DBObject> dbObjectList = new ArrayList<>(fsFileList);//(List<DBObject>) JSON.parse(new Gson().toJson(fsFileList));
														try {
															if(dbObjectList.size()>0) {
																destination.addRows("fs.files",dbObjectList,dbName);
																logger.info("Document added in fs.files :  {} ", dbObject.get("_id"));
																
																DBObject queryOnFileChunk = new BasicDBObject("files_id", new ObjectId(fileId));
																List<DBObject> fsChunkList = source.getDBObjects("fs.chunks",queryOnFileChunk,null, 0,0,null, dbName);
																List<DBObject> chunkList = new ArrayList<>(fsChunkList);
																		//(List<DBObject>) JSON.parse(new Gson().toJson(fsChunkList));
																if(chunkList.size()>0) {
																	destination.addRows("fs.chunks",chunkList,dbName);
																	logger.info("Document added in fs.chunks :  {} ", fileId);
									
																	//archMongoDao.addRow(coll,new BasicDBObject(dbObj),"communicationDBArch");//(List<DBObject>) JSON.parse(new Gson().toJson(dbObj)));
																	logger.info("Document added in coll :  {} ", dbObject.get("_id"));
																	source.deleteRow("fs.chunks",queryOnFileChunk, dbName);
																}
																source.deleteRow("fs.files",queryOnFile, dbName);
																//commBoxMongoDao.deleteRow(coll,getOneObjQuery,"communicationDB");
																logger.info("Document deleted");
															}
																													
														} catch (Exception e) {
															logger.error("Error inside cleanUpForCommbox , {} ", e.getMessage());
														}
													}else {
														logger.info("fileId no valid : {}", fileId);
													}
												}
											});
										}
									}
								});
							}
							
							
						}
						try {
							//addinEmailColl(coll, dbObject, getOneObjQuery, dbObj);

							DBObject obj = new BasicDBObject(dbObj);
							destination.addRow(coll, obj,dbName);
							logger.info("Document added in coll :  {} ", dbObject.get("_id"));
							source.deleteRow(coll,getOneObjQuery, dbName);
							DBObject queryJson = new BasicDBObject("_id", emailIsertedIdList.getObjectId("_id"));
//							DBObject setquery = new BasicDBObject("insertedId", dbObj.getObjectId("_id"));
							totalMigrated = totalMigrated + dbObjectsList.size();
							DBObject setquery = new BasicDBObject("movedId", dbObjectsList.get(dbObjectsList.size() - 1).get("_id"))
									.append("totalMigrated", totalMigrated)
									.append("updatedOn", new Date());
							DBObject rowJson = new BasicDBObject("$set", setquery);
							newArchMongoDao.updateRow(NStoS3util.lastUpdatedIdColl, queryJson, rowJson, false, false, NStoS3util.lastUpdatedIdDB);
						} catch (Exception e) {
							logger.error("Error inside cleanUpForCommbox , {} ", e.getMessage());
						}
					}

				}
				queryOnId.put("$gt", dbObjectsList.get(dbObjectsList.size()-1).get("_id"));
				
				queryObject.put("_id", queryOnId);
				dbObjectsList = source.getDBObjects(coll, queryObject, projectObj, offset, len, orderObject, dbName);
			}else {
				dbObjectsList = null;
			}

			
		}
	}


	@Override
	public void deleteNotBookLeadInCarLogger(String startId, String coll, int size, Boolean timeCheck) throws Exception {
		/*logger.info("Inside deleteNotBookLeadInCarLogger : {}", startId);
		
		Date startDate = new Date();
		startDate = DateUtil.setHours(startDate, 7);
		startDate = DateUtil.setMinute(startDate, 0);
		startDate = DateUtil.setSeconds(startDate, 0);

		Date endDate = new Date();
		endDate = DateUtil.setHours(endDate, 20);
		endDate = DateUtil.setMinute(endDate, 0);
		endDate = DateUtil.setSeconds(endDate, 0);
		
		DBObject queryObject = new BasicDBObject();
		
		DBObject orderObject = new BasicDBObject("_id", 1);
		DBObject projectObj = new BasicDBObject("_id", 1);
		
		List<ObjectId> listOfIds = new ArrayList<>();
		
		int offset = 0;
		int len = size;
		
		DBObject queryForDeletId = new BasicDBObject();
		queryForDeletId.put("coll", coll);
		BasicDBObject twoWheelerDeletedId = loggerMongoDao.getDBObject("deletedIdsCarLogger",queryForDeletId);
		
		DBObject queryOnId = new BasicDBObject();
		
		//only provide startId at once we are starting the batch.
		if(!StringUtils.isEmpty(startId)) {
			queryOnId.put("$gte", new ObjectId(startId));
		}else if(twoWheelerDeletedId!=null && twoWheelerDeletedId.size()>0){
			queryOnId.put("$gte", twoWheelerDeletedId.get("deletedId"));
		}
		
		queryOnId.put("$lt", new ObjectId(DateUtil.getStringToDate(DateUtil.getFormatedDate(-11, DateUtil.DATE_FORMAT_EE_MMM_dd_hh_mm_ss_z_yyyy),DateUtil.DATE_FORMAT_EE_MMM_dd_hh_mm_ss_z_yyyy)));
		//queryOnId.put("$lt", twoWheelerDeletedId.get("endId"));
		
		queryObject.put("_id", queryOnId);
		queryObject.put("IsLeadBooked", new BasicDBObject().append("$ne", true));
		List<BasicDBObject> dbObjectsList = loggerMongoDao.getDBObjects(coll, queryObject, projectObj, offset, len, orderObject);
		logger.info("StaticMaster.isDeleteCarLoggerStop() {}", StaticMaster.isDeleteCarLoggerStop());
		while (dbObjectsList != null && dbObjectsList.size() > 0 && !StaticMaster.isDeleteCarLoggerStop()) {
			Date todayDate = new Date();
			startDate = DateUtil.setHours(todayDate, 7);
			startDate = DateUtil.setMinute(startDate, 0);
			startDate = DateUtil.setSeconds(startDate, 0);
			
			endDate = DateUtil.setHours(todayDate, 20);
			endDate = DateUtil.setMinute(endDate, 0);
			endDate = DateUtil.setSeconds(endDate, 0);
			logger.info("Today date in service: {}, start date {} , end date {}", todayDate, startDate, endDate);
			if (todayDate.before(startDate) || todayDate.after(endDate) || !timeCheck) {
				
				for (BasicDBObject res : dbObjectsList) {
					//logger.info("Id found , {} ", res.getObjectId("_id"));
					listOfIds.add(res.getObjectId("_id"));
				}
				
				DBObject queryForDeleteIds = new BasicDBObject();
				queryForDeleteIds.put("_id", new BasicDBObject().append("$in", listOfIds));
				loggerMongoDao.deleteRow(coll, queryForDeleteIds, true);
				Thread.sleep(StaticMaster.getWaitForNextDeleteBatchInCar()*1000);
				
				listOfIds = new ArrayList<>();
				logger.info(" Last Id deleted : {}", dbObjectsList.get(dbObjectsList.size()-1).getObjectId("_id"));
				DBObject queryJson;
				if(twoWheelerDeletedId!=null && twoWheelerDeletedId.size()> 0 && twoWheelerDeletedId.getObjectId("_id")!=null) {
					DBObject setquery = new BasicDBObject("deletedId", dbObjectsList.get(dbObjectsList.size()-1).getObjectId("_id"));
					queryJson = new BasicDBObject("_id", twoWheelerDeletedId.getObjectId("_id"));
					DBObject rowJson = new BasicDBObject("$set", setquery);
					loggerMongoDao.updateRow("deletedIdsCarLogger", queryJson, rowJson, false,false);
				}
					queryOnId.put("$gte", dbObjectsList.get(dbObjectsList.size()-1).getObjectId("_id"));
				
				queryObject.put("_id", queryOnId);
				queryObject.put("IsLeadBooked", new BasicDBObject().append("$ne", true));
				
				dbObjectsList = loggerMongoDao.getDBObjects(coll, queryObject, projectObj, offset, len, orderObject);
				
			}else {
				dbObjectsList = null;
			}
		}*/
		
	}
	
	@Override
	public void deleteNotBookLeadInTwLogger(String startId, String coll, int size, Boolean timeCheck) throws Exception {
		logger.info("Inside deleteNotBookLeadInTwLogger : {}", startId);
		
		Date startDate = new Date();
		startDate = DateUtil.setHours(startDate, 7);
		startDate = DateUtil.setMinute(startDate, 0);
		startDate = DateUtil.setSeconds(startDate, 0);

		Date endDate = new Date();
		endDate = DateUtil.setHours(endDate, 20);
		endDate = DateUtil.setMinute(endDate, 0);
		endDate = DateUtil.setSeconds(endDate, 0);
		
		DBObject queryObject = new BasicDBObject();
		
		DBObject orderObject = new BasicDBObject("_id", 1);
		DBObject projectObj = new BasicDBObject("_id", 1);
		
		List<ObjectId> listOfIds = new ArrayList<>();
		
		int offset = 0;
		int len = size;
		
		DBObject queryForDeletId = new BasicDBObject();
		queryForDeletId.put("coll", coll);
		BasicDBObject twoWheelerDeletedId = motorLoggerDao.getDBObject("deletedIdsCarLogger",queryForDeletId);
		
		DBObject queryOnId = new BasicDBObject();
		
		//only provide startId at once we are starting the batch.
		if(!StringUtils.isEmpty(startId)) {
			queryOnId.put("$gte", new ObjectId(startId));
		}else if(twoWheelerDeletedId!=null && twoWheelerDeletedId.size()>0){
			queryOnId.put("$gte", twoWheelerDeletedId.get("deletedId"));
		}
		
		queryOnId.put("$lt", new ObjectId(DateUtil.getStringToDate(DateUtil.getFormatedDate(-10, DateUtil.DATE_FORMAT_EE_MMM_dd_hh_mm_ss_z_yyyy),DateUtil.DATE_FORMAT_EE_MMM_dd_hh_mm_ss_z_yyyy)));
		
		queryObject.put("_id", queryOnId);
		queryObject.put("IsLeadBooked", new BasicDBObject().append("$ne", true));
		List<DBObject> dbObjectsList = motorLoggerDao.getDBObjects(coll, queryObject, projectObj, offset, len, orderObject,"TwoWheelerDB");
		logger.info("StaticMaster.isDeleteCarLoggerStop() {}", StaticMaster.isDeleteTwLoggerStop());
		while (dbObjectsList != null && dbObjectsList.size() > 0 && !StaticMaster.isDeleteTwLoggerStop()) {
			Date todayDate = new Date();
			startDate = DateUtil.setHours(todayDate, 7);
			startDate = DateUtil.setMinute(startDate, 0);
			startDate = DateUtil.setSeconds(startDate, 0);
			
			endDate = DateUtil.setHours(todayDate, 20);
			endDate = DateUtil.setMinute(endDate, 0);
			endDate = DateUtil.setSeconds(endDate, 0);
			logger.info("Today date in service: {}, start date {} , end date {}", todayDate, startDate, endDate);
			if (todayDate.before(startDate) || todayDate.after(endDate) || !timeCheck) {
				
				for (DBObject res : dbObjectsList) {
					//logger.info("Id found , {} ", res.getObjectId("_id"));
					listOfIds.add((ObjectId) res.get("_id"));
				}
				
				DBObject queryForDeleteIds = new BasicDBObject();
				queryForDeleteIds.put("_id", new BasicDBObject().append("$in", listOfIds));
				motorLoggerDao.deleteRow(coll, queryForDeleteIds, true,"TwoWheelerDB");
				Thread.sleep(StaticMaster.getWaitForNextDeleteBatchInTw()*1000);
				
				listOfIds = new ArrayList<>();
				logger.info(" Last Id deleted : {}", dbObjectsList.get(dbObjectsList.size()-1).get("_id"));
				DBObject queryJson;
				if(twoWheelerDeletedId!=null && twoWheelerDeletedId.size()> 0 && twoWheelerDeletedId.getObjectId("_id")!=null) {
					DBObject setquery = new BasicDBObject("deletedId", dbObjectsList.get(dbObjectsList.size()-1).get("_id"));
					queryJson = new BasicDBObject("_id", twoWheelerDeletedId.getObjectId("_id"));
					DBObject rowJson = new BasicDBObject("$set", setquery);
					motorLoggerDao.updateRow("deletedIdsCarLogger", queryJson, rowJson, false,false);
				}
					queryOnId.put("$gte", dbObjectsList.get(dbObjectsList.size()-1).get("_id"));
				
				queryObject.put("_id", queryOnId);
				queryObject.put("IsLeadBooked", new BasicDBObject().append("$ne", true));
				
				dbObjectsList = loggerMongoDao.getDBObjects(coll, queryObject, projectObj, offset, len, orderObject,"TwoWheelerDB");
				
			}else {
				dbObjectsList = null;
			}
		}
		
	}

	@Override
	public void deleteAppNotificationMService(String startId, String dbColl, int size, Boolean timeCheck) throws Exception {
		logger.info("Inside deleteAppNotificationMService : {}", startId);
		String[] nsArray = dbColl.split("\\.");
		String db = nsArray[0];
		String coll = nsArray[1];
		Date startDate = new Date();
		long totalMigrated = 0;
		startDate = DateUtil.setHours(startDate, 7);
		startDate = DateUtil.setMinute(startDate, 0);
		startDate = DateUtil.setSeconds(startDate, 0);

		Date endDate = new Date();
		endDate = DateUtil.setHours(endDate, 20);
		endDate = DateUtil.setMinute(endDate, 0);
		endDate = DateUtil.setSeconds(endDate, 0);
		
		DBObject queryObject = new BasicDBObject();
		
		DBObject orderObject = new BasicDBObject("_id", 1);
		//DBObject projectObj = new BasicDBObject("_id", 1);
		
		List<ObjectId> listOfIds = new ArrayList<>();
		
		int offset = 0;
		int len = size;
		
		DBObject queryForDeletId = new BasicDBObject();
		queryForDeletId.put("coll", dbColl);
		BasicDBObject mServiceDeletedId = newArchMongoDao.getDBObjectByDbName(NStoS3util.lastUpdatedIdColl,queryForDeletId, NStoS3util.lastUpdatedIdDB);
		
		DBObject queryOnId = new BasicDBObject();
		
		//only provide startId at once we are starting the batch.
		if(!StringUtils.isEmpty(startId)) {
			queryOnId.put("$gte", new ObjectId(startId));
		}else if(mServiceDeletedId!=null && mServiceDeletedId.size()>0){
			queryOnId.put("$gte", mServiceDeletedId.get("movedId"));
			totalMigrated = (long)mServiceDeletedId.get("totalMigrated");
		}
		queryOnId.put("$lt", new ObjectId(DateUtil.getStringToDate(DateUtil.getFormatedDate(-20, DateUtil.DATE_FORMAT_EE_MMM_dd_hh_mm_ss_z_yyyy),DateUtil.DATE_FORMAT_EE_MMM_dd_hh_mm_ss_z_yyyy)));
		
		queryObject.put("_id", queryOnId);
		List<DBObject> dbObjectsList = e2eMongoDao.getDBObjects(coll, queryObject, null, offset, len, orderObject,db);
		logger.info("StaticMaster.isDeleteMserviceStop() {}", StaticMaster.isDeleteMserviceStop());
		while (dbObjectsList != null && dbObjectsList.size() > 0 && !StaticMaster.isDeleteMserviceStop()) {
			Date todayDate = new Date();
			startDate = DateUtil.setHours(todayDate, 7);
			startDate = DateUtil.setMinute(startDate, 0);
			startDate = DateUtil.setSeconds(startDate, 0);
			
			endDate = DateUtil.setHours(todayDate, 20);
			endDate = DateUtil.setMinute(endDate, 0);
			endDate = DateUtil.setSeconds(endDate, 0);
			logger.info("Today date in service: {}, start date {} , end date {}", todayDate, startDate, endDate);
			if (todayDate.before(startDate) || todayDate.after(endDate) || !timeCheck) {
				List<DBObject> chunkList = new ArrayList<>(dbObjectsList);
				try {
					newArchMongoDao.addRows(coll, chunkList,"MServiceDBArch");
				}catch(Exception e) {
					if(!e.getMessage().contains("duplicate key error collection")) {
						logger.info("Error while inserting :- {}", e.getMessage());
						throw e;
					}else {
						logger.info("Error while inserting :- {}", e.getMessage());
					}
				}
				
				for (DBObject res : dbObjectsList) {
					//logger.info("Id found isDeleteMserviceStop, {} ", res.("_id"));
					listOfIds.add((ObjectId) res.get("_id"));
				}
				
				DBObject queryForDeleteIds = new BasicDBObject();
				queryForDeleteIds.put("_id", new BasicDBObject().append("$in", listOfIds));
				e2eMongoDao.deleteRow(coll, queryForDeleteIds, true,db);
				Thread.sleep(StaticMaster.getWaitForNextDeleteBatchMService()*1000);
				
				listOfIds = new ArrayList<>();
				logger.info(" Last Id deleted : {}", dbObjectsList.get(dbObjectsList.size()-1).get("_id"));
				DBObject queryJson;
				if(mServiceDeletedId!=null && mServiceDeletedId.size()> 0 && mServiceDeletedId.getObjectId("_id")!=null) {
					totalMigrated = totalMigrated + dbObjectsList.size();
					DBObject setquery = new BasicDBObject("movedId", dbObjectsList.get(dbObjectsList.size() - 1).get("_id"))
							.append("totalMigrated", totalMigrated)
							.append("updatedOn", new Date());
					queryJson = new BasicDBObject("_id", mServiceDeletedId.getObjectId("_id"));
					DBObject rowJson = new BasicDBObject("$set", setquery);
					newArchMongoDao.updateRow(NStoS3util.lastUpdatedIdColl, queryJson, rowJson, false,false,NStoS3util.lastUpdatedIdDB);
				}
					queryOnId.put("$gte", dbObjectsList.get(dbObjectsList.size()-1).get("_id"));
				
				queryObject.put("_id", queryOnId);
				dbObjectsList = e2eMongoDao.getDBObjects(coll, queryObject, null, offset, len, orderObject,db);
				
			}else {
				dbObjectsList = null;
			}
		}
		
	}
	
	public static void main(String args[]){
		//MongoDatabase runComndDb = null;	
		try {
			//MongoClientURI uri = new MongoClientURI("mongodb://localhost:27017/?");
		MongoClientURI uri = new MongoClientURI(
				"*********************************************************************************************;readPreference=secondary;replicaset=rs6;connectTimeoutMS=6000;socketTimeoutMS=6000;maxPoolSize=30");

		MongoClient mongoClient = new MongoClient(uri);
		DB db =  mongoClient.getDB("Renewal");
		System.out.println(db.getCollectionNames());
		}
		catch(Exception e)
		{
			System.out.println("dfghj"+ e.getMessage());
		}
		
	}

	@Override
	public void updateChatUserToken(String db, String source, boolean timeCheck) {
		int offset =0;
		int len =1000;
		List<String> listOfIds = new ArrayList<>();
		DBObject orderObject = new BasicDBObject("_id", 1);
		DBObject projectObj = new BasicDBObject("_id", 1);
		DBObject queryObject = new BasicDBObject("type","user");
		try {
			List<DBObject> dbObjectsList = chatMongoDao.getDBObjects("users", queryObject, projectObj, offset, len, orderObject,db);
			while (dbObjectsList != null && dbObjectsList.size() > 0 ) {
				for (DBObject res : dbObjectsList) {
					listOfIds.add((String) res.get("_id"));
				}
				
				DBObject queryJson = new BasicDBObject();
				queryJson.put("_id", new BasicDBObject().append("$in", listOfIds));
				DBObject setquery = new BasicDBObject("services.resume.loginTokens", new ArrayList<>());
				DBObject rowJson = new BasicDBObject("$set", setquery);
				
				chatMongoDao.updateRow("users", queryJson, rowJson, false, true,db);
				
				offset = offset + len;
				// get Data;
				dbObjectsList = chatMongoDao.getDBObjects("users", queryObject, projectObj, offset, len, orderObject,db);
			}
			
		}catch(Exception e) {
			logger.error("Error inside migrateCollectionForChat : {} ", e.getMessage());
		}
		
	}

	@Override
	public void migrateUaeChatLog(String srcColl, String db,String source, boolean timeCheck, String startId) {
		DBObject queryOnId = new BasicDBObject();
		DBObject queryObject = new BasicDBObject();
		Date startDate = new Date();
		startDate = DateUtil.setHours(startDate, 7);
		startDate = DateUtil.setMinute(startDate, 0);
		startDate = DateUtil.setSeconds(startDate, 0);

		Date endDate = new Date();
		endDate = DateUtil.setHours(endDate, 20);
		endDate = DateUtil.setMinute(endDate, 0);
		endDate = DateUtil.setSeconds(endDate, 0);
		
		List<ObjectId> listOfIds = new ArrayList<>();
		
		try {
			DBObject orderObject = new BasicDBObject("_id", 1);
			DBObject queryForDeletId = new BasicDBObject();
			queryForDeletId.put("coll", srcColl);
			DBObject mDeletedId = uaeJourMongoDao.getDBObject("deletedIdsUaeLog",queryForDeletId,db);
			if(!StringUtils.isEmpty(startId)) {
				queryOnId.put("$gte", new ObjectId(startId));
			}else if(mDeletedId!=null){
				queryOnId.put("$gte", mDeletedId.get("deletedId"));
			}
			
			if(srcColl.equals("emaillogs"))
			{
				queryOnId.put("$lt",new ObjectId(DateUtil.getStringToDate(
						DateUtil.getFormatedDate(-180, DateUtil.DATE_FORMAT_EE_MMM_dd_hh_mm_ss_z_yyyy),
						DateUtil.DATE_FORMAT_EE_MMM_dd_hh_mm_ss_z_yyyy)));

			}else {
				queryOnId.put("$lt",new ObjectId(DateUtil.getStringToDate(
						DateUtil.getFormatedDate(-60, DateUtil.DATE_FORMAT_EE_MMM_dd_hh_mm_ss_z_yyyy),
						DateUtil.DATE_FORMAT_EE_MMM_dd_hh_mm_ss_z_yyyy)));

			}
					
			queryObject.put("_id", queryOnId);
			List<DBObject> dbObjectsList = uaeJourMongoDao.getDBObjects(srcColl, queryObject, 0, 1000, orderObject,db);
			logger.info("StaticMaster.uaeJourMongoDao() {}", StaticMaster.isDeleteUaeJourStop());
			while (dbObjectsList != null && dbObjectsList.size() > 0 && !StaticMaster.isDeleteUaeJourStop()) {
				Date todayDate = new Date();
				startDate = DateUtil.setHours(todayDate, 7);
				startDate = DateUtil.setMinute(startDate, 0);
				startDate = DateUtil.setSeconds(startDate, 0);
				
				endDate = DateUtil.setHours(todayDate, 20);
				endDate = DateUtil.setMinute(endDate, 0);
				endDate = DateUtil.setSeconds(endDate, 0);
				logger.info("Today date in uaeJourMongoDao service: {}, start date {} , end date {}", todayDate, startDate, endDate);
				if (todayDate.before(startDate) || todayDate.after(endDate) || !timeCheck) {
					try {
						uaeArchivalDao.addRows(srcColl, dbObjectsList,db);
					}catch(Exception e) {
						if(!e.getMessage().contains("duplicate key error collection")) {
							logger.info("Error while uaeJourMongoDao inserting :- {}", e.getMessage());
							throw e;
						}else {
							logger.info("Error while uaeJourMongoDao inserting :- {}", e.getMessage());
						}
					}
					
					for (DBObject res : dbObjectsList) {
						listOfIds.add((ObjectId) res.get("_id"));
					}
					
					DBObject queryForDeleteIds = new BasicDBObject();
					queryForDeleteIds.put("_id", new BasicDBObject().append("$in", listOfIds));
					uaeJourMongoDao.deleteRow(srcColl, queryForDeleteIds, true,db);
					Thread.sleep(StaticMaster.getWaitForNextDeleteBatchMService()*1000);
					
					listOfIds = new ArrayList<>();
					logger.info(" Last Id uaeJourMongoDao deleted : {}", dbObjectsList.get(dbObjectsList.size()-1).get("_id"));
					DBObject queryJson;
					if(mDeletedId!=null && mDeletedId.get("_id")!=null) {
						DBObject setquery = new BasicDBObject("deletedId", dbObjectsList.get(dbObjectsList.size()-1).get("_id")).append("updatedOn", new Date());;
						queryJson = new BasicDBObject("_id", mDeletedId.get("_id"));
						DBObject rowJson = new BasicDBObject("$set", setquery);
						uaeJourMongoDao.updateRow("deletedIdsUaeLog", queryJson, rowJson, false,false,db);
					}
					
					queryOnId.put("$gte", dbObjectsList.get(dbObjectsList.size()-1).get("_id"));
					
					queryObject.put("_id", queryOnId);
					dbObjectsList = uaeJourMongoDao.getDBObjects(srcColl, queryObject, null, 0, 1000, orderObject,db);
					
				}else {
					dbObjectsList = null;
				}
			}
		} catch (Exception e) {
			logger.error("Error inside migrateCollectionForChat : {} ", e.getMessage());
		}

	}
	
	/**
	 * 
	 * @param coll
	 * @param size
	 * @param timeCheck
	 * @param days
	 * @param checkColl
	 * @param db
	 * @param projectObj
	 * @param query
	 * @throws Exception
	 */
	@Override
	public void deleteNotBookLeadInCarLoggerV1(String coll, Integer size, Boolean timeCheck, int days, String checkColl, String db, DBObject projectObj, DBObject queryMain) throws Exception  {
		logger.info("Inside deleteNotBookLeadInCarLogger : {}");
		
		Date startDate = new Date();
		startDate = DateUtil.setHours(startDate, 7);
		startDate = DateUtil.setMinute(startDate, 0);
		startDate = DateUtil.setSeconds(startDate, 0);

		Date endDate = new Date();
		endDate = DateUtil.setHours(endDate, 20);
		endDate = DateUtil.setMinute(endDate, 0);
		endDate = DateUtil.setSeconds(endDate, 0);
		
		DBObject queryObject = new BasicDBObject();
		
		DBObject orderObject = new BasicDBObject("_id", 1);
		
		
		List<ObjectId> listOfIds = new ArrayList<>();
		
		int offset = 0;
		int len = size;
		
		//one entry should be present in this.
		DBObject queryForDeletId = new BasicDBObject();
		//queryForDeletId.put("coll", coll);
		List<BasicDBObject> deletedIds = motorLoggerDao.getDBObjectsByDbName("deletedIdsFrmDiffColl"+coll, queryForDeletId, null, 0,1,new BasicDBObject("_id", -1),db);
		
		BasicDBObject newObject = new BasicDBObject(deletedIds.get(0));
		newObject.remove("_id");
		newObject.put("cnt", 0);
		newObject.put("cAt", new Date());
		
		motorLoggerDao.addRow("deletedIdsFrmDiffColl"+coll, newObject,db);
		
		deletedIds = motorLoggerDao.getDBObjectsByDbName("deletedIdsFrmDiffColl"+coll, queryForDeletId, null, 0,1,new BasicDBObject("_id", -1),db);
		int count = deletedIds.get(0).getInt("cnt");
		DBObject queryOnId = new BasicDBObject();
		
		//only provide startId at once we are starting the batch.
	    if(deletedIds!=null && deletedIds.size()>0){
			queryOnId.put("$gte", deletedIds.get(0).get("deletedId"));
		}
		
		queryOnId.put("$lt", new ObjectId(DateUtil.getStringToDate(DateUtil.getFormatedDate(days, DateUtil.DATE_FORMAT_EE_MMM_dd_hh_mm_ss_z_yyyy),DateUtil.DATE_FORMAT_EE_MMM_dd_hh_mm_ss_z_yyyy)));
		
		queryObject.put("_id", queryOnId);
		//queryObject.put("IsLeadBooked", new BasicDBObject().append("$ne", true));
		List<BasicDBObject> dbObjectsList = motorLoggerDao.getDBObjectsByDbNameObj(coll, queryObject, projectObj, offset, len, orderObject,db);
		logger.info("StaticMaster.isDeleteCarLoggerStop() {}", StaticMaster.isDeleteCarLoggerV1Stop());
		while (dbObjectsList != null && dbObjectsList.size() > 0 && !StaticMaster.isDeleteCarLoggerV1Stop()) {
			DBObject query = new BasicDBObject();
			long startTime = System.currentTimeMillis();
			logger.info("while entry : {}", new Date());
			Date todayDate = new Date();
			startDate = DateUtil.setHours(todayDate, 8);
			startDate = DateUtil.setMinute(startDate, 30);
			startDate = DateUtil.setSeconds(startDate, 0);
			
			endDate = DateUtil.setHours(todayDate, 20);
			endDate = DateUtil.setMinute(endDate, 0);
			endDate = DateUtil.setSeconds(endDate, 0);
			logger.info("Today date in service: {}, start date {} , end date {}", todayDate, startDate, endDate);
			if (todayDate.before(startDate) || todayDate.after(endDate) || !timeCheck) {
				logger.info("check data : {}", new Date());
				for (BasicDBObject res : dbObjectsList) {
					//logger.info("Id found , {} ", res.getObjectId("_id"));
					Set<String> queryKeys =  queryMain.keySet();
					for(String key : queryKeys) {
						query.put(key, res.get(queryMain.get(key)));
					}
					BasicDBObject result = motorLoggerDao.getDBObjectByDbName(checkColl, query,db);
					if(result==null|| result.isEmpty())
					{
						if(db.equals("SmeLog")) {
							query.put("LeadId", res.get("MatrixLeadID"));
							result = motorLoggerDao.getDBObjectByDbName(checkColl, query,db);
							if(result==null|| result.isEmpty())
							{
								listOfIds.add(res.getObjectId("_id"));
							}
						}
						else if(db.equals("SMENonEBLog")) {
							query.removeField("EnquiryId");
							query.put("MatrixLeadID", res.get("MatrixLeadID"));
							result = motorLoggerDao.getDBObjectByDbName(checkColl, query,db);
							if(result==null|| result.isEmpty())
							{
								listOfIds.add(res.getObjectId("_id"));
							}
						}
						
						else {
							listOfIds.add(res.getObjectId("_id"));
						}
						
					}else {
						logger.info("booking data found ");
					}
				}
				logger.info("delete data : {} sec", (startTime - System.currentTimeMillis())/1000 );
				
				if(listOfIds!=null&&listOfIds.size()>0){
					logger.info("data for delete size: {}",listOfIds.size());
					DBObject queryForDeleteIds = new BasicDBObject();
					queryForDeleteIds.put("_id", new BasicDBObject().append("$in", listOfIds));
					motorLoggerDao.deleteRow(coll, queryForDeleteIds, true,db);
					//Thread.sleep(StaticMaster.getWaitForNextDeleteBatchInCar()*1000);
				}
				logger.info("delete data : {} sec", (startTime - System.currentTimeMillis())/1000 );
				logger.info(" Last Id deleted : {}", dbObjectsList.get(dbObjectsList.size()-1).getObjectId("_id"));
				
				if (deletedIds != null && deletedIds.size() > 0
						&& deletedIds.get(0).getObjectId("_id") != null) {
					count += listOfIds.size();
					DBObject setquery = new BasicDBObject("deletedId",
							dbObjectsList.get(dbObjectsList.size() - 1).getObjectId("_id")).append("cnt", count);
					DBObject queryJson = new BasicDBObject("_id", deletedIds.get(0).getObjectId("_id"));
					DBObject rowJson = new BasicDBObject("$set", setquery);
					motorLoggerDao.updateRow("deletedIdsFrmDiffColl"+coll, queryJson, rowJson, false, false,db);
				}
				
				logger.info("record update: {} sec", (startTime - System.currentTimeMillis())/1000 );
				listOfIds.clear();
				
				queryOnId.put("$gte", dbObjectsList.get(dbObjectsList.size()-1).getObjectId("_id"));
				
				queryObject.put("_id", queryOnId);
				dbObjectsList = motorLoggerDao.getDBObjectsByDbNameObj(coll, queryObject, projectObj, offset, len, orderObject,db);
				
				logger.info("new data : {} sec", (startTime - System.currentTimeMillis())/1000 );
				
			}else {
				dbObjectsList = null;
			}
		}
		
	}

	@Override
	public void migrateUaeJourneyLeadXLog(String dbColl, String source, boolean timeCheck, String startId) {
		DBObject queryOnId = new BasicDBObject();
		DBObject queryObject = new BasicDBObject();
		String[] nsArray = dbColl.split("\\.");
		String db = nsArray[0];
		String coll = nsArray[1];
		List<ObjectId> listOfIds = new ArrayList<>();
		try {
			long totalMigrated = 0;
			DBObject orderObject = new BasicDBObject("_id", 1);
			DBObject queryForDeletId = new BasicDBObject();
			queryForDeletId.put("coll", dbColl);
			DBObject mDeletedId = null;
			mDeletedId = newArchMongoDao.getDBObject(NStoS3util.lastUpdatedIdColl,queryForDeletId,NStoS3util.lastUpdatedIdDB);
			if(!StringUtils.isEmpty(startId)) {
				queryOnId.put("$gte", new ObjectId(startId));
			}else if(mDeletedId!=null){
				queryOnId.put("$gte", mDeletedId.get("movedId"));
				totalMigrated = (long) mDeletedId.get("totalMigrated");

			}
			if(db.equals("LoggerDB") && coll.equals("journeylogs")) {
				queryOnId.put("$lt",new ObjectId(DateUtil.getStringToDate(
						DateUtil.getFormatedDate(-180, DateUtil.DATE_FORMAT_EE_MMM_dd_hh_mm_ss_z_yyyy),
						DateUtil.DATE_FORMAT_EE_MMM_dd_hh_mm_ss_z_yyyy)));

			}
			else if(db.equals("LoggerDB") && coll.equals("LeadXRequestLogEntry")) {
				queryOnId.put("$lt", new ObjectId(DateUtil.getStringToDate(
						DateUtil.getFormatedDate(-3, DateUtil.DATE_FORMAT_EE_MMM_dd_hh_mm_ss_z_yyyy),
						DateUtil.DATE_FORMAT_EE_MMM_dd_hh_mm_ss_z_yyyy)));

			}
			queryObject.put("_id", queryOnId);
			List<DBObject> dbObjectsList = uaeJourMongoDaoNew.getDBObjects(coll, queryObject, 0, 1000, orderObject,db);
			logger.info("StaticMaster.uaeJourMongoDao() {}", StaticMaster.isDeleteUaeJourStop());
			while (dbObjectsList != null && dbObjectsList.size() > 0 && !StaticMaster.isDeleteUaeJourStop()) {
				Date todayDate = new Date();
				Date startDate = new Date();
				startDate = DateUtil.setHours(todayDate, 7);
				startDate = DateUtil.setMinute(startDate, 0);
				startDate = DateUtil.setSeconds(startDate, 0);
				Date endDate = new Date();
				endDate = DateUtil.setHours(todayDate, 20);
				endDate = DateUtil.setMinute(endDate, 0);
				endDate = DateUtil.setSeconds(endDate, 0);
				logger.info("Today date in uaeJourMongoDao service: {}, start date {} , end date {}", todayDate, startDate, endDate);
				if (todayDate.before(startDate) || todayDate.after(endDate) || !timeCheck) {
					try {
						uaeArchivalDao.addRows(coll, dbObjectsList,db);
					}catch(Exception e) {
						if(!e.getMessage().contains("duplicate key error collection")) {
							logger.info("Error while uaeJourMongoDao inserting :- {}", e.getMessage());
							throw e;
						}else {
							logger.info("Error while uaeJourMongoDao inserting :- {}", e.getMessage());
						}
					}

					for (DBObject res : dbObjectsList) {
						listOfIds.add((ObjectId) res.get("_id"));
					}

					DBObject queryForDeleteIds = new BasicDBObject();
					queryForDeleteIds.put("_id", new BasicDBObject().append("$in", listOfIds));
					uaeJourMongoDaoNew.deleteRow(coll, queryForDeleteIds, true,db);
					Thread.sleep(StaticMaster.getWaitForNextDeleteBatchMService()*1000);

					listOfIds = new ArrayList<>();
					logger.info(" Last Id uaeJourMongoDao deleted : {}", dbObjectsList.get(dbObjectsList.size()-1).get("_id"));
					DBObject queryJson;
					if(mDeletedId!=null && mDeletedId.get("_id")!=null) {
						totalMigrated = totalMigrated + dbObjectsList.size();
						DBObject setquery = new BasicDBObject("movedId", dbObjectsList.get(dbObjectsList.size() - 1).get("_id"))
								.append("totalMigrated", totalMigrated)
								.append("updatedOn", new Date());
						queryJson = new BasicDBObject("_id", mDeletedId.get("_id"));
						DBObject rowJson = new BasicDBObject("$set", setquery);
						newArchMongoDao.updateRow(NStoS3util.lastUpdatedIdColl, queryJson, rowJson, false,false, NStoS3util.lastUpdatedIdDB);
					}

					queryOnId.put("$gte", dbObjectsList.get(dbObjectsList.size()-1).get("_id"));

					queryObject.put("_id", queryOnId);
					dbObjectsList = uaeJourMongoDaoNew.getDBObjects(coll, queryObject, null, 0, 1000, orderObject,db);

				}else {
					dbObjectsList = null;
				}
			}
		} catch (Exception e) {
			logger.error("Error inside migrateCollectionForChat : {} ", e.getMessage());
		}

	}
	
}
