/**
 * 
 */
package com.docMigration.service.impl;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import com.docMigration.dao.AppMongoCustDao;
import com.docMigration.dao.ChatMongoDao;
import com.docMigration.dao.MigrationDao;
import com.docMigration.service.MigrationService;
import com.mongodb.BasicDBList;
import com.mongodb.BasicDBObject;
import com.mongodb.DBObject;

import com.docMigration.util.AppUtil;
import com.docMigration.util.DateUtil;
import com.docMigration.util.EncodeHashUtil;

/**
 * <AUTHOR>
 *
 */
@Service
public class MigrationServiceImpl implements MigrationService {

	private final Logger logger = LoggerFactory.getLogger(MigrationServiceImpl.class);
	
	@Autowired
	private MigrationDao migrationDaoImpl;
	
	@Autowired
	private AppMongoCustDao appMongoCustDao;
	
	@Autowired
	private ChatMongoDao coreMongoDao;
	
	private static boolean isInsertJobStop = false;
	
	private static boolean isInsertPriJobStop = false;
	
	private static boolean isEncJobStop = false;
	
	private static boolean insertCustEmMobInMongoStop = false;
	
	private static boolean isGetEncJobForMongoStop = false;
	
	private static boolean isUpdCorrectEncJobStop = false;
	
	private static boolean isUpdDupEncInMongoStop = false;
	
	private static boolean isUpdForOneMblManyEncStop = false ;
	
	private static boolean isUpdNewEncInSqlFrmMongoStop = false ;
	
	private static boolean iscorrectEncInErrorColl = false;
	
	public static boolean isIscorrectEncInErrorColl() {
		return iscorrectEncInErrorColl;
	}

	public static void setIscorrectEncInErrorColl(boolean iscorrectEncInErrorColl) {
		MigrationServiceImpl.iscorrectEncInErrorColl = iscorrectEncInErrorColl;
	}

	public static boolean isInsertCustEmMobInMongoStop() {
		return insertCustEmMobInMongoStop;
	}

	public static void setInsertCustEmMobInMongoStop(boolean insertCustEmMobInMongoStop) {
		MigrationServiceImpl.insertCustEmMobInMongoStop = insertCustEmMobInMongoStop;
	}

	public static boolean isInsertPriJobStop() {
		return isInsertPriJobStop;
	}

	public static void setInsertPriJobStop(boolean isInsertPriJobStop) {
		MigrationServiceImpl.isInsertPriJobStop = isInsertPriJobStop;
	}

	public static boolean isUpdNewEncInSqlFrmMongoStop() {
		return isUpdNewEncInSqlFrmMongoStop;
	}

	public static void setUpdNewEncInSqlFrmMongoStop(boolean isUpdNewEncInSqlFrmMongoStop) {
		MigrationServiceImpl.isUpdNewEncInSqlFrmMongoStop = isUpdNewEncInSqlFrmMongoStop;
	}

	public static boolean isUpdForOneMblManyEncStop() {
		return isUpdForOneMblManyEncStop;
	}

	public static void setUpdForOneMblManyEncStop(boolean isUpdForOneMblManyEnc) {
		MigrationServiceImpl.isUpdForOneMblManyEncStop = isUpdForOneMblManyEnc;
	}

	public static boolean isUpdDupEncInMongoStop() {
		return isUpdDupEncInMongoStop;
	}

	public static void setUpdDupEncInMongoStop(boolean isUpdDupEncInMongo) {
		MigrationServiceImpl.isUpdDupEncInMongoStop = isUpdDupEncInMongo;
	}

	public static boolean isUpdCorrectEncJobStop() {
		return isUpdCorrectEncJobStop;
	}

	public static void setUpdCorrectEncJobStop(boolean isUpdCorrectEncJobStop) {
		MigrationServiceImpl.isUpdCorrectEncJobStop = isUpdCorrectEncJobStop;
	}

	public static boolean isGetEncJobForMongoStop() {
		return isGetEncJobForMongoStop;
	}

	public static void setGetEncJobForMongoStop(boolean isGetEncJobForMongoStop) {
		MigrationServiceImpl.isGetEncJobForMongoStop = isGetEncJobForMongoStop;
	}

	public static boolean isEncJobStop() {
		return isEncJobStop;
	}

	public static void setEncJobStop(boolean isEncJobStop) {
		MigrationServiceImpl.isEncJobStop = isEncJobStop;
	}

	public static boolean isInsertJobStop() {
		return isInsertJobStop;
	}

	public static void setInsertJobStop(boolean isInsertJobStop) {
		MigrationServiceImpl.isInsertJobStop = isInsertJobStop;
	}


}
