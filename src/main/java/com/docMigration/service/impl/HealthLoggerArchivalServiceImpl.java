package com.docMigration.service.impl;

import com.docMigration.dao.ArchivalMongoDao;
import com.docMigration.dao.ChatMongoDao;
import com.docMigration.dao.GenericMongoDao;
import com.docMigration.service.S3BucketService;
import com.docMigration.util.AppConstants;
import com.docMigration.util.DateUtil;
import com.docMigration.util.NStoS3util;
import com.mongodb.BasicDBObject;
import com.mongodb.DBObject;
import com.mongodb.util.JSON;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.mongodb.core.aggregation.ArrayOperators;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.sql.DataSource;
import java.util.*;

import static com.docMigration.util.CompressionUtil.compressData;
@Service
public class HealthLoggerArchivalServiceImpl{
    private final Logger logger = LoggerFactory.getLogger(HealthLoggerArchivalServiceImpl.class);

    @Qualifier("jdbcTemplateReplHealth")
    private JdbcTemplate jdbcTemplateRepl;

    @Qualifier("jdbcTemplateHealth")
    private JdbcTemplate jdbcTemplate;

    private NamedParameterJdbcTemplate namedParameterJdbcTemplate;

    @Autowired
    private DataSource dataSourceReplHealth;

    @Autowired
    private DataSource dataSourceHealth;
    @Autowired
    @Qualifier("genericMongoDao")
    private GenericMongoDao genericMongoDao;
    @Autowired
    @Qualifier("newArchivalMongoDao")
    ChatMongoDao newArchMongoDao;
    @Autowired
    private S3BucketService s3service;

    public static boolean isHealthS3DataMoveJobRunning = false;
    public static boolean isHealthS3DataMoveJobStop = false;
    String GET_BOOKINGID_PROPOSERID_LEADID_FROM_HEALTH = "select pb.BookingID,tr.UserID as LeadId,hp.ProposerId from Healthdb.hi.PreEndorsementBookingStatus as pb (nolock) " +
            "inner join Healthdb.hi.Transactions as tr (nolock) on tr.HealthBookingID=pb.BookingID " +
            "inner join Healthdb.hi.HealthProposers as hp (nolock) on pb.EnquiryId= hp.EnquiryId " +
            "WHERE pb.enquiryid = ?;";

    @PostConstruct
    public void init() {
        this.jdbcTemplateRepl = new JdbcTemplate(dataSourceReplHealth);
        this.namedParameterJdbcTemplate = new NamedParameterJdbcTemplate(dataSourceHealth);
        this.jdbcTemplate = new JdbcTemplate(dataSourceHealth);
    }
    public void moveHealthBookLeads(String dbColl, Integer size, int days, int chunkSize) {
        logger.info("Inside moveHealthBookLeads , ns: {}, size: {}, timeCheck: {}, days", dbColl, size, days);
        List<DBObject> dbObjectsList = null;
        try {
            String[] nsArray = dbColl.split("\\.");
            String db = nsArray[0];
            String coll = nsArray[1];;
            int len = size;
            DBObject queryForDeletId = new BasicDBObject();
            queryForDeletId.put("coll", dbColl);
            BasicDBObject motorDeletedId = newArchMongoDao.getDBObjectByDbName("movedIdsLogger", queryForDeletId, db);

            DBObject queryOnId = new BasicDBObject();
            //only provide startId at once we are starting the batch.
            if (motorDeletedId != null && motorDeletedId.size() > 0) {
                queryOnId.put("$gte", motorDeletedId.get("movedId"));
            }
            queryOnId.put("$lt", new ObjectId(DateUtil.getStringToDate(DateUtil.getFormatedDate(days, DateUtil.DATE_FORMAT_EE_MMM_dd_hh_mm_ss_z_yyyy), DateUtil.DATE_FORMAT_EE_MMM_dd_hh_mm_ss_z_yyyy)));
            DBObject queryObject = new BasicDBObject();
            queryObject.put("_id", queryOnId);
            BasicDBObject projection = new BasicDBObject("_id", 1).append("EnquiryID", 1);
            DBObject orderObject = new BasicDBObject("_id", 1);
            dbObjectsList = genericMongoDao.getDBObjects(coll, queryObject, projection, 0, len, orderObject, db);
            logger.info("isHealthS3DataMoveJobRunning: {}", isHealthS3DataMoveJobRunning);
            while (dbObjectsList != null && dbObjectsList.size() > 0 && !isHealthS3DataMoveJobStop) {

                List<Object> listOfIds = new ArrayList<>();
                List<Long> enquiryIds = new ArrayList<>();
                for (DBObject jsonDocument : dbObjectsList) {
                    listOfIds.add(jsonDocument.get("_id"));
                    Long enquiryID = (Long) jsonDocument.get("EnquiryID");
                    DBObject queryOnrequestId = new BasicDBObject();
                    queryOnrequestId.put("EnquiryID", enquiryID);
                    if (!enquiryIds.contains(enquiryID) && enquiryID != null && enquiryID > 0) {
                        enquiryIds.add(enquiryID);

                        int offset = 0;
                        List<DBObject> dbList = genericMongoDao.getDBObjects(coll, queryOnrequestId, offset, chunkSize, orderObject, db);
                        if (dbList.size() == chunkSize) {
                            offset = offset + chunkSize;
                            dbList = getList(coll, queryOnrequestId, offset, orderObject, db, dbList, chunkSize);
                        }
                        // get bookingId, LeadId, proposerID from health with enquiryId and get logs for that as well
                        List<Map<String, Object>>healthIdsList = jdbcTemplateRepl.queryForList(GET_BOOKINGID_PROPOSERID_LEADID_FROM_HEALTH, enquiryID);
                        if(healthIdsList != null && !healthIdsList.isEmpty()){
                            addInDbListForHealth(chunkSize, healthIdsList, coll, offset, orderObject, db, dbList);
                        }
                        String jsonString;
                        if (dbList != null && dbList.size() > 0) {
                            try {
                                jsonString = JSON.serialize(dbList);
                                // Compress JSON data
                                byte[] compressedData = compressData(jsonString);
                                //String deCompressedData = decompressData(compressedData);
                                String fileName = "eid_" + enquiryID + ".json.gz";
                                logger.info("Health fileName : " + fileName);
                                String folderName = NStoS3util.MAP_NS_TO_S3_PATH.get(dbColl);
                                String url = s3service.addByteContentWithGzip(compressedData, NStoS3util.BUCKET_NAME_LOGGER_ARCHIVAL, folderName, fileName, "application/gzip");
                                if (url.equals(AppConstants.FILE_ALREADY_EXIST)) {
                                    try {
                                        newArchMongoDao.addRows(coll, dbList, db);
                                    } catch (Exception e) {
                                        logger.error("Error while adding in moveHealthBookLeads , {}", e.getMessage());
                                    }
                                }
                                for (DBObject reqIdDoc : dbList) {
                                    listOfIds.add(reqIdDoc.get("_id"));
                                }
                            } catch (Exception e) {
                                logger.error("Error serializing DBObject: " + e.getMessage());
                                return;
                            }
                        }
                    }
                }
                enquiryIds = null;
                List<Object> subList = new ArrayList<>();
                int startIndx = 0;
                int lastIndx = 100;
                if (listOfIds.size() > 100) {
                    while (startIndx < listOfIds.size()) {
                        if (startIndx + 100 >= listOfIds.size()) {
                            lastIndx = listOfIds.size();
                        }
                        subList = listOfIds.subList(startIndx, lastIndx);
                        DBObject queryFormovedIds = new BasicDBObject();
                        queryFormovedIds.put("_id", new BasicDBObject().append("$in", subList));
                        genericMongoDao.deleteRow(coll, queryFormovedIds, true, db);
                        logger.info("deleted Health Last _id: {}", subList.get(subList.size() - 1));
                        startIndx = lastIndx;
                        lastIndx = lastIndx + 100;
                        subList = null;
                    }
                } else {
                    DBObject queryFormovedIds = new BasicDBObject();
                    queryFormovedIds.put("_id", new BasicDBObject().append("$in", listOfIds));
                    genericMongoDao.deleteRow(coll, queryFormovedIds, true, db);
                    logger.info("deleted Health Last _id: {}", listOfIds.get(listOfIds.size() - 1));
                }
                DBObject queryJson;
                if (motorDeletedId != null && motorDeletedId.size() > 0 && motorDeletedId.getObjectId("_id") != null) {
                    DBObject setquery = new BasicDBObject("movedId", dbObjectsList.get(dbObjectsList.size() - 1).get("_id")).append("updatedOn", new Date());
                    queryJson = new BasicDBObject("_id", motorDeletedId.getObjectId("_id"));
                    DBObject rowJson = new BasicDBObject("$set", setquery);
                    genericMongoDao.updateRow("movedIdsLogger", queryJson, rowJson, false, false, db);
                }
                queryOnId.put("$gt", dbObjectsList.get(dbObjectsList.size() - 1).get("_id"));
                queryObject.put("_id", queryOnId);
                dbObjectsList = newArchMongoDao.getDBObjects(coll, queryObject, projection, 0, len, orderObject, db);
            }
        }catch (Exception e) {
            logger.error("Exception caught into moveHealthBookLeads, msg: {}", e.getMessage());
        } finally {
            isHealthS3DataMoveJobRunning = false;
            isHealthS3DataMoveJobStop = false;
        }
    }

    private void addInDbListForHealth(int chunkSize, List<Map<String, Object>> healthIdsList, String coll, int offset, DBObject orderObject, String db, List<DBObject> dbList) {

        //healthIdsList can contain multiple rows
        for(Map<String, Object>healthIdsMap: healthIdsList) {
            if (healthIdsMap.get("BookingID") != null && (Long) healthIdsMap.get("BookingID") > 0) {
                long bookingID = (long) healthIdsMap.get("BookingID");
                DBObject queryOnEnquiryId = new BasicDBObject();
                queryOnEnquiryId.put("EnquiryID", bookingID);
                List<DBObject> bookingIdDBList = genericMongoDao.getDBObjects(coll, queryOnEnquiryId, offset, chunkSize, orderObject, db);
                if (bookingIdDBList.size() == chunkSize) {
                    offset = offset + chunkSize;
                    bookingIdDBList = getList(coll, queryOnEnquiryId, offset, orderObject, db, bookingIdDBList, chunkSize);
                }
                if (bookingIdDBList != null && bookingIdDBList.size() > 0) {
                    dbList.addAll(bookingIdDBList);
                }
            }
            if (healthIdsMap.get("LeadId") != null && (Long) healthIdsMap.get("LeadId") > 0) {
                long leadID = (long) healthIdsMap.get("LeadId");
                DBObject queryOnEnquiryId = new BasicDBObject();
                queryOnEnquiryId.put("EnquiryID", leadID);
                List<DBObject> leadIDDBList = genericMongoDao.getDBObjects(coll, queryOnEnquiryId, offset, chunkSize, orderObject, db);
                if (leadIDDBList.size() == chunkSize) {
                    offset = offset + chunkSize;
                    leadIDDBList = getList(coll, queryOnEnquiryId, offset, orderObject, db, leadIDDBList, chunkSize);
                }
                if (leadIDDBList != null && leadIDDBList.size() > 0) {
                    dbList.addAll(leadIDDBList);
                }
            }
            if (healthIdsMap.get("ProposerId") != null && (Integer) healthIdsMap.get("ProposerId") > 0) {
                long proposerID = (int) healthIdsMap.get("ProposerId");
                DBObject queryOnEnquiryId = new BasicDBObject();
                queryOnEnquiryId.put("EnquiryID", proposerID);
                List<DBObject> proposerIDDBList = genericMongoDao.getDBObjects(coll, queryOnEnquiryId, offset, chunkSize, orderObject, db);
                if (proposerIDDBList.size() == chunkSize) {
                    offset = offset + chunkSize;
                    proposerIDDBList = getList(coll, queryOnEnquiryId, offset, orderObject, db, proposerIDDBList, chunkSize);
                }
                if (proposerIDDBList != null && proposerIDDBList.size() > 0) {
                    dbList.addAll(proposerIDDBList);
                }
            }
        }
    }

    private List<DBObject> getList(String coll, DBObject queryOnrequestId, int offset, DBObject orderObject, String db, List<DBObject> resultList, int chunkSize) {

        try {
            List<DBObject> dataList = genericMongoDao.getDBObjects(coll, queryOnrequestId, offset, chunkSize, orderObject, db);
            while (dataList.size() > 0){
                resultList.addAll(dataList);
                offset += chunkSize;
                dataList = genericMongoDao.getDBObjects(coll, queryOnrequestId, offset, chunkSize, orderObject, db);
            }
        } catch (Exception e) {
            logger.error("Exception while getList");
        }
        return resultList;
    }
}
