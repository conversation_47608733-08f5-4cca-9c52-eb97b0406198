package com.docMigration.service.impl;

import java.util.*;

import javax.annotation.PostConstruct;
import javax.sql.DataSource;

import com.docMigration.dao.*;
import com.docMigration.util.AppConstants;
import com.docMigration.util.NStoS3util;
import com.mongodb.util.JSON;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import com.docMigration.cleanup.entity.StaticMaster;
import com.docMigration.service.PbpartnersArchivalService;
import com.docMigration.service.S3BucketService;
import com.docMigration.util.DateUtil;
import com.mongodb.BasicDBObject;
import com.mongodb.DBObject;

import static com.docMigration.util.CompressionUtil.compressData;


@Service
public class PbPartnersArchivalServiceImpl implements PbpartnersArchivalService{
	
	private final Logger logger = LoggerFactory.getLogger(PbPartnersArchivalServiceImpl.class);

	@Autowired
	private S3BucketService s3BucketService;

	@Autowired
	@Qualifier("newArchivalMongoDao")
	ChatMongoDao newArchMongoDao;
	@Autowired
	@Qualifier("pbpSupplierDao")
	private GenericMongoDao pbpSupplierDao;
	@Autowired
	@Qualifier("pospArchivalMongoDao")
	private ChatMongoDao loggerMongoDao;
	@Autowired
	@Qualifier("pospLoggerMongoDao")
	private ChatMongoDao pospLoggerMongoDao;
	@Value("${aws.bucket_name}")
	private String bucketName;
	
	@Value("${doc.schedular.count}")
	private int count;
	
	@Qualifier("jdbcTemplate")
	@Autowired
    private JdbcTemplate jdbcTemplate;
	
	private DataSource dataSource;

	public static boolean isPBPCarLogS3JobRunning = false;
	public static boolean isPBPTwLogS3JobRunning = false;
	public static boolean isPBPCarLogS3JobStop = false;
	public static boolean isPBPTwLogS3JobStop = false;
	public static boolean isPBPTwQuoteDBJobStop = false;
	public static boolean isPBPTwQuoteDBJobRunning = false;
	public static boolean isPBPCarQuoteDBJobStop = false;
	public static boolean isPBPCarQuoteDBJobRunning = false;

	@PostConstruct
	public void init() {
		//namedParameterJdbcTemplate = new NamedParameterJdbcTemplate(dataSource);
	}
	

	/**
	 * 
	 * @param coll
	 * @param size
	 * @param timeCheck
	 * @param days
	 * @param checkColl
	 * @param db
	 * @param projectObj
	 * @param query
	 * @throws Exception
	 */
	@Override
	public void deleteNotBookLeadInCarLogger(String coll, Integer size, Boolean timeCheck, int days, String checkColl, String db, DBObject projectObj, DBObject query) throws Exception  {
		logger.info("Inside deleteNotBookLeadInCarLogger : {}");
		
		Date startDate = new Date();
		startDate = DateUtil.setHours(startDate, 7);
		startDate = DateUtil.setMinute(startDate, 0);
		startDate = DateUtil.setSeconds(startDate, 0);

		Date endDate = new Date();
		endDate = DateUtil.setHours(endDate, 20);
		endDate = DateUtil.setMinute(endDate, 0);
		endDate = DateUtil.setSeconds(endDate, 0);
		
		DBObject queryObject = new BasicDBObject();
		
		DBObject orderObject = new BasicDBObject("_id", 1);
		
		
		List<ObjectId> listOfIds = new ArrayList<>();
		
		int offset = 0;
		int len = size;
		
		//one entry should be present in this.
		DBObject queryForDeletId = new BasicDBObject();
		//queryForDeletId.put("coll", coll);
		List<BasicDBObject> deletedIds = loggerMongoDao.getDBObjectsByDbName("deletedIdsFrmDiffColl"+coll, queryForDeletId, null, 0,1,new BasicDBObject("_id", -1),db);
		
		BasicDBObject newObject = new BasicDBObject(deletedIds.get(0));
		newObject.remove("_id");
		newObject.append("cnt", 0);
		newObject.append("cAt", new Date());
		
		loggerMongoDao.addRow("deletedIdsFrmDiffColl"+coll, newObject,db);
		
		deletedIds = loggerMongoDao.getDBObjectsByDbName("deletedIdsFrmDiffColl"+coll, queryForDeletId, null, 0,1,new BasicDBObject("_id", -1),db);
		int count = deletedIds.get(0).getInt("cnt");
		DBObject queryOnId = new BasicDBObject();
		
		//only provide startId at once we are starting the batch.
	    if(deletedIds!=null && deletedIds.size()>0){
			queryOnId.put("$gte", deletedIds.get(0).get("deletedId"));
		}
		
		queryOnId.put("$lt", new ObjectId(DateUtil.getStringToDate(DateUtil.getFormatedDate(days, DateUtil.DATE_FORMAT_EE_MMM_dd_hh_mm_ss_z_yyyy),DateUtil.DATE_FORMAT_EE_MMM_dd_hh_mm_ss_z_yyyy)));
		
		queryObject.put("_id", queryOnId);
		//queryObject.put("IsLeadBooked", new BasicDBObject().append("$ne", true));
		List<BasicDBObject> dbObjectsList = loggerMongoDao.getDBObjectsByDbNameObj(coll, queryObject, projectObj, offset, len, orderObject,db);
		logger.info("StaticMaster.isDeleteCarLoggerStop() {}", StaticMaster.isDeletePbpartnerLoggerStop());
		while (dbObjectsList != null && dbObjectsList.size() > 0 && !StaticMaster.isDeletePbpartnerLoggerStop()) {
			long startTime = System.currentTimeMillis();
			logger.info("while entry : {}", new Date());
			Date todayDate = new Date();
			startDate = DateUtil.setHours(todayDate, 8);
			startDate = DateUtil.setMinute(startDate, 30);
			startDate = DateUtil.setSeconds(startDate, 0);
			
			endDate = DateUtil.setHours(todayDate, 20);
			endDate = DateUtil.setMinute(endDate, 0);
			endDate = DateUtil.setSeconds(endDate, 0);
			logger.info("Today date in service: {}, start date {} , end date {}", todayDate, startDate, endDate);
			if (todayDate.before(startDate) || todayDate.after(endDate) || !timeCheck) {
				logger.info("check data : {}", new Date());
				for (BasicDBObject res : dbObjectsList) {
					//logger.info("Id found , {} ", res.getObjectId("_id"));
					Set<String> queryKeys =  query.keySet();
					for(String key : queryKeys) {
						query.put(key, res.get(query.get(key)));
					}
					BasicDBObject result = loggerMongoDao.getDBObjectByDbName(checkColl, query,db);
					if(result==null|| result.isEmpty())
					{
						if(db.equals("SmeLog")) {
							query.put("LeadId", res.get("MatrixLeadID"));
							result = loggerMongoDao.getDBObjectByDbName(checkColl, query,db);
							if(result==null|| result.isEmpty())
							{
								listOfIds.add(res.getObjectId("_id"));
							}
						}
						else if(db.equals("SMENonEBLog")) {
							query.removeField("EnquiryId");
							query.put("MatrixLeadID", res.get("MatrixLeadID"));
							result = loggerMongoDao.getDBObjectByDbName(checkColl, query,db);
							if(result==null|| result.isEmpty())
							{
								listOfIds.add(res.getObjectId("_id"));
							}
						}
						
						else {
							listOfIds.add(res.getObjectId("_id"));
						}
						
						
					}
				}
				logger.info("delete data : {} sec", (startTime - System.currentTimeMillis())/1000 );
				
				if(listOfIds!=null&&listOfIds.size()>0){
					DBObject queryForDeleteIds = new BasicDBObject();
					queryForDeleteIds.put("_id", new BasicDBObject().append("$in", listOfIds));
					loggerMongoDao.deleteRow(coll, queryForDeleteIds, true,db);
					//Thread.sleep(StaticMaster.getWaitForNextDeleteBatchInCar()*1000);
				}
				logger.info("delete data : {} sec", (startTime - System.currentTimeMillis())/1000 );
				logger.info(" Last Id deleted : {}", dbObjectsList.get(dbObjectsList.size()-1).getObjectId("_id"));
				
				if (deletedIds != null && deletedIds.size() > 0
						&& deletedIds.get(0).getObjectId("_id") != null) {
					count += listOfIds.size();
					DBObject setquery = new BasicDBObject("deletedId",
							dbObjectsList.get(dbObjectsList.size() - 1).getObjectId("_id")).append("cnt", count);
					DBObject queryJson = new BasicDBObject("_id", deletedIds.get(0).getObjectId("_id"));
					DBObject rowJson = new BasicDBObject("$set", setquery);
					loggerMongoDao.updateRow("deletedIdsFrmDiffColl"+coll, queryJson, rowJson, false, false,db);
				}
				
				logger.info("record update: {} sec", (startTime - System.currentTimeMillis())/1000 );
				listOfIds.clear();
				
				queryOnId.put("$gte", dbObjectsList.get(dbObjectsList.size()-1).getObjectId("_id"));
				
				queryObject.put("_id", queryOnId);
				dbObjectsList = loggerMongoDao.getDBObjectsByDbNameObj(coll, queryObject, projectObj, offset, len, orderObject,db);
				
				logger.info("new data : {} sec", (startTime - System.currentTimeMillis())/1000 );
				
			}else {
				dbObjectsList = null;
			}
		}
		
	}

    @Override
	public void moveBookLeads(String coll, Integer size, Boolean timeCheck, String db, int days) throws Exception {

		logger.info("Inside moveBookLeads");
		
		Date startDate = new Date();
		startDate = DateUtil.setHours(startDate, 7);
		startDate = DateUtil.setMinute(startDate, 0);
		startDate = DateUtil.setSeconds(startDate, 0);

		Date endDate = new Date();
		endDate = DateUtil.setHours(endDate, 20);
		endDate = DateUtil.setMinute(endDate, 0);
		endDate = DateUtil.setSeconds(endDate, 0);
		
		DBObject queryObject = new BasicDBObject();
		
		DBObject orderObject = new BasicDBObject("_id", 1);
		DBObject projectObj = new BasicDBObject("_id", 1);
		
		List<Object> listOfIds = new ArrayList<>();
		
		int offset = 0;
		int len = size;
		
		DBObject queryForDeletId = new BasicDBObject();
		queryForDeletId.put("coll", coll);
		BasicDBObject twoWheelerDeletedId = loggerMongoDao.getDBObjectByDbName("movedIdsLogger",queryForDeletId,db);
		
		DBObject queryOnId = new BasicDBObject();
		
		//only provide startId at once we are starting the batch.
		if(twoWheelerDeletedId!=null && twoWheelerDeletedId.size()>0){
			queryOnId.put("$gte", twoWheelerDeletedId.get("movedId"));
		}
		
		queryOnId.put("$lt", new ObjectId(DateUtil.getStringToDate(DateUtil.getFormatedDate(days, DateUtil.DATE_FORMAT_EE_MMM_dd_hh_mm_ss_z_yyyy),DateUtil.DATE_FORMAT_EE_MMM_dd_hh_mm_ss_z_yyyy)));
		//queryOnId.put("$lt", twoWheelerDeletedId.get("endId"));
		
		queryObject.put("_id", queryOnId);
		List<DBObject> dbObjectsList = loggerMongoDao.getDBObjects(coll, queryObject, null, offset, len, orderObject,db);
		logger.info("StaticMaster.isMoveCarLoggerDataStop() {}", StaticMaster.isMoveCarLoggerDataStop());
		while (dbObjectsList != null && dbObjectsList.size() > 0 && !StaticMaster.isMoveCarLoggerDataStop()) {
			Date todayDate = new Date();
			startDate = DateUtil.setHours(todayDate, 7);
			startDate = DateUtil.setMinute(startDate, 0);
			startDate = DateUtil.setSeconds(startDate, 0);
			
			endDate = DateUtil.setHours(todayDate, 20);
			endDate = DateUtil.setMinute(endDate, 0);
			endDate = DateUtil.setSeconds(endDate, 0);
			logger.info("Today date in service: {}, start date {} , end date {}", todayDate, startDate, endDate);
			if (todayDate.before(startDate) || todayDate.after(endDate) || !timeCheck) {
				
				Date date = null;
				for (DBObject res : dbObjectsList) {
					//logger.info("Id found , {} ", res.getObjectId("_id"));
					listOfIds.add(res.get("_id"));
					date = ((ObjectId)res.get("_id")).getDate();
				}
				
				DBObject queryFormovedIds = new BasicDBObject();
				queryFormovedIds.put("_id", new BasicDBObject().append("$in", listOfIds));
				
				try {
					newArchMongoDao.addRows(coll+"_"+DateUtil.parseDate(date, DateUtil.DATE_FORMAT_YYYY_MM_DD), dbObjectsList, db);
				}catch(Exception e) {
					logger.error("Error while adding in moveBookLeads , {}", e.getMessage());
					if(!e.getMessage().contains("timeout")) {
						throw e;
					}
				}
				
				loggerMongoDao.deleteRow(coll, queryFormovedIds, true,db);
				Thread.sleep(StaticMaster.getWaitForNextDeleteBatchInCar()*1000);
				
				listOfIds = new ArrayList<>();
				logger.info(" Last Id deleted : {}", dbObjectsList.get(dbObjectsList.size()-1).get("_id"));
				DBObject queryJson;
				if(twoWheelerDeletedId!=null && twoWheelerDeletedId.size()> 0 && twoWheelerDeletedId.getObjectId("_id")!=null) {
					DBObject setquery = new BasicDBObject("movedId", dbObjectsList.get(dbObjectsList.size()-1).get("_id"));
					queryJson = new BasicDBObject("_id", twoWheelerDeletedId.getObjectId("_id"));
					DBObject rowJson = new BasicDBObject("$set", setquery);
					loggerMongoDao.updateRow("movedIdsLogger", queryJson, rowJson, false,false,db);
				}
				/*
					 * else { //queryJson = new BasicDBObject("_id",
					 * twoWheelerDeletedId.getObjectId("_id")); DBObject rowJson = new
					 * BasicDBObject("deletedId",
					 * dbObjectsList.get(dbObjectsList.size()-1).getObjectId("_id")).append("coll",
					 * coll); loggerMongoDao.insertRow("deletedIdsCarLogger",rowJson); }
					 */
				
				//twoWheelerDeletedId = loggerMongoDao.getDBObject("deletedIdsCarLogger",queryForDeletId);
				//if(twoWheelerDeletedId!=null && twoWheelerDeletedId.size()>0){
					queryOnId.put("$gt", dbObjectsList.get(dbObjectsList.size()-1).get("_id"));
				//}
				
				queryObject.put("_id", queryOnId);
				//queryObject.put("IsLeadBooked", new BasicDBObject().append("$ne", true));
				
				dbObjectsList = loggerMongoDao.getDBObjects(coll, queryObject, null, offset, len, orderObject,db);
				
			}else {
				dbObjectsList = null;
			}
		}
		
	
	}
    
    @Override
	public void deleteNotBookLeadInTwLogger(String startId, String coll, int size, Boolean timeCheck,String db,int days) throws Exception {
		logger.info("Inside deleteNotBookLeadInTwLogger : {}", startId);
		
		Date startDate = new Date();
		startDate = DateUtil.setHours(startDate, 7);
		startDate = DateUtil.setMinute(startDate, 0);
		startDate = DateUtil.setSeconds(startDate, 0);

		Date endDate = new Date();
		endDate = DateUtil.setHours(endDate, 20);
		endDate = DateUtil.setMinute(endDate, 0);
		endDate = DateUtil.setSeconds(endDate, 0);
		
		DBObject queryObject = new BasicDBObject();
		
		DBObject orderObject = new BasicDBObject("_id", 1);
		DBObject projectObj = new BasicDBObject("_id", 1);
		
		List<ObjectId> listOfIds = new ArrayList<>();
		
		int offset = 0;
		int len = size;
		
		DBObject queryForDeletId = new BasicDBObject();
		queryForDeletId.put("coll", coll);
		BasicDBObject twoWheelerDeletedId = loggerMongoDao.getDBObjectByDbName("deletedIds",queryForDeletId,db);
		
		DBObject queryOnId = new BasicDBObject();
		
		//only provide startId at once we are starting the batch.
		if(!StringUtils.isEmpty(startId)) {
			queryOnId.put("$gte", new ObjectId(startId));
		}else if(twoWheelerDeletedId!=null && twoWheelerDeletedId.size()>0){
			queryOnId.put("$gte", twoWheelerDeletedId.get("deletedId"));
		}
		
		queryOnId.put("$lt", new ObjectId(DateUtil.getStringToDate(DateUtil.getFormatedDate(-days, DateUtil.DATE_FORMAT_EE_MMM_dd_hh_mm_ss_z_yyyy),DateUtil.DATE_FORMAT_EE_MMM_dd_hh_mm_ss_z_yyyy)));
		
		queryObject.put("_id", queryOnId);
		queryObject.put("IsLeadBooked", new BasicDBObject().append("$ne", true));
		List<DBObject> dbObjectsList = loggerMongoDao.getDBObjects(coll, queryObject, projectObj, offset, len, orderObject,db);
		logger.info("StaticMaster.isDeletePbPartnersCarLoggerStop() {}", StaticMaster.isDeletePbPartnersTwLoggerStop());
		while (dbObjectsList != null && dbObjectsList.size() > 0 && !StaticMaster.isDeletePbPartnersTwLoggerStop()) {
			Date todayDate = new Date();
			startDate = DateUtil.setHours(todayDate, 7);
			startDate = DateUtil.setMinute(startDate, 0);
			startDate = DateUtil.setSeconds(startDate, 0);
			
			endDate = DateUtil.setHours(todayDate, 20);
			endDate = DateUtil.setMinute(endDate, 0);
			endDate = DateUtil.setSeconds(endDate, 0);
			logger.info("Today date in service: {}, start date {} , end date {}", todayDate, startDate, endDate);
			if (todayDate.before(startDate) || todayDate.after(endDate) || !timeCheck) {
				
				for (DBObject res : dbObjectsList) {
					//logger.info("Id found , {} ", res.getObjectId("_id"));
					listOfIds.add((ObjectId) res.get("_id"));
				}
				
				DBObject queryForDeleteIds = new BasicDBObject();
				queryForDeleteIds.put("_id", new BasicDBObject().append("$in", listOfIds));
				loggerMongoDao.deleteRow(coll, queryForDeleteIds, true,db);
				Thread.sleep(StaticMaster.getWaitForNextDeleteBatchInTw()*1000);
				
				listOfIds = new ArrayList<>();
				logger.info(" Last Id deleted : {}", dbObjectsList.get(dbObjectsList.size()-1).get("_id"));
				DBObject queryJson;
				if(twoWheelerDeletedId!=null && twoWheelerDeletedId.size()> 0 && twoWheelerDeletedId.getObjectId("_id")!=null) {
					DBObject setquery = new BasicDBObject("deletedId", dbObjectsList.get(dbObjectsList.size()-1).get("_id"));
					queryJson = new BasicDBObject("_id", twoWheelerDeletedId.getObjectId("_id"));
					DBObject rowJson = new BasicDBObject("$set", setquery);
					loggerMongoDao.updateRow("deletedIds", queryJson, rowJson, false,false,db);
				}
					queryOnId.put("$gte", dbObjectsList.get(dbObjectsList.size()-1).get("_id"));
				
				queryObject.put("_id", queryOnId);
				queryObject.put("IsLeadBooked", new BasicDBObject().append("$ne", true));
				
				dbObjectsList = loggerMongoDao.getDBObjects(coll, queryObject, projectObj, offset, len, orderObject,db);
				
			}else {
				dbObjectsList = null;
			}
		}
		
	}
    
    @Override
	public void deleteNotBookLeadInCarLogger(String startId, String coll, int size, Boolean timeCheck,String db,int days) throws Exception {
		logger.info("Inside deleteNotBookLeadInCarLogger : {}", startId);
		
		Date startDate = new Date();
		startDate = DateUtil.setHours(startDate, 7);
		startDate = DateUtil.setMinute(startDate, 0);
		startDate = DateUtil.setSeconds(startDate, 0);

		Date endDate = new Date();
		endDate = DateUtil.setHours(endDate, 20);
		endDate = DateUtil.setMinute(endDate, 0);
		endDate = DateUtil.setSeconds(endDate, 0);
		
		DBObject queryObject = new BasicDBObject();
		
		DBObject orderObject = new BasicDBObject("_id", 1);
		DBObject projectObj = new BasicDBObject("_id", 1);
		
		List<ObjectId> listOfIds = new ArrayList<>();
		
		int offset = 0;
		int len = size;
		
		DBObject queryForDeletId = new BasicDBObject();
		queryForDeletId.put("coll", coll);
		BasicDBObject twoWheelerDeletedId = loggerMongoDao.getDBObjectByDbName("deletedIds",queryForDeletId,db);
		
		DBObject queryOnId = new BasicDBObject();
		
		//only provide startId at once we are starting the batch.
		if(!StringUtils.isEmpty(startId)) {
			queryOnId.put("$gte", new ObjectId(startId));
		}else if(twoWheelerDeletedId!=null && twoWheelerDeletedId.size()>0){
			queryOnId.put("$gte", twoWheelerDeletedId.get("deletedId"));
		}
		
		queryOnId.put("$lt", new ObjectId(DateUtil.getStringToDate(DateUtil.getFormatedDate(-days, DateUtil.DATE_FORMAT_EE_MMM_dd_hh_mm_ss_z_yyyy),DateUtil.DATE_FORMAT_EE_MMM_dd_hh_mm_ss_z_yyyy)));
		//queryOnId.put("$lt", twoWheelerDeletedId.get("endId"));
		
		queryObject.put("_id", queryOnId);
		queryObject.put("IsLeadBooked", new BasicDBObject().append("$ne", true));
		List<BasicDBObject> dbObjectsList = loggerMongoDao.getDBObjectsByDbNameObj(coll, queryObject, projectObj, offset, len, orderObject,db);
		logger.info("StaticMaster.isDeleteCarLoggerStop() {}", StaticMaster.isDeletePbPartnersCarLoggerStop());
		while (dbObjectsList != null && dbObjectsList.size() > 0 && !StaticMaster.isDeletePbPartnersCarLoggerStop()) {
			Date todayDate = new Date();
			startDate = DateUtil.setHours(todayDate, 7);
			startDate = DateUtil.setMinute(startDate, 0);
			startDate = DateUtil.setSeconds(startDate, 0);
			
			endDate = DateUtil.setHours(todayDate, 20);
			endDate = DateUtil.setMinute(endDate, 0);
			endDate = DateUtil.setSeconds(endDate, 0);
			logger.info("Today date in service: {}, start date {} , end date {}", todayDate, startDate, endDate);
			if (todayDate.before(startDate) || todayDate.after(endDate) || !timeCheck) {
				
				for (BasicDBObject res : dbObjectsList) {
					//logger.info("Id found , {} ", res.getObjectId("_id"));
					listOfIds.add(res.getObjectId("_id"));
				}
				
				DBObject queryForDeleteIds = new BasicDBObject();
				queryForDeleteIds.put("_id", new BasicDBObject().append("$in", listOfIds));
				loggerMongoDao.deleteRow(coll, queryForDeleteIds, true,db);
				Thread.sleep(StaticMaster.getWaitForNextDeleteBatchInCar()*1000);
				
				listOfIds = new ArrayList<>();
				logger.info(" Last Id deleted : {}", dbObjectsList.get(dbObjectsList.size()-1).getObjectId("_id"));
				DBObject queryJson;
				if(twoWheelerDeletedId!=null && twoWheelerDeletedId.size()> 0 && twoWheelerDeletedId.getObjectId("_id")!=null) {
					DBObject setquery = new BasicDBObject("deletedId", dbObjectsList.get(dbObjectsList.size()-1).getObjectId("_id"));
					queryJson = new BasicDBObject("_id", twoWheelerDeletedId.getObjectId("_id"));
					DBObject rowJson = new BasicDBObject("$set", setquery);
					loggerMongoDao.updateRow("deletedIdsFrmDiffCollReqResLog", queryJson, rowJson, false,false,db);
				}
				/*
					 * else { //queryJson = new BasicDBObject("_id",
					 * twoWheelerDeletedId.getObjectId("_id")); DBObject rowJson = new
					 * BasicDBObject("deletedId",
					 * dbObjectsList.get(dbObjectsList.size()-1).getObjectId("_id")).append("coll",
					 * coll); loggerMongoDao.insertRow("deletedIdsCarLogger",rowJson); }
					 */
				
				//twoWheelerDeletedId = loggerMongoDao.getDBObject("deletedIdsCarLogger",queryForDeletId);
				//if(twoWheelerDeletedId!=null && twoWheelerDeletedId.size()>0){
					queryOnId.put("$gte", dbObjectsList.get(dbObjectsList.size()-1).getObjectId("_id"));
				//}
				
				queryObject.put("_id", queryOnId);
				queryObject.put("IsLeadBooked", new BasicDBObject().append("$ne", true));
				
				dbObjectsList = loggerMongoDao.getDBObjectsByDbNameObj(coll, queryObject, projectObj, offset, len, orderObject,db);
				
			}else {
				dbObjectsList = null;
			}
		}
		
	}

	@Override
	public void movePbPartnersMotorBookLeads(String dbColl, Integer size, int days, int chunkSize) {
		logger.info("Inside movePbPartnersMotorBookLeads");
		List<DBObject> dbObjectsList = null;
		try {
			String[] nsArray = dbColl.split("\\.");
			String db = nsArray[0];
			String coll = nsArray[1];;
			DBObject orderObject = new BasicDBObject("_id", 1);
			int offset = 0;
			int len = size;
			long totalMigrated = 0;

			DBObject queryForDeletId = new BasicDBObject();
			queryForDeletId.put("coll", dbColl);
			BasicDBObject motorDeletedId = newArchMongoDao.getDBObjectByDbName(NStoS3util.lastUpdatedIdColl, queryForDeletId, NStoS3util.lastUpdatedIdDB);
			DBObject queryOnId = new BasicDBObject();
			//only provide startId at once we are starting the batch.
			if (motorDeletedId != null && motorDeletedId.size() > 0) {
				queryOnId.put("$gte", motorDeletedId.get("movedId"));
				totalMigrated = (long)motorDeletedId.get("totalMigrated");
			}

			queryOnId.put("$lt", new ObjectId(DateUtil.getStringToDate(DateUtil.getFormatedDate(days, DateUtil.DATE_FORMAT_EE_MMM_dd_hh_mm_ss_z_yyyy), DateUtil.DATE_FORMAT_EE_MMM_dd_hh_mm_ss_z_yyyy)));
			DBObject queryObject = new BasicDBObject();
			queryObject.put("_id", queryOnId);
			BasicDBObject projection = new BasicDBObject("_id", 1).append("EnquiryID", 1).append("IsLeadBooked",1);
			dbObjectsList = pospLoggerMongoDao.getDBObjects(coll, queryObject, projection, offset, len, orderObject, db);
			logger.info("isPBPCarLogS3JobStop: {}, isPBPCarLogS3JobRunning: {}", isPBPCarLogS3JobStop, isPBPCarLogS3JobRunning);
			while (dbObjectsList != null && dbObjectsList.size() > 0 && !isPBPCarLogS3JobStop) {

				List<Object> listOfIds = new ArrayList<>();
				List<Long> enquiryIds = new ArrayList<>();
				for (DBObject jsonDocument : dbObjectsList) {
					listOfIds.add(jsonDocument.get("_id"));
					Long enquiryID = (Long) jsonDocument.get("EnquiryID");
					Boolean isLeadBooked = (Boolean) jsonDocument.get("IsLeadBooked");
					if (isLeadBooked != null && isLeadBooked && !enquiryIds.contains(enquiryID) && enquiryID != null && enquiryID > 0) {
						enquiryIds.add(enquiryID);
						DBObject queryOnEnqId = new BasicDBObject();
						queryOnEnqId.put("EnquiryID", enquiryID);
						DBObject queryOnrequestId = new BasicDBObject().append("EnquiryID", enquiryID);
						offset = 0;
						List<DBObject> dbList = pospLoggerMongoDao.getDBObjects(coll, queryOnrequestId, 0, chunkSize, null, db);
						if (dbList.size() == chunkSize) {
							offset = offset + chunkSize;
							dbList = getList(coll, queryOnrequestId, offset, db, dbList, chunkSize);

						}
						String jsonString;
						if (dbList != null && dbList.size() > 0) {
							try {
								jsonString = JSON.serialize(dbList);
								// Compress JSON data
								byte[] compressedData = compressData(jsonString);
								//String deCompressedData = decompressData(compressedData);
								String fileName = enquiryID + ".json.gz";
								logger.info("PBP Car fileName : " + fileName);
								String url = s3BucketService.addByteContentWithGzip(compressedData, NStoS3util.BUCKET_NAME_LOGGER_ARCHIVAL_PB_PARTNER, NStoS3util.MAP_NS_TO_S3_PATH.get(dbColl), fileName, "application/gzip");
								if (url.equals(AppConstants.FILE_ALREADY_EXIST)) {
									try {
										newArchMongoDao.addRows(coll, dbList, db);
									} catch (Exception e) {
										logger.error("Error while adding in movePbPartnersMotorBookLeads , {}", e.getMessage());
									}
								}else if (url !=null){
									logger.info("File Has uploaded on S3 name: {}, url: {}", fileName, url);
								}else{
									logger.info("url is name: {}, url: {}", fileName, url);
								}
								for (DBObject reqIdDoc : dbList) {
									listOfIds.add(reqIdDoc.get("_id"));
								}
							} catch (Exception e) {
								logger.error("Error serializing DBObject: msg: {}", e.getMessage());
								return;
							}
						}
					}
				}
				enquiryIds = null;
				List<Object> subList = new ArrayList<>();
				int startIndx = 0;
				int lastIndx = 100;
				if (listOfIds.size() > 100) {
					while (startIndx < listOfIds.size()) {
						if (startIndx + 100 >= listOfIds.size()) {
							lastIndx = listOfIds.size();
						}
						subList = listOfIds.subList(startIndx, lastIndx);
						DBObject queryFormovedIds = new BasicDBObject();
						queryFormovedIds.put("_id", new BasicDBObject().append("$in", subList));
						pospLoggerMongoDao.deleteRow(coll, queryFormovedIds, true, db);
						logger.info("PBP Car deleted Last Id : {}", subList.get(subList.size() - 1));
						startIndx = lastIndx;
						lastIndx = lastIndx + 100;
						subList = null;
					}
				} else {
					DBObject queryFormovedIds = new BasicDBObject();
					queryFormovedIds.put("_id", new BasicDBObject().append("$in", listOfIds));
					pospLoggerMongoDao.deleteRow(coll, queryFormovedIds, true, db);
					logger.info("PBP Car deleted PbPartnerCar Last Id : {}", dbObjectsList.get(dbObjectsList.size() - 1).get("_id"));
				}
				DBObject queryJson;
				if (motorDeletedId != null && motorDeletedId.size() > 0 && motorDeletedId.getObjectId("_id") != null) {
					totalMigrated = totalMigrated + dbObjectsList.size();
					DBObject setquery = new BasicDBObject("movedId", dbObjectsList.get(dbObjectsList.size() - 1).get("_id"))
							.append("totalMigrated", totalMigrated)
							.append("updatedOn", new Date());
					queryJson = new BasicDBObject("_id", motorDeletedId.getObjectId("_id"));
					DBObject rowJson = new BasicDBObject("$set", setquery);
					newArchMongoDao.updateRow(NStoS3util.lastUpdatedIdColl, queryJson, rowJson, false, false, NStoS3util.lastUpdatedIdDB);
				}
				queryOnId.put("$gt", dbObjectsList.get(dbObjectsList.size() - 1).get("_id"));
				queryObject.put("_id", queryOnId);
				dbObjectsList = pospLoggerMongoDao.getDBObjects(coll, queryObject, projection, offset, len, orderObject, db);
			}
		}catch (Exception e) {
			logger.error("Exception caught into movePbPartnersMotorBookLeads, msg: {}", e.getMessage());
		}finally {
			isPBPCarLogS3JobRunning = false;
			isPBPCarLogS3JobStop = false;
		}
	}
	private List<DBObject> getList(String coll, DBObject queryOnrequestId, int offset, String db, List<DBObject> resultList, int chunksize) {

		try {
			List<DBObject> dataList = pospLoggerMongoDao.getDBObjects(coll, queryOnrequestId, offset, chunksize, null, db);
			while (dataList.size() > 0){
				resultList.addAll(dataList);
				offset += chunksize;
				dataList = pospLoggerMongoDao.getDBObjects(coll, queryOnrequestId, offset, chunksize, null, db);
			}
		} catch (Exception e) {
			logger.error("Exception while getList");
		}
		return resultList;
	}

	@Override
	public void movePbPartnersTwBookLeads(String dbColl, Integer size, int days, int chunkSize) {
		logger.info("Inside moveBookLeads");
		List<DBObject> dbObjectsList = null;
		try {
			String db = dbColl.substring(0, dbColl.indexOf("."));
			String coll = dbColl.substring(dbColl.indexOf(".") + 1, dbColl.length());
			DBObject orderObject = new BasicDBObject("_id", 1);
			int offset = 0;
			int len = size;
			long totalMigrated = 0;
			DBObject queryForDeletId = new BasicDBObject();
			queryForDeletId.put("coll", dbColl);
			BasicDBObject motorDeletedId = newArchMongoDao.getDBObjectByDbName(NStoS3util.lastUpdatedIdColl, queryForDeletId, NStoS3util.lastUpdatedIdDB);

			DBObject queryOnId = new BasicDBObject();
			//only provide startId at once we are starting the batch.
			if (motorDeletedId != null && motorDeletedId.size() > 0) {
				queryOnId.put("$gte", motorDeletedId.get("movedId"));
				totalMigrated = (long)motorDeletedId.get("totalMigrated");
			}

			queryOnId.put("$lt", new ObjectId(DateUtil.getStringToDate(DateUtil.getFormatedDate(days, DateUtil.DATE_FORMAT_EE_MMM_dd_hh_mm_ss_z_yyyy), DateUtil.DATE_FORMAT_EE_MMM_dd_hh_mm_ss_z_yyyy)));
			DBObject queryObject = new BasicDBObject();
			queryObject.put("_id", queryOnId);
			BasicDBObject projection = new BasicDBObject("_id", 1).append("VehicleDetailId", 1).append("IsLeadBooked",1);
			dbObjectsList = pospLoggerMongoDao.getDBObjects(coll, queryObject, projection, offset, len, orderObject, db);
			logger.info("isPBPTwLogS3JobStop: {}, isPBPTwLogS3JobRunning:{}", isPBPTwLogS3JobStop, isPBPTwLogS3JobRunning);
			while (dbObjectsList != null && dbObjectsList.size() > 0 && !isPBPTwLogS3JobStop) {

				List<Object> listOfIds = new ArrayList<>();
				List<Long> vehicleDetailIds = new ArrayList<>();
				for (DBObject jsonDocument : dbObjectsList) {
					listOfIds.add(jsonDocument.get("_id"));
					Long vehicleDetailId = (Long) jsonDocument.get("VehicleDetailId");
					DBObject queryOnrequestId = new BasicDBObject();
					queryOnrequestId.put("VehicleDetailId", vehicleDetailId);
					Boolean isLeadBooked = (Boolean) jsonDocument.get("IsLeadBooked");
					if (isLeadBooked != null && isLeadBooked && !vehicleDetailIds.contains(vehicleDetailId) && vehicleDetailId != null && vehicleDetailId > 0) {
						offset = 0;
						List<DBObject> dbList = pospLoggerMongoDao.getDBObjects(coll, queryOnrequestId, 0, chunkSize, null, db);
						if (dbList.size() == chunkSize) {
							offset = offset + chunkSize;
							dbList = getList(coll, queryOnrequestId, offset, db, dbList, chunkSize);

						}
						vehicleDetailIds.add(vehicleDetailId);
						String jsonString;
						if (dbList != null && dbList.size() > 0) {
							try {
								jsonString = JSON.serialize(dbList);

								// Compress JSON data
								byte[] compressedData = compressData(jsonString);
								String fileName = vehicleDetailId + ".json.gz";
								logger.info("PBP TW fileName : " + fileName);
								String url = s3BucketService.addByteContentWithGzip(compressedData, NStoS3util.BUCKET_NAME_LOGGER_ARCHIVAL_PB_PARTNER, NStoS3util.MAP_NS_TO_S3_PATH.get(dbColl), fileName, "application/gzip");
								if (url.equals(AppConstants.FILE_ALREADY_EXIST)) {
									try {
										newArchMongoDao.addRows(coll, dbList, db);
									} catch (Exception e) {
										logger.error("Error while adding in movePbPartnersMotorBookLeads , {}", e.getMessage());
									}
								}else if (url !=null){
									logger.info("File Has uploaded on S3 name: {}, url: {}", fileName, url);
								}else{
									logger.info("url is name: {}, url: {}", fileName, url);
								}
								for (DBObject reqIdDoc : dbList) {
									listOfIds.add(reqIdDoc.get("_id"));
								}
							} catch (Exception e) {
								logger.error("Error serializing DBObject: msg: {}", e.getMessage());
								return;
							}
						}
					}
				}
				vehicleDetailIds = null;
				List<Object> subList = new ArrayList<>();
				int startIndx = 0;
				int lastIndx = 100;
				if (listOfIds.size() > 100) {
					while (startIndx < listOfIds.size()) {
						if (startIndx + 100 >= listOfIds.size()) {
							lastIndx = listOfIds.size();
						}
						subList = listOfIds.subList(startIndx, lastIndx);
						DBObject queryFormovedIds = new BasicDBObject();
						queryFormovedIds.put("_id", new BasicDBObject().append("$in", subList));
						pospLoggerMongoDao.deleteRow(coll, queryFormovedIds, true, db);
						logger.info("PBP TW Last Id deleted : {}", subList.get(subList.size() - 1));
						startIndx = lastIndx;
						lastIndx = lastIndx + 100;
						subList = null;
					}
				} else {
					DBObject queryFormovedIds = new BasicDBObject();
					queryFormovedIds.put("_id", new BasicDBObject().append("$in", listOfIds));
					pospLoggerMongoDao.deleteRow(coll, queryFormovedIds, true, db);
					logger.info("PBP TW Last Id deleted : {}", dbObjectsList.get(dbObjectsList.size() - 1).get("_id"));
				}
				DBObject queryJson;
				if (motorDeletedId != null && motorDeletedId.size() > 0 && motorDeletedId.getObjectId("_id") != null) {
					totalMigrated = totalMigrated + dbObjectsList.size();
					DBObject setquery = new BasicDBObject("movedId", dbObjectsList.get(dbObjectsList.size() - 1).get("_id"))
							.append("totalMigrated", totalMigrated)
							.append("updatedOn", new Date());
					queryJson = new BasicDBObject("_id", motorDeletedId.getObjectId("_id"));
					DBObject rowJson = new BasicDBObject("$set", setquery);
					newArchMongoDao.updateRow(NStoS3util.lastUpdatedIdColl, queryJson, rowJson, false, false, NStoS3util.lastUpdatedIdDB);
				}
				queryOnId.put("$gt", dbObjectsList.get(dbObjectsList.size() - 1).get("_id"));
				queryObject.put("_id", queryOnId);
				dbObjectsList = pospLoggerMongoDao.getDBObjects(coll, queryObject, projection, offset, len, orderObject, db);
			}
		}catch (Exception e) {
			logger.error("Exception caught into movePbPartnersTwBookLeads, msg: {}", e.getMessage());
		}finally {
			isPBPTwLogS3JobRunning = false;
			isPBPTwLogS3JobStop = false;
		}
	}

	public void movePbPartnersTwQuoteDB(String dbColl, Integer size, int days, int chunkSize) {
		logger.info("Inside movePbPartnersTwQuoteDB");
		List<DBObject> dbObjectsList = null;
		try {
			String db = dbColl.substring(0, dbColl.indexOf("."));
			String coll = dbColl.substring(dbColl.indexOf(".") + 1, dbColl.length());
			DBObject orderObject = new BasicDBObject("_id", 1);
			int offset = 0;
			int len = size;
			long totalMigrated = 0;
			DBObject queryForDeletId = new BasicDBObject();
			queryForDeletId.put("coll", dbColl);
			BasicDBObject motorDeletedId = newArchMongoDao.getDBObjectByDbName(NStoS3util.lastUpdatedIdColl, queryForDeletId, NStoS3util.lastUpdatedIdDB);

			DBObject queryOnId = new BasicDBObject();
			//only provide startId at once we are starting the batch.
			if (motorDeletedId != null && motorDeletedId.size() > 0) {
				queryOnId.put("$gte", motorDeletedId.get("movedId"));
				totalMigrated = (long)motorDeletedId.get("totalMigrated");
			}
			queryOnId.put("$lt", new ObjectId(DateUtil.getStringToDate(DateUtil.getFormatedDate(days, DateUtil.DATE_FORMAT_EE_MMM_dd_hh_mm_ss_z_yyyy), DateUtil.DATE_FORMAT_EE_MMM_dd_hh_mm_ss_z_yyyy)));
			DBObject queryObject = new BasicDBObject();
			queryObject.put("_id", queryOnId);
			BasicDBObject projection = new BasicDBObject("_id", 1).append("correlationId", 1);
			dbObjectsList = pbpSupplierDao.getDBObjects(coll, queryObject, projection, offset, len, orderObject, db);
			logger.info("isPBPTwQuoteDBJobStop: {}, isPBPTwQuoteDBJobRunning:{}", isPBPTwQuoteDBJobStop, isPBPTwQuoteDBJobRunning);
			while (dbObjectsList != null && dbObjectsList.size() > 0 && !isPBPTwQuoteDBJobStop) {

				List<Object> listOfIds = new ArrayList<>();
				List<String> correlationIds = new ArrayList<>();
				for (DBObject jsonDocument : dbObjectsList) {
					listOfIds.add(jsonDocument.get("_id"));
					String correlationId = (String) jsonDocument.get("correlationId");
					if (StringUtils.hasText(correlationId) && !correlationIds.contains(correlationId)) {
						correlationIds.add(correlationId);
						DBObject queryOnrequestId = new BasicDBObject();
						queryOnrequestId.put("correlationId", correlationId);
						offset = 0;
						List<DBObject> dbList = pbpSupplierDao.getDBObjects(coll, queryOnrequestId, 0, chunkSize, null, db);
						if (dbList.size() == chunkSize) {
							offset = offset + chunkSize;
							dbList = getPBPList(coll, queryOnrequestId, offset, db, dbList, chunkSize);
						}
						String jsonString;
						if (dbList != null && dbList.size() > 0) {
							try {
								jsonString = JSON.serialize(dbList);

								// Compress JSON data
								byte[] compressedData = compressData(jsonString);
								String fileName = correlationId + ".json.gz";
								logger.info("PBP TW Quote fileName : " + fileName);
								String url = s3BucketService.addByteContentWithGzip(compressedData, NStoS3util.BUCKET_NAME_LOGGER_ARCHIVAL_PB_PARTNER, NStoS3util.MAP_NS_TO_S3_PATH.get(dbColl), fileName, "application/gzip");
								if (url.equals(AppConstants.FILE_ALREADY_EXIST)) {
									try {
										loggerMongoDao.addRows(coll, dbList, db);
									} catch (Exception e) {
										logger.error("Error while adding in PBP TW Quote  , {}", e.getMessage());
									}
								}else if (url !=null){
									logger.info("File Has uploaded on S3 name: {}, url: {}", fileName, url);
								}else{
									logger.info("url is name: {}, url: {}", fileName, url);
									break;
								}
								for (DBObject reqIdDoc : dbList) {
									listOfIds.add(reqIdDoc.get("_id"));
								}
							} catch (Exception e) {
								logger.error("Error serializing DBObject: msg: {}", e.getMessage());
								return;
							}
						}
					}
				}
				correlationIds = null;
				List<Object> subList = new ArrayList<>();
				int startIndx = 0;
				int lastIndx = 100;
				if (listOfIds.size() > 100) {
					while (startIndx < listOfIds.size()) {
						if (startIndx + 100 >= listOfIds.size()) {
							lastIndx = listOfIds.size();
						}
						subList = listOfIds.subList(startIndx, lastIndx);
						DBObject queryFormovedIds = new BasicDBObject();
						queryFormovedIds.put("_id", new BasicDBObject().append("$in", subList));
						pbpSupplierDao.deleteRow(coll, queryFormovedIds, true, db);
						logger.info("PBP TW Quote Last Id deleted : {}", subList.get(subList.size() - 1));
						startIndx = lastIndx;
						lastIndx = lastIndx + 100;
						subList = null;
					}
				} else {
					DBObject queryFormovedIds = new BasicDBObject();
					queryFormovedIds.put("_id", new BasicDBObject().append("$in", listOfIds));
					pbpSupplierDao.deleteRow(coll, queryFormovedIds, true, db);
					logger.info("PBP TW Quote Last Id deleted : {}", dbObjectsList.get(dbObjectsList.size() - 1).get("_id"));
				}
				DBObject queryJson;
				if (motorDeletedId != null && motorDeletedId.size() > 0 && motorDeletedId.getObjectId("_id") != null) {
					totalMigrated = totalMigrated + dbObjectsList.size();
					DBObject setquery = new BasicDBObject("movedId", dbObjectsList.get(dbObjectsList.size() - 1).get("_id"))
							.append("totalMigrated", totalMigrated)
							.append("updatedOn", new Date());
					queryJson = new BasicDBObject("_id", motorDeletedId.getObjectId("_id"));
					DBObject rowJson = new BasicDBObject("$set", setquery);
					newArchMongoDao.updateRow(NStoS3util.lastUpdatedIdColl, queryJson, rowJson, false, false, NStoS3util.lastUpdatedIdDB);
				}
				queryOnId.put("$gt", dbObjectsList.get(dbObjectsList.size() - 1).get("_id"));
				queryObject.put("_id", queryOnId);
				dbObjectsList = null;
				dbObjectsList = pbpSupplierDao.getDBObjects(coll, queryObject, projection, offset, len, orderObject, db);
			}
		}catch (Exception e) {
			logger.error("Exception caught into PBP TW Quote , msg: {}", e.getMessage());
		}finally {
			isPBPTwQuoteDBJobRunning = false;
			isPBPTwQuoteDBJobStop = false;
		}
	}

	public void movePbPartnersCarQuoteDB(String dbColl, Integer size, int days, int chunkSize) {
		logger.info("Inside movePbPartnersCarQuoteDB");
		List<DBObject> dbObjectsList = null;
		try {
			String db = dbColl.substring(0, dbColl.indexOf("."));
			String coll = dbColl.substring(dbColl.indexOf(".") + 1, dbColl.length());
			DBObject orderObject = new BasicDBObject("_id", 1);
			int offset = 0;
			int len = size;
			long totalMigrated = 0;
			DBObject queryForDeletId = new BasicDBObject();
			queryForDeletId.put("coll", dbColl);
			BasicDBObject motorDeletedId = newArchMongoDao.getDBObjectByDbName(NStoS3util.lastUpdatedIdColl, queryForDeletId, NStoS3util.lastUpdatedIdDB);

			DBObject queryOnId = new BasicDBObject();
			//only provide startId at once we are starting the batch.
			if (motorDeletedId != null && motorDeletedId.size() > 0) {
				queryOnId.put("$gte", motorDeletedId.get("movedId"));
				totalMigrated = (long)motorDeletedId.get("totalMigrated");
			}

			queryOnId.put("$lt", new ObjectId(DateUtil.getStringToDate(DateUtil.getFormatedDate(days, DateUtil.DATE_FORMAT_EE_MMM_dd_hh_mm_ss_z_yyyy), DateUtil.DATE_FORMAT_EE_MMM_dd_hh_mm_ss_z_yyyy)));
			DBObject queryObject = new BasicDBObject();
			queryObject.put("_id", queryOnId);
			BasicDBObject projection = new BasicDBObject("_id", 1).append("correlationId", 1);
			dbObjectsList = pbpSupplierDao.getDBObjects(coll, queryObject, projection, offset, len, orderObject, db);
			logger.info("isPBPCarQuoteDBJobStop: {}, isPBPCarQuoteDBJobRunning:{}", isPBPCarQuoteDBJobStop, isPBPCarQuoteDBJobRunning);
			while (dbObjectsList != null && dbObjectsList.size() > 0 && !isPBPCarQuoteDBJobStop) {

				List<Object> listOfIds = new ArrayList<>();
				List<String> correlationIds = new ArrayList<>();
				for (DBObject jsonDocument : dbObjectsList) {
					listOfIds.add(jsonDocument.get("_id"));
					String correlationId = (String) jsonDocument.get("correlationId");
					if (StringUtils.hasText(correlationId) && !correlationIds.contains(correlationId)) {
						correlationIds.add(correlationId);
						DBObject queryOnrequestId = new BasicDBObject();
						queryOnrequestId.put("correlationId", correlationId);
						offset = 0;
						List<DBObject> dbList = pbpSupplierDao.getDBObjects(coll, queryOnrequestId, 0, chunkSize, null, db);
						if (dbList.size() == chunkSize) {
							offset = offset + chunkSize;
							dbList = getPBPList(coll, queryOnrequestId, offset, db, dbList, chunkSize);
						}
						String jsonString;
						if (dbList != null && dbList.size() > 0) {
							try {
								jsonString = JSON.serialize(dbList);

								// Compress JSON data
								byte[] compressedData = compressData(jsonString);
								String fileName = correlationId + ".json.gz";
								logger.info("PBP Car Quote fileName : " + fileName);
								String url = s3BucketService.addByteContentWithGzip(compressedData, NStoS3util.BUCKET_NAME_LOGGER_ARCHIVAL_PB_PARTNER, NStoS3util.MAP_NS_TO_S3_PATH.get(dbColl), fileName, "application/gzip");
								if (url.equals(AppConstants.FILE_ALREADY_EXIST)) {
									try {
										loggerMongoDao.addRows(coll, dbList, db);
									} catch (Exception e) {
										logger.error("Error while adding in PBP TW Quote  , {}", e.getMessage());
									}
								}else
								if (url !=null){
									logger.info("File Has uploaded on S3 name: {}, url: {}", fileName, url);
								}else{
									logger.info("url is name: {}, url: {}", fileName, url);
								}
								for (DBObject reqIdDoc : dbList) {
									listOfIds.add(reqIdDoc.get("_id"));
								}
							} catch (Exception e) {
								logger.error("Error serializing DBObject: msg: {}", e.getMessage());
								return;
							}
						}
					}
				}
				correlationIds = null;
				List<Object> subList = new ArrayList<>();
				int startIndx = 0;
				int lastIndx = 100;
				if (listOfIds.size() > 100) {
					while (startIndx < listOfIds.size()) {
						if (startIndx + 100 >= listOfIds.size()) {
							lastIndx = listOfIds.size();
						}
						subList = listOfIds.subList(startIndx, lastIndx);
						DBObject queryFormovedIds = new BasicDBObject();
						queryFormovedIds.put("_id", new BasicDBObject().append("$in", subList));
						pbpSupplierDao.deleteRow(coll, queryFormovedIds, true, db);
						logger.info("PBP Car Quote Last Id deleted : {}", subList.get(subList.size() - 1));
						startIndx = lastIndx;
						lastIndx = lastIndx + 100;
						subList = null;
					}
				} else {
					DBObject queryFormovedIds = new BasicDBObject();
					queryFormovedIds.put("_id", new BasicDBObject().append("$in", listOfIds));
					pbpSupplierDao.deleteRow(coll, queryFormovedIds, true, db);
					logger.info("PBP Car Quote Last Id deleted : {}", dbObjectsList.get(dbObjectsList.size() - 1).get("_id"));
				}
				DBObject queryJson;
				if (motorDeletedId != null && motorDeletedId.size() > 0 && motorDeletedId.getObjectId("_id") != null) {
					totalMigrated = totalMigrated + dbObjectsList.size();
					DBObject setquery = new BasicDBObject("movedId", dbObjectsList.get(dbObjectsList.size() - 1).get("_id"))
							.append("totalMigrated", totalMigrated)
							.append("updatedOn", new Date());
					queryJson = new BasicDBObject("_id", motorDeletedId.getObjectId("_id"));
					DBObject rowJson = new BasicDBObject("$set", setquery);
					newArchMongoDao.updateRow(NStoS3util.lastUpdatedIdColl, queryJson, rowJson, false, false, NStoS3util.lastUpdatedIdDB);
				}
				queryOnId.put("$gt", dbObjectsList.get(dbObjectsList.size() - 1).get("_id"));
				queryObject.put("_id", queryOnId);
				dbObjectsList = null;
				dbObjectsList = pbpSupplierDao.getDBObjects(coll, queryObject, projection, offset, len, orderObject, db);
			}
		}catch (Exception e) {
			logger.error("Exception caught into PBP Car Quote , msg: {}", e.getMessage());
		}finally {
			isPBPCarQuoteDBJobRunning = false;
			isPBPCarQuoteDBJobStop = false;
		}
	}
	private List<DBObject> getPBPList(String coll, DBObject queryOnrequestId, int offset, String db, List<DBObject> resultList, int chunksize) {

		try {
			List<DBObject> dataList = pbpSupplierDao.getDBObjects(coll, queryOnrequestId, offset, chunksize, null, db);
			while (dataList.size() > 0){
				resultList.addAll(dataList);
				offset += chunksize;
				dataList = pbpSupplierDao.getDBObjects(coll, queryOnrequestId, offset, chunksize, null, db);
			}
		} catch (Exception e) {
			logger.error("Exception while getList");
		}
		return resultList;
	}
}
