package com.docMigration.service.impl;

import com.docMigration.cleanup.entity.StaticMaster;
import com.docMigration.dao.ChatMongoDao;
import com.docMigration.service.S3BucketService;
import com.docMigration.util.*;
import com.mongodb.*;
import com.mongodb.util.JSON;
import org.apache.commons.io.output.ByteArrayOutputStream;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.*;
import java.util.zip.GZIPOutputStream;

@Service
public class BmsLoggerServiceImpl {
    private final Logger logger = LoggerFactory.getLogger(BmsLoggerServiceImpl.class);
    @Autowired
    @Qualifier("commLoggerMongoDao")
    ChatMongoDao commLoggerMongoDao;
    @Autowired
    @Qualifier("newArchivalMongoDao")
    ChatMongoDao newArchMongoDao;
    @Autowired
    private S3BucketService s3service;
    @Autowired
    private MongoUtil mongoUtil;
    @Autowired
    @Qualifier("commMongoDao")
    ChatMongoDao commMongoDao;
    @Autowired
    @Qualifier("newCommBoxDao")
    ChatMongoDao newCommMongoDao;

    public static boolean isMoveCommV2FileLogsLoggerJobStop = false;
    public static boolean isMoveCommV2FileLogsLoggerJobRunning = false;
    public static boolean isMoveCommV2FileLogsLoggerJobStopV1 = false;
    public static boolean isMoveCommV2FileLogsLoggerJobRunningV1 = false;

    public static boolean isMovePIVCLogsJobStop = false;
    public static boolean isMovePIVCLogsJobRunning = false;
    public static boolean isMoveDocPushLogsJobStop = false;
    public static boolean isMoveDocPushLogsJobRunning = false;
    public static boolean isMoveRequestLogsJobStop = false;
    public static boolean isMoveRequestLogsJobRunning = false;
    public static boolean isMoveIntRequestLogsJobStop = false;
    public static boolean isMoveIntRequestLogsJobRunning = false;
    public static boolean isMoveIntegrationExternalAPILogsJobStop = false;
    public static boolean isMoveIntegrationExternalAPILogsJobRunning = false;
    public static boolean isMoveCromaPBCromaV2LogsJobStop = false;
    public static boolean isMoveCromaPBCromaV2LogsJobRunning = false;
    public static boolean isMoveCromaPBCromaV2SalesLogsJobStop = false;
    public static boolean isMoveCromaPBCromaV2SalesLogsJobRunning = false;
    public static boolean isMoveDocPushLogsJobStopV2 = false;
    public static boolean isMoveDocPushLogsJobRunningV2 = false;
    private static final int BATCH_SIZE = 10;
    private static final int THREAD_POOL_SIZE = 5;
    public static List<String> commV2FileLogsJobList = new ArrayList<>();
    public static long totalMigratedCount = 0;
    public static boolean isTempMoveDocPushLogsJobStop = false;
    public static boolean isTempMoveDocPushLogsJobRunning = false;
    public static boolean isTempMoveDocPushLogsJobStopV2 = false;
    public static boolean isTempMoveDocPushLogsJobRunningV2 = false;

    public void moveCommV2FileLogsToS3(String dbColl, Integer size, int days, int chunkSize, String jobId, String movedIdsCollection) throws Exception {

        logger.info("Inside moveCommV2FileLogsToS3");
        try {
            String[] nsArray = dbColl.split("\\.");
            String db = nsArray[0];
            String coll = nsArray[1];
            int len = size;
            long totalMigrated = 0;
            DBObject queryForDeletId = new BasicDBObject();
            queryForDeletId.put("coll", dbColl);
            BasicDBObject commDeletedId = newArchMongoDao.getDBObjectByDbName(movedIdsCollection, queryForDeletId, NStoS3util.lastUpdatedIdDB);
            DBObject queryOnId = new BasicDBObject();
            //only provide startId at once we are starting the batch.
            if (commDeletedId != null && commDeletedId.size() > 0) {
                queryOnId.put("$gt", commDeletedId.get("movedId"));
                totalMigrated = (long) commDeletedId.get("totalMigrated");
            }
            queryOnId.put("$lt", new ObjectId(DateUtil.getStringToDate(DateUtil.getFormatedDate(days, DateUtil.DATE_FORMAT_EE_MMM_dd_hh_mm_ss_z_yyyy), DateUtil.DATE_FORMAT_EE_MMM_dd_hh_mm_ss_z_yyyy)));
            DBObject queryObject = new BasicDBObject();
            queryObject.put("_id", queryOnId);
            BasicDBObject projection = new BasicDBObject("_id", 1).append("RequestId", 1);
            DBObject orderObject = new BasicDBObject("_id", 1);
            // get list from mongoDB
            List<DBObject> dbObjectsList = commLoggerMongoDao.getDBObjects(coll, queryObject, projection, 0, len, orderObject, db);
            logger.info("isMoveCommV2FileLogsLoggerJobStop: {}, isMoveCommV2FileLogsLoggerJobRunning: {}", isMoveCommV2FileLogsLoggerJobStop, isMoveCommV2FileLogsLoggerJobRunning);

            // if list have data then proceed
            while (dbObjectsList != null && dbObjectsList.size() > 0 && !isMoveCommV2FileLogsLoggerJobStop) {
                List<Object> listOfIds = new ArrayList<>();
                HashSet<String> requestIdListCommV2FileLogs = new HashSet<>();

                for (DBObject jsonDocument : dbObjectsList) {
                    listOfIds.add(jsonDocument.get("_id"));
                    String requestId = (String) jsonDocument.get("RequestId");
                    DBObject queryOnrequestId = new BasicDBObject();
                    queryOnrequestId.put("RequestId", requestId);
                    if (!requestIdListCommV2FileLogs.contains(requestId) && requestId != null) {
                        requestIdListCommV2FileLogs.add(requestId);
                        int offset = 0;
                        List<DBObject> dbList = commLoggerMongoDao.getDBObjects(coll, queryOnrequestId, offset, chunkSize, null, db);
                        if (dbList.size() == chunkSize) {
                            offset = offset + chunkSize;
                            dbList = getList(coll, queryOnrequestId, offset, db, dbList, chunkSize);
                        }
                        String jsonString;
                        if (dbList != null && dbList.size() > 0) {
                            try {
                                jsonString = JSON.serialize(dbList);

                                // Compress JSON data
                                byte[] compressedData = compressData(jsonString);
                                //String deCompressedData = decompressData(compressedData);
                                String fileName = requestId + "_" + "json.gz";
                                logger.info("CommV2FileLogsV2: " + movedIdsCollection + " " + fileName);
                                String folderName = NStoS3util.MAP_NS_TO_S3_PATH.get(dbColl);
                                String url = s3service.addByteContentWithGzip(compressedData, NStoS3util.BUCKET_NAME_LOGGER_ARCHIVAL, folderName, fileName, "application/gzip");
                                if (url.equals(AppConstants.FILE_ALREADY_EXIST)) {
                                    try {
                                        commLoggerMongoDao.addRows("CommV2FileLogsV2_failOver", dbList, db);
                                    } catch (Exception e) {
                                        logger.error("Error while adding in moveCommV2FileLogsToS3 , {}", e.getMessage());
                                    }
                                }

                                for (DBObject reqIdDoc : dbList) {
                                    listOfIds.add(reqIdDoc.get("_id"));
                                }
                            } catch (Exception e) {
                                logger.error("Error serializing DBObject: " + e.getMessage());
                                return;
                            }
                        }
                    }
                }
                requestIdListCommV2FileLogs = null;

                // to delete from source by list of _id
                try {
                    List<Object> subList = new ArrayList<>();
                    int startIndx = 0;
                    int lastIndx = 100;
                    if (listOfIds.size() > 100) {
                        while (startIndx < listOfIds.size()) {
                            if (startIndx + 100 >= listOfIds.size()) {
                                lastIndx = listOfIds.size();
                            }
                            subList = listOfIds.subList(startIndx, lastIndx);
                            DBObject queryFormovedIds = new BasicDBObject();
                            queryFormovedIds.put("_id", new BasicDBObject().append("$in", subList));
                            commLoggerMongoDao.deleteRow(coll, queryFormovedIds, true, db);
                            logger.info("CommV2FileLogsV2 Last Id deleted : {}", subList.get(subList.size() - 1));
                            startIndx = lastIndx;
                            lastIndx = lastIndx + 100;
                            subList = null;
                        }
                    } else {
                        DBObject queryFormovedIds = new BasicDBObject();
                        queryFormovedIds.put("_id", new BasicDBObject().append("$in", listOfIds));
                        commLoggerMongoDao.deleteRow(coll, queryFormovedIds, true, db);
                        logger.info("CommV2FileLogsV2 Last Id deleted : {}", listOfIds.get(listOfIds.size() - 1));
                    }
                    DBObject queryFormovedIds = new BasicDBObject();
                    queryFormovedIds.put("_id", new BasicDBObject().append("$in", listOfIds));

                } catch (Exception e) {
                    logger.error("Error while adding in removeCommV2FilesV2Logs , {}", e.getMessage());
                    if (!e.getMessage().contains("timeout") && !e.getMessage().contains("duplicate")) {
                        throw e;
                    }
                }
                listOfIds = null;
                // updating last _id in metaData master mongoDB collection: movedIdsLogger
                DBObject queryJson;
                if (commDeletedId != null && commDeletedId.size() > 0 && commDeletedId.getObjectId("_id") != null) {
                    totalMigrated = totalMigrated + dbObjectsList.size();
                    DBObject setquery = new BasicDBObject("movedId", dbObjectsList.get(dbObjectsList.size() - 1).get("_id"))
                            .append("totalMigrated", totalMigrated)
                            .append("updatedOn", new Date());
                    queryJson = new BasicDBObject("_id", commDeletedId.getObjectId("_id"));
                    DBObject rowJson = new BasicDBObject("$set", setquery);
                    newArchMongoDao.updateRow(movedIdsCollection, queryJson, rowJson, false, false, NStoS3util.lastUpdatedIdDB);
                }
                queryOnId.put("$gt", dbObjectsList.get(dbObjectsList.size() - 1).get("_id"));
                queryObject.put("_id", queryOnId);
                dbObjectsList = null; // explicitly nullify
                dbObjectsList = commLoggerMongoDao.getDBObjects(coll, queryObject, projection, 0, len, orderObject, db);
            }

        } catch (Exception e) {
            logger.error("Exception caught in moveCommV2FileLogsToS3, ns:{} , msg: {}", e.getMessage());
        } finally {
            isMoveCommV2FileLogsLoggerJobStop = false;
            isMoveCommV2FileLogsLoggerJobRunning = false;
        }
    }
//    public void moveCommV2FileLogsToS3(String dbColl, Integer size, int days, int chunkSize, String jobId) throws Exception {
//
//        logger.info("Inside moveCommV2FileLogsToS3");
//        try {
//            String[] nsArray = dbColl.split("\\.");
//            String db = nsArray[0];
//            String coll = nsArray[1];
//            DBObject queryForDeletId = new BasicDBObject();
//            queryForDeletId.put("coll", dbColl);
//            BasicDBObject commDeletedId = newArchMongoDao.getDBObjectByDbName(NStoS3util.lastUpdatedIdColl, queryForDeletId, NStoS3util.lastUpdatedIdDB);
////            DBObject queryOnId = new BasicDBObject();
////            //only provide startId at once we are starting the batch.
////            if (commDeletedId != null && commDeletedId.size() > 0) {
////                totalMigratedCount = (long) commDeletedId.get("totalMigrated");
////            }
////            queryOnId.put("$lt", new ObjectId(DateUtil.getStringToDate(DateUtil.getFormatedDate(days, DateUtil.DATE_FORMAT_EE_MMM_dd_hh_mm_ss_z_yyyy), DateUtil.DATE_FORMAT_EE_MMM_dd_hh_mm_ss_z_yyyy)));
////            DBObject queryObject = new BasicDBObject();
////            queryObject.put("_id", queryOnId);
////            BasicDBObject projection = new BasicDBObject("_id", 1).append("RequestId", 1);
//            DBObject orderObject = new BasicDBObject("_id", 1);
////            // get list from mongoDB
////            List<DBObject> dbObjectsList = commLoggerMongoDao.getDBObjects(coll, queryObject, projection, 0, len, orderObject, db);
//            logger.info("isMoveCommV2FileLogsLoggerJobStop: {}, isMoveCommV2FileLogsLoggerJobRunning: {}", isMoveCommV2FileLogsLoggerJobStop, isMoveCommV2FileLogsLoggerJobRunning);
//            List<DBObject> dbObjectsList = getListOfCommV2FileLog(dbColl, days, size, jobId);
//            // if list have data then proceed
//            while (dbObjectsList != null && dbObjectsList.size() > 0 && !isMoveCommV2FileLogsLoggerJobStop) {
//                List<Object> listOfIds = new ArrayList<>();
//                List<String> requestIdListCommV2FileLogs = new ArrayList<>();
//
//                for (DBObject jsonDocument : dbObjectsList) {
//                    listOfIds.add(jsonDocument.get("_id"));
//                    String requestId = (String) jsonDocument.get("RequestId");
//                    DBObject queryOnrequestId = new BasicDBObject();
//                    queryOnrequestId.put("RequestId", requestId);
//                    if (!requestIdListCommV2FileLogs.contains(requestId) && requestId != null) {
//                        requestIdListCommV2FileLogs.add(requestId);
//                        int offset = 0;
//                        List<DBObject> dbList = commLoggerMongoDao.getDBObjects(coll, queryOnrequestId, offset, chunkSize, null, db);
//                        if (dbList.size() == chunkSize) {
//                            offset = offset + chunkSize;
//                            dbList = getList(coll, queryOnrequestId, offset, db, dbList, chunkSize);
//                        }
//                        String jsonString;
//                        if (dbList != null && dbList.size() > 0) {
//                            try {
//                                jsonString = JSON.serialize(dbList);
//
//                                // Compress JSON data
//                                byte[] compressedData = compressData(jsonString);
//                                //String deCompressedData = decompressData(compressedData);
//                                String fileName = requestId + "_" + "json.gz";
//                                logger.info(jobId + " CommV2FileLogsV2 fileName : " + fileName);
//                                String folderName = NStoS3util.MAP_NS_TO_S3_PATH.get(dbColl);
//                                String url = s3service.addByteContentWithGzip(compressedData, NStoS3util.BUCKET_NAME_LOGGER_ARCHIVAL, folderName, fileName, "application/gzip");
//                                if (url.equals(AppConstants.FILE_ALREADY_EXIST)) {
//                                    try {
//                                        commLoggerMongoDao.addRows("CommV2FileLogsV2_failOver", dbList, db);
//                                    } catch (Exception e) {
//                                        logger.error("Error while adding in moveCommV2FileLogsToS3 , {}", e.getMessage());
//                                    }
//                                }
//
//                                for (DBObject reqIdDoc : dbList) {
//                                    listOfIds.add(reqIdDoc.get("_id"));
//                                }
//                            } catch (Exception e) {
//                                System.err.println("Error serializing DBObject: " + e.getMessage());
//                                return;
//                            }
//                        }
//                    }
//                }
//                requestIdListCommV2FileLogs = null;
//
//                // to delete from source by list of _id
//                try {
//                    List<Object> subList = new ArrayList<>();
//                    int startIndx = 0;
//                    int lastIndx = 100;
//                    if (listOfIds.size() > 100) {
//                        while (startIndx < listOfIds.size()) {
//                            if (startIndx + 100 >= listOfIds.size()) {
//                                lastIndx = listOfIds.size();
//                            }
//                            subList = listOfIds.subList(startIndx, lastIndx);
//                            DBObject queryFormovedIds = new BasicDBObject();
//                            queryFormovedIds.put("_id", new BasicDBObject().append("$in", subList));
//                            commLoggerMongoDao.deleteRow(coll, queryFormovedIds, true, db);
//                            logger.info("CommV2FileLogsV2 Last Id deleted : {}", subList.get(subList.size() - 1));
//                            startIndx = lastIndx;
//                            lastIndx = lastIndx + 100;
//                            subList = null;
//                        }
//                    } else {
//                        DBObject queryFormovedIds = new BasicDBObject();
//                        queryFormovedIds.put("_id", new BasicDBObject().append("$in", listOfIds));
//                        commLoggerMongoDao.deleteRow(coll, queryFormovedIds, true, db);
//                        logger.info("CommV2FileLogsV2 Last Id deleted : {}", listOfIds.get(listOfIds.size() - 1));
//                    }
//
//                } catch (Exception e) {
//                    logger.error("Error while adding in removeCommV2FilesV2Logs , {}", e.getMessage());
//                    if (!e.getMessage().contains("timeout") && !e.getMessage().contains("duplicate")) {
//                        throw e;
//                    }
//                }
//
//                // updating last _id in metaData master mongoDB collection: movedIdsLogger
//                DBObject queryJson;
//                if (commDeletedId != null && commDeletedId.size() > 0 && commDeletedId.getObjectId("_id") != null) {
//                    totalMigratedCount = totalMigratedCount + dbObjectsList.size();
//                    DBObject setquery = new BasicDBObject("movedId", dbObjectsList.get(dbObjectsList.size() - 1).get("_id"))
//                            .append("totalMigrated", totalMigratedCount)
//                            .append("updatedOn", new Date());
//                    queryJson = new BasicDBObject("_id", commDeletedId.getObjectId("_id"));
//                    DBObject rowJson = new BasicDBObject("$set", setquery);
//                    newArchMongoDao.updateRow(NStoS3util.lastUpdatedIdColl, queryJson, rowJson, false, false, NStoS3util.lastUpdatedIdDB);
//                }
//                dbObjectsList = getListOfCommV2FileLog(dbColl, days, size, jobId);
//            }
//
//        } catch (Exception e) {
//            logger.error("Exception caught in moveCommV2FileLogsToS3, ns:{} , msg: {}", e.getMessage());
//        } finally {
//            isMoveCommV2FileLogsLoggerJobStop = false;
//            isMoveCommV2FileLogsLoggerJobRunning = false;
//            BmsLoggerServiceImpl.commV2FileLogsJobList.remove("job1");
//            BmsLoggerServiceImpl.commV2FileLogsJobList.remove("job2");
//        }
//    }

    private static List<DBObject> listCommV2FileLosgIds = new ArrayList<>();

    private synchronized List<DBObject> getListOfCommV2FileLog(String dbColl, int days, int len, String jobId) {
        Map<String, Object> response = new HashMap<>();
        String[] nsArray = dbColl.split("\\.");
        String db = nsArray[0];
        String coll = nsArray[1];
        DBObject queryForDeletId = new BasicDBObject();
        queryForDeletId.put("coll", dbColl);
        List<DBObject> list = new ArrayList<>();
        // if static list has sufficient size then get from static list
        if (listCommV2FileLosgIds != null && listCommV2FileLosgIds.size() > 0) {

            Iterator<DBObject> itr = listCommV2FileLosgIds.iterator();
            int batchSize = listCommV2FileLosgIds.size() >= len ? len : listCommV2FileLosgIds.size();
            for (int i = 0; i < batchSize; i++) {
                list.add(itr.next());
                itr.remove();
            }
        } else {

            BasicDBObject commDeletedId = newArchMongoDao.getDBObjectByDbName(NStoS3util.lastUpdatedIdColl, queryForDeletId, NStoS3util.lastUpdatedIdDB);
            DBObject queryOnId = new BasicDBObject();
            //only provide startId at once we are starting the batch.
            if (commDeletedId != null && commDeletedId.size() > 0) {
                totalMigratedCount = (long) commDeletedId.get("totalMigrated");
                queryOnId.put("$gt", commDeletedId.get("movedId"));
            }
            queryOnId.put("$lt", new ObjectId(DateUtil.getStringToDate(DateUtil.getFormatedDate(days, DateUtil.DATE_FORMAT_EE_MMM_dd_hh_mm_ss_z_yyyy), DateUtil.DATE_FORMAT_EE_MMM_dd_hh_mm_ss_z_yyyy)));
            DBObject queryObject = new BasicDBObject();
            queryObject.put("_id", queryOnId);
            BasicDBObject projection = new BasicDBObject("_id", 1).append("RequestId", 1);
            DBObject orderObject = new BasicDBObject("_id", 1);
            // get list from mongoDB
            List<DBObject> dbObjectsList = commLoggerMongoDao.getDBObjects(coll, queryObject, projection, 0, len * 8, orderObject, db);
            // remove duplicate requestId
            removeDuplicateRequestIdObj(dbObjectsList, commLoggerMongoDao, db, coll, queryObject, projection, len*8, orderObject);
            if (dbObjectsList.size()==0){
                dbObjectsList = commLoggerMongoDao.getDBObjects(coll, queryObject, projection, 0, len * 8, orderObject, db);
            }
            // if greater than 50 from dbList then return 5o and add rest in static list
            if (dbObjectsList != null && dbObjectsList.size() > 0) {
                int batchSize = dbObjectsList.size() >= len ? len : dbObjectsList.size();
                Iterator<DBObject> itr = dbObjectsList.iterator();
                for (int i = 0; i < batchSize; i++) {
                    list.add(itr.next());
                    itr.remove();
                }
                listCommV2FileLosgIds.addAll(dbObjectsList);
            }
        }
        return list;
    }

    public void moveCommV2FileLogsToS3V1(String dbColl, Integer size, int days, int chunkSize, String lte_Id) throws Exception {

        logger.info("Inside moveCommV2FileLogsToS3V1");
        try {
            String[] nsArray = dbColl.split("\\.");
            String db = nsArray[0];
            String coll = nsArray[1];
            ;
            int len = size;
            long totalMigrated = 0;
            DBObject queryForDeletId = new BasicDBObject();
            queryForDeletId.put("coll", "communicationDB.CommV2FileLogsv2_V1");
            BasicDBObject commDeletedId = newArchMongoDao.getDBObjectByDbName(NStoS3util.lastUpdatedIdColl, queryForDeletId, NStoS3util.lastUpdatedIdDB);
            DBObject queryOnId = new BasicDBObject();
            //only provide startId at once we are starting the batch.
            if (commDeletedId != null && commDeletedId.size() > 0) {
                queryOnId.put("$gt", commDeletedId.get("movedId"));
                totalMigrated = (long) commDeletedId.get("totalMigrated");
            }
            queryOnId.put("$lt", new ObjectId(lte_Id));//new ObjectId(DateUtil.getStringToDate(DateUtil.getFormatedDate(days, DateUtil.DATE_FORMAT_EE_MMM_dd_hh_mm_ss_z_yyyy), DateUtil.DATE_FORMAT_EE_MMM_dd_hh_mm_ss_z_yyyy)));
            DBObject queryObject = new BasicDBObject();
            queryObject.put("_id", queryOnId);
            BasicDBObject projection = new BasicDBObject("_id", 1).append("RequestId", 1);
            DBObject orderObject = new BasicDBObject("_id", 1);
            // get list from mongoDB
            List<DBObject> dbObjectsList = commLoggerMongoDao.getDBObjects(coll, queryObject, projection, 0, len, orderObject, db);
            logger.info("isMoveCommV2FileLogsLoggerJobStopV1: {}, isMoveCommV2FileLogsLoggerJobRunningV1: {}", isMoveCommV2FileLogsLoggerJobStopV1, isMoveCommV2FileLogsLoggerJobRunningV1);

            // if list have data then proceed
            while (dbObjectsList != null && dbObjectsList.size() > 0 && !isMoveCommV2FileLogsLoggerJobStopV1) {
                List<Object> listOfIds = new ArrayList<>();
                HashSet<String> requestIdListCommV2FileLogs = new HashSet<>();

                for (DBObject jsonDocument : dbObjectsList) {
                    listOfIds.add(jsonDocument.get("_id"));
                    String requestId = (String) jsonDocument.get("RequestId");
                    DBObject queryOnrequestId = new BasicDBObject();
                    queryOnrequestId.put("RequestId", requestId);
                    if (!requestIdListCommV2FileLogs.contains(requestId) && requestId != null) {
                        requestIdListCommV2FileLogs.add(requestId);
                        int offset = 0;
                        List<DBObject> dbList = commLoggerMongoDao.getDBObjects(coll, queryOnrequestId, offset, chunkSize, null, db);
                        if (dbList.size() == chunkSize) {
                            offset = offset + chunkSize;
                            dbList = getList(coll, queryOnrequestId, offset, db, dbList, chunkSize);
                        }
                        String jsonString;
                        if (dbList != null && dbList.size() > 0) {
                            try {
                                jsonString = JSON.serialize(dbList);

                                // Compress JSON data
                                byte[] compressedData = compressData(jsonString);
                                //String deCompressedData = decompressData(compressedData);
                                String fileName = requestId + "_" + "json.gz";
                                logger.info("CommV2FileLogsV2_v1 fileName : " + fileName);
                                String folderName = NStoS3util.MAP_NS_TO_S3_PATH.get(dbColl);
                                String url = s3service.addByteContentWithGzip(compressedData, NStoS3util.BUCKET_NAME_LOGGER_ARCHIVAL, folderName, fileName, "application/gzip");
                                if (url.equals(AppConstants.FILE_ALREADY_EXIST)) {
                                    try {
                                        commLoggerMongoDao.addRows("CommV2FileLogsV2_failOver", dbList, db);
                                    } catch (Exception e) {
                                        logger.error("Error while adding in moveCommV2FileLogsToS3V1 , {}", e.getMessage());
                                    }
                                }

                                for (DBObject reqIdDoc : dbList) {
                                    listOfIds.add(reqIdDoc.get("_id"));
                                }
                            } catch (Exception e) {
                                System.err.println("Error serializing DBObject: " + e.getMessage());
                                return;
                            }
                        }
                    }
                }
                requestIdListCommV2FileLogs = null;

                // to delete from source by list of _id
                try {
                    List<Object> subList = new ArrayList<>();
                    int startIndx = 0;
                    int lastIndx = 100;
                    if (listOfIds.size() > 100) {
                        while (startIndx < listOfIds.size()) {
                            if (startIndx + 100 >= listOfIds.size()) {
                                lastIndx = listOfIds.size();
                            }
                            subList = listOfIds.subList(startIndx, lastIndx);
                            DBObject queryFormovedIds = new BasicDBObject();
                            queryFormovedIds.put("_id", new BasicDBObject().append("$in", subList));
                            commLoggerMongoDao.deleteRow(coll, queryFormovedIds, true, db);
                            logger.info("CommV2FileLogsV2_v1 Last Id deleted : {}", subList.get(subList.size() - 1));
                            startIndx = lastIndx;
                            lastIndx = lastIndx + 100;
                            subList = null;
                        }
                    } else {
                        DBObject queryFormovedIds = new BasicDBObject();
                        queryFormovedIds.put("_id", new BasicDBObject().append("$in", listOfIds));
                        commLoggerMongoDao.deleteRow(coll, queryFormovedIds, true, db);
                        logger.info("CommV2FileLogsV2_v1 Last Id deleted : {}", listOfIds.get(listOfIds.size() - 1));
                    }
                    DBObject queryFormovedIds = new BasicDBObject();
                    queryFormovedIds.put("_id", new BasicDBObject().append("$in", listOfIds));

                } catch (Exception e) {
                    logger.error("Error while adding in removeCommV2FilesV2Logs , {}", e.getMessage());
                    if (!e.getMessage().contains("timeout") && !e.getMessage().contains("duplicate")) {
                        throw e;
                    }
                }
                listOfIds = null;
                // updating last _id in metaData master mongoDB collection: movedIdsLogger
                DBObject queryJson;
                if (commDeletedId != null && commDeletedId.size() > 0 && commDeletedId.getObjectId("_id") != null) {
                    totalMigrated = totalMigrated + dbObjectsList.size();
                    DBObject setquery = new BasicDBObject("movedId", dbObjectsList.get(dbObjectsList.size() - 1).get("_id"))
                            .append("totalMigrated", totalMigrated)
                            .append("updatedOn", new Date());
                    queryJson = new BasicDBObject("_id", commDeletedId.getObjectId("_id"));
                    DBObject rowJson = new BasicDBObject("$set", setquery);
                    newArchMongoDao.updateRow(NStoS3util.lastUpdatedIdColl, queryJson, rowJson, false, false, NStoS3util.lastUpdatedIdDB);
                }
                queryOnId.put("$gt", dbObjectsList.get(dbObjectsList.size() - 1).get("_id"));
                queryObject.put("_id", queryOnId);
                dbObjectsList = null; // explicitly nullify
                dbObjectsList = commLoggerMongoDao.getDBObjects(coll, queryObject, projection, 0, len, orderObject, db);
            }

        } catch (Exception e) {
            logger.error("Exception caught in moveCommV2FileLogsToS3, ns:{} , msg: {}", e.getMessage());
        } finally {
            isMoveCommV2FileLogsLoggerJobStopV1 = false;
            isMoveCommV2FileLogsLoggerJobRunningV1 = false;
        }
    }

    public void moveCommV2FileLogsToS3V2(String dbColl, Integer size, int days, int chunkSize, String lte_Id) throws Exception {

        logger.info("Inside moveCommV2FileLogsToS3V2");
        ExecutorService executor = Executors.newFixedThreadPool(THREAD_POOL_SIZE);
        try {
            String[] nsArray = dbColl.split("\\.");
            String db = nsArray[0];
            String coll = nsArray[1];
            int len = size;
            long totalMigrated = 0;
            DBObject queryForDeletId = new BasicDBObject();
            queryForDeletId.put("coll", "communicationDB.CommV2FileLogsv2_V1");
            BasicDBObject commDeletedId = newArchMongoDao.getDBObjectByDbName(NStoS3util.lastUpdatedIdColl, queryForDeletId, NStoS3util.lastUpdatedIdDB);
            DBObject queryOnId = new BasicDBObject();
            //only provide startId at once we are starting the batch.
            if (commDeletedId != null && commDeletedId.size(


            ) > 0) {
                queryOnId.put("$gt", commDeletedId.get("movedId"));
                totalMigrated = (long) commDeletedId.get("totalMigrated");
            }
            queryOnId.put("$lt", new ObjectId(lte_Id));//new ObjectId(DateUtil.getStringToDate(DateUtil.getFormatedDate(days, DateUtil.DATE_FORMAT_EE_MMM_dd_hh_mm_ss_z_yyyy), DateUtil.DATE_FORMAT_EE_MMM_dd_hh_mm_ss_z_yyyy)));
            DBObject queryObject = new BasicDBObject();
            queryObject.put("_id", queryOnId);
            BasicDBObject projection = new BasicDBObject("_id", 1).append("RequestId", 1);
            DBObject orderObject = new BasicDBObject("_id", 1);
            // get list from mongoDB
            List<DBObject> dbObjectsList = commLoggerMongoDao.getDBObjects(coll, queryObject, projection, 0, len, orderObject, db);

            logger.info("isMoveCommV2FileLogsLoggerJobStopV1: {}, isMoveCommV2FileLogsLoggerJobRunningV1: {}", isMoveCommV2FileLogsLoggerJobStopV1, isMoveCommV2FileLogsLoggerJobRunningV1);

            // if list have data then proceed
            while (dbObjectsList != null && dbObjectsList.size() > 0 && !isMoveCommV2FileLogsLoggerJobStopV1) {
                logger.info("job running: isMoveCommV2FileLogsLoggerJobStopV1:{}", isMoveCommV2FileLogsLoggerJobStopV1);
                ObjectId lastUpdatedID = new ObjectId(String.valueOf(dbObjectsList.get(dbObjectsList.size() - 1).get("_id")));

                // remove duplicate requestId
//                removeDuplicateRequestIdObj(dbObjectsList, commLoggerMongoDao);

                // Process IDs in batches
                List<Future<String>> futures = new ArrayList<>();
                for (int i = 0; i < dbObjectsList.size(); i += BATCH_SIZE) {
                    List<DBObject> batch = dbObjectsList.subList(i, Math.min(i + BATCH_SIZE, dbObjectsList.size()));
                    Future<String> future = executor.submit(new BatchProcessor(commLoggerMongoDao, s3service, batch, dbColl, chunkSize, lte_Id));
                    futures.add(future);
                }
                // Wait for all tasks to complete
                int threadCount = 1;
                for (Future<String> future : futures) {
                    try {
                        logger.info("threadCount: {}, msg:{}", threadCount++, future.get());  // Block until the task is complete
                    } catch (InterruptedException | ExecutionException e) {
                        logger.error("Error in futures: , {}", e.getMessage());
                        throw e;
                    }
                }
                // updating last _id in metaData master mongoDB collection: movedIdsLogger
                DBObject queryJson;
                if (commDeletedId != null && commDeletedId.size() > 0 && commDeletedId.getObjectId("_id") != null) {
                    totalMigrated = totalMigrated + dbObjectsList.size();
                    DBObject setquery = new BasicDBObject("movedId", lastUpdatedID)
                            .append("totalMigrated", totalMigrated)
                            .append("updatedOn", new Date());
                    queryJson = new BasicDBObject("_id", commDeletedId.getObjectId("_id"));
                    DBObject rowJson = new BasicDBObject("$set", setquery);
                    newArchMongoDao.updateRow(NStoS3util.lastUpdatedIdColl, queryJson, rowJson, false, false, NStoS3util.lastUpdatedIdDB);
                }
                queryOnId.put("$gt", dbObjectsList.get(dbObjectsList.size() - 1).get("_id"));
                queryObject.put("_id", queryOnId);
                dbObjectsList = null; // explicitly nullify
                dbObjectsList = commLoggerMongoDao.getDBObjects(coll, queryObject, projection, 0, len, orderObject, db);
            }
        } catch (Exception e) {
            logger.error("Exception caught in moveCommV2FileLogsToS3V2, ns:{} , msg: {}", dbColl, e.getMessage());
        } finally {
            executor.shutdown();  // Properly shut down the executor
            isMoveCommV2FileLogsLoggerJobStopV1 = false;
            isMoveCommV2FileLogsLoggerJobRunningV1 = false;
        }
    }

    private static void removeDuplicateRequestIdObj(List<DBObject> dbObjectsList, ChatMongoDao commLoggerMongoDao, String db, String coll,DBObject queryObject, DBObject projection, int len, DBObject orderObject){
        Iterator<DBObject> itr = dbObjectsList.iterator();
        List<String> uniqueRequestIdList = new ArrayList<>();
        List<Object> listOfIdsToDelete = new ArrayList<>();
        while (itr.hasNext()) {
            DBObject jsonDocument = itr.next();
            String requestId = (String) jsonDocument.get("RequestId");
            if(requestId == null){
                listOfIdsToDelete.add(jsonDocument.get("_id"));
            }
            if (StringUtils.hasText(requestId) && !uniqueRequestIdList.contains(requestId)) {
                uniqueRequestIdList.add(requestId);
            } else {
                itr.remove();
            }
        }
        if(listOfIdsToDelete.size() > 0){
            try {
                DBObject queryFormovedIds = new BasicDBObject();
                queryFormovedIds.put("_id", new BasicDBObject().append("$in", listOfIdsToDelete));
                commLoggerMongoDao.deleteRow(coll, queryFormovedIds, true, db);
            }catch (Exception e){

            }
        }
    }

    public void moveCommV2FileLogsToS3V3(String dbColl, Integer size, int days, int chunkSize, String lte_Id) {

        logger.info("Inside moveCommV2FileLogsToS3V3");
        try {
            String[] nsArray = dbColl.split("\\.");
            String db = nsArray[0];
            String coll = nsArray[1];
            ;
            int len = size;
            long totalMigrated = 0;
            DBObject queryForDeletId = new BasicDBObject();
            queryForDeletId.put("coll", "communicationDB.CommV2FileLogsv2_V1");
            List<DBObject> dbObjectsList = null;
            BasicDBObject commDeletedId = newArchMongoDao.getDBObjectByDbName(NStoS3util.lastUpdatedIdColl, queryForDeletId, NStoS3util.lastUpdatedIdDB);
            DBObject queryOnId = new BasicDBObject();
            //only provide startId at once we are starting the batch.
            if (commDeletedId != null && commDeletedId.size() > 0) {
                queryOnId.put("$gt", commDeletedId.get("movedId"));
                totalMigrated = (long) commDeletedId.get("totalMigrated");
            }
            queryOnId.put("$lt", new ObjectId(lte_Id));//new ObjectId(DateUtil.getStringToDate(DateUtil.getFormatedDate(days, DateUtil.DATE_FORMAT_EE_MMM_dd_hh_mm_ss_z_yyyy), DateUtil.DATE_FORMAT_EE_MMM_dd_hh_mm_ss_z_yyyy)));
            DBObject queryObject = new BasicDBObject();
            queryObject.put("_id", queryOnId);
            BasicDBObject projection = new BasicDBObject("_id", 1).append("RequestId", 1);
            DBObject orderObject = new BasicDBObject("_id", 1);
            // get list from mongoDB
            dbObjectsList = commLoggerMongoDao.getDBObjects(coll, queryObject, projection, 0, len, orderObject, db);
            logger.info("isMoveCommV2FileLogsLoggerJobStopV1: {}, isMoveCommV2FileLogsLoggerJobRunningV1: {}", isMoveCommV2FileLogsLoggerJobStopV1, isMoveCommV2FileLogsLoggerJobRunningV1);
            // if list have data then proceed
            while (dbObjectsList != null && dbObjectsList.size() > 0 && !isMoveCommV2FileLogsLoggerJobStopV1) {
                List<Object> listOfIds = new ArrayList<>();
                HashSet<String> requestIdListCommV2FileLogs = new HashSet<>();

                for (DBObject jsonDocument : dbObjectsList) {
                    listOfIds.add(jsonDocument.get("_id"));
                    String requestId = (String) jsonDocument.get("RequestId");
                    DBObject queryOnrequestId = new BasicDBObject();
                    queryOnrequestId.put("RequestId", requestId);
                    if (!requestIdListCommV2FileLogs.contains(requestId) && requestId != null) {
                        requestIdListCommV2FileLogs.add(requestId);
                        int offset = 0;
                        List<DBObject> dbList = commLoggerMongoDao.getDBObjects(coll, queryOnrequestId, offset, chunkSize, null, db);
                        if (dbList.size() == chunkSize) {
                            offset = offset + chunkSize;
                            dbList = getList(coll, queryOnrequestId, offset, db, dbList, chunkSize);
                        }
                        String jsonString;
                        if (dbList != null && dbList.size() > 0) {
                            try {
                                jsonString = JSON.serialize(dbList);

                                // Compress JSON data
                                byte[] compressedData = compressData(jsonString);
                                //String deCompressedData = decompressData(compressedData);
                                String fileName = requestId + "_" + "json.gz";
                                logger.info("CommV2FileLogsV2_v3 fileName : " + fileName);
                                String folderName = NStoS3util.MAP_NS_TO_S3_PATH.get(dbColl);
                                String url = s3service.addByteContentWithGzip(compressedData, NStoS3util.BUCKET_NAME_LOGGER_ARCHIVAL, folderName, fileName, "application/gzip");
                                if (url.equals(AppConstants.FILE_ALREADY_EXIST)) {
                                    try {
                                        commLoggerMongoDao.addRows("CommV2FileLogsV2_failOver", dbList, db);
                                    } catch (Exception e) {
                                        logger.error("Error while adding in moveCommV2FileLogsToS3V2 , {}", e.getMessage());
                                    }
                                }

                                for (DBObject reqIdDoc : dbList) {
                                    listOfIds.add(reqIdDoc.get("_id"));
                                }
                            } catch (Exception e) {
                                System.err.println("Error serializing DBObject: " + e.getMessage());
                                return;
                            }
                        }
                    }
                }
                requestIdListCommV2FileLogs = null;

                // to delete from source by list of _id
                try {
                    List<Object> subList = new ArrayList<>();
                    int startIndx = 0;
                    int lastIndx = 100;
                    if (listOfIds.size() > 100) {
                        while (startIndx < listOfIds.size()) {
                            if (startIndx + 100 >= listOfIds.size()) {
                                lastIndx = listOfIds.size();
                            }
                            subList = listOfIds.subList(startIndx, lastIndx);
                            DBObject queryFormovedIds = new BasicDBObject();
                            queryFormovedIds.put("_id", new BasicDBObject().append("$in", subList));
                            commLoggerMongoDao.deleteRow(coll, queryFormovedIds, true, db);
                            logger.info("CommV2FileLogsV2_v3 Last Id deleted : {}", subList.get(subList.size() - 1));
                            startIndx = lastIndx;
                            lastIndx = lastIndx + 100;
                            subList = null;
                        }
                    } else {
                        DBObject queryFormovedIds = new BasicDBObject();
                        queryFormovedIds.put("_id", new BasicDBObject().append("$in", listOfIds));
                        commLoggerMongoDao.deleteRow(coll, queryFormovedIds, true, db);
                        logger.info("CommV2FileLogsV2_v3 Last Id deleted : {}", listOfIds.get(listOfIds.size() - 1));
                    }
                    DBObject queryFormovedIds = new BasicDBObject();
                    queryFormovedIds.put("_id", new BasicDBObject().append("$in", listOfIds));

                } catch (Exception e) {
                    logger.error("Error while adding in removeCommV2FilesV2Logs , {}", e.getMessage());
                    if (!e.getMessage().contains("timeout") && !e.getMessage().contains("duplicate")) {
                        throw e;
                    }
                }

                // updating last _id in metaData master mongoDB collection: movedIdsLogger
                DBObject queryJson;
                if (commDeletedId != null && commDeletedId.size() > 0 && commDeletedId.getObjectId("_id") != null) {
                    totalMigrated = totalMigrated + dbObjectsList.size();
                    DBObject setquery = new BasicDBObject("movedId", dbObjectsList.get(dbObjectsList.size() - 1).get("_id"))
                            .append("totalMigrated", totalMigrated)
                            .append("updatedOn", new Date());
                    queryJson = new BasicDBObject("_id", commDeletedId.getObjectId("_id"));
                    DBObject rowJson = new BasicDBObject("$set", setquery);
                    newArchMongoDao.updateRow(NStoS3util.lastUpdatedIdColl, queryJson, rowJson, false, false, NStoS3util.lastUpdatedIdDB);
                }
                queryOnId.put("$gt", dbObjectsList.get(dbObjectsList.size() - 1).get("_id"));
                queryObject.put("_id", queryOnId);
                dbObjectsList = null; // explicitly nullify
                dbObjectsList = commLoggerMongoDao.getDBObjects(coll, queryObject, projection, 0, len, orderObject, db);
            }

        } catch (Exception e) {
            logger.error("Exception caught in CommV2FileLogsV2_v3, ns:{} , msg: {}", e.getMessage());
        } finally {
            isMoveCommV2FileLogsLoggerJobStopV1 = false;
            isMoveCommV2FileLogsLoggerJobRunningV1 = false;
        }
    }

    public void moveIntegrationRequestLogsLogsToS3(String dbColl, Integer size, int days, int chunkSize) {
        logger.info("Inside moveIntegrationRequestLogsLogsToS3");
        try {
            String[] nsArray = dbColl.split("\\.");
            String db = nsArray[0];
            String coll = nsArray[1];
            int len = size;
            long totalMigrated = 0;
            DBObject queryForDeletId = new BasicDBObject();
            queryForDeletId.put("coll", dbColl);
            BasicDBObject commDeletedId = newArchMongoDao.getDBObjectByDbName(NStoS3util.lastUpdatedIdColl, queryForDeletId, NStoS3util.lastUpdatedIdDB);
            DBObject queryOnId = new BasicDBObject();
            //only provide startId at once we are starting the batch.
            if (commDeletedId != null && commDeletedId.size() > 0) {
                queryOnId.put("$gte", commDeletedId.get("movedId"));
                totalMigrated = (long) commDeletedId.get("totalMigrated");
            }
            queryOnId.put("$lt", new ObjectId(DateUtil.getStringToDate(DateUtil.getFormatedDate(days, DateUtil.DATE_FORMAT_EE_MMM_dd_hh_mm_ss_z_yyyy), DateUtil.DATE_FORMAT_EE_MMM_dd_hh_mm_ss_z_yyyy)));
            DBObject queryObject = new BasicDBObject();
            queryObject.put("_id", queryOnId);
            BasicDBObject projection = new BasicDBObject("_id", 1).append("BookingId", 1);
            DBObject orderObject = new BasicDBObject("_id", 1);
            // get list from mongoDB
            List<DBObject> dbObjectsList = commLoggerMongoDao.getDBObjects(coll, queryObject, projection, 0, len, orderObject, db);
            logger.info("isMoveIntRequestLogsJobStop: {}, isMoveIntRequestLogsJobRunning: {}", isMoveIntRequestLogsJobStop, isMoveIntRequestLogsJobRunning);

            // if list have data then proceed
            while (dbObjectsList != null && dbObjectsList.size() > 0 && !isMoveIntRequestLogsJobStop) {
                Date currentDate = new Date();
                // Calculate the date 30 days ago
                Calendar calendar = Calendar.getInstance();
                calendar.setTime(currentDate);
                calendar.add(Calendar.DAY_OF_YEAR, -60);
                Date twoMonthAgo = calendar.getTime();
                List<Object> listOfIds = new ArrayList<>();
                HashSet<Integer> leadIdListIntReqLogs = new HashSet<>();
                List<DBObject> addFileNameObjList = new ArrayList<>();
                for (DBObject jsonDocument : dbObjectsList) {
                    listOfIds.add(jsonDocument.get("_id"));

                    DBObject queryOnrequestId = new BasicDBObject();
                    Integer leadId = null;
                    Long longLeadId = null;
                    if (jsonDocument.get("BookingId") instanceof Integer) {
                        leadId = (Integer) jsonDocument.get("BookingId");
                        queryOnrequestId.put("BookingId", leadId);
                    } else if (jsonDocument.get("BookingId") instanceof Long) {
                        longLeadId = (Long) jsonDocument.get("BookingId");
                        queryOnrequestId.put("BookingId", longLeadId);
                    }
                    if (((leadId != null && leadId > 0) || (longLeadId != null && longLeadId > 0)) && !leadIdListIntReqLogs.contains(leadId)) {
                        leadIdListIntReqLogs.add(leadId);
                        int offset = 0;
                        List<DBObject> dbList = commLoggerMongoDao.getDBObjects(coll, queryOnrequestId, offset, chunkSize, null, db);
                        if (dbList.size() == chunkSize) {
                            offset = offset + chunkSize;
                            dbList = getList(coll, queryOnrequestId, offset, db, dbList, chunkSize);
                        }
                        String jsonString;
                        if (dbList != null && dbList.size() > 0) {
                            try {

                                List<DBObject> listToAddInS3 = new ArrayList<>();
                                for (DBObject obj : dbList) {
                                    ObjectId objectId = new ObjectId(String.valueOf(obj.get("_id")));
                                    Date objectIdDate = objectId.getDate();
                                    if (!objectIdDate.after(twoMonthAgo)) {
                                        listToAddInS3.add(obj);
                                    }
                                }
                                if (!listToAddInS3.isEmpty()) {
                                    jsonString = JSON.serialize(listToAddInS3);
                                    // Compress JSON data
                                    byte[] compressedData = compressData(jsonString);
                                    //String deCompressedData = decompressData(compressedData);
                                    String fileName = leadId + "_1.json.gz";
                                    // check This fileName exist or Not
                                    DBObject orderQuery = new BasicDBObject("_id", -1);
                                    DBObject queryOnLeadIdId = new BasicDBObject().append("Leadid", leadId);
                                    List<DBObject> fileExistList = commLoggerMongoDao.getDBObjects("integrationReqLogsS3Filename", queryOnLeadIdId, null, 0, 1, orderQuery, db);
                                    if (fileExistList != null && fileExistList.size() > 0) {
                                        String lastFileName = (String) fileExistList.get(0).get("FileName");
                                        fileName = AppUtil.incrementFilenameNumber(lastFileName);
                                    }
                                    // add in lifeS3FilenameLogs collection for url map
                                    DBObject lifeFileObject = new BasicDBObject().append("Leadid", leadId).append("FileName", fileName).append("C_AT", new Date());
                                    addFileNameObjList.add(lifeFileObject);
                                    logger.info("BMS int RequestLogs fileName : " + fileName);
                                    String folderName = NStoS3util.MAP_NS_TO_S3_PATH.get(dbColl);
                                    String url = s3service.addByteContentWithGzip(compressedData, NStoS3util.BUCKET_NAME_LOGGER_ARCHIVAL, folderName, fileName, "application/gzip");
                                    if (url.equals(AppConstants.FILE_ALREADY_EXIST)) {
                                        try {
                                            commLoggerMongoDao.addRows(dbColl + "_failOver", dbList, db);
                                        } catch (Exception e) {
                                            logger.error("Error while adding in moveIntegrationRequestLogsLogsToS3 , {}", e.getMessage());
                                        }
                                    }

                                    for (DBObject reqIdDoc : listToAddInS3) {
                                        listOfIds.add(reqIdDoc.get("_id"));
                                    }
                                }
                            } catch (Exception e) {
                                System.err.println("Error serializing DBObject: " + e.getMessage());
                                return;
                            }
                        }
                    }
                }
                // add FileName Object list in docPushLogS3FilenameLog
                if (!CollectionUtils.isEmpty(addFileNameObjList)) {
                    commLoggerMongoDao.addRows("integrationReqLogsS3Filename", addFileNameObjList, db);
                }
                leadIdListIntReqLogs = null;

                // to delete from source by list of _id
                try {
                    List<Object> subList = new ArrayList<>();
                    int startIndx = 0;
                    int lastIndx = 100;
                    if (listOfIds.size() > 100) {
                        while (startIndx < listOfIds.size()) {
                            if (startIndx + 100 >= listOfIds.size()) {
                                lastIndx = listOfIds.size();
                            }
                            subList = listOfIds.subList(startIndx, lastIndx);
                            DBObject queryFormovedIds = new BasicDBObject();
                            queryFormovedIds.put("_id", new BasicDBObject().append("$in", subList));
                            commLoggerMongoDao.deleteRow(coll, queryFormovedIds, true, db);
                            logger.info("BMS int RequestLogs Last Id deleted : {}", subList.get(subList.size() - 1));
                            startIndx = lastIndx;
                            lastIndx = lastIndx + 100;
                            subList = null;
                        }
                    } else if (listOfIds != null && listOfIds.size() > 0) {
                        DBObject queryFormovedIds = new BasicDBObject();
                        queryFormovedIds.put("_id", new BasicDBObject().append("$in", listOfIds));
                        commLoggerMongoDao.deleteRow(coll, queryFormovedIds, true, db);
                        logger.info("BMS int RequestLogs  Last Id deleted : {}", listOfIds.get(listOfIds.size() - 1));
                    }

                } catch (Exception e) {
                    logger.error("Error while adding in moveIntegrationRequestLogsLogsToS3 , {}", e.getMessage());
                    if (!e.getMessage().contains("timeout") && !e.getMessage().contains("duplicate")) {
                        throw e;
                    }
                }

                // updating last _id in metaData master mongoDB collection: movedIdsLogger
                DBObject queryJson;
                if (commDeletedId != null && commDeletedId.size() > 0 && commDeletedId.getObjectId("_id") != null) {
                    totalMigrated = totalMigrated + dbObjectsList.size();
                    DBObject setquery = new BasicDBObject("movedId", dbObjectsList.get(dbObjectsList.size() - 1).get("_id"))
                            .append("totalMigrated", totalMigrated)
                            .append("updatedOn", new Date());
                    queryJson = new BasicDBObject("_id", commDeletedId.getObjectId("_id"));
                    DBObject rowJson = new BasicDBObject("$set", setquery);
                    newArchMongoDao.updateRow(NStoS3util.lastUpdatedIdColl, queryJson, rowJson, false, false, NStoS3util.lastUpdatedIdDB);
                }
                queryOnId.put("$gt", dbObjectsList.get(dbObjectsList.size() - 1).get("_id"));
                queryObject.put("_id", queryOnId);
                dbObjectsList = null; // explicitly nullify
                dbObjectsList = commLoggerMongoDao.getDBObjects(coll, queryObject, projection, 0, len, orderObject, db);
            }

        } catch (Exception e) {
            logger.error("Exception caught in moveIntegrationRequestLogsLogsToS3, ns:{} , msg: {}", e.getMessage());
        } finally {
            isMoveIntRequestLogsJobRunning = false;
            isMoveIntRequestLogsJobStop = false;
        }
    }

    public void syncShortUrl(String coll, Integer size, Boolean timeCheck, String db) throws Exception {
        logger.info("Inside syncShortUrl : {}");

        Date startDate = new Date();
        startDate = DateUtil.setHours(startDate, 7);
        startDate = DateUtil.setMinute(startDate, 0);
        startDate = DateUtil.setSeconds(startDate, 0);

        Date endDate = new Date();
        endDate = DateUtil.setHours(endDate, 20);
        endDate = DateUtil.setMinute(endDate, 0);
        endDate = DateUtil.setSeconds(endDate, 0);

        DBObject queryObject = new BasicDBObject();

        DBObject orderObject = new BasicDBObject("ud", 1);


        int offset = 0;
        int len = size;

        //one entry should be present in this.
        DBObject queryForDeletId = new BasicDBObject();
        //queryForDeletId.put("coll", coll);
        List<BasicDBObject> deletedIds = commMongoDao.getDBObjectsByDbName("processedIdsFromShortURLDetails", queryForDeletId, null, 0, 1, null, db);
        long count = deletedIds.get(0).getInt("cnt");
        DBObject queryOnId = new BasicDBObject();

        //only provide startId at once we are starting the batch.
        if (!deletedIds.isEmpty()) {
            queryOnId.put("$gte", deletedIds.get(0).get("lastUd"));
        }
        queryObject.put("ud", queryOnId);
        List<BasicDBObject> dbObjectsList = commMongoDao.getDBObjectsByDbNameObj(coll, queryObject, null, offset, len, orderObject, db);
        long startTime = 0;
        while (dbObjectsList != null && !dbObjectsList.isEmpty() && !StaticMaster.isSyncShortUrlStop()) {
            startTime = System.currentTimeMillis();
            logger.info("while entry : {}", new Date());
            Date todayDate = new Date();
            startDate = DateUtil.setHours(todayDate, 8);
            startDate = DateUtil.setMinute(startDate, 30);
            startDate = DateUtil.setSeconds(startDate, 0);

            endDate = DateUtil.setHours(todayDate, 20);
            endDate = DateUtil.setMinute(endDate, 0);
            endDate = DateUtil.setSeconds(endDate, 0);
            logger.info("Today date in service: {}, start date {} , end date {}", todayDate, startDate, endDate);
            if (todayDate.before(startDate) || todayDate.after(endDate) || !timeCheck) {
                logger.info("check data : {}", new Date());

                List<DBObject> insertList = new ArrayList<>();
                List<Object> deleteList = new ArrayList<>();
                List<Object> ids = new ArrayList<>();
                for (BasicDBObject res : dbObjectsList) {
                    ids.add(res.get("_id"));
                }
                List<BasicDBObject> newData = newCommMongoDao.getDBObjectsByDbName(coll, new BasicDBObject("_id", new BasicDBObject().append("$in", ids)), null, offset, 0, null, db);
                Map<Object, BasicDBObject> oldDbObjectListMap = new HashMap<>();
                Map<Object, BasicDBObject> newDbObjectListMap = new HashMap<>();
                if (!CollectionUtils.isEmpty(newData)) {
                    for (BasicDBObject res : dbObjectsList) {
                        oldDbObjectListMap.put(res.get("_id"), res);
//                        if (res.get("lurl") != null && !res.get("lurl").toString().isEmpty()) {
//                            oldDbObjectListMap.put(res.get("_id"), res);
//                        }
                    }
                    if (!oldDbObjectListMap.isEmpty()) {
                        for (BasicDBObject res : newData) {
                            newDbObjectListMap.put(res.get("_id"), res);
                        }
                        for (Object id : ids) {
                            if (!newDbObjectListMap.containsKey(id)) {
                                insertList.add(oldDbObjectListMap.get(id));
                            }
                        }
                    }

//                    if (!oldDbObjectListMap.isEmpty()) {
//                        for (BasicDBObject res : newData) {
//                            if (res.get("lurl") == null && oldDbObjectListMap.containsKey(res.get("_id"))){
//                                deleteList.add(res.get("_id"));
//                                insertList.add(oldDbObjectListMap.get(res.get("_id")));
//                            }
                }
            }

//                    if(!CollectionUtils.isEmpty(deleteList) && !CollectionUtils.isEmpty(insertList)){
//                        newCommMongoDao.deleteRow(coll, new BasicDBObject().append("_id" , new BasicDBObject().append("$in", deleteList)), true,db);
//                        newCommMongoDao.addRows(coll,insertList,db);
//                        logger.info("successfully deleted & inserted data in new shortUrl");
//                    }

            logger.info("delete shortUrl data : {} sec", (startTime - System.currentTimeMillis()) / 1000);
            logger.info("shortUrl Last Id deleted : {}", dbObjectsList.get(dbObjectsList.size() - 1).getObjectId("_id"));

            if (!deletedIds.isEmpty() && deletedIds.get(0).getObjectId("_id") != null) {
                count += dbObjectsList.size();
                DBObject setquery = new BasicDBObject("lastUd", dbObjectsList.get(dbObjectsList.size() - 1).getDate("ud")).append("cnt", count);
                DBObject queryJson = new BasicDBObject("_id", deletedIds.get(0).getObjectId("_id"));
                DBObject rowJson = new BasicDBObject("$set", setquery);
                commMongoDao.updateRow("processedIdsFromShortURLDetails", queryJson, rowJson, false, false, db);
            }

            logger.info("record update: {} sec", (startTime - System.currentTimeMillis()) / 1000);

            queryOnId.put("$gt", dbObjectsList.get(dbObjectsList.size() - 1).getDate("ud"));

            queryObject.put("ud", queryOnId);
            dbObjectsList = commMongoDao.getDBObjectsByDbNameObj(coll, queryObject, null, offset, len, orderObject, db);

            logger.info("new data : {} sec", (startTime - System.currentTimeMillis()) / 1000);
        }
    }

    public void tempMoveDocPushLogsToS3V1(String dbColl, Integer size, int days, int chunkSize) {

        logger.info("Inside moveDocPushLogsToS3");
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        try {
            String[] nsArray = dbColl.split("\\.");
            String db = nsArray[0];
            String coll = nsArray[1];
            int len = size;
            long totalMigrated = 0;
            DBObject queryForDeletId = new BasicDBObject();
            queryForDeletId.put("coll", dbColl);
            BasicDBObject commDeletedId = newArchMongoDao.getDBObjectByDbName(NStoS3util.lastUpdatedIdColl, queryForDeletId, NStoS3util.lastUpdatedIdDB);
            DBObject queryOnId = new BasicDBObject();
            //only provide startId at once we are starting the batch.
            if (commDeletedId != null && commDeletedId.size() > 0) {
                queryOnId.put("$gte", commDeletedId.get("movedId"));
                totalMigrated = (long) commDeletedId.get("totalMigrated");
            }
            queryOnId.put("$lt", new ObjectId(DateUtil.getStringToDate(DateUtil.getFormatedDate(days, DateUtil.DATE_FORMAT_EE_MMM_dd_hh_mm_ss_z_yyyy), DateUtil.DATE_FORMAT_EE_MMM_dd_hh_mm_ss_z_yyyy)));
            DBObject queryObject = new BasicDBObject();
            queryObject.put("_id", queryOnId);
            DBObject orderObject = new BasicDBObject("_id", 1);
            // get list from mongoDB
            List<DBObject> dbObjectsList = newArchMongoDao.getDBObjects(coll, queryObject, null, 0, len, orderObject, db);
            logger.info("isTempMoveDocPushLogsJobStop: {}, isTempMoveDocPushLogsJobRunning: {}", isTempMoveDocPushLogsJobStop, isTempMoveDocPushLogsJobRunning);

            // if list have data then proceed
            while (dbObjectsList != null && dbObjectsList.size() > 0 && !isTempMoveDocPushLogsJobStop) {

                List<Object> listOfIds = new ArrayList<>();
                List<DBObject> addFileNameObjList = new ArrayList<>();
                for (DBObject jsonDocument : dbObjectsList) {
                    try {
                        listOfIds.add(jsonDocument.get("_id"));
                        String jsonString = JSON.serialize(jsonDocument);
                        // Compress JSON data
                        byte[] compressedData = compressData(jsonString);
                        //String deCompressedData = decompressData(compressedData);
                        ObjectId docObjId = (ObjectId) jsonDocument.get("_id");
                        Integer leadId = (Integer) jsonDocument.get("Leadid");
                        String createdOn = null;
                        if( jsonDocument.get("CreatedOn") instanceof Date){
                            createdOn = formatter.format(jsonDocument.get("CreatedOn"));
                        }
                        else createdOn = (String) jsonDocument.get("CreatedOn");
                        String methodName = (String) jsonDocument.get("MethodName");
                        String fileName = docObjId + "_json.gz";
                        // check This fileName exist or Not
                        DBObject docPushMetaFileObject = new BasicDBObject().append("_id", docObjId).append("Leadid", leadId).append("MethodName", methodName).append("fileName", fileName).append("CreatedOn", createdOn);
                        // add in docPushLogS3FilenameLogs collection for url map
                        addFileNameObjList.add(docPushMetaFileObject);
                        logger.info("BMS DocPushLog fileName : " + fileName);
                        String folderName = NStoS3util.MAP_NS_TO_S3_PATH.get("DocsPoint.DocPushLog");
                        String url = s3service.addByteContentWithGzip(compressedData, NStoS3util.BUCKET_NAME_LOGGER_ARCHIVAL, folderName, fileName, "application/gzip");
                        if (url.equals(AppConstants.FILE_ALREADY_EXIST)) {
                            try {
                                newArchMongoDao.addRow("DocPushLog_failOver", jsonDocument, db);
                            } catch (Exception e) {
                                logger.error("Error while adding in moveDocPushLogsToS3 , {}", e.getMessage());
                            }
                        }
                    } catch (Exception e) {
                        System.err.println("Error serializing DBObject: " + e.getMessage());
                        return;
                    }
                }
                // add FileName Object list in docPushLogS3FilenameLog
                if(!CollectionUtils.isEmpty(addFileNameObjList)){
                    try {
                        commLoggerMongoDao.addRows("DocPushLog_S3Archive", addFileNameObjList, "DocsPoint");
                    }catch (Exception e){
                        logger.error("Error while adding in DocPushLog_S3Archive , {}", e.getMessage());
                    }
                    addFileNameObjList = null;
                }

                // to delete from source by list of _id
                try {
                    List<Object> subList = new ArrayList<>();
                    int startIndx = 0;
                    int lastIndx = 100;
                    if (listOfIds.size() > 100) {
                        while (startIndx < listOfIds.size()) {
                            if (startIndx + 100 >= listOfIds.size()) {
                                lastIndx = listOfIds.size();
                            }
                            subList = listOfIds.subList(startIndx, lastIndx);
                            DBObject queryFormovedIds = new BasicDBObject();
                            queryFormovedIds.put("_id", new BasicDBObject().append("$in", subList));
                            newArchMongoDao.deleteRow(coll, queryFormovedIds, true, db);
                            logger.info("BMS DocPushLog Last Id deleted : {}", subList.get(subList.size() - 1));
                            startIndx = lastIndx;
                            lastIndx = lastIndx + 100;
                            subList = null;
                        }
                    } else if (listOfIds != null && listOfIds.size() > 0) {
                        DBObject queryFormovedIds = new BasicDBObject();
                        queryFormovedIds.put("_id", new BasicDBObject().append("$in", listOfIds));
                        newArchMongoDao.deleteRow(coll, queryFormovedIds, true, db);
                        logger.info("BMS DocPushLog  Last Id deleted : {}", listOfIds.get(listOfIds.size() - 1));
                    }

                } catch (Exception e) {
                    logger.error("Error while adding in moveDocPushLogsToS3 , {}", e.getMessage());
                    if (!e.getMessage().contains("timeout") && !e.getMessage().contains("duplicate")) {
                        throw e;
                    }
                }
                listOfIds = null;
                // updating last _id in metaData master mongoDB collection: movedIdsLogger
                DBObject queryJson;
                if (commDeletedId != null && commDeletedId.size() > 0 && commDeletedId.getObjectId("_id") != null) {
                    totalMigrated = totalMigrated + dbObjectsList.size();
                    DBObject setquery = new BasicDBObject("movedId", dbObjectsList.get(dbObjectsList.size() - 1).get("_id"))
                            .append("totalMigrated", totalMigrated)
                            .append("updatedOn", new Date());
                    queryJson = new BasicDBObject("_id", commDeletedId.getObjectId("_id"));
                    DBObject rowJson = new BasicDBObject("$set", setquery);
                    newArchMongoDao.updateRow(NStoS3util.lastUpdatedIdColl, queryJson, rowJson, false, false, NStoS3util.lastUpdatedIdDB);
                }
                queryOnId.put("$gt", dbObjectsList.get(dbObjectsList.size() - 1).get("_id"));
                queryObject.put("_id", queryOnId);
                dbObjectsList = null; // explicitly nullify
                dbObjectsList = newArchMongoDao.getDBObjects(coll, queryObject, null, 0, len, orderObject, db);
            }

        } catch (Exception e) {
            logger.error("Exception caught in moveDocPushLogsToS3, ns:{} , msg: {}", e.getMessage());
        } finally {
            isTempMoveDocPushLogsJobRunning = false;
            isTempMoveDocPushLogsJobStop = false;
        }
    }

    public void tempMoveDocPushLogsToS3V2(String dbColl, Integer size, int days, int chunkSize, String lte_id) {

        logger.info("Inside moveDocPushLogsToS3");
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        try {
            String[] nsArray = dbColl.split("\\.");
            String db = nsArray[0];
            String coll = nsArray[1];
            int len = size;
            long totalMigrated = 0;
            DBObject queryForDeletId = new BasicDBObject();
            queryForDeletId.put("coll", "CommBox_Logger-DocsPoint.DocPushLog_V2");
            BasicDBObject commDeletedId = newArchMongoDao.getDBObjectByDbName(NStoS3util.lastUpdatedIdColl, queryForDeletId, NStoS3util.lastUpdatedIdDB);
            DBObject queryOnId = new BasicDBObject();
            //only provide startId at once we are starting the batch.
            if (commDeletedId != null && commDeletedId.size() > 0) {
                queryOnId.put("$gte", commDeletedId.get("movedId"));
                totalMigrated = (long) commDeletedId.get("totalMigrated");
            }
            queryOnId.put("$lt", new ObjectId(lte_id));//new ObjectId(DateUtil.getStringToDate(DateUtil.getFormatedDate(days, DateUtil.DATE_FORMAT_EE_MMM_dd_hh_mm_ss_z_yyyy), DateUtil.DATE_FORMAT_EE_MMM_dd_hh_mm_ss_z_yyyy)));
            DBObject queryObject = new BasicDBObject();
            queryObject.put("_id", queryOnId);
            DBObject orderObject = new BasicDBObject("_id", 1);
            // get list from mongoDB
            List<DBObject> dbObjectsList = newArchMongoDao.getDBObjects(coll, queryObject, null, 0, len, orderObject, db);
            logger.info("isTempMoveDocPushLogsJobStopV2: {}, isTempMoveDocPushLogsJobRunningV2: {}", isTempMoveDocPushLogsJobStopV2, isTempMoveDocPushLogsJobRunningV2);

            // if list have data then proceed
            while (dbObjectsList != null && dbObjectsList.size() > 0 && !isTempMoveDocPushLogsJobStopV2) {

                List<Object> listOfIds = new ArrayList<>();
                List<DBObject> addFileNameObjList = new ArrayList<>();
                for (DBObject jsonDocument : dbObjectsList) {
                    try {
                        listOfIds.add(jsonDocument.get("_id"));
                        String jsonString = JSON.serialize(jsonDocument);
                        // Compress JSON data
                        byte[] compressedData = compressData(jsonString);
                        //String deCompressedData = decompressData(compressedData);
                        ObjectId docObjId = (ObjectId) jsonDocument.get("_id");
                        Integer leadId = (Integer) jsonDocument.get("Leadid");
                        String createdOn = null;
                        if( jsonDocument.get("CreatedOn") instanceof Date){
                            createdOn = formatter.format(jsonDocument.get("CreatedOn"));
                        }
                        else createdOn = (String) jsonDocument.get("CreatedOn");
                        String methodName = (String) jsonDocument.get("MethodName");
                        String fileName = docObjId + "_json.gz";
                        // check This fileName exist or Not
                        DBObject docPushMetaFileObject = new BasicDBObject().append("_id", docObjId).append("Leadid", leadId).append("MethodName", methodName).append("fileName", fileName).append("CreatedOn", createdOn);
                        // add in docPushLogS3FilenameLogs collection for url map
                        addFileNameObjList.add(docPushMetaFileObject);
                        logger.info("BMS DocPushLog V2 fileName : " + fileName);
                        String folderName = NStoS3util.MAP_NS_TO_S3_PATH.get("DocsPoint.DocPushLog");
                        String url = s3service.addByteContentWithGzip(compressedData, NStoS3util.BUCKET_NAME_LOGGER_ARCHIVAL, folderName, fileName, "application/gzip");
                        if (url.equals(AppConstants.FILE_ALREADY_EXIST)) {
                            try {
                                newArchMongoDao.addRow("DocPushLog_failOver", jsonDocument, db);
                            } catch (Exception e) {
                                logger.error("Error while adding in moveDocPushLogsToS3V2 , {}", e.getMessage());
                            }
                        }
                    } catch (Exception e) {
                        System.err.println("Error serializing DBObject: " + e.getMessage());
                        return;
                    }
                }
                // add FileName Object list in docPushLogS3FilenameLog
                if(!CollectionUtils.isEmpty(addFileNameObjList)){
                    try {
                        commLoggerMongoDao.addRows("DocPushLog_S3Archive", addFileNameObjList, "DocsPoint");
                    }catch (Exception e){
                        logger.error("Error while adding in DocPushLog_S3Archive , {}", e.getMessage());
                    }
                    addFileNameObjList = null;
                }

                // to delete from source by list of _id
                try {
                    List<Object> subList = new ArrayList<>();
                    int startIndx = 0;
                    int lastIndx = 100;
                    if (listOfIds.size() > 100) {
                        while (startIndx < listOfIds.size()) {
                            if (startIndx + 100 >= listOfIds.size()) {
                                lastIndx = listOfIds.size();
                            }
                            subList = listOfIds.subList(startIndx, lastIndx);
                            DBObject queryFormovedIds = new BasicDBObject();
                            queryFormovedIds.put("_id", new BasicDBObject().append("$in", subList));
                            newArchMongoDao.deleteRow(coll, queryFormovedIds, true, db);
                            logger.info("BMS DocPushLog V2 Last Id deleted : {}", subList.get(subList.size() - 1));
                            startIndx = lastIndx;
                            lastIndx = lastIndx + 100;
                            subList = null;
                        }
                    } else if (listOfIds != null && listOfIds.size() > 0) {
                        DBObject queryFormovedIds = new BasicDBObject();
                        queryFormovedIds.put("_id", new BasicDBObject().append("$in", listOfIds));
                        newArchMongoDao.deleteRow(coll, queryFormovedIds, true, db);
                        logger.info("BMS DocPushLog V2   Last Id deleted : {}", listOfIds.get(listOfIds.size() - 1));
                    }

                } catch (Exception e) {
                    logger.error("Error while adding in moveDocPushLogsToS3 V2  , {}", e.getMessage());
                    if (!e.getMessage().contains("timeout") && !e.getMessage().contains("duplicate")) {
                        throw e;
                    }
                }
                listOfIds = null;
                // updating last _id in metaData master mongoDB collection: movedIdsLogger
                DBObject queryJson;
                if (commDeletedId != null && commDeletedId.size() > 0 && commDeletedId.getObjectId("_id") != null) {
                    totalMigrated = totalMigrated + dbObjectsList.size();
                    DBObject setquery = new BasicDBObject("movedId", dbObjectsList.get(dbObjectsList.size() - 1).get("_id"))
                            .append("totalMigrated", totalMigrated)
                            .append("updatedOn", new Date());
                    queryJson = new BasicDBObject("_id", commDeletedId.getObjectId("_id"));
                    DBObject rowJson = new BasicDBObject("$set", setquery);
                    newArchMongoDao.updateRow(NStoS3util.lastUpdatedIdColl, queryJson, rowJson, false, false, NStoS3util.lastUpdatedIdDB);
                }
                queryOnId.put("$gt", dbObjectsList.get(dbObjectsList.size() - 1).get("_id"));
                queryObject.put("_id", queryOnId);
                dbObjectsList = null; // explicitly nullify
                dbObjectsList = newArchMongoDao.getDBObjects(coll, queryObject, null, 0, len, orderObject, db);
            }

        } catch (Exception e) {
            logger.error("Exception caught in moveDocPushLogsToS3, ns:{} , msg: {}", e.getMessage());
        } finally {
            isTempMoveDocPushLogsJobRunningV2 = false;
            isTempMoveDocPushLogsJobStopV2 = false;
        }
    }


    private static class BatchProcessor implements Callable<String> {
        private final Logger logger = LoggerFactory.getLogger(BatchProcessor.class);
        private ChatMongoDao commLoggerMongoDao;
        private S3BucketService s3service;

        private final List<DBObject> batchObjectsList;
        private final String dbColl;
        private final int chunkSize;
        private final String lte_Id;

        public BatchProcessor(ChatMongoDao commLoggerMongoDao, S3BucketService s3service, List<DBObject> batchObjectsList, String dbColl, int chunkSize, String lteId) {
            this.dbColl = dbColl;
            this.chunkSize = chunkSize;
            this.lte_Id = lteId;
            this.batchObjectsList = batchObjectsList;
            this.commLoggerMongoDao = commLoggerMongoDao;
            this.s3service = s3service;

        }

        @Override
        public String call() throws Exception {
            processBatch(commLoggerMongoDao, s3service, batchObjectsList, dbColl, chunkSize, lte_Id);
            return "job threads processed successfully";
        }

        private void processBatch(ChatMongoDao commLoggerMongoDao, S3BucketService s3service, List<DBObject> batchObjectsList, String dbColl, int chunkSize, String lte_Id) throws Exception {
            String[] nsArray = dbColl.split("\\.");
            String db = nsArray[0];
            String coll = nsArray[1];
            DBObject orderObject = new BasicDBObject("_id", 1);
            try {
                List<Object> listOfIds = new ArrayList<>();
                for (DBObject jsonDocument : batchObjectsList) {
                    listOfIds.add(jsonDocument.get("_id"));
                    String requestId = (String) jsonDocument.get("RequestId");
                    DBObject queryOnrequestId = new BasicDBObject();
                    queryOnrequestId.put("RequestId", requestId);
                    if (requestId != null) {
                        int offset = 0;
                        List<DBObject> dbList = commLoggerMongoDao.getDBObjects(coll, queryOnrequestId, offset, chunkSize, orderObject, db);
                        if (dbList.size() == chunkSize) {
                            offset = offset + chunkSize;
                            dbList = getList(coll, queryOnrequestId, offset, orderObject, db, dbList, chunkSize);
                        }
                        String jsonString;
                        if (dbList != null && dbList.size() > 0) {
                            try {
                                jsonString = JSON.serialize(dbList);
                                // Compress JSON data
                                byte[] compressedData = compressData(jsonString);
                                //String deCompressedData = decompressData(compressedData);
                                String fileName = requestId + "_" + "json.gz";
                                logger.info("Thread CommV2FileLogsV2_v1 fileName : " + fileName);
                                String folderName = NStoS3util.MAP_NS_TO_S3_PATH.get(dbColl);
                                String url = s3service.addByteContentWithGzip(compressedData, NStoS3util.BUCKET_NAME_LOGGER_ARCHIVAL, folderName, fileName, "application/gzip");
                                if (url.equals(AppConstants.FILE_ALREADY_EXIST)) {
                                    logger.info("already file exists");
                                    try {
                                        commLoggerMongoDao.addRows("CommV2FileLogsV2_failOver", dbList, db);
                                    } catch (Exception e) {
                                        logger.error("Error while adding in CommV2FileLogsV2 , {}", e.getMessage());
                                    }
                                }
                                for (DBObject reqIdDoc : dbList) {
                                    listOfIds.add(reqIdDoc.get("_id"));
                                }
                            } catch (Exception e) {
                                logger.error("Error serializing DBObject: , {}", e.getMessage());
                            }
                        }
                    }
                }

                // to delete from source by list of _id
                try {
                    List<Object> subList = new ArrayList<>();
                    int startIndx = 0;
                    int lastIndx = 100;
                    if (listOfIds.size() > 100) {
                        while (startIndx < listOfIds.size()) {
                            if (startIndx + 100 >= listOfIds.size()) {
                                lastIndx = listOfIds.size();
                            }
                            subList = listOfIds.subList(startIndx, lastIndx);
                            DBObject queryFormovedIds = new BasicDBObject();
                            queryFormovedIds.put("_id", new BasicDBObject().append("$in", subList));
                            commLoggerMongoDao.deleteRow(coll, queryFormovedIds, true, db);
                            logger.info("CommV2FileLogsV2_v1 Last Id deleted : {}", subList.get(subList.size() - 1));
                            startIndx = lastIndx;
                            lastIndx = lastIndx + 100;
                            subList = null;
                        }
                    } else {
                        DBObject queryFormovedIds = new BasicDBObject();
                        queryFormovedIds.put("_id", new BasicDBObject().append("$in", listOfIds));
                        commLoggerMongoDao.deleteRow(coll, queryFormovedIds, true, db);
                        logger.info("CommV2FileLogsV2_v1 Last Id deleted : {}", listOfIds.get(listOfIds.size() - 1));
                    }
                    DBObject queryFormovedIds = new BasicDBObject();
                    queryFormovedIds.put("_id", new BasicDBObject().append("$in", listOfIds));

                } catch (Exception e) {
                    logger.error("Error while adding in removeCommV2FilesV2Logs , {}", e.getMessage());
                    if (!e.getMessage().contains("timeout") && !e.getMessage().contains("duplicate")) {
                        throw e;
                    }
                }
            } catch (Exception e) {
                logger.error("Exception caught in moveCommV2FileLogsToS3, ns:{} , msg: {}", e.getMessage());
            }
        }

        private List<DBObject> getList(String coll, DBObject queryOnrequestId, int offset, DBObject orderObject, String db, List<DBObject> resultList, int chunkSize) {
            logger.info("in loop getList start");
            try {
                List<DBObject> dataList = commLoggerMongoDao.getDBObjects(coll, queryOnrequestId, offset, chunkSize, orderObject, db);
                while (dataList.size() > 0) {
                    resultList.addAll(dataList);
                    offset += chunkSize;
                    dataList = commLoggerMongoDao.getDBObjects(coll, queryOnrequestId, offset, chunkSize, orderObject, db);
                }
            } catch (Exception e) {
                logger.error("Exception while getList");
            }
            logger.info("in loop getList end");
            return resultList;
        }
    }


    private List<DBObject> getList(String coll, DBObject queryOnrequestId, int offset, String db, List<DBObject> resultList, int chunkSize) {

        try {
            List<DBObject> dataList = commLoggerMongoDao.getDBObjects(coll, queryOnrequestId, offset, chunkSize, null, db);
            while (dataList.size() > 0) {
                resultList.addAll(dataList);
                offset += chunkSize;
                dataList = commLoggerMongoDao.getDBObjects(coll, queryOnrequestId, offset, chunkSize, null, db);
                if(db.equalsIgnoreCase("DocsPoint") && coll.equalsIgnoreCase("DocPushLog") && resultList.size()>3000){
                    break;
                }
                else if(db.equalsIgnoreCase("DocsPoint") && coll.equalsIgnoreCase("RequestLogs") && resultList.size()>=3000){
                    break;
                }
                else if(db.equalsIgnoreCase("Integration") && coll.equalsIgnoreCase("RequestLogs") && resultList.size()>=3000){
                    break;
                }
                else if(db.equalsIgnoreCase("Integration") && coll.equalsIgnoreCase("ExternalAPILogs") && resultList.size()>=1000){
                    offset = 0;
                    while (dataList.size() > 0) {
                        resultList.addAll(dataList);
                        offset += chunkSize;
                        deleteForExcessDocs(coll, db, resultList);
                        dataList = commLoggerMongoDao.getDBObjects(coll, queryOnrequestId, offset, chunkSize, null, db);
                        resultList = new ArrayList<>();
                    }
                }
            }
        } catch (Exception e) {
            logger.error("Exception while getList");
        }
        return resultList;
    }

    private void deleteForExcessDocs(String coll, String db, List<DBObject> resultList) throws Exception {
        try {
            List<Object>listOfIds = new ArrayList<>();
            for (DBObject reqIdDoc : resultList) {
                listOfIds.add(reqIdDoc.get("_id"));
            }
            List<Object> subList = new ArrayList<>();
            int startIndx = 0;
            int lastIndx = 100;
            if (listOfIds.size() > 100) {
                while (startIndx < listOfIds.size()) {
                    if (startIndx + 100 >= listOfIds.size()) {
                        lastIndx = listOfIds.size();
                    }
                    subList = listOfIds.subList(startIndx, lastIndx);
                    DBObject queryFormovedIds = new BasicDBObject();
                    queryFormovedIds.put("_id", new BasicDBObject().append("$in", subList));
                    commLoggerMongoDao.deleteRow(coll, queryFormovedIds, true, db);
                    logger.info("BMS Integration.ExternalAPILogs Last Id deleted : {}", subList.get(subList.size() - 1));
                    startIndx = lastIndx;
                    lastIndx = lastIndx + 100;
                    subList = null;
                }
            } else if (listOfIds != null && listOfIds.size() > 0) {
                DBObject queryFormovedIds = new BasicDBObject();
                queryFormovedIds.put("_id", new BasicDBObject().append("$in", listOfIds));
                commLoggerMongoDao.deleteRow(coll, queryFormovedIds, true, db);
                logger.info("BMS Integration.ExternalAPILogs  Last Id deleted : {}", listOfIds.get(listOfIds.size() - 1));
            }

        } catch (Exception e) {
            logger.error("Error while adding in moveIntegrationExternalAPILogsToS3 , {}", e.getMessage());
            if (!e.getMessage().contains("timeout") && !e.getMessage().contains("duplicate")) {
                throw e;
            }
        }
    }

    // Method to compress data using GZIP
    private static byte[] compressData(String data) {
        ByteArrayOutputStream outputStream = null;
        GZIPOutputStream gzipOutputStream = null;
        try {
            outputStream = new ByteArrayOutputStream();
            gzipOutputStream = new GZIPOutputStream(outputStream);
            gzipOutputStream.write(data.getBytes());
            gzipOutputStream.close();
            return outputStream.toByteArray();
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        } finally {
            if (outputStream != null) {
                try {
                    outputStream.close();
                } catch (IOException e) {
                }
            }
            if (gzipOutputStream != null) {
                try {
                    gzipOutputStream.close();
                } catch (IOException e) {
                }
            }
        }
    }

    public void movePIVCLogsToS3(String dbColl, Integer size, int days, int chunkSize) {

        logger.info("Inside movePIVCLogsToS3");
        try {
            String[] nsArray = dbColl.split("\\.");
            String db = nsArray[0];
            String coll = nsArray[1];
            ;
            int len = size;
            long totalMigrated = 0;
            DBObject queryForDeletId = new BasicDBObject();
            queryForDeletId.put("coll", dbColl);
            BasicDBObject commDeletedId = newArchMongoDao.getDBObjectByDbName(NStoS3util.lastUpdatedIdColl, queryForDeletId, NStoS3util.lastUpdatedIdDB);
            DBObject queryOnId = new BasicDBObject();
            //only provide startId at once we are starting the batch.
            if (commDeletedId != null && commDeletedId.size() > 0) {
                queryOnId.put("$gte", commDeletedId.get("movedId"));
                totalMigrated = (long) commDeletedId.get("totalMigrated");
            }
            queryOnId.put("$lt", new ObjectId(DateUtil.getStringToDate(DateUtil.getFormatedDate(days, DateUtil.DATE_FORMAT_EE_MMM_dd_hh_mm_ss_z_yyyy), DateUtil.DATE_FORMAT_EE_MMM_dd_hh_mm_ss_z_yyyy)));
            DBObject queryObject = new BasicDBObject();
            queryObject.put("_id", queryOnId);
            BasicDBObject projection = new BasicDBObject("_id", 1).append("LeadId", 1);
            DBObject orderObject = new BasicDBObject("_id", 1);
            // get list from mongoDB
            List<DBObject> dbObjectsList = commLoggerMongoDao.getDBObjects(coll, queryObject, projection, 0, len, orderObject, db);
            logger.info("isMovePIVCLogsJobStop: {}, isMovePIVCLogsJobRunning: {}", isMovePIVCLogsJobStop, isMovePIVCLogsJobRunning);

            // if list have data then proceed
            while (dbObjectsList != null && dbObjectsList.size() > 0 && !isMovePIVCLogsJobStop) {
                List<Object> listOfIds = new ArrayList<>();
                HashSet<Integer> requestIdListCommV2FileLogs = new HashSet<>();

                for (DBObject jsonDocument : dbObjectsList) {
                    listOfIds.add(jsonDocument.get("_id"));
                    Integer leadId = (Integer) jsonDocument.get("LeadId");
                    DBObject queryOnrequestId = new BasicDBObject();
                    queryOnrequestId.put("LeadId", leadId);
                    if (leadId != null && leadId > 0 && !requestIdListCommV2FileLogs.contains(leadId)) {
                        requestIdListCommV2FileLogs.add(leadId);
                        int offset = 0;
                        List<DBObject> dbList = commLoggerMongoDao.getDBObjects(coll, queryOnrequestId, offset, chunkSize, null, db);
                        if (dbList.size() == chunkSize) {
                            offset = offset + chunkSize;
                            dbList = getList(coll, queryOnrequestId, offset, db, dbList, chunkSize);
                        }
                        String jsonString;
                        if (dbList != null && dbList.size() > 0) {
                            try {
                                jsonString = JSON.serialize(dbList);

                                // Compress JSON data
                                byte[] compressedData = compressData(jsonString);
                                //String deCompressedData = decompressData(compressedData);
                                String fileName = leadId + "_" + "json.gz";
                                logger.info("PIVCLogs fileName : " + fileName);
                                String folderName = NStoS3util.MAP_NS_TO_S3_PATH.get(dbColl);
                                String url = s3service.addByteContentWithGzip(compressedData, NStoS3util.BUCKET_NAME_LOGGER_ARCHIVAL, folderName, fileName, "application/gzip");
                                if (url.equals(AppConstants.FILE_ALREADY_EXIST)) {
                                    try {
                                        commLoggerMongoDao.addRows("PIVCLogs_failOver", dbList, db);
                                    } catch (Exception e) {
                                        logger.error("Error while adding in movePIVCLogsToS3 , {}", e.getMessage());
                                    }
                                }

                                for (DBObject reqIdDoc : dbList) {
                                    listOfIds.add(reqIdDoc.get("_id"));
                                }
                            } catch (Exception e) {
                                System.err.println("Error serializing DBObject: " + e.getMessage());
                                return;
                            }
                        }
                    }
                }
                requestIdListCommV2FileLogs = null;

                // to delete from source by list of _id
                try {
                    List<Object> subList = new ArrayList<>();
                    int startIndx = 0;
                    int lastIndx = 100;
                    if (listOfIds.size() > 100) {
                        while (startIndx < listOfIds.size()) {
                            if (startIndx + 100 >= listOfIds.size()) {
                                lastIndx = listOfIds.size();
                            }
                            subList = listOfIds.subList(startIndx, lastIndx);
                            DBObject queryFormovedIds = new BasicDBObject();
                            queryFormovedIds.put("_id", new BasicDBObject().append("$in", subList));
                            commLoggerMongoDao.deleteRow(coll, queryFormovedIds, true, db);
                            logger.info("PIVCLogs Last Id deleted : {}", subList.get(subList.size() - 1));
                            startIndx = lastIndx;
                            lastIndx = lastIndx + 100;
                            subList = null;
                        }
                    } else if (listOfIds != null && listOfIds.size() > 0) {
                        DBObject queryFormovedIds = new BasicDBObject();
                        queryFormovedIds.put("_id", new BasicDBObject().append("$in", listOfIds));
                        commLoggerMongoDao.deleteRow(coll, queryFormovedIds, true, db);
                        logger.info("PIVCLogs Last Id deleted : {}", listOfIds.get(listOfIds.size() - 1));
                    }

                } catch (Exception e) {
                    logger.error("Error while adding in movePIVCLogsToS3 , {}", e.getMessage());
                    if (!e.getMessage().contains("timeout") && !e.getMessage().contains("duplicate")) {
                        throw e;
                    }
                }

                // updating last _id in metaData master mongoDB collection: movedIdsLogger
                DBObject queryJson;
                if (commDeletedId != null && commDeletedId.size() > 0 && commDeletedId.getObjectId("_id") != null) {
                    totalMigrated = totalMigrated + dbObjectsList.size();
                    DBObject setquery = new BasicDBObject("movedId", dbObjectsList.get(dbObjectsList.size() - 1).get("_id"))
                            .append("totalMigrated", totalMigrated)
                            .append("updatedOn", new Date());
                    queryJson = new BasicDBObject("_id", commDeletedId.getObjectId("_id"));
                    DBObject rowJson = new BasicDBObject("$set", setquery);
                    newArchMongoDao.updateRow(NStoS3util.lastUpdatedIdColl, queryJson, rowJson, false, false, NStoS3util.lastUpdatedIdDB);
                }
                queryOnId.put("$gt", dbObjectsList.get(dbObjectsList.size() - 1).get("_id"));
                queryObject.put("_id", queryOnId);
                dbObjectsList = null; // explicitly nullify
                dbObjectsList = commLoggerMongoDao.getDBObjects(coll, queryObject, projection, 0, len, orderObject, db);
            }

        } catch (Exception e) {
            logger.error("Exception caught in movePIVCLogsToS3, ns:{} , msg: {}", dbColl, e.getMessage());
        } finally {
            isMovePIVCLogsJobRunning = false;
            isMovePIVCLogsJobStop = false;
        }
    }

    public void moveDocPushLogsToS3(String dbColl, Integer size, int days, int chunkSize) {

        logger.info("Inside moveDocPushLogsToS3");
        try {
            String[] nsArray = dbColl.split("\\.");
            String db = nsArray[0];
            String coll = nsArray[1];
            int len = size;
            long totalMigrated = 0;
            DBObject queryForDeletId = new BasicDBObject();
            queryForDeletId.put("coll", dbColl);
            BasicDBObject commDeletedId = newArchMongoDao.getDBObjectByDbName(NStoS3util.lastUpdatedIdColl, queryForDeletId, NStoS3util.lastUpdatedIdDB);
            DBObject queryOnId = new BasicDBObject();
            //only provide startId at once we are starting the batch.
            if (commDeletedId != null && commDeletedId.size() > 0) {
                queryOnId.put("$gte", commDeletedId.get("movedId"));
                totalMigrated = (long) commDeletedId.get("totalMigrated");
            }
            queryOnId.put("$lt", new ObjectId(DateUtil.getStringToDate(DateUtil.getFormatedDate(days, DateUtil.DATE_FORMAT_EE_MMM_dd_hh_mm_ss_z_yyyy), DateUtil.DATE_FORMAT_EE_MMM_dd_hh_mm_ss_z_yyyy)));
            DBObject queryObject = new BasicDBObject();
            queryObject.put("_id", queryOnId);
            BasicDBObject projection = new BasicDBObject("_id", 1).append("Leadid", 1);
            DBObject orderObject = new BasicDBObject("_id", 1);
            // get list from mongoDB
            List<DBObject> dbObjectsList = commLoggerMongoDao.getDBObjects(coll, queryObject, projection, 0, len, orderObject, db);
            logger.info("isMoveDocPushLogsJobStop: {}, isMoveDocPushLogsJobRunning: {}", isMoveDocPushLogsJobStop, isMoveDocPushLogsJobRunning);

            // if list have data then proceed
            while (dbObjectsList != null && dbObjectsList.size() > 0 && !isMoveDocPushLogsJobStop) {
                Date currentDate = new Date();
                // Calculate the date 90 days ago
                Calendar calendar = Calendar.getInstance();
                calendar.setTime(currentDate);
                calendar.add(Calendar.DAY_OF_YEAR, -90);
                Date threeMonthAgo = calendar.getTime();
                List<Object> listOfIds = new ArrayList<>();
                HashSet<Integer> leadIdListDocPushLogLogs = new HashSet<>();
                List<DBObject> addFileNameObjList = new ArrayList<>();
                for (DBObject jsonDocument : dbObjectsList) {
                    listOfIds.add(jsonDocument.get("_id"));
                    Integer leadId = (Integer) jsonDocument.get("Leadid");
                    DBObject queryOnLeadId = new BasicDBObject();
                    queryOnLeadId.put("Leadid", leadId);
                    if (leadId != null && leadId > 0 && !leadIdListDocPushLogLogs.contains(leadId)) {
                        leadIdListDocPushLogLogs.add(leadId);
                        int offset = 0;
                        List<DBObject> dbList = commLoggerMongoDao.getDBObjects(coll, queryOnLeadId, offset, chunkSize, null, db);
                        if (dbList.size() == chunkSize) {
                            offset = offset + chunkSize;
                            dbList = getList(coll, queryOnLeadId, offset, db, dbList, chunkSize);
                        }
                        if (dbList != null && dbList.size() > 0) {
                            try {
                                List<DBObject>listToAddInS3 = new ArrayList<>();
                                for(DBObject obj : dbList) {
                                    ObjectId objectId = new ObjectId(String.valueOf(obj.get("_id")));
                                    Date objectIdDate = objectId.getDate();
                                    if (!objectIdDate.after(threeMonthAgo)) {
                                        listToAddInS3.add(obj);
                                    }
                                }
                                if(!listToAddInS3.isEmpty()) {
                                    String jsonString = JSON.serialize(listToAddInS3);
                                    // Compress JSON data
                                    byte[] compressedData = compressData(jsonString);
                                    //String deCompressedData = decompressData(compressedData);
                                    String fileName = leadId + "_1.json.gz";
                                    // check This fileName exist or Not
                                    DBObject orderQuery = new BasicDBObject("_id", -1);
                                    List<DBObject> fileExistList = commLoggerMongoDao.getDBObjects("docPushLogS3FilenameLogs", queryOnLeadId, null, 0, 1, orderQuery, db);
                                    if (fileExistList != null && fileExistList.size() > 0) {
                                        String lastFileName = (String) fileExistList.get(0).get("FileName");
                                        fileName = AppUtil.incrementFilenameNumber(lastFileName);
                                    }
                                    // add in lifeS3FilenameLogs collection for url map
                                    DBObject lifeFileObject = new BasicDBObject().append("Leadid", leadId).append("FileName", fileName).append("C_AT", new Date());
                                    addFileNameObjList.add(lifeFileObject);
                                    logger.info("BMS DocPushLog fileName : " + fileName);
                                    String folderName = NStoS3util.MAP_NS_TO_S3_PATH.get(dbColl);
                                    String url = s3service.addByteContentWithGzip(compressedData, NStoS3util.BUCKET_NAME_LOGGER_ARCHIVAL, folderName, fileName, "application/gzip");
                                    if (url.equals(AppConstants.FILE_ALREADY_EXIST)) {
                                        try {
                                            commLoggerMongoDao.addRows("DocPushLog_failOver", dbList, db);
                                        } catch (Exception e) {
                                            logger.error("Error while adding in moveDocPushLogsToS3 , {}", e.getMessage());
                                        }
                                    }

                                    for (DBObject reqIdDoc : listToAddInS3) {
                                        listOfIds.add(reqIdDoc.get("_id"));
                                    }
                                }
                            } catch (Exception e) {
                                System.err.println("Error serializing DBObject: " + e.getMessage());
                                return;
                            }
                        }
                    }
                }
                // add FileName Object list in docPushLogS3FilenameLog
                if(!CollectionUtils.isEmpty(addFileNameObjList)){
                    commLoggerMongoDao.addRows("docPushLogS3FilenameLogs", addFileNameObjList, db);
                }
                leadIdListDocPushLogLogs = null;

                // to delete from source by list of _id
                try {
                    List<Object> subList = new ArrayList<>();
                    int startIndx = 0;
                    int lastIndx = 100;
                    if (listOfIds.size() > 100) {
                        while (startIndx < listOfIds.size()) {
                            if (startIndx + 100 >= listOfIds.size()) {
                                lastIndx = listOfIds.size();
                            }
                            subList = listOfIds.subList(startIndx, lastIndx);
                            DBObject queryFormovedIds = new BasicDBObject();
                            queryFormovedIds.put("_id", new BasicDBObject().append("$in", subList));
                            commLoggerMongoDao.deleteRow(coll, queryFormovedIds, true, db);
                            logger.info("BMS DocPushLog Last Id deleted : {}", subList.get(subList.size() - 1));
                            startIndx = lastIndx;
                            lastIndx = lastIndx + 100;
                            subList = null;
                        }
                    } else if (listOfIds != null && listOfIds.size() > 0) {
                        DBObject queryFormovedIds = new BasicDBObject();
                        queryFormovedIds.put("_id", new BasicDBObject().append("$in", listOfIds));
                        commLoggerMongoDao.deleteRow(coll, queryFormovedIds, true, db);
                        logger.info("BMS DocPushLog  Last Id deleted : {}", listOfIds.get(listOfIds.size() - 1));
                    }

                } catch (Exception e) {
                    logger.error("Error while adding in moveDocPushLogsToS3 , {}", e.getMessage());
                    if (!e.getMessage().contains("timeout") && !e.getMessage().contains("duplicate")) {
                        throw e;
                    }
                }
                listOfIds = null;
                // updating last _id in metaData master mongoDB collection: movedIdsLogger
                DBObject queryJson;
                if (commDeletedId != null && commDeletedId.size() > 0 && commDeletedId.getObjectId("_id") != null) {
                    totalMigrated = totalMigrated + dbObjectsList.size();
                    DBObject setquery = new BasicDBObject("movedId", dbObjectsList.get(dbObjectsList.size() - 1).get("_id"))
                            .append("totalMigrated", totalMigrated)
                            .append("updatedOn", new Date());
                    queryJson = new BasicDBObject("_id", commDeletedId.getObjectId("_id"));
                    DBObject rowJson = new BasicDBObject("$set", setquery);
                    newArchMongoDao.updateRow(NStoS3util.lastUpdatedIdColl, queryJson, rowJson, false, false, NStoS3util.lastUpdatedIdDB);
                }
                queryOnId.put("$gt", dbObjectsList.get(dbObjectsList.size() - 1).get("_id"));
                queryObject.put("_id", queryOnId);
                dbObjectsList = null; // explicitly nullify
                dbObjectsList = commLoggerMongoDao.getDBObjects(coll, queryObject, projection, 0, len, orderObject, db);
            }

        } catch (Exception e) {
            logger.error("Exception caught in moveDocPushLogsToS3, ns:{} , msg: {}", e.getMessage());
        } finally {
            isMoveDocPushLogsJobRunning = false;
            isMoveDocPushLogsJobStop = false;
        }
    }

    public void moveRequestLogsLogsToS3(String dbColl, Integer size, int days, int chunkSize) {

        logger.info("Inside moveRequestLogsLogsToS3");
        try {
            String[] nsArray = dbColl.split("\\.");
            String db = nsArray[0];
            String coll = nsArray[1];
            int len = size;
            long totalMigrated = 0;
            DBObject queryForDeletId = new BasicDBObject();
            queryForDeletId.put("coll", dbColl);
            BasicDBObject commDeletedId = newArchMongoDao.getDBObjectByDbName(NStoS3util.lastUpdatedIdColl, queryForDeletId, NStoS3util.lastUpdatedIdDB);
            DBObject queryOnId = new BasicDBObject();
            //only provide startId at once we are starting the batch.
            if (commDeletedId != null && commDeletedId.size() > 0) {
                queryOnId.put("$gte", commDeletedId.get("movedId"));
                totalMigrated = (long) commDeletedId.get("totalMigrated");
            }
            queryOnId.put("$lt", new ObjectId(DateUtil.getStringToDate(DateUtil.getFormatedDate(days, DateUtil.DATE_FORMAT_EE_MMM_dd_hh_mm_ss_z_yyyy), DateUtil.DATE_FORMAT_EE_MMM_dd_hh_mm_ss_z_yyyy)));
            DBObject queryObject = new BasicDBObject();
            queryObject.put("_id", queryOnId);
            BasicDBObject projection = new BasicDBObject("_id", 1).append("Leadid", 1);
            DBObject orderObject = new BasicDBObject("_id", 1);
            // get list from mongoDB
            List<DBObject> dbObjectsList = commLoggerMongoDao.getDBObjects(coll, queryObject, projection, 0, len, orderObject, db);
            logger.info("isMoveRequestLogsJobStop: {}, isMoveRequestLogsJobRunning: {}", isMoveRequestLogsJobStop, isMoveRequestLogsJobRunning);

            // if list have data then proceed
            while (dbObjectsList != null && dbObjectsList.size() > 0 && !isMoveRequestLogsJobStop) {
                Date currentDate = new Date();
                // Calculate the date 90 days ago
                Calendar calendar = Calendar.getInstance();
                calendar.setTime(currentDate);
                calendar.add(Calendar.DAY_OF_YEAR, -60);
                Date twoMonthAgo = calendar.getTime();
                List<Object> listOfIds = new ArrayList<>();
                HashSet<Integer> leadIdListDocPushLogLogs = new HashSet<>();
                List<DBObject> addFileNameObjList = new ArrayList<>();
                for (DBObject jsonDocument : dbObjectsList) {
                    listOfIds.add(jsonDocument.get("_id"));
                    Integer leadId = (Integer) jsonDocument.get("Leadid");
                    DBObject queryOnLeadId = new BasicDBObject();
                    queryOnLeadId.put("Leadid", leadId);
                    if (leadId != null && leadId > 0 && !leadIdListDocPushLogLogs.contains(leadId)) {
                        leadIdListDocPushLogLogs.add(leadId);
                        int offset = 0;
                        List<DBObject> dbList = commLoggerMongoDao.getDBObjects(coll, queryOnLeadId, offset, chunkSize, null, db);
                        if (dbList.size() == chunkSize) {
                            offset = offset + chunkSize;
                            dbList = getList(coll, queryOnLeadId, offset, db, dbList, chunkSize);
                        }
                        String jsonString;
                        if (dbList != null && dbList.size() > 0) {
                            try {
                                List<DBObject> listToAddInS3 = new ArrayList<>();
                                for (DBObject obj : dbList) {
                                    ObjectId objectId = new ObjectId(String.valueOf(obj.get("_id")));
                                    Date objectIdDate = objectId.getDate();
                                    if (!objectIdDate.after(twoMonthAgo)) {
                                        listToAddInS3.add(obj);
                                    }
                                }
                                if (!listToAddInS3.isEmpty()) {
                                    jsonString = JSON.serialize(listToAddInS3);
                                    // Compress JSON data
                                    byte[] compressedData = compressData(jsonString);
                                    //String deCompressedData = decompressData(compressedData);
                                    String fileName = leadId + "_1.json.gz";
                                    // check This fileName exist or Not
                                    DBObject orderQuery = new BasicDBObject("_id", -1);
                                    List<DBObject> fileExistList = commLoggerMongoDao.getDBObjects("docPointRequestLogsS3Filename", queryOnLeadId, null, 0, 1, orderQuery, db);
                                    if (fileExistList != null && fileExistList.size() > 0) {
                                        String lastFileName = (String) fileExistList.get(0).get("FileName");
                                        fileName = AppUtil.incrementFilenameNumber(lastFileName);
                                    }
                                    // add in lifeS3FilenameLogs collection for url map
                                    DBObject reqLogFileObject = new BasicDBObject().append("Leadid", leadId).append("FileName", fileName).append("C_AT", new Date());
                                    addFileNameObjList.add(reqLogFileObject);
                                    logger.info("BMS doc RequestLogs fileName : " + fileName);
                                    String folderName = NStoS3util.MAP_NS_TO_S3_PATH.get(dbColl);
                                    String url = s3service.addByteContentWithGzip(compressedData, NStoS3util.BUCKET_NAME_LOGGER_ARCHIVAL, folderName, fileName, "application/gzip");
                                    if (url.equals(AppConstants.FILE_ALREADY_EXIST)) {
                                        try {
                                            commLoggerMongoDao.addRows(coll + "_failOver", dbList, db);
                                        } catch (Exception e) {
                                            logger.error("Error while adding in moveRequestLogsLogsToS3 , {}", e.getMessage());
                                        }
                                    }
                                    for (DBObject reqIdDoc : listToAddInS3) {
                                        listOfIds.add(reqIdDoc.get("_id"));
                                    }
                                }
                            }catch (Exception e) {
                                System.err.println("Error serializing DBObject: " + e.getMessage());
                                return;
                            }
                        }
                    }
                }
                // add FileName Object list in docPushLogS3FilenameLog
                if(!CollectionUtils.isEmpty(addFileNameObjList)){
                    commLoggerMongoDao.addRows("docPointRequestLogsS3Filename", addFileNameObjList, db);
                }
                leadIdListDocPushLogLogs = null;

                // to delete from source by list of _id
                try {
                    List<Object> subList = new ArrayList<>();
                    int startIndx = 0;
                    int lastIndx = 100;
                    if (listOfIds.size() > 100) {
                        while (startIndx < listOfIds.size()) {
                            if (startIndx + 100 >= listOfIds.size()) {
                                lastIndx = listOfIds.size();
                            }
                            subList = listOfIds.subList(startIndx, lastIndx);
                            DBObject queryFormovedIds = new BasicDBObject();
                            queryFormovedIds.put("_id", new BasicDBObject().append("$in", subList));
                            commLoggerMongoDao.deleteRow(coll, queryFormovedIds, true, db);
                            logger.info("BMS doc RequestLogs Last Id deleted : {}", subList.get(subList.size() - 1));
                            startIndx = lastIndx;
                            lastIndx = lastIndx + 100;
                            subList = null;
                        }
                    } else if (listOfIds != null && listOfIds.size() > 0) {
                        DBObject queryFormovedIds = new BasicDBObject();
                        queryFormovedIds.put("_id", new BasicDBObject().append("$in", listOfIds));
                        commLoggerMongoDao.deleteRow(coll, queryFormovedIds, true, db);
                        logger.info("BMS doc RequestLogs  Last Id deleted : {}", listOfIds.get(listOfIds.size() - 1));
                    }

                } catch (Exception e) {
                    logger.error("Error while adding in moveRequestLogsLogsToS3 , {}", e.getMessage());
                    if (!e.getMessage().contains("timeout") && !e.getMessage().contains("duplicate")) {
                        throw e;
                    }
                }

                // updating last _id in metaData master mongoDB collection: movedIdsLogger
                DBObject queryJson;
                if (commDeletedId != null && commDeletedId.size() > 0 && commDeletedId.getObjectId("_id") != null) {
                    totalMigrated = totalMigrated + dbObjectsList.size();
                    DBObject setquery = new BasicDBObject("movedId", dbObjectsList.get(dbObjectsList.size() - 1).get("_id"))
                            .append("totalMigrated", totalMigrated)
                            .append("updatedOn", new Date());
                    queryJson = new BasicDBObject("_id", commDeletedId.getObjectId("_id"));
                    DBObject rowJson = new BasicDBObject("$set", setquery);
                    newArchMongoDao.updateRow(NStoS3util.lastUpdatedIdColl, queryJson, rowJson, false, false, NStoS3util.lastUpdatedIdDB);
                }
                queryOnId.put("$gt", dbObjectsList.get(dbObjectsList.size() - 1).get("_id"));
                queryObject.put("_id", queryOnId);
                dbObjectsList = null; // explicitly nullify
                dbObjectsList = commLoggerMongoDao.getDBObjects(coll, queryObject, projection, 0, len, orderObject, db);
            }

        } catch (Exception e) {
            logger.error("Exception caught in moveRequestLogsLogsToS3, ns:{} , msg: {}", e.getMessage());
        } finally {
            isMoveRequestLogsJobRunning = false;
            isMoveRequestLogsJobStop = false;
        }
    }

    public void moveIntegrationExternalAPILogsToS3(String dbColl, Integer size, int days, int chunkSize, boolean timeCheck) {

        logger.info("Inside moveIntegrationExternalAPILogsToS3");
        try {
            String[] nsArray = dbColl.split("\\.");
            String db = nsArray[0];
            String coll = nsArray[1];
            int len = size;
            long totalMigrated = 0;
            DBObject queryForDeletId = new BasicDBObject();
            queryForDeletId.put("coll", dbColl);
            BasicDBObject commDeletedId = newArchMongoDao.getDBObjectByDbName(NStoS3util.lastUpdatedIdColl, queryForDeletId, NStoS3util.lastUpdatedIdDB);
            DBObject queryOnId = new BasicDBObject();
            //only provide startId at once we are starting the batch.
            if (commDeletedId != null && commDeletedId.size() > 0) {
                queryOnId.put("$gte", commDeletedId.get("movedId"));
                totalMigrated = (long) commDeletedId.get("totalMigrated");
            }
            queryOnId.put("$lt", new ObjectId(DateUtil.getStringToDate(DateUtil.getFormatedDate(days, DateUtil.DATE_FORMAT_EE_MMM_dd_hh_mm_ss_z_yyyy), DateUtil.DATE_FORMAT_EE_MMM_dd_hh_mm_ss_z_yyyy)));
            DBObject queryObject = new BasicDBObject();
            queryObject.put("_id", queryOnId);
            BasicDBObject projection = new BasicDBObject("_id", 1).append("BookingId", 1);
            DBObject orderObject = new BasicDBObject("_id", 1);
            // get list from mongoDB
            List<DBObject> dbObjectsList = commLoggerMongoDao.getDBObjects(coll, queryObject, projection, 0, len, orderObject, db);
            logger.info("isMoveIntegrationExternalAPILogsJobStop: {}, isMoveIntegrationExternalAPILogsJobRunning: {}", isMoveIntegrationExternalAPILogsJobStop, isMoveIntegrationExternalAPILogsJobRunning);

            // if list have data then proceed
            while (dbObjectsList != null && dbObjectsList.size() > 0 && !isMoveIntegrationExternalAPILogsJobStop) {

                Date todayDate = new Date();
                /* Date endDate = DateUtil.addDays(todayDate, 1); */
                Date startDate = new Date();
                startDate = DateUtil.setHours(startDate, 7);
                startDate = DateUtil.setMinute(startDate, 0);
                startDate = DateUtil.setSeconds(startDate, 0);

                Date endDate = new Date();
                endDate = DateUtil.setHours(endDate, 20);
                endDate = DateUtil.setMinute(endDate, 0);
                endDate = DateUtil.setSeconds(endDate, 0);
                logger.info("Today date in uaeJourMongoDao service: {}, start date {} , end date {}", todayDate, startDate, endDate);
                List<Object> listOfIds = new ArrayList<>();
                if (todayDate.before(startDate) || todayDate.after(endDate) || !timeCheck) {

                    HashSet<Integer> bookingIds = new HashSet<>();
                    for (DBObject jsonDocument : dbObjectsList) {
                        listOfIds.add(jsonDocument.get("_id"));
                        Integer bookingId = (Integer) jsonDocument.get("BookingId");
                        DBObject queryOnrequestId = new BasicDBObject();
                        queryOnrequestId.put("BookingId", bookingId);
                        if (bookingId != null && bookingId > 0 && !bookingIds.contains(bookingId)) {
                            bookingIds.add(bookingId);
                            int offset = 0;
                            List<DBObject> dbList = commLoggerMongoDao.getDBObjects(coll, queryOnrequestId, offset, chunkSize, null, db);
                            if (dbList.size() == chunkSize) {
                                offset = offset + chunkSize;
                                dbList = getList(coll, queryOnrequestId, offset, db, dbList, chunkSize);
                            }
                            String jsonString;
                            if (dbList != null && dbList.size() > 0) {
                                try {
                                    jsonString = JSON.serialize(dbList);

                                    // Compress JSON data
                                    byte[] compressedData = compressData(jsonString);
                                    //String deCompressedData = decompressData(compressedData);
                                    String fileName = bookingId + ".json.gz";
                                    logger.info("BMS Integration.ExternalAPILogs fileName : " + fileName);
                                    String folderName = NStoS3util.MAP_NS_TO_S3_PATH.get(dbColl);
                                    String url = s3service.addByteContentWithGzip(compressedData, NStoS3util.BUCKET_NAME_LOGGER_ARCHIVAL, folderName, fileName, "application/gzip");
                                    if (url.equals(AppConstants.FILE_ALREADY_EXIST)) {
                                        try {
                                            commLoggerMongoDao.addRows(coll + "_failOver", dbList, db);
                                        } catch (Exception e) {
                                            logger.error("Error while adding in moveRequestLogsLogsToS3 , {}", e.getMessage());
                                        }
                                    }

                                    for (DBObject reqIdDoc : dbList) {
                                        listOfIds.add(reqIdDoc.get("_id"));
                                    }
                                } catch (Exception e) {
                                    System.err.println("Error serializing DBObject: " + e.getMessage());
                                    return;
                                }
                            }
                        }
                    }
                    bookingIds = null;
                    // to delete from source by list of _id
                    try {
                        List<Object> subList = new ArrayList<>();
                        int startIndx = 0;
                        int lastIndx = 100;
                        if (listOfIds.size() > 100) {
                            while (startIndx < listOfIds.size()) {
                                if (startIndx + 100 >= listOfIds.size()) {
                                    lastIndx = listOfIds.size();
                                }
                                subList = listOfIds.subList(startIndx, lastIndx);
                                DBObject queryFormovedIds = new BasicDBObject();
                                queryFormovedIds.put("_id", new BasicDBObject().append("$in", subList));
                                commLoggerMongoDao.deleteRow(coll, queryFormovedIds, true, db);
                                logger.info("BMS Integration.ExternalAPILogs Last Id deleted : {}", subList.get(subList.size() - 1));
                                startIndx = lastIndx;
                                lastIndx = lastIndx + 100;
                                subList = null;
                            }
                        } else if (listOfIds != null && listOfIds.size() > 0) {
                            DBObject queryFormovedIds = new BasicDBObject();
                            queryFormovedIds.put("_id", new BasicDBObject().append("$in", listOfIds));
                            commLoggerMongoDao.deleteRow(coll, queryFormovedIds, true, db);
                            logger.info("BMS Integration.ExternalAPILogs  Last Id deleted : {}", listOfIds.get(listOfIds.size() - 1));
                        }

                    } catch (Exception e) {
                        logger.error("Error while adding in moveIntegrationExternalAPILogsToS3 , {}", e.getMessage());
                        if (!e.getMessage().contains("timeout") && !e.getMessage().contains("duplicate")) {
                            throw e;
                        }
                    }

                    // updating last _id in metaData master mongoDB collection: movedIdsLogger
                    DBObject queryJson;
                    if (commDeletedId != null && commDeletedId.size() > 0 && commDeletedId.getObjectId("_id") != null) {
                        totalMigrated = totalMigrated + dbObjectsList.size();
                        DBObject setquery = new BasicDBObject("movedId", dbObjectsList.get(dbObjectsList.size() - 1).get("_id"))
                                .append("totalMigrated", totalMigrated)
                                .append("updatedOn", new Date());
                        queryJson = new BasicDBObject("_id", commDeletedId.getObjectId("_id"));
                        DBObject rowJson = new BasicDBObject("$set", setquery);
                        newArchMongoDao.updateRow(NStoS3util.lastUpdatedIdColl, queryJson, rowJson, false, false, NStoS3util.lastUpdatedIdDB);
                    }
                    queryOnId.put("$gt", dbObjectsList.get(dbObjectsList.size() - 1).get("_id"));
                    queryObject.put("_id", queryOnId);
                    dbObjectsList = null; // explicitly nullify
                    dbObjectsList = commLoggerMongoDao.getDBObjects(coll, queryObject, projection, 0, len, orderObject, db);
                }else {
                    dbObjectsList = null;
                }
            }

        } catch (Exception e) {
            logger.error("Exception caught in moveRequestLogsLogsToS3, ns:{} , msg: {}", e.getMessage());
        } finally {
            isMoveIntegrationExternalAPILogsJobRunning = false;
            isMoveIntegrationExternalAPILogsJobStop = false;
        }
    }

    public void movePBCromaV2ToS3(String dbColl, Integer size, int days, int chunkSize) throws Exception {
        logger.info("Inside movePBCromaV2ToS3");
        try {
            String[] nsArray = dbColl.split("\\.");
            String db = nsArray[0];
            String coll = nsArray[1];
            int len = size;
            long totalMigrated = 0;
            DBObject queryForDeletId = new BasicDBObject();
            queryForDeletId.put("coll", dbColl);
            BasicDBObject commDeletedId = newArchMongoDao.getDBObjectByDbName(NStoS3util.lastUpdatedIdColl, queryForDeletId, NStoS3util.lastUpdatedIdDB);
            DBObject queryOnId = new BasicDBObject();
            //only provide startId at once we are starting the batch.
            if (commDeletedId != null && commDeletedId.size() > 0) {
                queryOnId.put("$gte", commDeletedId.get("movedId"));
                totalMigrated = (long) commDeletedId.get("totalMigrated");
            }
            queryOnId.put("$lt", new ObjectId(DateUtil.getStringToDate(DateUtil.getFormatedDate(days, DateUtil.DATE_FORMAT_EE_MMM_dd_hh_mm_ss_z_yyyy), DateUtil.DATE_FORMAT_EE_MMM_dd_hh_mm_ss_z_yyyy)));
            DBObject queryObject = new BasicDBObject();
            queryObject.put("_id", queryOnId);
            BasicDBObject projection = new BasicDBObject("_id", 1).append("TrackingID", 1);
            DBObject orderObject = new BasicDBObject("_id", 1);
            // get list from mongoDB
            List<DBObject> dbObjectsList = commLoggerMongoDao.getDBObjects(coll, queryObject, projection, 0, len, orderObject, db);
            logger.info("isMoveCromaPBCromaV2LogsJobStop:{}, isMoveCromaPBCromaV2LogsJobRunning:{}", isMoveCromaPBCromaV2LogsJobStop, isMoveCromaPBCromaV2LogsJobRunning);
            while (dbObjectsList != null && dbObjectsList.size() > 0 && !isMoveCromaPBCromaV2LogsJobStop) {
                List<Object> listOfIds = new ArrayList<>();
                HashSet<String> trackingIDs = new HashSet<>();
                for (DBObject jsonDocument : dbObjectsList) {
                    listOfIds.add(jsonDocument.get("_id"));
                    String trackingID = (String) jsonDocument.get("TrackingID");
                    DBObject queryOnrequestId = new BasicDBObject();
                    queryOnrequestId.put("TrackingID", trackingID);
                    if (StringUtils.hasText(trackingID) && !trackingIDs.contains(trackingID)) {
                        trackingIDs.add(trackingID);
                        int offset = 0;
                        List<DBObject> dbList = commLoggerMongoDao.getDBObjects(coll, queryOnrequestId, offset, chunkSize, null, db);
                        if (dbList.size() == chunkSize) {
                            offset = offset + chunkSize;
                            dbList = getList(coll, queryOnrequestId, offset, db, dbList, chunkSize);
                        }
                        String jsonString;
                        if (dbList != null && dbList.size() > 0) {
                            try {
                                jsonString = JSON.serialize(dbList);

                                // Compress JSON data
                                byte[] compressedData = compressData(jsonString);
                                //String deCompressedData = decompressData(compressedData);
                                String fileName = trackingID + ".json.gz";
                                logger.info("BMS PBCromaV2 fileName : " + fileName);
                                String folderName = NStoS3util.MAP_NS_TO_S3_PATH.get(dbColl);
                                String url = s3service.addByteContentWithGzip(compressedData, NStoS3util.BUCKET_NAME_LOGGER_ARCHIVAL, folderName, fileName, "application/gzip");
                                if (url.equals(AppConstants.FILE_ALREADY_EXIST)) {
                                    try {
                                        commLoggerMongoDao.addRows(coll + "_failOver", dbList, db);
                                    } catch (Exception e) {
                                        logger.error("Error while adding in moveRequestLogsLogsToS3 , {}", e.getMessage());
                                    }
                                }

                                for (DBObject reqIdDoc : dbList) {
                                    listOfIds.add(reqIdDoc.get("_id"));
                                }
                            } catch (Exception e) {
                                System.err.println("Error serializing DBObject: " + e.getMessage());
                                return;
                            }
                        }
                    }
                }
                trackingIDs = null;

                // to delete from source by list of _id
                try {
                    List<Object> subList = new ArrayList<>();
                    int startIndx = 0;
                    int lastIndx = 100;
                    if (listOfIds.size() > 100) {
                        while (startIndx < listOfIds.size()) {
                            if (startIndx + 100 >= listOfIds.size()) {
                                lastIndx = listOfIds.size();
                            }
                            subList = listOfIds.subList(startIndx, lastIndx);
                            DBObject queryFormovedIds = new BasicDBObject();
                            queryFormovedIds.put("_id", new BasicDBObject().append("$in", subList));
                            commLoggerMongoDao.deleteRow(coll, queryFormovedIds, true, db);
                            logger.info("BMS PBCromaV2 Last Id deleted : {}", subList.get(subList.size() - 1));
                            startIndx = lastIndx;
                            lastIndx = lastIndx + 100;
                            subList = null;
                        }
                    } else if (listOfIds != null && listOfIds.size() > 0) {
                        DBObject queryFormovedIds = new BasicDBObject();
                        queryFormovedIds.put("_id", new BasicDBObject().append("$in", listOfIds));
                        commLoggerMongoDao.deleteRow(coll, queryFormovedIds, true, db);
                        logger.info("BMS PBCromaV2 Last Id deleted : {}", listOfIds.get(listOfIds.size() - 1));
                    }

                } catch (Exception e) {
                    logger.error("Error while adding in movePBCromaV2ToS3 , {}", e.getMessage());
                    if (!e.getMessage().contains("timeout") && !e.getMessage().contains("duplicate")) {
                        throw e;
                    }
                }

                // updating last _id in metaData master mongoDB collection: movedIdsLogger
                DBObject queryJson;
                if (commDeletedId != null && commDeletedId.size() > 0 && commDeletedId.getObjectId("_id") != null) {
                    totalMigrated = totalMigrated + dbObjectsList.size();
                    DBObject setquery = new BasicDBObject("movedId", dbObjectsList.get(dbObjectsList.size() - 1).get("_id"))
                            .append("totalMigrated", totalMigrated)
                            .append("updatedOn", new Date());
                    queryJson = new BasicDBObject("_id", commDeletedId.getObjectId("_id"));
                    DBObject rowJson = new BasicDBObject("$set", setquery);
                    newArchMongoDao.updateRow(NStoS3util.lastUpdatedIdColl, queryJson, rowJson, false, false, NStoS3util.lastUpdatedIdDB);
                }
                queryOnId.put("$gt", dbObjectsList.get(dbObjectsList.size() - 1).get("_id"));
                queryObject.put("_id", queryOnId);
                dbObjectsList = null; // explicitly nullify
                dbObjectsList = commLoggerMongoDao.getDBObjects(coll, queryObject, projection, 0, len, orderObject, db);
            }

        } catch (Exception e) {
            logger.error("Exception caught in movePBCromaV2ToS3, ns:{} , msg: {}", e.getMessage());
        } finally {
            isMoveCromaPBCromaV2LogsJobStop = false;
            isMoveCromaPBCromaV2LogsJobRunning = false;
        }
    }
    public void movePBCromaV2_SalesToS3(String dbColl, Integer size, int days, int chunkSize) throws Exception {
        logger.info("Inside movePBCromaV2ToS3");
        try {
            String[] nsArray = dbColl.split("\\.");
            String db = nsArray[0];
            String coll = nsArray[1];
            int len = size;
            long totalMigrated = 0;
            DBObject queryForDeletId = new BasicDBObject();
            queryForDeletId.put("coll", dbColl);
            BasicDBObject commDeletedId = newArchMongoDao.getDBObjectByDbName(NStoS3util.lastUpdatedIdColl, queryForDeletId, NStoS3util.lastUpdatedIdDB);
            DBObject queryOnId = new BasicDBObject();
            //only provide startId at once we are starting the batch.
            if (commDeletedId != null && commDeletedId.size() > 0) {
                queryOnId.put("$gte", commDeletedId.get("movedId"));
                totalMigrated = (long) commDeletedId.get("totalMigrated");
            }
            queryOnId.put("$lt", new ObjectId(DateUtil.getStringToDate(DateUtil.getFormatedDate(days, DateUtil.DATE_FORMAT_EE_MMM_dd_hh_mm_ss_z_yyyy), DateUtil.DATE_FORMAT_EE_MMM_dd_hh_mm_ss_z_yyyy)));
            DBObject queryObject = new BasicDBObject();
            queryObject.put("_id", queryOnId);
            BasicDBObject projection = new BasicDBObject("_id", 1).append("TrackingID", 1);
            DBObject orderObject = new BasicDBObject("_id", 1);
            // get list from mongoDB
            List<DBObject> dbObjectsList = commLoggerMongoDao.getDBObjects(coll, queryObject, projection, 0, len, orderObject, db);
            logger.info("isMoveCromaPBCromaV2SalesLogsJobStop:{}, isMoveCromaPBCromaV2SalesLogsJobRunning:{}", isMoveCromaPBCromaV2SalesLogsJobStop, isMoveCromaPBCromaV2SalesLogsJobRunning);
            while (dbObjectsList != null && dbObjectsList.size() > 0 && !isMoveCromaPBCromaV2SalesLogsJobStop) {
                List<Object> listOfIds = new ArrayList<>();
                HashSet<String> trackingIDs = new HashSet<>();
                for (DBObject jsonDocument : dbObjectsList) {
                    listOfIds.add(jsonDocument.get("_id"));
                    String trackingID = (String) jsonDocument.get("TrackingID");
                    DBObject queryOnrequestId = new BasicDBObject();
                    queryOnrequestId.put("TrackingID", trackingID);
                    if (StringUtils.hasText(trackingID) && !trackingIDs.contains(trackingID)) {
                        trackingIDs.add(trackingID);
                        int offset = 0;
                        List<DBObject> dbList = commLoggerMongoDao.getDBObjects(coll, queryOnrequestId, offset, chunkSize, null, db);
                        if (dbList.size() == chunkSize) {
                            offset = offset + chunkSize;
                            dbList = getList(coll, queryOnrequestId, offset, db, dbList, chunkSize);
                        }
                        String jsonString;
                        if (dbList != null && dbList.size() > 0) {
                            try {
                                jsonString = JSON.serialize(dbList);

                                // Compress JSON data
                                byte[] compressedData = compressData(jsonString);
                                //String deCompressedData = decompressData(compressedData);
                                String fileName = trackingID + ".json.gz";
                                logger.info("BMS PBCromaV2_Sales fileName : " + fileName);
                                String folderName = NStoS3util.MAP_NS_TO_S3_PATH.get(dbColl);
                                String url = s3service.addByteContentWithGzip(compressedData, NStoS3util.BUCKET_NAME_LOGGER_ARCHIVAL, folderName, fileName, "application/gzip");
                                if (url.equals(AppConstants.FILE_ALREADY_EXIST)) {
                                    try {
                                        commLoggerMongoDao.addRows(coll + "_failOver", dbList, db);
                                    } catch (Exception e) {
                                        logger.error("Error while adding in moveRequestLogsLogsToS3 , {}", e.getMessage());
                                    }
                                }

                                for (DBObject reqIdDoc : dbList) {
                                    listOfIds.add(reqIdDoc.get("_id"));
                                }
                            } catch (Exception e) {
                                System.err.println("Error serializing DBObject: " + e.getMessage());
                                return;
                            }
                        }
                    }
                }
                trackingIDs = null;

                // to delete from source by list of _id
                try {
                    List<Object> subList = new ArrayList<>();
                    int startIndx = 0;
                    int lastIndx = 100;
                    if (listOfIds.size() > 100) {
                        while (startIndx < listOfIds.size()) {
                            if (startIndx + 100 >= listOfIds.size()) {
                                lastIndx = listOfIds.size();
                            }
                            subList = listOfIds.subList(startIndx, lastIndx);
                            DBObject queryFormovedIds = new BasicDBObject();
                            queryFormovedIds.put("_id", new BasicDBObject().append("$in", subList));
                            commLoggerMongoDao.deleteRow(coll, queryFormovedIds, true, db);
                            logger.info("BMS PBCromaV2_Sales Last Id deleted : {}", subList.get(subList.size() - 1));
                            startIndx = lastIndx;
                            lastIndx = lastIndx + 100;
                            subList = null;
                        }
                    } else if (listOfIds != null && listOfIds.size() > 0) {
                        DBObject queryFormovedIds = new BasicDBObject();
                        queryFormovedIds.put("_id", new BasicDBObject().append("$in", listOfIds));
                        commLoggerMongoDao.deleteRow(coll, queryFormovedIds, true, db);
                        logger.info("BMS PBCromaV2_Sales Last Id deleted : {}", listOfIds.get(listOfIds.size() - 1));
                    }

                } catch (Exception e) {
                    logger.error("Error while adding in movePBCromaV2ToS3 Sales , {}", e.getMessage());
                    if (!e.getMessage().contains("timeout") && !e.getMessage().contains("duplicate")) {
                        throw e;
                    }
                }

                // updating last _id in metaData master mongoDB collection: movedIdsLogger
                DBObject queryJson;
                if (commDeletedId != null && commDeletedId.size() > 0 && commDeletedId.getObjectId("_id") != null) {
                    totalMigrated = totalMigrated + dbObjectsList.size();
                    DBObject setquery = new BasicDBObject("movedId", dbObjectsList.get(dbObjectsList.size() - 1).get("_id"))
                            .append("totalMigrated", totalMigrated)
                            .append("updatedOn", new Date());
                    queryJson = new BasicDBObject("_id", commDeletedId.getObjectId("_id"));
                    DBObject rowJson = new BasicDBObject("$set", setquery);
                    newArchMongoDao.updateRow(NStoS3util.lastUpdatedIdColl, queryJson, rowJson, false, false, NStoS3util.lastUpdatedIdDB);
                }
                queryOnId.put("$gt", dbObjectsList.get(dbObjectsList.size() - 1).get("_id"));
                queryObject.put("_id", queryOnId);
                dbObjectsList = null; // explicitly nullify
                dbObjectsList = commLoggerMongoDao.getDBObjects(coll, queryObject, projection, 0, len, orderObject, db);
            }

        } catch (Exception e) {
            logger.error("Exception caught in movePBCromaV2ToS3, ns:{} , msg: {}", e.getMessage());
        } finally {
            isMoveCromaPBCromaV2SalesLogsJobStop = false;
            isMoveCromaPBCromaV2SalesLogsJobRunning = false;
        }
    }

    public void moveDocPushLogsToS3V1(String dbColl, Integer size, int days, int chunkSize, String movedIdsCollection) {

        logger.info("Inside moveDocPushLogsToS3");
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        try {
            String[] nsArray = dbColl.split("\\.");
            String db = nsArray[0];
            String coll = nsArray[1];
            int len = size;
            long totalMigrated = 0;

            List<String> excludedMethods = Arrays.asList(
                    "DocService_GetttlUrl_Manual",
                    "BMSDocStatusStamping",
                    "BMSeventTracking",
                    "GetTTLURL",
                    "MergeRequestDocs",
                    "PushDocumentsToInspection",
                    "DocToTextNew",
                    "GetCustomerDetails",
                    "GetPIVCDetails",
                    "GetAnalyserOCRAData",
                    "GetCorePolicyVehicleDetails",
                    "UpdateQCDetails",
                    "PushAgentUpdateEvents",
                    "BMSbookingUpdate",
                    "GetInsuredMemberDetails",
                    "PushWAEventTicket",
                    "UpdateBookingE2E",
                    "System UW Update",
                    "PIVCAPI",
                    "GetPolicyStatusByLeadID",
                    "HealthDocPush",
                    "NEWStatementAnalyse"
            );

            DBObject queryForDeletId = new BasicDBObject();
            queryForDeletId.put("coll", dbColl);
            BasicDBObject commDeletedId = newArchMongoDao.getDBObjectByDbName(movedIdsCollection, queryForDeletId, NStoS3util.lastUpdatedIdDB);
            DBObject queryOnId = new BasicDBObject();
            //only provide startId at once we are starting the batch.
            if (commDeletedId != null && commDeletedId.size() > 0) {
                queryOnId.put("$gte", commDeletedId.get("movedId"));
                totalMigrated = (long) commDeletedId.get("totalMigrated");
            }
            queryOnId.put("$lt", new ObjectId(DateUtil.getStringToDate(DateUtil.getFormatedDate(days, DateUtil.DATE_FORMAT_EE_MMM_dd_hh_mm_ss_z_yyyy), DateUtil.DATE_FORMAT_EE_MMM_dd_hh_mm_ss_z_yyyy)));
            DBObject queryObject = new BasicDBObject();
            queryObject.put("_id", queryOnId);

            // queryObject.put("MethodName", new BasicDBObject("$nin", excludedMethods));

            DBObject orderObject = new BasicDBObject("_id", 1);
            // get list from mongoDB
            List<DBObject> dbObjectsList = commLoggerMongoDao.getDBObjects(coll, queryObject, null, 0, len, orderObject, db);
            logger.info("isMoveDocPushLogsJobStop: {}, isMoveDocPushLogsJobRunning: {}", isMoveDocPushLogsJobStop, isMoveDocPushLogsJobRunning);

            // if list have data then proceed
            while (dbObjectsList != null && dbObjectsList.size() > 0 && !isMoveDocPushLogsJobStop) {
                long start = System.currentTimeMillis();
                List<Object> listOfIds = new ArrayList<>();
                List<DBObject> addFileNameObjList = new ArrayList<>();
                for (DBObject jsonDocument : dbObjectsList) {
                    try {
                        listOfIds.add(jsonDocument.get("_id"));
                        String jsonString = JSON.serialize(jsonDocument);
                        // Compress JSON data
                        byte[] compressedData = compressData(jsonString);
                        //String deCompressedData = decompressData(compressedData);
                        ObjectId docObjId = (ObjectId) jsonDocument.get("_id");
                        Integer leadId = (Integer) jsonDocument.get("Leadid");
                        String createdOn = null;
                        if( jsonDocument.get("CreatedOn") instanceof Date){
                            createdOn = formatter.format(jsonDocument.get("CreatedOn"));
                        }
                        else createdOn = (String) jsonDocument.get("CreatedOn");
                        String methodName = (String) jsonDocument.get("MethodName");
                        String fileName = docObjId + "_json.gz";
                        // check This fileName exist or Not
                        DBObject docPushMetaFileObject = new BasicDBObject().append("_id", docObjId).append("Leadid", leadId).append("MethodName", methodName).append("fileName", fileName).append("CreatedOn", createdOn);
                        // add in docPushLogS3FilenameLogs collection for url map
                        addFileNameObjList.add(docPushMetaFileObject);
                        logger.info("BMS DocPushLog: " + movedIdsCollection + " " + leadId + " " + fileName);
                        String folderName = NStoS3util.MAP_NS_TO_S3_PATH.get("DocsPoint.DocPushLog");
                        String url = s3service.addByteContentWithGzip(compressedData, NStoS3util.BUCKET_NAME_LOGGER_ARCHIVAL, folderName, fileName, "application/gzip");
                        if (url.equals(AppConstants.FILE_ALREADY_EXIST)) {
                            try {
                                commLoggerMongoDao.addRow("DocPushLog_failOver", jsonDocument, db);
                            } catch (Exception e) {
                                logger.error("Error while adding in moveDocPushLogsToS3 , {}", e.getMessage());
                            }
                        }
                    } catch (Exception e) {
                        System.err.println("Error serializing DBObject: " + e.getMessage());
                        return;
                    }
                }
                // add FileName Object list in docPushLogS3FilenameLog
                if(!CollectionUtils.isEmpty(addFileNameObjList)){
                    try {
                        commLoggerMongoDao.addRows("DocPushLog_S3Archive", addFileNameObjList, "DocsPoint");
                    }catch (Exception e){
                        logger.error("Error while adding in DocPushLog_S3Archive , {}", e.getMessage());
                    }
                    addFileNameObjList = null;
                }

                // to delete from source by list of _id
                try {
                    List<Object> subList = new ArrayList<>();
                    int startIndx = 0;
                    int lastIndx = 100;
                    if (listOfIds.size() > 100) {
                        while (startIndx < listOfIds.size()) {
                            if (startIndx + 100 >= listOfIds.size()) {
                                lastIndx = listOfIds.size();
                            }
                            subList = listOfIds.subList(startIndx, lastIndx);
                            DBObject queryFormovedIds = new BasicDBObject();
                            queryFormovedIds.put("_id", new BasicDBObject().append("$in", subList));
                            commLoggerMongoDao.deleteRow(coll, queryFormovedIds, true, db);
                            logger.info("BMS DocPushLog Last Id deleted : {}", subList.get(subList.size() - 1));
                            startIndx = lastIndx;
                            lastIndx = lastIndx + 100;
                            subList = null;
                        }
                    } else if (listOfIds != null && listOfIds.size() > 0) {
                        DBObject queryFormovedIds = new BasicDBObject();
                        queryFormovedIds.put("_id", new BasicDBObject().append("$in", listOfIds));
                        commLoggerMongoDao.deleteRow(coll, queryFormovedIds, true, db);
                        logger.info("BMS DocPushLog  Last Id deleted : {}", listOfIds.get(listOfIds.size() - 1));
                    }

                } catch (Exception e) {
                    logger.error("Error while adding in moveDocPushLogsToS3 , {}", e.getMessage());
                    if (!e.getMessage().contains("timeout") && !e.getMessage().contains("duplicate")) {
                        throw e;
                    }
                }
                listOfIds = null;
                // updating last _id in metaData master mongoDB collection: movedIdsLogger
                DBObject queryJson;
                if (commDeletedId != null && commDeletedId.size() > 0 && commDeletedId.getObjectId("_id") != null) {
                    totalMigrated = totalMigrated + dbObjectsList.size();
                    DBObject setquery = new BasicDBObject("movedId", dbObjectsList.get(dbObjectsList.size() - 1).get("_id"))
                            .append("totalMigrated", totalMigrated)
                            .append("updatedOn", new Date());
                    queryJson = new BasicDBObject("_id", commDeletedId.getObjectId("_id"));
                    DBObject rowJson = new BasicDBObject("$set", setquery);
                    newArchMongoDao.updateRow(movedIdsCollection, queryJson, rowJson, false, false, NStoS3util.lastUpdatedIdDB);
                }
                queryOnId.put("$gt", dbObjectsList.get(dbObjectsList.size() - 1).get("_id"));
                queryObject.put("_id", queryOnId);
                queryObject.put("MethodName", new BasicDBObject("$nin", excludedMethods));

                dbObjectsList = null; // explicitly nullify
                dbObjectsList = commLoggerMongoDao.getDBObjects(coll, queryObject, null, 0, len, orderObject, db);

                long end = System.currentTimeMillis();
                long diff = end - start;
                logger.info("Time for {} records in docPushLogs: {} ms", len, diff);
            }

        } catch (Exception e) {
            logger.error("Exception caught in moveDocPushLogsToS3, ns:{} , msg: {}", e.getMessage());
        } finally {
            isMoveDocPushLogsJobRunning = false;
            isMoveDocPushLogsJobStop = false;
        }
    }

    public void moveDocPushLogsToS3V2(String dbColl, Integer size, int days, int chunkSize, String lte_id) {

        logger.info("Inside moveDocPushLogsToS3");
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        try {
            String[] nsArray = dbColl.split("\\.");
            String db = nsArray[0];
            String coll = nsArray[1];
            int len = size;
            long totalMigrated = 0;
            DBObject queryForDeletId = new BasicDBObject();
            queryForDeletId.put("coll", "DocsPoint.DocPushLog_v2");
            BasicDBObject commDeletedId = newArchMongoDao.getDBObjectByDbName(NStoS3util.lastUpdatedIdColl, queryForDeletId, NStoS3util.lastUpdatedIdDB);
            DBObject queryOnId = new BasicDBObject();
            //only provide startId at once we are starting the batch.
            if (commDeletedId != null && commDeletedId.size() > 0) {
                queryOnId.put("$gte", commDeletedId.get("movedId"));
                totalMigrated = (long) commDeletedId.get("totalMigrated");
            }
            queryOnId.put("$lt", new ObjectId(lte_id));//new ObjectId(DateUtil.getStringToDate(DateUtil.getFormatedDate(days, DateUtil.DATE_FORMAT_EE_MMM_dd_hh_mm_ss_z_yyyy), DateUtil.DATE_FORMAT_EE_MMM_dd_hh_mm_ss_z_yyyy)));
            DBObject queryObject = new BasicDBObject();
            queryObject.put("_id", queryOnId);
            DBObject orderObject = new BasicDBObject("_id", 1);
            // get list from mongoDB
            List<DBObject> dbObjectsList = commLoggerMongoDao.getDBObjects(coll, queryObject, null, 0, len, orderObject, db);
            logger.info("isMoveDocPushLogsJobStop: {}, isMoveDocPushLogsJobRunning: {}", isMoveDocPushLogsJobStopV2, isMoveDocPushLogsJobRunningV2);

            // if list have data then proceed
            while (dbObjectsList != null && dbObjectsList.size() > 0 && !isMoveDocPushLogsJobStopV2) {

                List<Object> listOfIds = new ArrayList<>();
                List<DBObject> addFileNameObjList = new ArrayList<>();
                for (DBObject jsonDocument : dbObjectsList) {
                    try {
                        listOfIds.add(jsonDocument.get("_id"));
                        String jsonString = JSON.serialize(jsonDocument);
                        // Compress JSON data
                        byte[] compressedData = compressData(jsonString);
                        //String deCompressedData = decompressData(compressedData);
                        ObjectId docObjId = (ObjectId) jsonDocument.get("_id");
                        Integer leadId = (Integer) jsonDocument.get("Leadid");
                        String createdOn = null;
                        if( jsonDocument.get("CreatedOn") instanceof Date){
                            createdOn = formatter.format(jsonDocument.get("CreatedOn"));
                        }
                        else createdOn = (String) jsonDocument.get("CreatedOn");
                        String methodName = (String) jsonDocument.get("MethodName");
                        String fileName = docObjId + "_json.gz";
                        // check This fileName exist or Not
                        DBObject docPushMetaFileObject = new BasicDBObject().append("_id", docObjId).append("Leadid", leadId).append("MethodName", methodName).append("fileName", fileName).append("CreatedOn", createdOn);
                        // add in docPushLogS3FilenameLogs collection for url map
                        addFileNameObjList.add(docPushMetaFileObject);
                        logger.info("BMS DocPushLog V2 fileName : " + fileName);
                        String folderName = NStoS3util.MAP_NS_TO_S3_PATH.get(dbColl);
                        String url = s3service.addByteContentWithGzip(compressedData, NStoS3util.BUCKET_NAME_LOGGER_ARCHIVAL, folderName, fileName, "application/gzip");
                        if (url.equals(AppConstants.FILE_ALREADY_EXIST)) {
                            try {
                                commLoggerMongoDao.addRow("DocPushLog_failOver", jsonDocument, db);
                            } catch (Exception e) {
                                logger.error("Error while adding in moveDocPushLogsToS3V2 , {}", e.getMessage());
                            }
                        }
                    } catch (Exception e) {
                        System.err.println("Error serializing DBObject: " + e.getMessage());
                        return;
                    }
                }
                // add FileName Object list in docPushLogS3FilenameLog
                if(!CollectionUtils.isEmpty(addFileNameObjList)){
                    try {
                        commLoggerMongoDao.addRows("DocPushLog_S3Archive", addFileNameObjList, db);
                    }catch (Exception e){
                        logger.error("Error while adding in DocPushLog_S3Archive , {}", e.getMessage());
                    }
                    addFileNameObjList = null;
                }

                // to delete from source by list of _id
                try {
                    List<Object> subList = new ArrayList<>();
                    int startIndx = 0;
                    int lastIndx = 100;
                    if (listOfIds.size() > 100) {
                        while (startIndx < listOfIds.size()) {
                            if (startIndx + 100 >= listOfIds.size()) {
                                lastIndx = listOfIds.size();
                            }
                            subList = listOfIds.subList(startIndx, lastIndx);
                            DBObject queryFormovedIds = new BasicDBObject();
                            queryFormovedIds.put("_id", new BasicDBObject().append("$in", subList));
                            commLoggerMongoDao.deleteRow(coll, queryFormovedIds, true, db);
                            logger.info("BMS DocPushLog V2 Last Id deleted : {}", subList.get(subList.size() - 1));
                            startIndx = lastIndx;
                            lastIndx = lastIndx + 100;
                            subList = null;
                        }
                    } else if (listOfIds != null && listOfIds.size() > 0) {
                        DBObject queryFormovedIds = new BasicDBObject();
                        queryFormovedIds.put("_id", new BasicDBObject().append("$in", listOfIds));
                        commLoggerMongoDao.deleteRow(coll, queryFormovedIds, true, db);
                        logger.info("BMS DocPushLog V2   Last Id deleted : {}", listOfIds.get(listOfIds.size() - 1));
                    }

                } catch (Exception e) {
                    logger.error("Error while adding in moveDocPushLogsToS3 V2  , {}", e.getMessage());
                    if (!e.getMessage().contains("timeout") && !e.getMessage().contains("duplicate")) {
                        throw e;
                    }
                }
                listOfIds = null;
                // updating last _id in metaData master mongoDB collection: movedIdsLogger
                DBObject queryJson;
                if (commDeletedId != null && commDeletedId.size() > 0 && commDeletedId.getObjectId("_id") != null) {
                    totalMigrated = totalMigrated + dbObjectsList.size();
                    DBObject setquery = new BasicDBObject("movedId", dbObjectsList.get(dbObjectsList.size() - 1).get("_id"))
                            .append("totalMigrated", totalMigrated)
                            .append("updatedOn", new Date());
                    queryJson = new BasicDBObject("_id", commDeletedId.getObjectId("_id"));
                    DBObject rowJson = new BasicDBObject("$set", setquery);
                    newArchMongoDao.updateRow(NStoS3util.lastUpdatedIdColl, queryJson, rowJson, false, false, NStoS3util.lastUpdatedIdDB);
                }
                queryOnId.put("$gt", dbObjectsList.get(dbObjectsList.size() - 1).get("_id"));
                queryObject.put("_id", queryOnId);
                dbObjectsList = null; // explicitly nullify
                dbObjectsList = commLoggerMongoDao.getDBObjects(coll, queryObject, null, 0, len, orderObject, db);
            }

        } catch (Exception e) {
            logger.error("Exception caught in moveDocPushLogsToS3, ns:{} , msg: {}", e.getMessage());
        } finally {
            isMoveDocPushLogsJobRunningV2 = false;
            isMoveDocPushLogsJobStopV2 = false;
        }
    }


    /*
    {
    "_id" : NumberInt(1),
    "jobId" : "j1TestJob",
    "jobDescription" : "",
    "srcIp" : "",
    "srcDB" : "CVLogs",
    "srcCollection" : "",
    "srcDBUser" : "migrationMDB7U",
    "srcDBPwd" : "migrationU8730x",
    "srcLasMigrated_id" : "",
    "srcLastMigratedDate" : "",
    "updatedOn" : "",
    "dayBefore" : "-15",
    "s3Path" : "log_testing",
    "s3FileNameKey" : "enquiryId",
    "failOverMongoDBIP" : "",
    "failOverDB" : "",
    "failOverCollection" : "",
    "failOverDBUser" : "",
    "failOverDBPwd" : "",
    "authKey" : "qkejrl90282j334n4j45j5b5bu4jbiKJSKLdjF",
    "isActive" : NumberInt(1),
    "statusMsg" : "inProgress"
}
     */
    public static List<String> staticJobList = new ArrayList<>();

//    public void migrationOfLogs(String jobId) {
//        Map<String, Object> configMap = new HashMap<>();
//        configMap.put("jobId", jobId);
//        configMap.put("srcMongoDBURL", "******************************************************************************************************************");
//        configMap.put("ns", "CnRDB.reqResLog");
//        configMap.put("lastUpdated_id", "lastUpdated_id");
//        configMap.put("chunkSize", 200);
//        configMap.put("batchSize", 50);
//        configMap.put("daysBefore", 30);
//        configMap.put("s3BucketName", "s3BucketName");
//        configMap.put("s3BucketPath", "s3BucketPath");
//        configMap.put("uniqueField", "LeadId");
//        configMap.put("jobPriority", 1);
//
////        List<DBObject> configMapList = archMongoDao.getDBObjects("collection", null, null, 0, 0, null, "dbName");
////        if (configMapList != null && configMapList.size() > 0) {
////            for (DBObject configMp : configMapList) {
////                staticJobList.add((String) configMp.get("jobId"));
////            }
////        }
//
//        MongoClient srcMongoClient = mongoUtil.getMongoClient((String) configMap.get("srcMongoDBURL"));
//
//            logger.info("Inside movePBCromaV2ToS3");
//            try {
//                String ns = (String) configMap.get("ns");
//                String[] nsArray = ns.split("\\.");
//                String db = nsArray[0];
//                String coll = nsArray[1];
//                int len = (int) configMap.get("batchSize");
//                int chunkSize = (int) configMap.get("chunkSize");
//                String uniqueField = (String) configMap.get("uniqueField");
//                long totalMigrated = 0;
//                DBObject queryForDeletId = new BasicDBObject();
//                queryForDeletId.put("coll", ns);
//                BasicDBObject commDeletedId = genericMongoDao.getDBObjectByDbName(NStoS3util.lastUpdatedIdColl, queryForDeletId, NStoS3util.lastUpdatedIdDB);
//                DBObject queryOnId = new BasicDBObject();
//                //only provide startId at once we are starting the batch.
//                if (commDeletedId != null && commDeletedId.size() > 0) {
//                    queryOnId.put("$gte", commDeletedId.get("movedId"));
//                    totalMigrated = (long) commDeletedId.get("totalMigrated");
//                }
//                queryOnId.put("$lt", new ObjectId(DateUtil.getStringToDate(DateUtil.getFormatedDate((int)configMap.get("daysBefore"), DateUtil.DATE_FORMAT_EE_MMM_dd_hh_mm_ss_z_yyyy), DateUtil.DATE_FORMAT_EE_MMM_dd_hh_mm_ss_z_yyyy)));
//                Document queryObject = new Document();
//                queryObject.put("_id", queryOnId);
//                Document projection = new Document("_id", 1).append(uniqueField, 1);
//                Document orderObject = new Document("_id", 1);
//                // get list from mongoDB
//                List<Document> dbObjectsList = mongoUtil.getDBObjects(srcMongoClient, coll, queryObject, projection, 0, len, orderObject, db);
//                logger.info("isMoveCromaPBCromaV2LogsJobStop:{}, isMoveCromaPBCromaV2LogsJobRunning:{}", isMoveCromaPBCromaV2LogsJobStop, isMoveCromaPBCromaV2LogsJobRunning);
//                while (dbObjectsList != null && dbObjectsList.size() > 0 && !isMoveCromaPBCromaV2LogsJobStop) {
//                    List<Object> listOfIds = new ArrayList<>();
//                    List<String> trackingIDs = new ArrayList<>();
//                    for (Document jsonDocument : dbObjectsList) {
//                        listOfIds.add(jsonDocument.get("_id"));
//                        String fieldValue = (String) jsonDocument.get(uniqueField);
//                        Document queryOnrequestId = new Document();
//                        queryOnrequestId.put(uniqueField, fieldValue);
//                        if (StringUtils.hasText(fieldValue) && !trackingIDs.contains(fieldValue)) {
//                            trackingIDs.add(fieldValue);
//                            int offset = 0;
//                            List<Document> dbList = mongoUtil.getDBObjects(srcMongoClient, coll, queryOnrequestId, projection, offset, chunkSize, orderObject, db);
//                            if (dbList.size() == chunkSize) {
//                                offset = offset + chunkSize;
//                                dbList = getList(srcMongoClient, coll, queryOnrequestId, offset, orderObject, db, dbList, chunkSize);
//                            }
//                            String jsonString;
//                            if (dbList != null && dbList.size() > 0) {
//                                try {
//                                    jsonString = JSON.serialize(dbList);
//
//                                    // Compress JSON data
//                                    byte[] compressedData = compressData(jsonString);
//                                    //String deCompressedData = decompressData(compressedData);
//                                    String fileName = fieldValue + ".json.gz";
//                                    logger.info("BMS PBCromaV2 fileName : " + fileName);
//                                    String folderName = NStoS3util.MAP_NS_TO_S3_PATH.get(ns);
////                                    String url = s3service.addByteContentWithGzip(compressedData, NStoS3util.BUCKET_NAME_LOGGER_ARCHIVAL, folderName, fileName, "application/gzip");
////                                    if (url.equals(AppConstants.FILE_ALREADY_EXIST)) {
////                                        try {
////                                            mongoUtil.addRows(srcMongoClient, coll + "_failOver", dbList, db);
////                                        } catch (Exception e) {
////                                            logger.error("Error while adding in moveRequestLogsLogsToS3 , {}", e.getMessage());
////                                        }
////                                    }
//
//                                    for (Document reqIdDoc : dbList) {
//                                        listOfIds.add(reqIdDoc.get("_id"));
//                                    }
//                                } catch (Exception e) {
//                                    System.err.println("Error serializing DBObject: " + e.getMessage());
//                                    return;
//                                }
//                            }
//                        }
//                    }
//                    trackingIDs = null;
//
//                    // to delete from source by list of _id
//                    try {
//                        List<Object> subList = new ArrayList<>();
//                        int startIndx = 0;
//                        int lastIndx = 100;
//                        if (listOfIds.size() > 100) {
//                            while (startIndx < listOfIds.size()) {
//                                if (startIndx + 100 >= listOfIds.size()) {
//                                    lastIndx = listOfIds.size();
//                                }
//                                subList = listOfIds.subList(startIndx, lastIndx);
//                                DBObject queryFormovedIds = new BasicDBObject();
//                                queryFormovedIds.put("_id", new BasicDBObject().append("$in", subList));
//                                mongoUtil.deleteRow(srcMongoClient, coll, queryFormovedIds, true, db);
//                                logger.info("BMS PBCromaV2 Last Id deleted : {}", subList.get(subList.size() - 1));
//                                startIndx = lastIndx;
//                                lastIndx = lastIndx + 100;
//                                subList = null;
//                            }
//                        } else if (listOfIds != null && listOfIds.size() > 0) {
//                            DBObject queryFormovedIds = new BasicDBObject();
//                            queryFormovedIds.put("_id", new BasicDBObject().append("$in", listOfIds));
//                            mongoUtil.deleteRow(srcMongoClient, coll, queryFormovedIds, true, db);
//                            logger.info("BMS PBCromaV2 Last Id deleted : {}", listOfIds.get(listOfIds.size() - 1));
//                        }
//
//                    } catch (Exception e) {
//                        logger.error("Error while adding in movePBCromaV2ToS3 , {}", e.getMessage());
//                        if (!e.getMessage().contains("timeout") && !e.getMessage().contains("duplicate")) {
//                            throw e;
//                        }
//                    }
//
//                    // updating last _id in metaData master mongoDB collection: movedIdsLogger
//                    Document queryJson;
//                    if (commDeletedId != null && commDeletedId.size() > 0 && commDeletedId.getObjectId("_id") != null) {
//                        totalMigrated = totalMigrated + dbObjectsList.size();
//                        Document setquery = new Document("movedId", dbObjectsList.get(dbObjectsList.size() - 1).get("_id"))
//                                .append("totalMigrated", totalMigrated)
//                                .append("updatedOn", new Date());
//                        queryJson = new Document("_id", commDeletedId.getObjectId("_id"));
//                        Document rowJson = new Document("$set", setquery);
////                        genericMongoDao.updateRow(NStoS3util.lastUpdatedIdColl, queryJson, rowJson, false, false, NStoS3util.lastUpdatedIdDB);
//                    }
//                    queryOnId.put("$gt", dbObjectsList.get(dbObjectsList.size() - 1).get("_id"));
//                    queryObject.put("_id", queryOnId);
//                    dbObjectsList = null; // explicitly nullify
//                    dbObjectsList = mongoUtil.getDBObjects(srcMongoClient, coll, queryObject, projection, 0, len, orderObject, db);
//                }
//
//            } catch (Exception e) {
//                logger.error("Exception caught in movePBCromaV2ToS3, ns:{} , msg: {}", e.getMessage());
//            } finally {
//                isMoveCromaPBCromaV2LogsJobStop = false;
//                isMoveCromaPBCromaV2LogsJobRunning = false;
//            }
//    }

//    private List<Document> getList(MongoClient srcMongoClient, String coll, Document queryOnrequestId, int offset, Document orderObject, String db, List<Document> resultList, int chunkSize) {
//
//        try {
//            List<Document> dataList = mongoUtil.getDBObjects(srcMongoClient, coll, queryOnrequestId, null, offset, chunkSize, orderObject, db);
//            while (dataList.size() > 0) {
//                resultList.addAll(dataList);
//                offset += chunkSize;
//                dataList = mongoUtil.getDBObjects(srcMongoClient, coll, queryOnrequestId, null, offset, chunkSize, orderObject, db);
//            }
//        } catch (Exception e) {
//            logger.error("Exception while getList");
//        }
//        return resultList;
//    }

}
