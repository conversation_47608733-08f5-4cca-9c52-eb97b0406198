package com.docMigration.service.impl;

import com.docMigration.dao.ChatMongoDao;
import com.docMigration.service.PcdLoggerArchivalService;
import com.docMigration.service.S3BucketService;
import com.docMigration.util.AppConstants;
import com.docMigration.util.DateUtil;
import com.docMigration.util.NStoS3util;
import com.mongodb.BasicDBObject;
import com.mongodb.DBObject;
import com.mongodb.util.JSON;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashSet;
import java.util.List;

import static com.docMigration.util.CompressionUtil.compressData;
@Service
public class PcdLoggerArchivalServiceImpl implements PcdLoggerArchivalService {
    private final Logger logger = LoggerFactory.getLogger(PcdLoggerArchivalServiceImpl.class);

    public static boolean isPCDMoveS3JobRunning = false;
    public static boolean isPCDMoveS3JobStop = false;
    @Autowired
    private S3BucketService s3service;
    @Autowired
    @Qualifier("newArchivalMongoDao")
    private ChatMongoDao newArchivalMongoDao;

    @Autowired
    @Qualifier("pcdMongoDao")
    private ChatMongoDao pcdMongoDao;
    @Override
    public void movePcdBookLeads(String dbColl, Integer size, int days, int chunkSize) throws Exception {

        logger.info("Inside movePcdBookLeads , ns: {}, size: {}, timeCheck: {}, days", dbColl, size, days);
        List<DBObject> dbObjectsList = null;
        try {
            String[] nsArray = dbColl.split("\\.");
            String db = nsArray[0];
            String coll = nsArray[1];
            int len = size;
            long totalMigrated = 0;

            DBObject queryForDeletId = new BasicDBObject();
            queryForDeletId.put("coll", dbColl);
            BasicDBObject motorDeletedId = newArchivalMongoDao.getDBObjectByDbName(NStoS3util.lastUpdatedIdColl, queryForDeletId, NStoS3util.lastUpdatedIdDB);
            DBObject queryOnId = new BasicDBObject();
            //only provide startId at once we are starting the batch.
            if (motorDeletedId != null && motorDeletedId.size() > 0) {
                queryOnId.put("$gte", motorDeletedId.get("movedId"));
                totalMigrated = (long)motorDeletedId.get("totalMigrated");
            }
            queryOnId.put("$lt", new ObjectId(DateUtil.getStringToDate(DateUtil.getFormatedDate(days, DateUtil.DATE_FORMAT_EE_MMM_dd_hh_mm_ss_z_yyyy), DateUtil.DATE_FORMAT_EE_MMM_dd_hh_mm_ss_z_yyyy)));
            DBObject queryObject = new BasicDBObject();
            queryObject.put("_id", queryOnId);
            BasicDBObject projection = new BasicDBObject("_id", 1).append("leadId", 1);
            DBObject orderObject = new BasicDBObject("_id", 1);
            dbObjectsList = pcdMongoDao.getDBObjects(coll, queryObject, projection, 0, len, orderObject, db);
            logger.info("isPCDMoveS3JobStop : {} , isPCDMoveS3JobRunning: {} ", isPCDMoveS3JobStop, isPCDMoveS3JobRunning);
            while (dbObjectsList != null && dbObjectsList.size() > 0 && !isPCDMoveS3JobStop) {

                List<Object> listOfIds = new ArrayList<>();
                HashSet<String> enquiryIds = new HashSet<>();
                for (DBObject jsonDocument : dbObjectsList) {
                    Integer ileadId = null;
                    Long lleadId = null;
                    String sleadId = null;
                    if(jsonDocument.get("leadId") instanceof Integer) {
                        ileadId = (Integer) jsonDocument.get("leadId");
                        sleadId = String.valueOf(ileadId);
                    } else if(jsonDocument.get("leadId") instanceof Long) {
                        lleadId = (Long) jsonDocument.get("leadId");
                        sleadId = String.valueOf(lleadId);
                    }
                    if (sleadId != null && !enquiryIds.contains(sleadId)){
                        enquiryIds.add(sleadId);
                        // query to find booked EnquiryId
                        DBObject queryOnLeadId = new BasicDBObject();
                        if(ileadId != null && ileadId > 0) {
                            queryOnLeadId.put("leadId", ileadId);
                        } else if(lleadId != null && lleadId > 0){
                            queryOnLeadId.put("leadId", lleadId);
                        }
                        if((ileadId != null && ileadId > 0) || lleadId != null && lleadId > 0) {
                            listOfIds.add(jsonDocument.get("_id"));
                            int offset = 0;
                            List<DBObject> dbList = pcdMongoDao.getDBObjects(coll, queryOnLeadId, offset, chunkSize, null, db);
                            if (dbList.size() == chunkSize) {
                                offset = offset + chunkSize;
                                dbList = getList(chunkSize, coll, queryOnLeadId, offset, db, dbList);

                            }
                            String jsonString;
                            if (dbList != null && dbList.size() > 0) {
                                try {
                                    jsonString = JSON.serialize(dbList);
                                    // Compress JSON data
                                    byte[] compressedData = compressData(jsonString);
                                    String fileName = sleadId + "." + "json.gz";
                                    logger.info("PCD fileName : " + fileName);
                                    String folderName = NStoS3util.MAP_NS_TO_S3_PATH.get(dbColl);
                                    String url = s3service.addByteContentWithGzip(compressedData, NStoS3util.BUCKET_NAME_LOGGER_ARCHIVAL, folderName, fileName, "application/gzip");
                                    if (url.equals(AppConstants.FILE_ALREADY_EXIST)) {
                                        try {
                                            pcdMongoDao.addRows(coll + "_failOver", dbList, db);
                                        } catch (Exception e) {
                                            logger.error("Error while adding in PCD moveBookLeads , {}", e.getMessage());
                                        }
                                    }
                                    for (DBObject reqIdDoc : dbList) {
                                        listOfIds.add(reqIdDoc.get("_id"));
                                    }
                                } catch (Exception e) {
                                    logger.error("Error serializing DBObject: " + e.getMessage());
                                    throw e;
                                }

                            }
                        }
                    }
                }

                List<Object> subList;
                int startIndx = 0;
                int lastIndx = 100;
                if (listOfIds.size() > 100) {
                    while (startIndx < listOfIds.size()) {
                        if (startIndx + 100 >= listOfIds.size()) {
                            lastIndx = listOfIds.size();
                        }
                        subList = listOfIds.subList(startIndx, lastIndx);
                        DBObject queryFormovedIds = new BasicDBObject();
                        queryFormovedIds.put("_id", new BasicDBObject().append("$in", subList));
                        pcdMongoDao.deleteRow(coll, queryFormovedIds, true, db);
                        logger.info("deleted PCD Last _Id : {}", subList.get(subList.size() - 1));
                        startIndx = lastIndx;
                        lastIndx = lastIndx + 100;
                        subList = null;
                    }
                } else if (listOfIds != null && listOfIds.size() > 0) {
                    DBObject queryFormovedIds = new BasicDBObject();
                    queryFormovedIds.put("_id", new BasicDBObject().append("$in", listOfIds));
                    pcdMongoDao.deleteRow(coll, queryFormovedIds, true, db);
                    logger.info("deleted PCD Last _Id : {}", listOfIds.get(listOfIds.size() - 1));
                }
                listOfIds = null;
                enquiryIds = null;
                DBObject queryJson;
                if (motorDeletedId != null && motorDeletedId.size() > 0 && motorDeletedId.getObjectId("_id") != null) {
                    totalMigrated = totalMigrated + dbObjectsList.size();
                    DBObject setquery = new BasicDBObject("movedId", dbObjectsList.get(dbObjectsList.size() - 1).get("_id"))
                            .append("totalMigrated", totalMigrated)
                            .append("updatedOn", new Date());
                    queryJson = new BasicDBObject("_id", motorDeletedId.getObjectId("_id"));
                    DBObject rowJson = new BasicDBObject("$set", setquery);
                    newArchivalMongoDao.updateRow(NStoS3util.lastUpdatedIdColl, queryJson, rowJson, false, false, NStoS3util.lastUpdatedIdDB);
                }
                queryOnId.put("$gt", dbObjectsList.get(dbObjectsList.size() - 1).get("_id"));
                queryObject.put("_id", queryOnId);
                dbObjectsList = null;
                dbObjectsList = pcdMongoDao.getDBObjects(coll, queryObject, projection, 0  , len, orderObject, db);
            }
        }catch (Exception e) {
            logger.error("Exception caught into movePcdBookLeads, msg: {}", e.getMessage());
            throw new Exception("Exception cuaght while processing, Pcd , msg: " + e.getMessage());
        } finally {
            isPCDMoveS3JobStop = false;
            isPCDMoveS3JobRunning = false;
        }
    }

    private List<DBObject> getList(int chunkSize , String coll, DBObject queryOnrequestId, int offset, String db, List<DBObject> resultList) {

        try {
            List<DBObject> dataList = pcdMongoDao.getDBObjects(coll, queryOnrequestId, offset, chunkSize, null, db);
            while (dataList.size() > 0){
                resultList.addAll(dataList);
                offset += chunkSize;
                dataList = pcdMongoDao.getDBObjects(coll, queryOnrequestId, offset, chunkSize, null, db);
            }
        } catch (Exception e) {
            logger.error("Exception while getList, msg: {}", e.getMessage());
        }
        return resultList;
    }
}
