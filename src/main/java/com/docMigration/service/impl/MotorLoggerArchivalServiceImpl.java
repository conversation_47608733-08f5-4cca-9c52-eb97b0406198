package com.docMigration.service.impl;

import com.docMigration.dao.ChatMongoDao;
import com.docMigration.service.MotorLoggerArchivalService;
import com.docMigration.service.S3BucketService;
import com.docMigration.util.*;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import com.mongodb.BasicDBObject;
import com.mongodb.DBObject;
import com.mongodb.util.JSON;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.io.BufferedReader;
import java.io.FileReader;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;

import static com.docMigration.util.CompressionUtil.compressData;

@Service
public class MotorLoggerArchivalServiceImpl implements MotorLoggerArchivalService {
    private final Logger logger = LoggerFactory.getLogger(MotorLoggerArchivalServiceImpl.class);

    public static boolean isClaimMoveS3JobRunning = false;
    public static boolean isClaimMoveS3JobStop = false;

    public static boolean isCarMoveS3JobRunning = false;
    public static boolean isCarMoveS3JobStop = false;
    public static boolean isOldCarMoveS3JobRunning = false;
    public static boolean isOldCarMoveS3JobStop = false;

    public static boolean isKYCCarMoveS3JobRunning = false;
    public static boolean isKYCCarMoveS3JobStop = false;
    public static boolean isOldKYCCarMoveS3JobRunning = false;
    public static boolean isOldCKYCCarMoveS3JobStop = false;
    public static boolean isTwMoveS3JobRunning = false;
    public static boolean isTwS3MoveDataStop = false;
    public static boolean isOldTwMoveS3JobRunning = false;
    public static boolean isOldTwS3MoveDataStop = false;

    public static boolean isTwCoreKycMoveS3JobRunning = false;
    public static boolean isTwoCoreKycS3MoveDataStop = false;

    public static boolean isKYCS3MoveDataStop = false;
    public static boolean isKYCJobS3MoveRunning = false;

    public static boolean isOldCarMoveS3JobRunning1 = false;
    public static boolean isOldCarMoveS3JobRunning2 = false;
    public static boolean isOldCarMoveS3JobRunning3 = false;
    public static boolean isOldCarMoveS3JobRunning4 = false;
    public static boolean isOldCarMoveS3JobRunning5 = false;

    @Autowired
    @Qualifier("motorLoggerDao")
    private ChatMongoDao motorLoggerDao;
    @Autowired
    @Qualifier("newArchivalMongoDao")
    private ChatMongoDao newArchivalMongoDao;
    @Autowired
    private S3BucketService s3service;
    @Autowired
    @Qualifier("newArchivalMongoDao")
    ChatMongoDao newArchMongoDao;
    @Autowired
    @Qualifier("newTempMongoDao")
    ChatMongoDao newTempMongoDao;

    @Override
    public void moveMotorBookLeads(String dbColl, Integer size, int days, int chunkSize, int startDayForBookingEnqID) throws Exception {

        logger.info("Inside moveMotorBookLeads , ns: {}, size: {}, timeCheck: {}, days", dbColl, size, days);
        List<DBObject> dbObjectsList = null;
        try {
            String[] nsArray = dbColl.split("\\.");
            String db = nsArray[0];
            String coll = nsArray[1];
            int len = size;
            long totalMigrated = 0;
            // move booked enquiryId in collection "BookedEnquiry117Temp1"
            // run this job before start so that we can check booked logs and move to s3
            moveCarEnquiryIdLeadID(days+5, startDayForBookingEnqID);

            DBObject queryForDeletId = new BasicDBObject();
            queryForDeletId.put("coll", dbColl);
            BasicDBObject motorDeletedId = newArchivalMongoDao.getDBObjectByDbName(NStoS3util.lastUpdatedIdColl, queryForDeletId, NStoS3util.lastUpdatedIdDB);
            DBObject queryOnId = new BasicDBObject();
            //only provide startId at once we are starting the batch.
            if (motorDeletedId != null && motorDeletedId.size() > 0) {
                queryOnId.put("$gte", motorDeletedId.get("movedId"));
                totalMigrated = (long)motorDeletedId.get("totalMigrated");
            }
            queryOnId.put("$lt", new ObjectId(DateUtil.getStringToDate(DateUtil.getFormatedDate(days, DateUtil.DATE_FORMAT_EE_MMM_dd_hh_mm_ss_z_yyyy), DateUtil.DATE_FORMAT_EE_MMM_dd_hh_mm_ss_z_yyyy)));
            DBObject queryObject = new BasicDBObject();
            queryObject.put("_id", queryOnId);
            BasicDBObject projection = new BasicDBObject("_id", 1).append("EnquiryID", 1).append("IsLeadBooked", 1);
            DBObject orderObject = new BasicDBObject("_id", 1);
            dbObjectsList = motorLoggerDao.getDBObjects(coll, queryObject, projection, 0, len, orderObject, db);
            logger.info("isCarMoveS3JobStop : {} , isCarMoveS3JobRunning: {} ", isCarMoveS3JobStop, isCarMoveS3JobRunning);
            while (dbObjectsList != null && dbObjectsList.size() > 0 && !isCarMoveS3JobStop) {

                List<Object> listOfIds = new ArrayList<>();
                HashSet<String> enquiryIds = new HashSet<>();
                for (DBObject jsonDocument : dbObjectsList) {
                    listOfIds.add(jsonDocument.get("_id"));

                    Integer iEnquiryID = null;
                    Long lEnquiryID = null;
                    String sEnquirtyId = null;
                    if(jsonDocument.get("EnquiryID") instanceof Integer) {
                        iEnquiryID = (Integer) jsonDocument.get("EnquiryID");
                        sEnquirtyId = String.valueOf(iEnquiryID);
                    } else if(jsonDocument.get("EnquiryID") instanceof Long) {
                        lEnquiryID = (Long) jsonDocument.get("EnquiryID");
                        sEnquirtyId = String.valueOf(lEnquiryID);
                    }
                    if(sEnquirtyId.equals("9999")){
                        continue;
                    }
                    Boolean isLeadBooked = (Boolean) jsonDocument.get("IsLeadBooked");
                    logger.info("IsLeadBooked : {}", isLeadBooked);
                    if (((iEnquiryID != null && iEnquiryID>0) || (lEnquiryID  != null && lEnquiryID>0 )) && !enquiryIds.contains(sEnquirtyId)){
                        enquiryIds.add(sEnquirtyId);
                        // query to find booked EnquiryId
                        DBObject queryOnEnqId = new BasicDBObject();
                        if(iEnquiryID != null && iEnquiryID > 0) {
                            queryOnEnqId.put("EnquiryID", iEnquiryID);
                        } else if(lEnquiryID != null && lEnquiryID > 0){
                            queryOnEnqId.put("EnquiryID", lEnquiryID);
                        }
                        List<DBObject> bookedObj = null;
                        if (isLeadBooked == null || !isLeadBooked) {
                            DBObject queryOnsEnqId = new BasicDBObject();
                            queryOnsEnqId.put("EnquiryID", sEnquirtyId);
                            DBObject queryToReturnField = new BasicDBObject("_id", 0).append("EnquiryID", 1);
                            bookedObj = motorLoggerDao.getDBObjects("BookedEnquiry117Temp1", queryOnsEnqId, queryToReturnField, 0, 1, null, db);
                            logger.info("bookedObj.size() : {}", bookedObj.size());
                        }
                        if ((isLeadBooked != null && isLeadBooked) || (bookedObj != null && bookedObj.size() > 0)) {
                            int offset = 0;
                            List<DBObject> dbList = motorLoggerDao.getDBObjects(coll, queryOnEnqId, offset, chunkSize, null, db);
                            if (dbList.size() == chunkSize) {
                                offset = offset + chunkSize;
                                dbList = getList(chunkSize, coll, queryOnEnqId, offset, db, dbList);

                            }
                            String jsonString;
                            if (dbList != null && dbList.size() > 0) {
                                try {
                                    jsonString = JSON.serialize(dbList);
                                    // Compress JSON data
                                    byte[] compressedData = compressData(jsonString);
                                    String fileName = sEnquirtyId + "." + "json.gz";
                                    logger.info("Car fileName : " + fileName);
                                    String folderName = NStoS3util.MAP_NS_TO_S3_PATH.get(dbColl);
                                    String url = s3service.addByteContentWithGzip(compressedData, NStoS3util.BUCKET_NAME_LOGGER_ARCHIVAL, folderName, fileName, "application/gzip");
                                    if (url.equals(AppConstants.FILE_ALREADY_EXIST)) {
                                        try {
                                            newArchMongoDao.addRows(coll, dbList, db);
                                        } catch (Exception e) {
                                            logger.error("Error while adding in motor moveBookLeads , {}", e.getMessage());
                                        }
                                    }
                                    for (DBObject reqIdDoc : dbList) {
                                        listOfIds.add(reqIdDoc.get("_id"));
                                    }
                                } catch (Exception e) {
                                    logger.error("Error serializing DBObject: " + e.getMessage());
                                    throw e;
                                }
                            }
                        }
                    }
                }

                List<Object> subList;
                int startIndx = 0;
                int lastIndx = 100;
                if (listOfIds.size() > 100) {
                    while (startIndx < listOfIds.size()) {
                        if (startIndx + 100 >= listOfIds.size()) {
                            lastIndx = listOfIds.size();
                        }
                        subList = listOfIds.subList(startIndx, lastIndx);
                        DBObject queryFormovedIds = new BasicDBObject();
                        queryFormovedIds.put("_id", new BasicDBObject().append("$in", subList));
                        motorLoggerDao.deleteRow(coll, queryFormovedIds, true, db);
                        logger.info("deleted Car Last _Id : {}", subList.get(subList.size() - 1));
                        startIndx = lastIndx;
                        lastIndx = lastIndx + 100;
                        subList = null;
                    }
                } else if (listOfIds != null && listOfIds.size() > 0) {
                    DBObject queryFormovedIds = new BasicDBObject();
                    queryFormovedIds.put("_id", new BasicDBObject().append("$in", listOfIds));
                    motorLoggerDao.deleteRow(coll, queryFormovedIds, true, db);
                    logger.info("deleted Car Last _Id : {}", listOfIds.get(listOfIds.size() - 1));
                }
                listOfIds = null;
                enquiryIds = null;
                DBObject queryJson;
                if (motorDeletedId != null && motorDeletedId.size() > 0 && motorDeletedId.getObjectId("_id") != null) {
                    totalMigrated = totalMigrated + dbObjectsList.size();
                    DBObject setquery = new BasicDBObject("movedId", dbObjectsList.get(dbObjectsList.size() - 1).get("_id"))
                            .append("totalMigrated", totalMigrated)
                            .append("updatedOn", new Date());
                    queryJson = new BasicDBObject("_id", motorDeletedId.getObjectId("_id"));
                    DBObject rowJson = new BasicDBObject("$set", setquery);
                    newArchivalMongoDao.updateRow(NStoS3util.lastUpdatedIdColl, queryJson, rowJson, false, false, NStoS3util.lastUpdatedIdDB);
                }
                queryOnId.put("$gt", dbObjectsList.get(dbObjectsList.size() - 1).get("_id"));
                queryObject.put("_id", queryOnId);
                dbObjectsList = null;
                dbObjectsList = motorLoggerDao.getDBObjects(coll, queryObject, projection, 0  , len, orderObject, db);
            }
        }catch (Exception e) {
            logger.error("Exception caught into moveMotorBookLeads, msg: {}", e.getMessage());
            throw new Exception("Exception cuaght while processing, Car , msg: " + e.getMessage());
        } finally {
            isCarMoveS3JobRunning = false;
            isCarMoveS3JobStop = false;
        }
    }

    @Override
    public void moveBookedLeadTwV1(String dbColl, Integer size, int days) {

        logger.info("Inside TW moveBookedLead dbColl: {}, days: {}", dbColl, days);
        List<DBObject> dbObjectsList = null;
        int twChunkSize = 100;
        try {
            String[] nsArray = dbColl.split("\\.");
            String db = nsArray[0];
            String coll = nsArray[1];;
            int len = size;
            long totalMigrated = 0;
            DBObject queryForDeletId = new BasicDBObject();
            queryForDeletId.put("coll", dbColl);
            BasicDBObject motorDeletedId = newArchivalMongoDao.getDBObjectByDbName(NStoS3util.lastUpdatedIdColl, queryForDeletId, NStoS3util.lastUpdatedIdDB);

            DBObject queryOnId = new BasicDBObject();
            //only provide startId at once we are starting the batch.
            if (motorDeletedId != null && motorDeletedId.size() > 0) {
                queryOnId.put("$gte", motorDeletedId.get("movedId"));
                totalMigrated = (long)motorDeletedId.get("totalMigrated");
            }
            DBObject queryObject = new BasicDBObject();
            DBObject orderObject = new BasicDBObject("_id", 1);
            queryOnId.put("$lt", new ObjectId(DateUtil.getStringToDate(DateUtil.getFormatedDate(days, DateUtil.DATE_FORMAT_EE_MMM_dd_hh_mm_ss_z_yyyy), DateUtil.DATE_FORMAT_EE_MMM_dd_hh_mm_ss_z_yyyy)));
            queryObject.put("_id", queryOnId);
            BasicDBObject projection = new BasicDBObject("_id", 1).append("VehicleDetailId", 1).append("IsLeadBooked",1);
            dbObjectsList = motorLoggerDao.getDBObjects(coll, queryObject, projection, 0, len, orderObject, db);
            logger.info("isTwMoveS3JobRunning: {} , isTwS3MoveDataStop:{}", isTwMoveS3JobRunning, isTwS3MoveDataStop);
            while (dbObjectsList != null && dbObjectsList.size() > 0 && !isTwS3MoveDataStop) {

                List<Object> listOfIds = new ArrayList<>();
                List<String> vehicleDetailIds = new ArrayList<>();
                for (DBObject jsonDocument : dbObjectsList) {
                    listOfIds.add(jsonDocument.get("_id"));

                    Integer vehicleDetailId = null;
                    Long lVehicleDetailId = null;
                    String sVehicleDetailId = null;
                    if(jsonDocument.get("VehicleDetailId") instanceof Integer) {
                        vehicleDetailId = (Integer) jsonDocument.get("VehicleDetailId");
                        sVehicleDetailId = String.valueOf(vehicleDetailId);
                    }
                    else if(jsonDocument.get("VehicleDetailId") instanceof Long) {
                        lVehicleDetailId = (Long) jsonDocument.get("VehicleDetailId");
                        sVehicleDetailId = String.valueOf(lVehicleDetailId);
                    }
                    else if(jsonDocument.get("VehicleDetailId") instanceof String) {
                        sVehicleDetailId = (String) jsonDocument.get("VehicleDetailId");
                    }else{
                        continue;
                    }
                    Boolean isLeadBooked = (Boolean) jsonDocument.get("IsLeadBooked");
                   if (sVehicleDetailId != null && sVehicleDetailId.length() > 2 && !vehicleDetailIds.contains(sVehicleDetailId)) {
                       vehicleDetailIds.add(sVehicleDetailId);
                       DBObject queryOnEnqId = new BasicDBObject();
                       if(vehicleDetailId != null && vehicleDetailId > 0){
                           queryOnEnqId.put("VehicleDetailId", vehicleDetailId);
                       }
                       else if(lVehicleDetailId != null && lVehicleDetailId > 0){
                           queryOnEnqId.put("VehicleDetailId", lVehicleDetailId);
                       }
                       else if(sVehicleDetailId != null && Long.parseLong(sVehicleDetailId) > 0){
                           queryOnEnqId.put("VehicleDetailId", sVehicleDetailId);
                       }
                       List<DBObject> bookedObj = null;
                       if (isLeadBooked == null || !isLeadBooked) {
                           DBObject queryToReturnField = new BasicDBObject("_id", 0).append("VehicleDetailId", 1);
                           DBObject queryToCheckBookedEnq = new BasicDBObject().append("VehicleDetailId", sVehicleDetailId);
                           bookedObj = motorLoggerDao.getDBObjects("BookedEnquiryVehicleId114Temp", queryToCheckBookedEnq, queryToReturnField, 0, 1, null, db);
                       }
                       if ((isLeadBooked != null && isLeadBooked) || (bookedObj != null && bookedObj.size() > 0)) {
                           System.out.println("Moved: VehicleDetailId: " + vehicleDetailId);
                           DBObject queryOnVehicleDetailId = new BasicDBObject();
                           queryOnVehicleDetailId.put("VehicleDetailId", vehicleDetailId);
                           int offset = 0;
                           List<DBObject> dbList = motorLoggerDao.getDBObjects(coll, queryOnVehicleDetailId, offset, twChunkSize, null, db);
                           if (dbList.size() == twChunkSize) {
                               offset = offset + twChunkSize;
                               dbList = getList(twChunkSize, coll, queryOnVehicleDetailId, offset, db, dbList);

                           }
                           String jsonString = null;
                           if (dbList != null && dbList.size() > 0) {
                               try {
                                   jsonString = JSON.serialize(dbList);

                                   // Compress JSON data
                                   byte[] compressedData = compressData(jsonString);
                                   //String deCompressedData = decompressData(compressedData);
                                   String fileName = "vid_" + vehicleDetailId + "." + "json.gz";
                                   logger.info("TW fileName : " + fileName);
                                   String folderName = NStoS3util.MAP_NS_TO_S3_PATH.get(dbColl);
                                   String url = s3service.addByteContentWithGzip(compressedData, NStoS3util.BUCKET_NAME_LOGGER_ARCHIVAL, folderName, fileName, "application/gzip");
                                   if (url.equals(AppConstants.FILE_ALREADY_EXIST)) {
                                       try {
                                           newArchMongoDao.addRows(coll, dbList, db);
                                       } catch (Exception e) {
                                           logger.error("Error while adding in TW moveBookLeads , {}", e.getMessage());
                                       }
                                   }
                                   for (DBObject reqIdDoc : dbList) {
                                       listOfIds.add(reqIdDoc.get("_id"));
                                   }
                               } catch (Exception e) {
                                   logger.error("Error serializing DBObject: " + e.getMessage());
                                   return;
                               }
                           }
                       }
                    }else {
                        logger.info("EnquiryId & VehicleDetailId both are not present");
                    }
                }
                vehicleDetailIds = null;
                List<Object> subList = new ArrayList<>();
                int startIndx = 0;
                int lastIndx = 100;
                if (listOfIds != null && listOfIds.size() > 100) {
                    while (startIndx < listOfIds.size()) {
                        if (startIndx + 100 >= listOfIds.size()) {
                            lastIndx = listOfIds.size();
                        }
                        subList = listOfIds.subList(startIndx, lastIndx);
                        DBObject queryFormovedIds = new BasicDBObject();
                        queryFormovedIds.put("_id", new BasicDBObject().append("$in", subList));
                        motorLoggerDao.deleteRow(coll, queryFormovedIds, true, db);
                        logger.info("TW deleted Last _Id : {}", subList.get(subList.size() - 1));
                        startIndx = lastIndx;
                        lastIndx = lastIndx + 100;
                        subList = null;
                    }
                } else if (listOfIds != null && listOfIds.size() > 0) {
                    DBObject queryFormovedIds = new BasicDBObject();
                    queryFormovedIds.put("_id", new BasicDBObject().append("$in", listOfIds));
                    motorLoggerDao.deleteRow(coll, queryFormovedIds, true, db);
                    logger.info("TW deleted Last _Id : {}", listOfIds.get(listOfIds.size() - 1));
                }

                // nullify after delete all
                listOfIds = null;

                DBObject queryJson;
                if (motorDeletedId != null && motorDeletedId.size() > 0 && motorDeletedId.getObjectId("_id") != null) {
                    totalMigrated = totalMigrated + dbObjectsList.size();
                    DBObject setquery = new BasicDBObject("movedId", dbObjectsList.get(dbObjectsList.size() - 1).get("_id"))
                            .append("totalMigrated", totalMigrated)
                            .append("updatedOn", new Date());
                    queryJson = new BasicDBObject("_id", motorDeletedId.getObjectId("_id"));
                    DBObject rowJson = new BasicDBObject("$set", setquery);
                    newArchivalMongoDao.updateRow(NStoS3util.lastUpdatedIdColl, queryJson, rowJson, false, false, NStoS3util.lastUpdatedIdDB);
                }
                queryOnId.put("$gt", dbObjectsList.get(dbObjectsList.size() - 1).get("_id"));
                queryObject.put("_id", queryOnId);
                dbObjectsList = null;
                dbObjectsList = motorLoggerDao.getDBObjects(coll, queryObject, projection, 0, len, orderObject, db);

            }
        }catch (Exception e) {
            logger.error("Exception caught into TW moveBookedLead, msg: {}", e.getMessage());
        }finally {
            isTwS3MoveDataStop = false;
            isTwMoveS3JobRunning = false;
        }
    }

    @Override
    public void moveKycLogs(String dbColl, Integer size, int days, int kycChunkSize) {
        logger.info("Inside moveKycLogs , ns: {}, size: {}, timeCheck: {}, days", dbColl, size, days);
        List<DBObject> dbObjectsList = null;

        try {
            String[] nsArray = dbColl.split("\\.");
            String db = nsArray[0];
            String coll = nsArray[1];
            int len = size;
            long totalMigrated = 0;
            Gson gson = null;
            DBObject queryForDeletId = new BasicDBObject();
            queryForDeletId.put("coll", dbColl);
            BasicDBObject motorDeletedId = newArchivalMongoDao.getDBObjectByDbName(NStoS3util.lastUpdatedIdColl, queryForDeletId, NStoS3util.lastUpdatedIdDB);

            DBObject queryOnId = new BasicDBObject();
            //only provide startId at once we are starting the batch.
            if (motorDeletedId != null && motorDeletedId.size() > 0) {
                queryOnId.put("$gte", motorDeletedId.get("movedId"));
                totalMigrated = (long) motorDeletedId.get("totalMigrated");
            }
            queryOnId.put("$lt", new ObjectId(DateUtil.getStringToDate(DateUtil.getFormatedDate(days, DateUtil.DATE_FORMAT_EE_MMM_dd_hh_mm_ss_z_yyyy), DateUtil.DATE_FORMAT_EE_MMM_dd_hh_mm_ss_z_yyyy)));
            DBObject queryObject = new BasicDBObject();
            queryObject.put("_id", queryOnId);
            BasicDBObject projection = new BasicDBObject("_id", 1).append("UniqueId", 1).append("UniqueType", 1);
            DBObject orderObject = new BasicDBObject("_id", 1);
            dbObjectsList = motorLoggerDao.getDBObjects(coll, queryObject, projection, 0, len, orderObject, db);
            logger.info("isKYCS3MoveDataStop:  {}, isKYCJobS3MoveRunning:{}", isKYCS3MoveDataStop, isKYCJobS3MoveRunning);
            while (dbObjectsList != null && dbObjectsList.size() > 0 && !isKYCS3MoveDataStop) {

                List<Object> listOfIds = new ArrayList<>();
                HashSet<String> uniqueIds = new HashSet<>();
                for (DBObject jsonDocument : dbObjectsList) {
                    String uniqueId = (String) jsonDocument.get("UniqueId");
                    String uniqueType = (String) jsonDocument.get("UniqueType");
                    logger.info("kyc uniqueType :{}", uniqueType);
                    if (StringUtils.hasText(uniqueId) && !uniqueIds.contains(uniqueId) && StringUtils.hasText(uniqueType) && uniqueType.equals("LeadId")) {
                        uniqueIds.add(uniqueId);
                        DBObject queryOnrequestId = new BasicDBObject();
                        queryOnrequestId.put("UniqueId", uniqueId);
//                            List<DBObject> dbList = motorLoggerDao.getDBObjects(coll, queryOnrequestId, null, 0, 0, orderObject, db);
                        int offset = 0;
                        List<DBObject> dbList = motorLoggerDao.getDBObjects(coll, queryOnrequestId, 0, kycChunkSize, null, db);
                        if (dbList.size() == kycChunkSize) {
                            offset = offset + kycChunkSize;
                            dbList = getList(kycChunkSize, coll, queryOnrequestId, offset, db, dbList);

                        }
                        String jsonString;
                        if (dbList != null && dbList.size()>0) {
                            try {
                                jsonString = JSON.serialize(dbList);
                                // Compress JSON data
                                byte[] compressedData = compressData(jsonString);
                                //String deCompressedData = decompressData(compressedData);
                                String fileName = uniqueType + "_" + uniqueId + ".json.gz";
                                logger.info("KYC fileName : " + fileName);
                                String folderName = NStoS3util.MAP_NS_TO_S3_PATH.get(dbColl);
                                String url = s3service.addByteContentWithGzip(compressedData, NStoS3util.BUCKET_NAME_LOGGER_ARCHIVAL, folderName, fileName, "application/gzip");
                                if (url.equals(AppConstants.FILE_ALREADY_EXIST)) {
                                    try {
                                        newArchMongoDao.addRows(coll, dbList, db);
                                    } catch (Exception e) {
                                        logger.error("Error while adding in moveKycLogs , {}", e.getMessage());
                                    }
                                }
                                for (DBObject reqIdDoc : dbList) {
                                    listOfIds.add(reqIdDoc.get("_id"));
                                }
                            } catch (Exception e) {
                                logger.error("Error serializing DBObject: " + e.getMessage());
                                return;
                            }
                        }
                    }
                }
                uniqueIds = null;
                List<Object> subList = new ArrayList<>();
                int startIndx = 0;
                int lastIndx = 100;
                if (listOfIds.size() > 100) {
                    while (startIndx < listOfIds.size()) {
                        if (startIndx + 100 >= listOfIds.size()) {
                            lastIndx = listOfIds.size();
                        }
                        subList = listOfIds.subList(startIndx, lastIndx);
                        DBObject queryFormovedIds = new BasicDBObject();
                        queryFormovedIds.put("_id", new BasicDBObject().append("$in", subList));
                        motorLoggerDao.deleteRow(coll, queryFormovedIds, true, db);
                        logger.info("deleted KYC Last _id : {}", subList.get(subList.size() - 1));
                        startIndx = lastIndx;
                        lastIndx = lastIndx + 100;
                        subList = null;
                    }
                } else if (listOfIds != null && listOfIds.size() > 0) {
                    DBObject queryFormovedIds = new BasicDBObject();
                    queryFormovedIds.put("_id", new BasicDBObject().append("$in", listOfIds));
                    motorLoggerDao.deleteRow(coll, queryFormovedIds, true, db);
                    logger.info("deleted KYC Last _id : {}", listOfIds.get(listOfIds.size() - 1));
                }
                listOfIds = null;
                DBObject queryJson;
                if (motorDeletedId != null && motorDeletedId.size() > 0 && motorDeletedId.getObjectId("_id") != null) {
                    totalMigrated = totalMigrated + dbObjectsList.size();
                    DBObject setquery = new BasicDBObject("movedId", dbObjectsList.get(dbObjectsList.size() - 1).get("_id"))
                            .append("totalMigrated", totalMigrated)
                            .append("updatedOn", new Date());
                    queryJson = new BasicDBObject("_id", motorDeletedId.getObjectId("_id"));
                    DBObject rowJson = new BasicDBObject("$set", setquery);
                    newArchivalMongoDao.updateRow(NStoS3util.lastUpdatedIdColl, queryJson, rowJson, false, false, NStoS3util.lastUpdatedIdDB);
                }
                queryOnId.put("$gt", dbObjectsList.get(dbObjectsList.size() - 1).get("_id"));
                queryObject.put("_id", queryOnId);
                dbObjectsList = null;
                dbObjectsList = motorLoggerDao.getDBObjects(coll, queryObject, projection, 0, len, orderObject, db);
            }
        }catch (Exception e) {
            logger.error("Exception caught into moveKycLogs, msg: {}", e.getMessage());
        } finally {
            isKYCJobS3MoveRunning = false;
            isKYCS3MoveDataStop = false;
            logger.error("KYC job completed/stopped for ns: {}, msg: {}  ", dbColl);
        }
    }

    @Override
    public void moveClaimLogs(String dbColl, Integer size, int days, int claimChunkSize)  {
        logger.info("Inside moveClaimLogs , ns: {}, size: {}, timeCheck: {}, days", dbColl, size, days);
        List<DBObject> dbObjectsList = null;
        try {
            String[] nsArray = dbColl.split("\\.");
            String db = nsArray[0];
            String coll = nsArray[1];
            int len = size;
            long totalMigrated = 0;
            DBObject queryForDeletId = new BasicDBObject();
            queryForDeletId.put("coll", dbColl);
            BasicDBObject motorDeletedId = newArchivalMongoDao.getDBObjectByDbName(NStoS3util.lastUpdatedIdColl, queryForDeletId, NStoS3util.lastUpdatedIdDB);

            DBObject queryOnId = new BasicDBObject();
            //only provide startId at once we are starting the batch.
            if (motorDeletedId != null && motorDeletedId.size() > 0) {
                queryOnId.put("$gte", motorDeletedId.get("movedId"));
                totalMigrated = (long) motorDeletedId.get("totalMigrated");
            }
            queryOnId.put("$lt", new ObjectId(DateUtil.getStringToDate(DateUtil.getFormatedDate(days, DateUtil.DATE_FORMAT_EE_MMM_dd_hh_mm_ss_z_yyyy), DateUtil.DATE_FORMAT_EE_MMM_dd_hh_mm_ss_z_yyyy)));
            DBObject queryObject = new BasicDBObject();
            queryObject.put("_id", queryOnId);
            BasicDBObject projection = new BasicDBObject("_id", 1).append("UniqueId", 1).append("UniqueType", 1);
            DBObject orderObject = new BasicDBObject("_id", 1);
            dbObjectsList = motorLoggerDao.getDBObjects(coll, queryObject, projection, 0, len, orderObject, db);
            logger.info("isClaimMoveS3JobStop : {} , isClaimMoveS3JobRunning: {}", isClaimMoveS3JobStop , isClaimMoveS3JobRunning);
            List<Object> listOfIds = null;
            HashSet<String> uniqueIds = null;
            while (dbObjectsList != null && dbObjectsList.size() > 0 && !isClaimMoveS3JobStop) {

                listOfIds = new ArrayList<>();
                uniqueIds = new HashSet<>();
                for (DBObject jsonDocument : dbObjectsList) {
                    String uniqueId = (String) jsonDocument.get("UniqueId");
                    String uniqueType = (String) jsonDocument.get("UniqueType");
                    logger.info("claim uniqueType :{}", uniqueType);
                    if(uniqueType.equalsIgnoreCase("ClaimId")){
                        logger.info("inside claim uniqueType :{}", uniqueType);
                    }
                    if (StringUtils.hasText(uniqueId) && !uniqueId.equals("0") && !uniqueIds.contains(uniqueId) && StringUtils.hasText(uniqueType) && uniqueType.equals("ClaimId")) {
                        uniqueIds.add(uniqueId);
                        DBObject queryOnrequestId = new BasicDBObject().append("UniqueId", uniqueId);
                        int offset = 0;
                        List<DBObject> dbList = motorLoggerDao.getDBObjects(coll, queryOnrequestId, offset, claimChunkSize, null, db);
                        if (dbList.size() == claimChunkSize) {
                            offset = offset + claimChunkSize;
                            dbList = getList(claimChunkSize, coll, queryOnrequestId, offset, db, dbList);

                        }
                        String jsonString;
                        if (dbList != null && dbList.size()>0) {
                            try {
                                jsonString = JSON.serialize(dbList);
                                // Compress JSON data
                                byte[] compressedData = compressData(jsonString);
                                //String deCompressedData = decompressData(compressedData);
                                String fileName = uniqueType + "_" + uniqueId + ".json.gz";
                                String folderName = NStoS3util.MAP_NS_TO_S3_PATH.get(dbColl);
                                logger.info("Claim fileName : " + fileName);
                                String url = s3service.addByteContentWithGzip(compressedData, NStoS3util.BUCKET_NAME_LOGGER_ARCHIVAL, folderName, fileName, "application/gzip");
                                if (url.equals(AppConstants.FILE_ALREADY_EXIST)) {
                                    try {
                                        newArchMongoDao.addRows(coll, dbList, db);
                                    } catch (Exception e) {
                                        logger.error("Error while adding in moveClaimLogs , {}", e.getMessage());
                                    }
                                }
                                for (DBObject reqIdDoc : dbList) {
                                    listOfIds.add(reqIdDoc.get("_id"));
                                }
                            } catch (Exception e) {
                                logger.error("Error serializing DBObject: " + e.getMessage());
                                return;
                            }
                        }
                    }
                }

                List<Object> subList = new ArrayList<>();
                int startIndx = 0;
                int lastIndx = 100;
                if (listOfIds.size() > 100) {
                    while (startIndx < listOfIds.size()) {
                        if (startIndx + 100 >= listOfIds.size()) {
                            lastIndx = listOfIds.size();
                        }
                        subList = listOfIds.subList(startIndx, lastIndx);
                        DBObject queryFormovedIds = new BasicDBObject();
                        queryFormovedIds.put("_id", new BasicDBObject().append("$in", subList));
                        motorLoggerDao.deleteRow(coll, queryFormovedIds, true, db);
                        logger.info("deleted Claim Last _id : {}", subList.get(subList.size() - 1));
                        startIndx = lastIndx;
                        lastIndx = lastIndx + 100;
                        subList = null;
                    }
                } else if (listOfIds != null && listOfIds.size() > 0) {
                    DBObject queryFormovedIds = new BasicDBObject();
                    queryFormovedIds.put("_id", new BasicDBObject().append("$in", listOfIds));
                    motorLoggerDao.deleteRow(coll, queryFormovedIds, true, db);
                    logger.info("deleted Claim Last _id : {}", listOfIds.get(listOfIds.size() - 1));
                }

                DBObject queryJson;
                if (motorDeletedId != null && motorDeletedId.size() > 0 && motorDeletedId.getObjectId("_id") != null) {
                    totalMigrated = totalMigrated + dbObjectsList.size();
                    DBObject setquery = new BasicDBObject("movedId", dbObjectsList.get(dbObjectsList.size() - 1).get("_id"))
                            .append("totalMigrated", totalMigrated)
                            .append("updatedOn", new Date());                    queryJson = new BasicDBObject("_id", motorDeletedId.getObjectId("_id"));
                    DBObject rowJson = new BasicDBObject("$set", setquery);
                    newArchivalMongoDao.updateRow(NStoS3util.lastUpdatedIdColl, queryJson, rowJson, false, false, NStoS3util.lastUpdatedIdDB);
                }
                queryOnId.put("$gt", dbObjectsList.get(dbObjectsList.size() - 1).get("_id"));
                queryObject.put("_id", queryOnId);
                dbObjectsList = null;
                dbObjectsList = motorLoggerDao.getDBObjects(coll, queryObject, projection, 0, len, orderObject, db);

                listOfIds = null;
                uniqueIds = null;
            }
        }catch (Exception e) {
            logger.error("Exception caught into moveClaimLogs for ns: {}, msg: {}", dbColl, e.getMessage());
        } finally {
            isClaimMoveS3JobRunning = false;
            isClaimMoveS3JobStop = false;
        }
    }

    @Override
    public void moveOldMotorBookLeads(String dbColl, Integer size, int days, int chunkSize) throws Exception {

        logger.info("Inside moveOldMotorBookLeads , ns: {}, size: {}, timeCheck: {}, days", dbColl, size, days);
        List<DBObject> dbObjectsList = null;
        try {
            String[] nsArray = dbColl.split("\\.");
            String db = nsArray[0];
            String coll = nsArray[1];
            int len = size;
            long totalMigrated = 0;
            DBObject queryForDeletId = new BasicDBObject();
            queryForDeletId.put("coll", "CarLoggerDB.ReqResLog_Arch");
            BasicDBObject motorDeletedId = newArchivalMongoDao.getDBObjectByDbName(NStoS3util.lastUpdatedIdColl, queryForDeletId, NStoS3util.lastUpdatedIdDB);
            DBObject queryOnId = new BasicDBObject();
            //only provide startId at once we are starting the batch.
            if (motorDeletedId != null && motorDeletedId.size() > 0) {
                queryOnId.put("$gte", motorDeletedId.get("movedId"));
                totalMigrated = (long)motorDeletedId.get("totalMigrated");
            }
            queryOnId.put("$lt", new ObjectId(DateUtil.getStringToDate(DateUtil.getFormatedDate(days, DateUtil.DATE_FORMAT_EE_MMM_dd_hh_mm_ss_z_yyyy), DateUtil.DATE_FORMAT_EE_MMM_dd_hh_mm_ss_z_yyyy)));
            DBObject queryObject = new BasicDBObject();
            queryObject.put("_id", queryOnId);
            BasicDBObject projection = new BasicDBObject("_id", 1).append("EnquiryID", 1);
            DBObject orderObject = new BasicDBObject("_id", 1);
            dbObjectsList = newArchivalMongoDao.getDBObjects(coll, queryObject, projection, 0, len, orderObject, db);
            logger.info("isOldCarMoveS3JobStop : {} , isOldCarMoveS3JobRunning: {} ", isOldCarMoveS3JobStop, isOldCarMoveS3JobRunning);
            while (dbObjectsList != null && dbObjectsList.size() > 0 && !isOldCarMoveS3JobStop) {
                logger.info("Car Old migration running");
                List<Object> listOfIds = new ArrayList<>();
                HashSet<String> enquiryIds = new HashSet<>();
                for (DBObject jsonDocument : dbObjectsList) {
                    listOfIds.add(jsonDocument.get("_id"));

                    Integer iEnquiryID = null;
                    Long lEnquiryID = null;
                    String sEnquirtyId = null;
                    if(jsonDocument.get("EnquiryID") instanceof Integer) {
                        iEnquiryID = (Integer) jsonDocument.get("EnquiryID");
                        sEnquirtyId = String.valueOf(iEnquiryID);
                    } else if(jsonDocument.get("EnquiryID") instanceof Long) {
                        lEnquiryID = (Long) jsonDocument.get("EnquiryID");
                        sEnquirtyId = String.valueOf(lEnquiryID);
                    }
                    if(sEnquirtyId.equals("9999") || sEnquirtyId.equals("999999")){
                        continue;
                    }
                    if (((iEnquiryID != null && iEnquiryID>0) || (lEnquiryID  != null && lEnquiryID>0 )) && !enquiryIds.contains(sEnquirtyId)) {
                        enquiryIds.add(sEnquirtyId);
                        // query to find booked EnquiryId
                        DBObject queryOnEnqId = new BasicDBObject();
                        if(iEnquiryID != null && iEnquiryID > 0) {
                            queryOnEnqId.put("EnquiryID", iEnquiryID);
                        } else if(lEnquiryID != null && lEnquiryID > 0){
                            queryOnEnqId.put("EnquiryID", lEnquiryID);
                        }
                        int offset = 0;
                        List<DBObject> dbList = newArchivalMongoDao.getDBObjects(coll, queryOnEnqId, offset, chunkSize, null, db);
                        if (dbList.size() == chunkSize) {
                            offset = offset + chunkSize;
                            dbList = getOldList(chunkSize, coll, queryOnEnqId, offset, db, dbList);

                        }
                        String jsonString;
                        if (dbList != null && dbList.size() > 0) {
                            try {
                                jsonString = JSON.serialize(dbList);
                                // Compress JSON data
                                byte[] compressedData = compressData(jsonString);
                                String fileName = sEnquirtyId + "." + "json.gz";
                                logger.info("Car Old fileName : " + fileName);
                                String folderName = NStoS3util.MAP_NS_TO_S3_PATH.get(dbColl);
                                String url = s3service.addByteContentWithGzip(compressedData, NStoS3util.BUCKET_NAME_LOGGER_ARCHIVAL, folderName, fileName, "application/gzip");
                                logger.info("Car Old Url :{} ", url);
                                if (url.equals(AppConstants.FILE_ALREADY_EXIST)) {
                                    try {
//                                        archMongoDao.addRows(coll, dbList, db);
                                    } catch (Exception e) {
                                        logger.error("Error while adding in motor moveBookLeads , {}", e.getMessage());
                                    }
                                }
                                for (DBObject reqIdDoc : dbList) {
                                    listOfIds.add(reqIdDoc.get("_id"));
                                }
                            } catch (Exception e) {
                                logger.error("Error serializing DBObject: " + e.getMessage());
                                throw e;
                            }
                        }
                    }
                }

                List<Object> subList;
                int startIndx = 0;
                int lastIndx = 100;
                if (listOfIds.size() > 100) {
                    while (startIndx < listOfIds.size()) {
                        if (startIndx + 100 >= listOfIds.size()) {
                            lastIndx = listOfIds.size();
                        }
                        subList = listOfIds.subList(startIndx, lastIndx);
                        DBObject queryFormovedIds = new BasicDBObject();
                        queryFormovedIds.put("_id", new BasicDBObject().append("$in", subList));
                        newArchivalMongoDao.deleteRow(coll, queryFormovedIds, true, db);
                        logger.info("deleted Car Last _Id : {}", subList.get(subList.size() - 1));
                        startIndx = lastIndx;
                        lastIndx = lastIndx + 100;
                        subList = null;
                    }
                } else if (listOfIds != null && listOfIds.size() > 0) {
                    DBObject queryFormovedIds = new BasicDBObject();
                    queryFormovedIds.put("_id", new BasicDBObject().append("$in", listOfIds));
                    newArchivalMongoDao.deleteRow(coll, queryFormovedIds, true, db);
                    logger.info("deleted Car Last _Id : {}", listOfIds.get(listOfIds.size() - 1));
                }
                listOfIds = null;
                enquiryIds = null;
                DBObject queryJson;
                if (motorDeletedId != null && motorDeletedId.size() > 0 && motorDeletedId.getObjectId("_id") != null) {
                    totalMigrated = totalMigrated + dbObjectsList.size();
                    DBObject setquery = new BasicDBObject("movedId", dbObjectsList.get(dbObjectsList.size() - 1).get("_id"))
                            .append("totalMigrated", totalMigrated)
                            .append("updatedOn", new Date());
                    queryJson = new BasicDBObject("_id", motorDeletedId.getObjectId("_id"));
                    DBObject rowJson = new BasicDBObject("$set", setquery);
                    newArchivalMongoDao.updateRow(NStoS3util.lastUpdatedIdColl, queryJson, rowJson, false, false, NStoS3util.lastUpdatedIdDB);
                }
                queryOnId.put("$gt", dbObjectsList.get(dbObjectsList.size() - 1).get("_id"));
                queryObject.put("_id", queryOnId);
                dbObjectsList = newArchivalMongoDao.getDBObjects(coll, queryObject, projection, 0  , len, orderObject, db);
            }
        }catch (Exception e) {
            logger.error("Exception caught into moveMotorBookLeads, msg: {}", e.getMessage());
            throw new Exception("Exception cuaght while processing, Car , msg: " + e.getMessage());
        } finally {
            isOldCarMoveS3JobRunning = false;
            isOldCarMoveS3JobStop = false;
        }
    }

    @Override
    public void moveBookedLeadOldTw(String dbColl, Integer size, int days, int twChunkSize) throws Exception {

        logger.info("Inside TW moveBookedLeadOldTw dbColl: {}, days: {}", dbColl, days);
        List<DBObject> dbObjectsList = null;
        try {
            String[] nsArray = dbColl.split("\\.");
            String db = nsArray[0];
            String coll = nsArray[1];;
            int len = size;
            long totalMigrated = 0;
            DBObject queryForDeletId = new BasicDBObject();
            queryForDeletId.put("coll", "TwoWheelerDB.ReqResTWowheelerLog_Arch");
            BasicDBObject motorDeletedId = newArchivalMongoDao.getDBObjectByDbName(NStoS3util.lastUpdatedIdColl, queryForDeletId, NStoS3util.lastUpdatedIdDB);

            DBObject queryOnId = new BasicDBObject();
            //only provide startId at once we are starting the batch.
            if (motorDeletedId != null && motorDeletedId.size() > 0) {
                queryOnId.put("$gte", motorDeletedId.get("movedId"));
                totalMigrated = (long)motorDeletedId.get("totalMigrated");
            }
            DBObject queryObject = new BasicDBObject();
            DBObject orderObject = new BasicDBObject("_id", 1);
            queryOnId.put("$lt", new ObjectId(DateUtil.getStringToDate(DateUtil.getFormatedDate(days, DateUtil.DATE_FORMAT_EE_MMM_dd_hh_mm_ss_z_yyyy), DateUtil.DATE_FORMAT_EE_MMM_dd_hh_mm_ss_z_yyyy)));
            queryObject.put("_id", queryOnId);
            BasicDBObject projection = new BasicDBObject("_id", 1).append("VehicleDetailId", 1);
            dbObjectsList = newArchivalMongoDao.getDBObjects(coll, queryObject, projection, 0, len, orderObject, db);
            logger.info("isOldTwMoveS3JobRunning: {} , isOldTwS3MoveDataStop:{}", isOldTwMoveS3JobRunning, isOldTwS3MoveDataStop);
            while (dbObjectsList != null && dbObjectsList.size() > 0 && !isOldTwS3MoveDataStop) {

                List<Object> listOfIds = new ArrayList<>();
                HashSet<String> vehicleDetailIds = new HashSet<>();
                for (DBObject jsonDocument : dbObjectsList) {
                    listOfIds.add(jsonDocument.get("_id"));

                    Integer vehicleDetailId = null;
                    Long lVehicleDetailId = null;
                    String sVehicleDetailId = null;
                    if(jsonDocument.get("VehicleDetailId") instanceof Integer) {
                        vehicleDetailId = (Integer) jsonDocument.get("VehicleDetailId");
                        sVehicleDetailId = String.valueOf(vehicleDetailId);
                    }
                    else if(jsonDocument.get("VehicleDetailId") instanceof Long) {
                        lVehicleDetailId = (Long) jsonDocument.get("VehicleDetailId");
                        sVehicleDetailId = String.valueOf(lVehicleDetailId);
                    }
                    else if(jsonDocument.get("VehicleDetailId") instanceof String) {
                        sVehicleDetailId = (String) jsonDocument.get("VehicleDetailId");
                    }else{
                        continue;
                    }
                    if (sVehicleDetailId != null && sVehicleDetailId.length() > 2 && !vehicleDetailIds.contains(sVehicleDetailId)) {
                        vehicleDetailIds.add(sVehicleDetailId);
                        DBObject queryOnEnqId = new BasicDBObject();
                        if(vehicleDetailId != null && vehicleDetailId > 0){
                            queryOnEnqId.put("VehicleDetailId", vehicleDetailId);
                        }
                        else if(lVehicleDetailId != null && lVehicleDetailId > 0){
                            queryOnEnqId.put("VehicleDetailId", lVehicleDetailId);
                        }
                        else if(sVehicleDetailId != null && Long.parseLong(sVehicleDetailId) > 0){
                            queryOnEnqId.put("VehicleDetailId", sVehicleDetailId);
                        }
                            DBObject queryOnVehicleDetailId = new BasicDBObject();
                            queryOnVehicleDetailId.put("VehicleDetailId", vehicleDetailId);
                            int offset = 0;
                            List<DBObject> dbList = newArchivalMongoDao.getDBObjects(coll, queryOnVehicleDetailId, offset, twChunkSize, null, db);
                            if (dbList.size() == twChunkSize) {
                                offset = offset + twChunkSize;
                                dbList = getOldList(twChunkSize, coll, queryOnVehicleDetailId, offset, db, dbList);

                            }
                            String jsonString = null;
                            if (dbList != null && dbList.size() > 0) {
                                try {
                                    jsonString = JSON.serialize(dbList);

                                    // Compress JSON data
                                    byte[] compressedData = compressData(jsonString);
                                    //String deCompressedData = decompressData(compressedData);
                                    String fileName = "vid_" + vehicleDetailId + "." + "json.gz";
                                    logger.info("TW Old fileName : " + fileName);
                                    String folderName = NStoS3util.MAP_NS_TO_S3_PATH.get(dbColl);
                                    String url = s3service.addByteContentWithGzip(compressedData, NStoS3util.BUCKET_NAME_LOGGER_ARCHIVAL, folderName, fileName, "application/gzip");
                                    logger.info("TW Old Url :{} ", url);
//                                    if (url.equals(AppConstants.FILE_ALREADY_EXIST)) {
//                                        try {
//                                            archMongoDao.addRows(coll, dbList, db);
//                                        } catch (Exception e) {
//                                            logger.error("Error while adding in TW moveBookLeads , {}", e.getMessage());
//                                        }
//                                    }
                                    for (DBObject reqIdDoc : dbList) {
                                        listOfIds.add(reqIdDoc.get("_id"));
                                    }
                                } catch (Exception e) {
                                    logger.error("Error serializing DBObject: " + e.getMessage());
                                    return;
                                }
                            }
                    }
                }
                List<Object> subList = new ArrayList<>();
                int startIndx = 0;
                int lastIndx = 100;
                if (listOfIds != null && listOfIds.size() > 100) {
                    while (startIndx < listOfIds.size()) {
                        if (startIndx + 100 >= listOfIds.size()) {
                            lastIndx = listOfIds.size();
                        }
                        subList = listOfIds.subList(startIndx, lastIndx);
                        DBObject queryFormovedIds = new BasicDBObject();
                        queryFormovedIds.put("_id", new BasicDBObject().append("$in", subList));
                        newArchivalMongoDao.deleteRow(coll, queryFormovedIds, true, db);
                        logger.info("TW deleted Last _Id : {}", subList.get(subList.size() - 1));
                        startIndx = lastIndx;
                        lastIndx = lastIndx + 100;
                        subList = null;
                    }
                } else if (listOfIds != null && listOfIds.size() > 0) {
                    DBObject queryFormovedIds = new BasicDBObject();
                    queryFormovedIds.put("_id", new BasicDBObject().append("$in", listOfIds));
                    newArchivalMongoDao.deleteRow(coll, queryFormovedIds, true, db);
                    logger.info("TW deleted Last _Id : {}", listOfIds.get(listOfIds.size() - 1));
                }

                // nullify after delete all
                listOfIds = null;
                vehicleDetailIds = null;
                DBObject queryJson;
                if (motorDeletedId != null && motorDeletedId.size() > 0 && motorDeletedId.getObjectId("_id") != null) {
                    totalMigrated = totalMigrated + dbObjectsList.size();
                    DBObject setquery = new BasicDBObject("movedId", dbObjectsList.get(dbObjectsList.size() - 1).get("_id"))
                            .append("totalMigrated", totalMigrated)
                            .append("updatedOn", new Date());
                    queryJson = new BasicDBObject("_id", motorDeletedId.getObjectId("_id"));
                    DBObject rowJson = new BasicDBObject("$set", setquery);
                    newArchivalMongoDao.updateRow(NStoS3util.lastUpdatedIdColl, queryJson, rowJson, false, false, NStoS3util.lastUpdatedIdDB);
                }
                queryOnId.put("$gt", dbObjectsList.get(dbObjectsList.size() - 1).get("_id"));
                queryObject.put("_id", queryOnId);
                dbObjectsList = null;
                dbObjectsList = newArchivalMongoDao.getDBObjects(coll, queryObject, projection, 0, len, orderObject, db);

            }
        }catch (Exception e) {
            logger.error("Exception caught into TW moveBookedLead, msg: {}", e.getMessage());
        }finally {
            isOldTwS3MoveDataStop = false;
            isOldTwMoveS3JobRunning = false;
        }
    }

//    @Override
//    public void moveOldMotorKyc(String dbColl, Integer size, int days, int chunkSize) throws Exception {
//
//        logger.info("Inside moveOldMotorKyc , ns: {}, size: {}, timeCheck: {}, days", dbColl, size, days);
//        List<DBObject> dbObjectsList = null;
//        try {
//            String[] nsArray = dbColl.split("\\.");
//            String db = nsArray[0];
//            String coll = nsArray[1];
//            int len = size;
//            long totalMigrated = 0;
//            DBObject queryForDeletId = new BasicDBObject();
//            queryForDeletId.put("coll", dbColl);
//            BasicDBObject motorDeletedId = genericMongoDao.getDBObjectByDbName(NStoS3util.lastUpdatedIdColl, queryForDeletId, NStoS3util.lastUpdatedIdDB);
//            DBObject queryOnId = new BasicDBObject();
//            //only provide startId at once we are starting the batch.
//            if (motorDeletedId != null && motorDeletedId.size() > 0) {
//                queryOnId.put("$gte", motorDeletedId.get("movedId"));
//                totalMigrated = (long)motorDeletedId.get("totalMigrated");
//            }
//            queryOnId.put("$lt", new ObjectId(DateUtil.getStringToDate(DateUtil.getFormatedDate(days, DateUtil.DATE_FORMAT_EE_MMM_dd_hh_mm_ss_z_yyyy), DateUtil.DATE_FORMAT_EE_MMM_dd_hh_mm_ss_z_yyyy)));
//            DBObject queryObject = new BasicDBObject();
//            queryObject.put("_id", queryOnId);
//            BasicDBObject projection = new BasicDBObject("_id", 1).append("EnquiryId", 1);
//            DBObject orderObject = new BasicDBObject("_id", 1);
//            dbObjectsList = newArchivalMongoDao.getDBObjects(coll, queryObject, projection, 0, len, orderObject, db);
//            logger.info("isOldCKYCCarMoveS3JobStop : {} , isOldKYCCarMoveS3JobRunning: {} ", isOldCKYCCarMoveS3JobStop, isOldKYCCarMoveS3JobRunning);
//            while (dbObjectsList != null && dbObjectsList.size() > 0 && !isOldCKYCCarMoveS3JobStop) {
//                logger.info("Car Old KYC migration running");
//                List<Object> listOfIds = new ArrayList<>();
//                List<String> enquiryIds = new ArrayList<>();
//                for (DBObject jsonDocument : dbObjectsList) {
//                    listOfIds.add(jsonDocument.get("_id"));
//
//                    Integer iEnquiryID = null;
//                    Long lEnquiryID = null;
//                    String sEnquirtyId = null;
//                    if(jsonDocument.get("EnquiryId") instanceof Integer) {
//                        iEnquiryID = (Integer) jsonDocument.get("EnquiryId");
//                        sEnquirtyId = String.valueOf(iEnquiryID);
//                    } else if(jsonDocument.get("EnquiryId") instanceof Long) {
//                        lEnquiryID = (Long) jsonDocument.get("EnquiryId");
//                        sEnquirtyId = String.valueOf(lEnquiryID);
//                    }
//                    if (((iEnquiryID != null && iEnquiryID>0) || (lEnquiryID  != null && lEnquiryID>0 )) && !enquiryIds.contains(sEnquirtyId)) {
//                        enquiryIds.add(sEnquirtyId);
//                        // query to find booked EnquiryId
//                        DBObject queryOnEnqId = new BasicDBObject();
//                        if (iEnquiryID != null && iEnquiryID > 0) {
//                            queryOnEnqId.put("EnquiryId", iEnquiryID);
//                        } else if (lEnquiryID != null && lEnquiryID > 0) {
//                            queryOnEnqId.put("EnquiryId", lEnquiryID);
//                        }
//                        int offset = 0;
//                        List<DBObject> dbList = newArchivalMongoDao.getDBObjects(coll, queryOnEnqId, offset, chunkSize, orderObject, db);
//                        if (dbList.size() == chunkSize) {
//                            offset = offset + chunkSize;
//                            dbList = getOldList(chunkSize, coll, queryOnEnqId, offset, orderObject, db, dbList);
//
//                        }
//                        String jsonString;
//                        if (dbList != null && dbList.size() > 0) {
//                            try {
//                                jsonString = JSON.serialize(dbList);
//                                // Compress JSON data
//                                byte[] compressedData = compressData(jsonString);
//                                String fileName = sEnquirtyId + "." + "json.gz";
//                                logger.info("KYC Car Old fileName : " + fileName);
//                                String folderName = NStoS3util.MAP_NS_TO_S3_PATH.get(dbColl);
//                                String url = s3service.addByteContentWithGzip(compressedData, NStoS3util.BUCKET_NAME_LOGGER_ARCHIVAL, folderName, fileName, "application/gzip");
//                                if (url.equals(AppConstants.FILE_ALREADY_EXIST)) {
//                                    try {
//                                        archMongoDao.addRows(coll, dbList, db);
//                                    } catch (Exception e) {
//                                        logger.error("Error while adding in motor moveOldMotorKyc , {}", e.getMessage());
//                                    }
//                                }
//                                for (DBObject reqIdDoc : dbList) {
//                                    listOfIds.add(reqIdDoc.get("_id"));
//                                }
//                            } catch (Exception e) {
//                                logger.error("Error serializing DBObject: " + e.getMessage());
//                                throw e;
//                            }
//                        }
//                    }
//                }
//                enquiryIds = null;
//                List<Object> subList;
//                int startIndx = 0;
//                int lastIndx = 100;
//                if (listOfIds.size() > 100) {
//                    while (startIndx < listOfIds.size()) {
//                        if (startIndx + 100 >= listOfIds.size()) {
//                            lastIndx = listOfIds.size();
//                        }
//                        subList = listOfIds.subList(startIndx, lastIndx);
//                        DBObject queryFormovedIds = new BasicDBObject();
//                        queryFormovedIds.put("_id", new BasicDBObject().append("$in", subList));
//                        newArchivalMongoDao.deleteRow(coll, queryFormovedIds, true, db);
//                        logger.info("deleted KYC Car Last _Id : {}", subList.get(subList.size() - 1));
//                        startIndx = lastIndx;
//                        lastIndx = lastIndx + 100;
//                        subList = null;
//                    }
//                } else if (listOfIds != null && listOfIds.size() > 0) {
//                    DBObject queryFormovedIds = new BasicDBObject();
//                    queryFormovedIds.put("_id", new BasicDBObject().append("$in", listOfIds));
//                    newArchivalMongoDao.deleteRow(coll, queryFormovedIds, true, db);
//                    logger.info("deleted KYC Car Last _Id : {}", listOfIds.get(listOfIds.size() - 1));
//                }
//                listOfIds = null;
//
//                DBObject queryJson;
//                if (motorDeletedId != null && motorDeletedId.size() > 0 && motorDeletedId.getObjectId("_id") != null) {
//                    totalMigrated = totalMigrated + dbObjectsList.size();
//                    DBObject setquery = new BasicDBObject("movedId", dbObjectsList.get(dbObjectsList.size() - 1).get("_id"))
//                            .append("totalMigrated", totalMigrated)
//                            .append("updatedOn", new Date());
//                    queryJson = new BasicDBObject("_id", motorDeletedId.getObjectId("_id"));
//                    DBObject rowJson = new BasicDBObject("$set", setquery);
//                    genericMongoDao.updateRow(NStoS3util.lastUpdatedIdColl, queryJson, rowJson, false, false, NStoS3util.lastUpdatedIdDB);
//                }
//                queryOnId.put("$gt", dbObjectsList.get(dbObjectsList.size() - 1).get("_id"));
//                queryObject.put("_id", queryOnId);
//                dbObjectsList = newArchivalMongoDao.getDBObjects(coll, queryObject, projection, 0  , len, orderObject, db);
//            }
//        }catch (Exception e) {
//            logger.error("Exception caught into moveOldMotorKyc, msg: {}", e.getMessage());
//            throw new Exception("Exception cuaght while processing, Car , msg: " + e.getMessage());
//        } finally {
//            isOldKYCCarMoveS3JobRunning = false;
//            isOldCKYCCarMoveS3JobStop = false;
//        }
//    }
    @Override
    public void moveTWCoreKycDocsByVehicleId(String dbColl, Integer size, int days, Integer twChunkSize) {

        logger.info("Inside TW moveTWCoreKycDocsByVehicleId dbColl: {}, days: {}", dbColl, days);
        List<DBObject> dbObjectsList = null;
        try {
            String[] nsArray = dbColl.split("\\.");
            String db = nsArray[0];
            String coll = nsArray[1];;
            int len = size;
            long totalMigrated = 0;
            DBObject queryForDeletId = new BasicDBObject();
            queryForDeletId.put("coll", dbColl);
            BasicDBObject motorDeletedId = newArchivalMongoDao.getDBObjectByDbName(NStoS3util.lastUpdatedIdColl, queryForDeletId, NStoS3util.lastUpdatedIdDB);

            DBObject queryOnId = new BasicDBObject();
            //only provide startId at once we are starting the batch.
            if (motorDeletedId != null && motorDeletedId.size() > 0) {
                queryOnId.put("$gte", motorDeletedId.get("movedId"));
                totalMigrated = (long)motorDeletedId.get("totalMigrated");
            }
            DBObject queryObject = new BasicDBObject();
            DBObject orderObject = new BasicDBObject("_id", 1);
            queryOnId.put("$lt", new ObjectId(DateUtil.getStringToDate(DateUtil.getFormatedDate(days, DateUtil.DATE_FORMAT_EE_MMM_dd_hh_mm_ss_z_yyyy), DateUtil.DATE_FORMAT_EE_MMM_dd_hh_mm_ss_z_yyyy)));
            queryObject.put("_id", queryOnId);
            BasicDBObject projection = new BasicDBObject("_id", 1).append("MatrixLeadId", 1).append("CustomerId", 1);
            dbObjectsList = motorLoggerDao.getDBObjects(coll, queryObject, projection, 0, len, orderObject, db);
            logger.info("isTwCoreKycMoveS3JobRunning: {} , isTwoCoreKycS3MoveDataStop:{}", isTwCoreKycMoveS3JobRunning, isTwoCoreKycS3MoveDataStop);
            while (dbObjectsList != null && dbObjectsList.size() > 0 && !isTwoCoreKycS3MoveDataStop) {
                List<Object> listOfIds = new ArrayList<>();
                HashSet<String> customerIds = new HashSet<>();
                HashSet<String> sMatrixLeadIds = new HashSet<>();
                for (DBObject jsonDocument : dbObjectsList) {
                    listOfIds.add(jsonDocument.get("_id"));
                    Integer matrixLeadId = null;
                    Long lMatrixLeadId = null;
                    String sMatrixLeadId = null;
                    String fileName = null;
                    if(jsonDocument.get("MatrixLeadId") instanceof Integer) {
                        matrixLeadId = (Integer) jsonDocument.get("MatrixLeadId");
                        sMatrixLeadId = String.valueOf(matrixLeadId);
                    }
                    else if(jsonDocument.get("MatrixLeadId") instanceof Long) {
                        lMatrixLeadId = (Long) jsonDocument.get("MatrixLeadId");
                        sMatrixLeadId = String.valueOf(lMatrixLeadId);
                    }
                    else if(jsonDocument.get("MatrixLeadId") instanceof String) {
                        sMatrixLeadId = (String) jsonDocument.get("MatrixLeadId");
                    }

                    if (sMatrixLeadId != null && sMatrixLeadId.length() > 2 && !sMatrixLeadIds.contains(sMatrixLeadId)) {
                        sMatrixLeadIds.add(sMatrixLeadId);
                        DBObject queryOnLeadId = new BasicDBObject();
                        if (matrixLeadId != null && matrixLeadId > 0) {
                            queryOnLeadId.put("MatrixLeadId", matrixLeadId);
                        } else if (lMatrixLeadId != null && lMatrixLeadId > 0) {
                            queryOnLeadId.put("MatrixLeadId", lMatrixLeadId);
                        } else if (sMatrixLeadId != null && Long.parseLong(sMatrixLeadId) > 0) {
                            queryOnLeadId.put("MatrixLeadId", sMatrixLeadId);
                        }
                        logger.info("Fetching documents for MatrixLeadId: {}", sMatrixLeadId);
                        processDocumentsInChunks(coll, queryOnLeadId, db, twChunkSize, "lid_" + sMatrixLeadId, sMatrixLeadId, listOfIds);
                    }

                    // if MatrixLeadId is null or zero then use vehicleDetailId 
                    if(sMatrixLeadId == null || AppUtil.getLong(sMatrixLeadId) == 0) {
                        Integer customerId = null;
                        Long lCustomerId = null;
                        String sCustomerId = null;
                        if (jsonDocument.get("CustomerId") instanceof Integer) {
                            customerId = (Integer) jsonDocument.get("CustomerId");
                            sCustomerId = String.valueOf(customerId);
                        } else if (jsonDocument.get("CustomerId") instanceof Long) {
                            lCustomerId = (Long) jsonDocument.get("CustomerId");
                            sCustomerId = String.valueOf(lCustomerId);
                        } else if (jsonDocument.get("CustomerId") instanceof String) {
                            sCustomerId = (String) jsonDocument.get("CustomerId");
                        } else {
                            continue;
                        }
                        if (sCustomerId != null && sCustomerId.length() > 2 && !customerIds.contains(sCustomerId)) {
                            customerIds.add(sCustomerId);
                            DBObject queryOnVehicleDetailId = new BasicDBObject();
                            if (customerId != null && customerId > 0) {
                                queryOnVehicleDetailId.put("CustomerId", customerId);
                            } else if (lCustomerId != null && lCustomerId > 0) {
                                queryOnVehicleDetailId.put("CustomerId", lCustomerId);
                            } else if (sCustomerId != null && Long.parseLong(sCustomerId) > 0) {
                                queryOnVehicleDetailId.put("CustomerId", sCustomerId);
                            }
                            logger.info("Fetching documents for CustomerId: {}", sCustomerId);
                            processDocumentsInChunks(coll, queryOnVehicleDetailId, db, twChunkSize, "cid_" + sCustomerId, sCustomerId, listOfIds);
                        }
                        else {
                            continue;
                        }
                    }
                }

                customerIds = null;
                sMatrixLeadIds = null;

                // Delete processed documents (already deleted inside processDocumentsInChunks)
                deleteDocuments(listOfIds, coll, db);
                listOfIds = null;

                 // Update last processed ID
                updateLastProcessedId(motorDeletedId, dbObjectsList, totalMigrated);
                
                // Get next batch of documents
                queryOnId.put("$gt", dbObjectsList.get(dbObjectsList.size() - 1).get("_id"));
                queryObject.put("_id", queryOnId);
                dbObjectsList = null;
                dbObjectsList = motorLoggerDao.getDBObjects(coll, queryObject, projection, 0, len, orderObject, db);
            }
        }catch (Exception e) {
            logger.error("Exception caught into TW moveTWCoreKycDocsByVehicleId, msg: {}", e.getMessage());
        }finally {
            isTwoCoreKycS3MoveDataStop = false;
            isTwCoreKycMoveS3JobRunning = false;
        }
    }

    private void processDocumentsInChunks(String coll, DBObject query, String db, int chunkSize, 
                                            String fileNamePrefix, String id, List<Object> listOfIds) {
        int offset = 0;
        boolean hasMoreDocuments = true;
        int fileCounter = 1;
        
        while (hasMoreDocuments) {
            long startTime1 = System.currentTimeMillis();

            long startTime = System.currentTimeMillis();

            List<DBObject> dbChunk = motorLoggerDao.getDBObjects(coll, query, offset, chunkSize, null, db);
            long endTime = System.currentTimeMillis();

            long timeDiffMillis = endTime - startTime;
            logger.info("Time taken to get chunk data is: " + timeDiffMillis + " milliseconds");
            if (dbChunk == null || dbChunk.isEmpty()) {
                break;
            }
            
            if (dbChunk.size() > 0) {
                // Extract IDs for deletion
                List<Object> chunkIds = new ArrayList<>();
                for (DBObject doc : dbChunk) {
                    chunkIds.add(doc.get("_id"));
                }

                startTime = System.currentTimeMillis();
                String fileName = fileNamePrefix + "_" + System.currentTimeMillis() + ".json.gz";
                boolean success = saveChunkToS3(dbChunk, fileName, coll, db);
                endTime = System.currentTimeMillis();
                timeDiffMillis = endTime - startTime;
                logger.info("Time taken to save chunk to S3 is: " + timeDiffMillis + " milliseconds");

                if (success) {
                    // Save filename to MongoDB
                    startTime = System.currentTimeMillis();

                    saveFilenameToMongo(fileNamePrefix, fileName, id, NStoS3util.lastUpdatedIdDB);
                    endTime = System.currentTimeMillis();
                    timeDiffMillis = endTime - startTime;
                    logger.info("Time taken to save filename to MongoDB is: " + timeDiffMillis + " milliseconds");

                    // Delete the processed chunk immediately
                    startTime = System.currentTimeMillis();

                    deleteDocuments(chunkIds, coll, db);
                    endTime = System.currentTimeMillis();
                    timeDiffMillis = endTime - startTime;
                    logger.info("Time taken to delete chunk from MongoDB is: " + timeDiffMillis + " milliseconds");
                    
                    logger.info("Processed and deleted chunk {} for {}", fileCounter, fileNamePrefix);
                    // fileCounter++;
                } else {
                    logger.error("Failed to save chunk {} to S3 for {}", fileCounter, fileNamePrefix);
                }
            }
            
            if (dbChunk.size() < chunkSize) {
                hasMoreDocuments = false;
            } else {
                // offset += chunkSize;
            }
            endTime = System.currentTimeMillis();

            timeDiffMillis = endTime - startTime1;
            logger.info("Time taken to process chunk of size " + chunkSize + " is: " + timeDiffMillis + " milliseconds");
        }
    }

    private boolean saveChunkToS3(List<DBObject> dbChunk, String fileName, String coll, String db) {
        try {
            String jsonString = JSON.serialize(dbChunk);
            byte[] compressedData = compressData(jsonString);
            
            logger.info("TW CoreKyc fileName: {}", fileName);
            String folderName = NStoS3util.MAP_NS_TO_S3_PATH.get(coll);
            s3service.addByteContentWithGzip(compressedData, NStoS3util.BUCKET_NAME_LOGGER_ARCHIVAL, folderName, fileName, "application/gzip");

            jsonString = null;
            compressedData = null;

            return true;
        } catch (Exception e) {
            logger.error("Error serializing DBObject: {}", e.getMessage());
            return false;
        }
    }
    
    private void saveFilenameToMongo(String identifier, String fileName, String id, String db) {
        try {
            DBObject fileRecord = new BasicDBObject();
            fileRecord.put("identifier", identifier);
            fileRecord.put("id", id);
            fileRecord.put("fileName", fileName);
            fileRecord.put("createdAt", new Date());
            
            newArchivalMongoDao.addRow("twCoreKycS3Filenames", fileRecord, db);
        } catch (Exception e) {
            logger.error("Error saving filename to MongoDB: {}", e.getMessage());
        }
    }
    

    private void deleteDocuments(List<Object> listOfIds, String coll, String db) {
        if (listOfIds == null || listOfIds.isEmpty()) {
            return;
        }
        
        List<Object> subList;
        int startIndx = 0;
        int lastIndx = 100;
        
        if (listOfIds.size() > 100) {
            while (startIndx < listOfIds.size()) {
                if (startIndx + 100 >= listOfIds.size()) {
                    lastIndx = listOfIds.size();
                }
                subList = listOfIds.subList(startIndx, lastIndx);
                DBObject queryFormovedIds = new BasicDBObject();
                queryFormovedIds.put("_id", new BasicDBObject().append("$in", subList));
                try {
                    motorLoggerDao.deleteRow(coll, queryFormovedIds, true, db);
                    logger.info("TW CoreKyc deleted batch. Last _Id: {}", subList.get(subList.size() - 1));

                } catch (Exception e) {
                    // TODO Auto-generated catch block
                    e.printStackTrace();
                }
                startIndx = lastIndx;
                lastIndx = lastIndx + 100;
            }
        } else {
            DBObject queryFormovedIds = new BasicDBObject();
            queryFormovedIds.put("_id", new BasicDBObject().append("$in", listOfIds));
            try {
                motorLoggerDao.deleteRow(coll, queryFormovedIds, true, db);
                logger.info("TW CoreKyc deleted batch. Last _Id: {}", listOfIds.get(listOfIds.size() - 1));

            } catch (Exception e) {
                // TODO Auto-generated catch block
                e.printStackTrace();
            }
        }
    }
    
    private void updateLastProcessedId(BasicDBObject motorDeletedId, List<DBObject> dbObjectsList, long totalMigrated) {
        if (motorDeletedId != null && motorDeletedId.size() > 0 && motorDeletedId.getObjectId("_id") != null) {
            totalMigrated = totalMigrated + dbObjectsList.size();
            DBObject setquery = new BasicDBObject("movedId", dbObjectsList.get(dbObjectsList.size() - 1).get("_id"))
                    .append("totalMigrated", totalMigrated)
                    .append("updatedOn", new Date());
            DBObject queryJson = new BasicDBObject("_id", motorDeletedId.getObjectId("_id"));
            DBObject rowJson = new BasicDBObject("$set", setquery);
            try {
                newArchivalMongoDao.updateRow(NStoS3util.lastUpdatedIdColl, queryJson, rowJson, false, false, NStoS3util.lastUpdatedIdDB);
            } catch (Exception e) {
                // TODO Auto-generated catch block
                e.printStackTrace();
            }
        }
    }


    @Override
    public void moveCarIntegrationLogs(String dbColl, Integer size, int days, Integer twChunkSize) {

        logger.info("Inside TW moveCarIntegrationLogs dbColl: {}, days: {}", dbColl, days);
        List<DBObject> dbObjectsList = null;
        try {
            String[] nsArray = dbColl.split("\\.");
            String db = nsArray[0];
            String coll = nsArray[1];;
            int len = size;
            long totalMigrated = 0;
            DBObject queryForDeletId = new BasicDBObject();
            queryForDeletId.put("coll", dbColl);
            BasicDBObject motorDeletedId = newArchivalMongoDao.getDBObjectByDbName(NStoS3util.lastUpdatedIdColl, queryForDeletId, NStoS3util.lastUpdatedIdDB);

            DBObject queryOnId = new BasicDBObject();
            //only provide startId at once we are starting the batch.
            if (motorDeletedId != null && motorDeletedId.size() > 0) {
                queryOnId.put("$gte", motorDeletedId.get("movedId"));
                totalMigrated = (long)motorDeletedId.get("totalMigrated");
            }
            DBObject queryObject = new BasicDBObject();
            DBObject orderObject = new BasicDBObject("_id", 1);
            queryOnId.put("$lt", new ObjectId(DateUtil.getStringToDate(DateUtil.getFormatedDate(days, DateUtil.DATE_FORMAT_EE_MMM_dd_hh_mm_ss_z_yyyy), DateUtil.DATE_FORMAT_EE_MMM_dd_hh_mm_ss_z_yyyy)));
            queryObject.put("_id", queryOnId);
            BasicDBObject projection = new BasicDBObject("_id", 1).append("EnquiryId", 1);
            dbObjectsList = motorLoggerDao.getDBObjects(coll, queryObject, projection, 0, len, orderObject, db);
            // logger.info("isTwCoreKycMoveS3JobRunning: {} , isTwoCoreKycS3MoveDataStop:{}", isTwCoreKycMoveS3JobRunning, isTwoCoreKycS3MoveDataStop);
            while (dbObjectsList != null && dbObjectsList.size() > 0 && !isTwoCoreKycS3MoveDataStop) {
                List<Object> listOfIds = new ArrayList<>();
                HashSet<String> enquiryIds = new HashSet<>();
                for (DBObject jsonDocument : dbObjectsList) {
                    listOfIds.add(jsonDocument.get("_id"));
                    
                    Integer iEnquiryID = null;
                    Long lEnquiryID = null;
                    String sEnquiryId = null;
                    if(jsonDocument.get("EnquiryId") instanceof Integer) {
                        iEnquiryID = (Integer) jsonDocument.get("EnquiryId");
                        sEnquiryId = String.valueOf(iEnquiryID);
                    } else if(jsonDocument.get("EnquiryId") instanceof Long) {
                        lEnquiryID = (Long) jsonDocument.get("EnquiryId");
                        sEnquiryId = String.valueOf(lEnquiryID);
                    }
                    if (((iEnquiryID != null && iEnquiryID>0) || (lEnquiryID  != null && lEnquiryID>0 )) && !enquiryIds.contains(sEnquiryId)) {
                        enquiryIds.add(sEnquiryId);
                        // query to find booked EnquiryId
                        DBObject queryOnEnqId = new BasicDBObject();
                        if (iEnquiryID != null && iEnquiryID > 0) {
                            queryOnEnqId.put("EnquiryId", iEnquiryID);
                        } else if (lEnquiryID != null && lEnquiryID > 0) {
                            queryOnEnqId.put("EnquiryId", lEnquiryID);
                        }
                        // logger.info("Fetching documents for EnquiryId: {}", );
                        processDocumentsInChunks(coll, queryOnEnqId, db, twChunkSize, sEnquiryId, sEnquiryId, listOfIds);
                    }
                }

                // Delete processed documents (already deleted inside processDocumentsInChunks)
                deleteDocuments(listOfIds, coll, db);
                listOfIds = null;

                 // Update last processed ID
                updateLastProcessedId(motorDeletedId, dbObjectsList, totalMigrated);
                
                // Get next batch of documents
                queryOnId.put("$gt", dbObjectsList.get(dbObjectsList.size() - 1).get("_id"));
                queryObject.put("_id", queryOnId);
                dbObjectsList = null;
                dbObjectsList = motorLoggerDao.getDBObjects(coll, queryObject, projection, 0, len, orderObject, db);
            }
        }catch (Exception e) {
            logger.error("Exception caught into TW moveTWCoreKycDocsByVehicleId, msg: {}", e.getMessage());
        }finally {
            isTwoCoreKycS3MoveDataStop = false;
            isTwCoreKycMoveS3JobRunning = false;
        }
    }


    @Override
    public void moveMotorKyc(String dbColl, Integer size, int days, int chunkSize) throws Exception {

        logger.info("Inside moveMotorKyc , ns: {}, size: {}, timeCheck: {}, days", dbColl, size, days);
        List<DBObject> dbObjectsList = null;
        try {
            String[] nsArray = dbColl.split("\\.");
            String db = nsArray[0];
            String coll = nsArray[1];
            int len = size;
            long totalMigrated = 0;
            DBObject queryForDeletId = new BasicDBObject();
            queryForDeletId.put("coll", dbColl);
            BasicDBObject motorDeletedId = newArchivalMongoDao.getDBObjectByDbName(NStoS3util.lastUpdatedIdColl, queryForDeletId, NStoS3util.lastUpdatedIdDB);
            DBObject queryOnId = new BasicDBObject();
            //only provide startId at once we are starting the batch.
            if (motorDeletedId != null && motorDeletedId.size() > 0) {
                queryOnId.put("$gte", motorDeletedId.get("movedId"));
                totalMigrated = (long)motorDeletedId.get("totalMigrated");
            }
            queryOnId.put("$lt", new ObjectId(DateUtil.getStringToDate(DateUtil.getFormatedDate(days, DateUtil.DATE_FORMAT_EE_MMM_dd_hh_mm_ss_z_yyyy), DateUtil.DATE_FORMAT_EE_MMM_dd_hh_mm_ss_z_yyyy)));
            DBObject queryObject = new BasicDBObject();
            queryObject.put("_id", queryOnId);
            BasicDBObject projection = new BasicDBObject("_id", 1).append("EnquiryId", 1);
            DBObject orderObject = new BasicDBObject("_id", 1);
            dbObjectsList = motorLoggerDao.getDBObjects(coll, queryObject, projection, 0, len, orderObject, db);
            logger.info("isKYCCarMoveS3JobStop : {} , isKYCCarMoveS3JobRunning: {} ", isKYCCarMoveS3JobStop, isKYCCarMoveS3JobRunning);
            while (dbObjectsList != null && dbObjectsList.size() > 0 && !isKYCCarMoveS3JobStop) {
                logger.info("Car New KYC migration running");
                List<Object> listOfIds = new ArrayList<>();
                HashSet<String> enquiryIds = new HashSet<>();
                for (DBObject jsonDocument : dbObjectsList) {
                    listOfIds.add(jsonDocument.get("_id"));

                    Integer iEnquiryID = null;
                    Long lEnquiryID = null;
                    String sEnquiryId = null;
                    if(jsonDocument.get("EnquiryId") instanceof Integer) {
                        iEnquiryID = (Integer) jsonDocument.get("EnquiryId");
                        sEnquiryId = String.valueOf(iEnquiryID);
                    } else if(jsonDocument.get("EnquiryId") instanceof Long) {
                        lEnquiryID = (Long) jsonDocument.get("EnquiryId");
                        sEnquiryId = String.valueOf(lEnquiryID);
                    }
                    if (((iEnquiryID != null && iEnquiryID>0) || (lEnquiryID  != null && lEnquiryID>0 )) && !enquiryIds.contains(sEnquiryId)) {
                        enquiryIds.add(sEnquiryId);
                        // query to find booked EnquiryId
                        DBObject queryOnEnqId = new BasicDBObject();
                        if (iEnquiryID != null && iEnquiryID > 0) {
                            queryOnEnqId.put("EnquiryId", iEnquiryID);
                        } else if (lEnquiryID != null && lEnquiryID > 0) {
                            queryOnEnqId.put("EnquiryId", lEnquiryID);
                        }
                        int offset = 0;
                        List<DBObject> dbList = motorLoggerDao.getDBObjects(coll, queryOnEnqId, offset, chunkSize, null, db);
                        if (dbList.size() == chunkSize) {
                            offset = offset + chunkSize;
                            dbList = getList(chunkSize, coll, queryOnEnqId, offset, db, dbList);
                        }
                        String jsonString;
                        if (dbList != null && dbList.size()>0) {
                            try {
                                jsonString = JSON.serialize(dbList);
                                // Compress JSON data
                                byte[] compressedData = compressData(jsonString);
                                String fileName = sEnquiryId + "." + "json.gz";
                                logger.info("KYC Car New fileName : " + fileName);
                                String folderName = NStoS3util.MAP_NS_TO_S3_PATH.get(dbColl);
                                String url = s3service.addByteContentWithGzip(compressedData, NStoS3util.BUCKET_NAME_LOGGER_ARCHIVAL, folderName, fileName, "application/gzip");
                                if (url.equals(AppConstants.FILE_ALREADY_EXIST)) {
                                    try {
                                        newArchMongoDao.addRows(coll, dbList, db);
                                    } catch (Exception e) {
                                        logger.error("Error while adding in motor moveMotorKyc , {}", e.getMessage());
                                    }
                                }
                                for (DBObject reqIdDoc : dbList) {
                                    listOfIds.add(reqIdDoc.get("_id"));
                                }
                            } catch (Exception e) {
                                logger.error("Error serializing DBObject: " + e.getMessage());
                                throw e;
                            }
                        }
                    }
                }
                enquiryIds = null;
                List<Object> subList;
                int startIndx = 0;
                int lastIndx = 100;
                if (listOfIds.size() > 100) {
                    while (startIndx < listOfIds.size()) {
                        if (startIndx + 100 >= listOfIds.size()) {
                            lastIndx = listOfIds.size();
                        }
                        subList = listOfIds.subList(startIndx, lastIndx);
                        DBObject queryFormovedIds = new BasicDBObject();
                        queryFormovedIds.put("_id", new BasicDBObject().append("$in", subList));
                        motorLoggerDao.deleteRow(coll, queryFormovedIds, true, db);
                        logger.info("deleted KYC Car Last _Id : {}", subList.get(subList.size() - 1));
                        startIndx = lastIndx;
                        lastIndx = lastIndx + 100;
                        subList = null;
                    }
                } else if (listOfIds != null && listOfIds.size() > 0) {
                    DBObject queryFormovedIds = new BasicDBObject();
                    queryFormovedIds.put("_id", new BasicDBObject().append("$in", listOfIds));
                    motorLoggerDao.deleteRow(coll, queryFormovedIds, true, db);
                    logger.info("deleted KYC Car Last _Id : {}", listOfIds.get(listOfIds.size() - 1));
                }
                listOfIds = null;

                DBObject queryJson;
                if (motorDeletedId != null && motorDeletedId.size() > 0 && motorDeletedId.getObjectId("_id") != null) {
                    totalMigrated = totalMigrated + dbObjectsList.size();
                    DBObject setquery = new BasicDBObject("movedId", dbObjectsList.get(dbObjectsList.size() - 1).get("_id"))
                            .append("totalMigrated", totalMigrated)
                            .append("updatedOn", new Date());
                    queryJson = new BasicDBObject("_id", motorDeletedId.getObjectId("_id"));
                    DBObject rowJson = new BasicDBObject("$set", setquery);
                    newArchivalMongoDao.updateRow(NStoS3util.lastUpdatedIdColl, queryJson, rowJson, false, false, NStoS3util.lastUpdatedIdDB);
                }
                queryOnId.put("$gt", dbObjectsList.get(dbObjectsList.size() - 1).get("_id"));
                queryObject.put("_id", queryOnId);
                dbObjectsList = null;
                dbObjectsList = motorLoggerDao.getDBObjects(coll, queryObject, projection, 0  , len, orderObject, db);
            }
        }catch (Exception e) {
            logger.error("Exception caught into moveMotorKyc, msg: {}", e.getMessage());
            throw new Exception("Exception cuaght while processing, Car , msg: " + e.getMessage());
        } finally {
            isKYCCarMoveS3JobRunning = false;
            isKYCCarMoveS3JobStop = false;
        }
    }

    private void moveCarEnquiryIdLeadID(int days, int startDay) throws Exception {

        try{
            List<DBObject> bookedEnqList = new ArrayList<>();
            while (startDay >= days) {
                String url = "https://bmscromaapi.policybazaar.com/api/CromaServices/GetBookingInfoByProductId";
                Map<String, String> header = new HashMap<>(2);
                header.put("clientKey", "GUYYUwUg3");
                header.put("authKey", "UIY85UGu8HVi97vKIi");
                header.put("source", "hrms");
                Map<String, Object> requestMap = new HashMap<>(2);
                String offerCreatedON = DateUtil.parseDate(DateUtil.addDays(new Date(), -startDay), DateUtil.DATE_FORMAT_YYYY_MM_DD_HH_MM_SS);
                startDay --;
                DateTimeFormatter formatter1 = DateTimeFormatter.ofPattern(DateUtil.DATE_FORMAT_YYYY_MM_DD);
                String dateOnlyString = offerCreatedON.substring(0, 10);
                // Parse the input date string into a LocalDateTime object
                LocalDate inputDate = LocalDate.parse(dateOnlyString, formatter1);
                // Get the current date
                LocalDate currentDate = LocalDate.now();
                if (inputDate.isAfter(currentDate)) {
                   logger.info( "greater than today's date.");
                   break;
                }
                requestMap.put("OfferCreatedON", offerCreatedON);
                requestMap.put("ProductID", 117);
                Map<String, Object> response = HttpUtil.post(url, new Gson().toJson(requestMap), header);
                int statusCode = (int) response.get("status");
                if (statusCode == HttpStatus.OK.value()) {
                    String responseBody = (String) response.get("responseBody");
                    Map<String, Object> responseFromBody = new ObjectMapper().readValue(responseBody, Map.class);
                    if (responseFromBody != null && !responseFromBody.isEmpty() && responseFromBody.get("BookingInfoList") != null) {
                        List<Map<String, Object>>list = (List<Map<String, Object>>) responseFromBody.get("BookingInfoList");
                        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
                        if(list.size() > 0) {
                            logger.info("Enquiry for car job running");
                            for(Map<String, Object> entry: list) {
                                Integer leadId = (Integer) entry.get("BookingId");
                                String bookingDate = (String) entry.get("BookingDate");
                                String enquiryId = null;
                                if(entry.get("EnquiryId") != null && (Integer) entry.get("EnquiryId") > 0){
                                    enquiryId = String.valueOf(entry.get("EnquiryId"));
                                }
                                if (enquiryId != null) {
                                    BasicDBObject queryObject = new BasicDBObject("EnquiryID", enquiryId);
                                    List<DBObject>existDBEnqList = motorLoggerDao.getDBObjects("BookedEnquiry117Temp1", queryObject, null, 0  , 1, null, "CarLoggerDB");
                                    if(existDBEnqList == null || existDBEnqList.size() == 0){
                                        logger.info("Enquiry not exist , enquiryId{}", enquiryId);
                                        BasicDBObject dbObject = null;
                                        try {
                                            dbObject = new BasicDBObject("LeadId", leadId).append("EnquiryID", enquiryId).append("OfferCreatedON", formatter.parse(bookingDate));
                                        }catch (Exception e){
                                            logger.info("format Exception , leadId:{}, enquiryId{}, bookingdate: {}", leadId, enquiryId, bookingDate);
                                        }
                                        bookedEnqList.add(dbObject);
                                    }else{
                                        logger.info("Enquiry exist , enquiryId{}", enquiryId);
                                    }
                                }
                                if(bookedEnqList!= null && bookedEnqList.size() > 500){
                                    try {
                                        motorLoggerDao.addRows("BookedEnquiry117Temp1", bookedEnqList, "CarLoggerDB");
                                    } catch (Exception e) {
                                        logger.error("Error while adding in motor moveMotorKyc , {}", e.getMessage());
                                    }
                                    bookedEnqList = new ArrayList<>();
                                }
                            }
                            if(bookedEnqList!= null && bookedEnqList.size() > 0){
                                try {
                                    motorLoggerDao.addRows("BookedEnquiry117Temp1", bookedEnqList, "CarLoggerDB");
                                } catch (Exception e) {
                                    logger.error("Error while adding in motor moveMotorKyc , {}", e.getMessage());
                                }
                                bookedEnqList = new ArrayList<>();
                            }
                        }
                    }
                }
            }
        }catch (Exception e){
            logger.error("Exception while moveCarEnquiryIdLeadID, msg: {}", e.getMessage());
            throw e;
        }
    }

    /**
     * This method is used to get list of Document in chunk provided.
     *
     * @param chunkSize
     * @param coll
     * @param queryOnrequestId
     * @param offset
     * @param db
     * @param resultList
     * @return
     */
    private List<DBObject> getList(int chunkSize , String coll, DBObject queryOnrequestId, int offset, String db, List<DBObject> resultList) {

        try {

            logger.info("queryOnrequestId: {}", queryOnrequestId);
            List<DBObject> dataList = motorLoggerDao.getDBObjects(coll, queryOnrequestId, offset, chunkSize, null, db);
            while (dataList.size() > 0){
                resultList.addAll(dataList);
                offset += chunkSize;
                dataList = motorLoggerDao.getDBObjects(coll, queryOnrequestId, offset, chunkSize, null, db);
            }
        } catch (Exception e) {
            logger.error("Exception while getList, msg: {}", e.getMessage());
        }
        return resultList;
    }
    private List<DBObject> getOldList(int chunkSize , String coll, DBObject queryOnrequestId, int offset, String db, List<DBObject> resultList) {

        try {
            List<DBObject> dataList = newArchivalMongoDao.getDBObjects(coll, queryOnrequestId, offset, chunkSize, null, db);
            while (dataList.size() > 0){
                resultList.addAll(dataList);
                offset += chunkSize;
                dataList = newArchivalMongoDao.getDBObjects(coll, queryOnrequestId, offset, chunkSize, null, db);
            }
        } catch (Exception e) {
            logger.error("Exception while getList, msg: {}", e.getMessage());
        }
        return resultList;
    }
    private List<DBObject> getTempOldList(int chunkSize , String coll, DBObject queryOnrequestId, int offset, String db, List<DBObject> resultList) {

        try {
            List<DBObject> dataList = newTempMongoDao.getDBObjects(coll, queryOnrequestId, offset, chunkSize, null, db);
            while (dataList.size() > 0){
                resultList.addAll(dataList);
                offset += chunkSize;
                dataList = newTempMongoDao.getDBObjects(coll, queryOnrequestId, offset, chunkSize, null, db);
            }
        } catch (Exception e) {
            logger.error("Exception while getList, msg: {}", e.getMessage());
        }
        return resultList;
    }

    @Override
    public void moveCarCsvToBookedEnquiry() {
        String[] fileList = {"17571_car.csv"};
        //"jan2020_to_dec2020.csv", "jan2021_to_dec2021.csv", "jan2022_to_dec2022.csv",  "jan2023 _to_dec2023.csv", "jan2024_to_dec2024.csv"};
        try {
            for (String file : fileList) {
                String csvFile =  "/Users/<USER>/Downloads/" + file;
                String line = "";
                String csvSplitBy = ",";
                List<DBObject> dbList = new ArrayList<>();
                try (BufferedReader br = new BufferedReader(new FileReader(csvFile))) {
                    // leadID, customerid, productid, OfferCreatedON, EnquiryId
                    while ((line = br.readLine()) != null) {
                        // Use comma as separator
                        String[] values = line.split(csvSplitBy);
                        Integer leadId = Integer.parseInt(values[0].replaceAll("\uFEFF", "").trim());
                        String offerCreatedON = values[3].trim();
                        String enquiryId = values[4].trim();
                        DBObject queryOnsEnqId = new BasicDBObject();
                        queryOnsEnqId.put("EnquiryID", enquiryId);
                        DBObject queryToReturnField = new BasicDBObject("_id", 0).append("EnquiryID", 1);
                        List<DBObject> bookedObj = motorLoggerDao.getDBObjects("BookedEnquiry117Temp1", queryOnsEnqId, queryToReturnField, 0, 1, null, "CarLoggerDB");
                        if (bookedObj == null || bookedObj.isEmpty()) {
                            logger.info("Enquiry not exist , enquiryId: {}", enquiryId);
                            BasicDBObject obj = new BasicDBObject().append("LeadId", leadId).append("EnquiryID", enquiryId).append("OfferCreatedON", DateUtil.getStringToDate(offerCreatedON, DateUtil.DATE_FORMAT_YYYY_MM_DD_HH_MM_SS));
                            dbList.add(obj);
                            if (dbList.size() >= 1000) {
                                try{
                                    motorLoggerDao.addRows("BookedEnquiry117Temp1", dbList, "CarLoggerDB");
                                    dbList = new ArrayList<>();
                                }catch (Exception e){
                                    logger.info("Exception caught in moveCarCsvToBookedEnquiry, msg: {}",  e.getMessage());
                                }

                            }
                        }else{
                            logger.info("Enquiry exist , enquiryId: {}", enquiryId);
                        }
                    }
                    if (dbList != null && dbList.size() > 0) {
                        try{
                            motorLoggerDao.addRows("BookedEnquiry117Temp1", dbList, "CarLoggerDB");
                        }catch (Exception e){
                            logger.info("Exception caught in moveCarCsvToBookedEnquiry, msg: {}",  e.getMessage());
                        }
                    }
                    dbList = null;

                } catch (Exception e) {
                    logger.info("Exception caught in moveCarCsvToBookedEnquiry, msg: {}", e.getMessage());
                }
                logger.info("file done, msg: {}", file);
            }
        }catch  (Exception e) {
            throw new RuntimeException(e);
        }
    }
    @Override
    public void moveTWCsvToBookedEnquiry() {
        //14020692 before move
        String[] fileList = {"tw_may_june_2024.csv"};
        try {
            for (String file : fileList) {
                String csvFile = "/Users/<USER>/Downloads/" + file;
                String line = "";
                String csvSplitBy = ",";
                List<DBObject> dbList = new ArrayList<>();
                try (BufferedReader br = new BufferedReader(new FileReader(csvFile))) {
                    // leadID, customerid, productid, OfferCreatedON, EnquiryId
                    while ((line = br.readLine()) != null) {
                        // Use comma as separator
                        String[] values = line.split(csvSplitBy);
                        String vehicleDetailId = values[0].replaceAll("\uFEFF", "").trim();
                        Integer enquiryId = Integer.parseInt(values[2].trim());
                        DBObject queryOnsEnqId = new BasicDBObject();
                        queryOnsEnqId.put("VehicleDetailId", vehicleDetailId);
                        DBObject queryToReturnField = new BasicDBObject("_id", 0).append("VehicleDetailId", 1);
                        List<DBObject> bookedObj = motorLoggerDao.getDBObjects("BookedEnquiryVehicleId114Temp", queryOnsEnqId, queryToReturnField, 0, 1, null, "TwoWheelerDB");
                        if (bookedObj == null || bookedObj.isEmpty()) {
                            logger.info("Enquiry not exist , enquiryId: {}", enquiryId);
                            BasicDBObject obj = new BasicDBObject().append("VehicleDetailId", vehicleDetailId).append("EnquiryID", enquiryId);
                            dbList.add(obj);
                            if (dbList.size() >= 1000) {
                                try{
                                    motorLoggerDao.addRows("BookedEnquiryVehicleId114Temp", dbList, "TwoWheelerDB");
                                    dbList = new ArrayList<>();
                                }catch (Exception e){
                                    logger.info("Exception caught in moveCarCsmoveTWCsvToBookedEnquiryvToBookedEnquiry, msg: {}",  e.getMessage());
                                }

                            }
                        }else{
                            logger.info("Enquiry exist , enquiryId: {}", enquiryId);
                        }
                    }
                    if (dbList != null && dbList.size() > 0) {
                        try{
                            motorLoggerDao.addRows("BookedEnquiryVehicleId114Temp", dbList, "TwoWheelerDB");
                        }catch (Exception e){
                            logger.info("Exception caught in moveTWCsvToBookedEnquiry, msg: {}",  e.getMessage());
                        }
                    }
                    dbList = null;

                } catch (Exception e) {
                    logger.info("Exception caught in moveTWCsvToBookedEnquiry, msg: {}", e.getMessage());
                }
                logger.info("file done, msg: {}", file);
            }
        }catch  (Exception e) {
            throw new RuntimeException(e);
        }
    }

    public void moveOldMotorBookLeadsV2(String dbColl, Integer size, int days, int chunkSize) throws Exception {

        logger.info("Inside moveOldMotorBookLeadsV2 , ns: {}, size: {}, timeCheck: {}, days", dbColl, size, days);
        List<DBObject> dbObjectsList = null;
        try {
            String[] nsArray = dbColl.split("\\.");
            String db = nsArray[0];
            String coll = nsArray[1];
            int len = size;
            long totalMigrated = 0;
            DBObject queryForDeletId = new BasicDBObject();
            queryForDeletId.put("coll", "CarLoggerDB.ReqResLog_109");
            BasicDBObject motorDeletedId = newArchivalMongoDao.getDBObjectByDbName(NStoS3util.lastUpdatedIdColl, queryForDeletId, NStoS3util.lastUpdatedIdDB);
            DBObject queryOnId = new BasicDBObject();
            //only provide startId at once we are starting the batch.
            if (motorDeletedId != null && motorDeletedId.size() > 0) {
                queryOnId.put("$gt", motorDeletedId.get("movedEnqId"));
                totalMigrated = (long)motorDeletedId.get("totalMigrated");
            }
//            queryOnId.put("$lt", new ObjectId(DateUtil.getStringToDate(DateUtil.getFormatedDate(days, DateUtil.DATE_FORMAT_EE_MMM_dd_hh_mm_ss_z_yyyy), DateUtil.DATE_FORMAT_EE_MMM_dd_hh_mm_ss_z_yyyy)));
            DBObject queryObject = new BasicDBObject();
            queryObject.put("EnquiryId", queryOnId);
            BasicDBObject projection = new BasicDBObject("_id", 1).append("EnquiryId", 1);
            DBObject orderObject = new BasicDBObject("EnquiryId", 1);
            dbObjectsList = newTempMongoDao.getDBObjects("BookedEnquiry117Temp1", queryObject, projection, 0, len, orderObject, db);
            removeDuplicateEnquiryId(dbObjectsList);
            logger.info("isOldCarMoveS3JobStop : {} , isOldCarMoveS3JobRunning: {} ", isOldCarMoveS3JobStop, isOldCarMoveS3JobRunning);
            while (dbObjectsList != null && dbObjectsList.size() > 0 && !isOldCarMoveS3JobStop) {
                logger.info("Car Old migration running");
//                List<Object> listOfIds = new ArrayList<>();
                HashSet<String> enquiryIds = new HashSet<>();
                for (DBObject jsonDocument : dbObjectsList) {
//                    listOfIds.add(jsonDocument.get("_id"));

                    Integer iEnquiryID = null;
                    Long lEnquiryID = null;
                    String sEnquirtyId = null;
                    if (jsonDocument.get("EnquiryId") instanceof Integer) {
                        iEnquiryID = (Integer) jsonDocument.get("EnquiryId");
                        sEnquirtyId = String.valueOf(iEnquiryID);
                    }
                    if (sEnquirtyId.equals("9999") || sEnquirtyId.equals("999999")) {
                        continue;
                    }
                    if (iEnquiryID != null && iEnquiryID > 0 && !enquiryIds.contains(sEnquirtyId)) {
                        enquiryIds.add(sEnquirtyId);
                        // query to find booked EnquiryId
                        DBObject queryOnEnqId = new BasicDBObject();
                        queryOnEnqId.put("EnquiryID", iEnquiryID);
                        int offset = 0;
                        List<DBObject> dbList = newTempMongoDao.getDBObjects("ReqResLog", queryOnEnqId, offset, chunkSize, null, db);
                        if (dbList.size() > 0) {
                            if (dbList.size() == chunkSize) {
                                offset = offset + chunkSize;
                                dbList = getTempOldList(chunkSize, "ReqResLog", queryOnEnqId, offset, db, dbList);
                            }
                        } else {
                            lEnquiryID = Long.valueOf(iEnquiryID);
                            queryOnEnqId.put("EnquiryID", lEnquiryID);
                            dbList = newTempMongoDao.getDBObjects("ReqResLog", queryOnEnqId, offset, chunkSize, null, db);
                            if (dbList.size() > 0) {
                                if (dbList.size() == chunkSize) {
                                    offset = offset + chunkSize;
                                    dbList = getTempOldList(chunkSize, "ReqResLog", queryOnEnqId, offset, db, dbList);
                                }
                            } else {
                                queryOnEnqId.put("EnquiryID", sEnquirtyId);
                                dbList = newTempMongoDao.getDBObjects("ReqResLog", queryOnEnqId, offset, chunkSize, null, db);
                                if (dbList.size() > 0) {
                                    if (dbList.size() == chunkSize) {
                                        offset = offset + chunkSize;
                                        dbList = getTempOldList(chunkSize, "ReqResLog", queryOnEnqId, offset, db, dbList);
                                    }
                                }
                            }
                        }

                        String jsonString;
                        if (dbList != null && dbList.size() > 0) {
                            try {
                                jsonString = JSON.serialize(dbList);
                                // Compress JSON data
                                byte[] compressedData = compressData(jsonString);
                                String fileName = sEnquirtyId + "." + "json.gz";
                                logger.info("Car Old fileName : " + fileName);
                                String folderName = NStoS3util.S3_FOLDER_PATH_CAR;
                                String url = s3service.addByteContentWithGzip(compressedData, NStoS3util.BUCKET_NAME_LOGGER_ARCHIVAL, folderName, fileName, "application/gzip");

                                if (url.equals(AppConstants.FILE_ALREADY_EXIST)) {
                                    try {
//                                        newTempMongoDao.addRows("ReqResLog_Failover", dbList, db);
                                    } catch (Exception e) {
                                        logger.error("Error while adding in motor moveBookLeads , {}", e.getMessage());
                                    }
                                }
                                DBObject queryJson = new BasicDBObject("_id", jsonDocument.get("_id"));
                                DBObject setquery = new BasicDBObject("isMigrated", true);
                                DBObject rowJson = new BasicDBObject("$set", setquery);
                                newTempMongoDao.updateRow("BookedEnquiry117Temp1", queryJson, rowJson, false, false, db);

                            } catch (Exception e) {
                                logger.error("Error serializing DBObject: " + e.getMessage());
                                throw e;
                            }
                        } else {
                            DBObject queryJson = new BasicDBObject("_id", jsonDocument.get("_id"));
                            DBObject setquery = new BasicDBObject("isMigrated", false);
                            DBObject rowJson = new BasicDBObject("$set", setquery);
                            newTempMongoDao.updateRow("BookedEnquiry117Temp1", queryJson, rowJson, false, false, db);
                        }
                    }
                }
                enquiryIds = null;
                DBObject queryJson;
                if (motorDeletedId != null && motorDeletedId.size() > 0 && motorDeletedId.getObjectId("_id") != null) {
                    totalMigrated = totalMigrated + dbObjectsList.size();
                    DBObject setquery = new BasicDBObject("movedEnqId", dbObjectsList.get(dbObjectsList.size() - 1).get("EnquiryId"))
                            .append("totalMigrated", totalMigrated)
                            .append("updatedOn", new Date());
                    queryJson = new BasicDBObject("_id", motorDeletedId.getObjectId("_id"));
                    DBObject rowJson = new BasicDBObject("$set", setquery);
                    newArchivalMongoDao.updateRow(NStoS3util.lastUpdatedIdColl, queryJson, rowJson, false, false, NStoS3util.lastUpdatedIdDB);
                }
                queryOnId.put("$gt", dbObjectsList.get(dbObjectsList.size() - 1).get("EnquiryId"));
                queryObject.put("EnquiryId", queryOnId);
                dbObjectsList = newTempMongoDao.getDBObjects("BookedEnquiry117Temp1", queryObject, projection, 0, len, orderObject, db);
            }
        }catch (Exception e) {
            logger.error("Exception caught into moveMotorBookLeads, msg: {}", e.getMessage());
            throw new Exception("Exception cuaght while processing, Car , msg: " + e.getMessage());
        } finally {
            isOldCarMoveS3JobRunning = false;
            isOldCarMoveS3JobStop = false;
        }
    }

    public void moveOldMotorBookLeadsV3(String dbColl, Integer size, int days, int chunkSize) throws Exception {

        logger.info("Inside moveOldMotorBookLeadsV2 , ns: {}, size: {}, timeCheck: {}, days", dbColl, size, days);
        List<DBObject> dbObjectsList = null;
        try {
            String[] nsArray = dbColl.split("\\.");
            String db = nsArray[0];
            String coll = nsArray[1];
            int len = size;
            long totalMigrated = 0;
            DBObject queryForDeletId = new BasicDBObject();
            queryForDeletId.put("coll", "CarLoggerDB.ReqResLog_109_1");
            BasicDBObject motorDeletedId = newArchivalMongoDao.getDBObjectByDbName(NStoS3util.lastUpdatedIdColl, queryForDeletId, NStoS3util.lastUpdatedIdDB);
            DBObject queryOnId = new BasicDBObject();
            //only provide startId at once we are starting the batch.
            if (motorDeletedId != null && motorDeletedId.size() > 0) {
                queryOnId.put("$gt", motorDeletedId.get("movedEnqId"));
                totalMigrated = (long)motorDeletedId.get("totalMigrated");
            }
            queryOnId.put("$lt", 373263334);//new ObjectId(DateUtil.getStringToDate(DateUtil.getFormatedDate(days, DateUtil.DATE_FORMAT_EE_MMM_dd_hh_mm_ss_z_yyyy), DateUtil.DATE_FORMAT_EE_MMM_dd_hh_mm_ss_z_yyyy)));
            DBObject queryObject = new BasicDBObject();
            queryObject.put("EnquiryId", queryOnId);
            BasicDBObject projection = new BasicDBObject("_id", 1).append("EnquiryId", 1).append("isMigrated",1);
            DBObject orderObject = new BasicDBObject("EnquiryId", 1);
            dbObjectsList = newTempMongoDao.getDBObjects("BookedEnquiry117Temp1", queryObject, projection, 0, len, orderObject, db);
            removeDuplicateEnquiryId(dbObjectsList);
            logger.info("isOldCarMoveS3JobStop : {} , isOldCarMoveS3JobRunning: {} ", isOldCarMoveS3JobStop, isOldCarMoveS3JobRunning);
            while (dbObjectsList != null && dbObjectsList.size() > 0 && !isOldCarMoveS3JobStop) {
                logger.info("Car Old migration running");
//                List<Object> listOfIds = new ArrayList<>();
                HashSet<String> enquiryIds = new HashSet<>();
                for (DBObject jsonDocument : dbObjectsList) {
//                    listOfIds.add(jsonDocument.get("_id"));

                    Integer iEnquiryID = null;
                    Long lEnquiryID = null;
                    String sEnquirtyId = null;
                    Boolean isMigrated = (Boolean) jsonDocument.get("isMigrated");
                    if(isMigrated == null || !isMigrated){
                        if (jsonDocument.get("EnquiryId") instanceof Integer) {
                            iEnquiryID = (Integer) jsonDocument.get("EnquiryId");
                            sEnquirtyId = String.valueOf(iEnquiryID);
                        }
                        if (sEnquirtyId.equals("9999") || sEnquirtyId.equals("999999")) {
                            continue;
                        }
                        if (iEnquiryID != null && iEnquiryID > 0 && !enquiryIds.contains(sEnquirtyId)) {
                            enquiryIds.add(sEnquirtyId);
                            // query to find booked EnquiryId
                            DBObject queryOnEnqId = new BasicDBObject();
                            queryOnEnqId.put("EnquiryID", iEnquiryID);
                            int offset = 0;
                            List<DBObject> dbList = newTempMongoDao.getDBObjects("ReqResLog", queryOnEnqId, offset, chunkSize, null, db);
                            if (dbList.size() > 0) {
                                if (dbList.size() == chunkSize) {
                                    offset = offset + chunkSize;
                                    dbList = getTempOldList(chunkSize, "ReqResLog", queryOnEnqId, offset, db, dbList);
                                }
                            } else {
                                lEnquiryID = Long.valueOf(iEnquiryID);
                                queryOnEnqId.put("EnquiryID", lEnquiryID);
                                dbList = newTempMongoDao.getDBObjects("ReqResLog", queryOnEnqId, offset, chunkSize, null, db);
                                if (dbList.size() > 0) {
                                    if (dbList.size() == chunkSize) {
                                        offset = offset + chunkSize;
                                        dbList = getTempOldList(chunkSize, "ReqResLog", queryOnEnqId, offset, db, dbList);
                                    }
                                } else {
                                    queryOnEnqId.put("EnquiryID", sEnquirtyId);
                                    dbList = newTempMongoDao.getDBObjects("ReqResLog", queryOnEnqId, offset, chunkSize, null, db);
                                    if (dbList.size() > 0) {
                                        if (dbList.size() == chunkSize) {
                                            offset = offset + chunkSize;
                                            dbList = getTempOldList(chunkSize, "ReqResLog", queryOnEnqId, offset, db, dbList);
                                        }
                                    }
                                }
                            }
                            System.out.println("V3 dbList size is  " + dbList.size());
                            String jsonString;
                            if (dbList != null && dbList.size() > 0) {
                                try {
                                    jsonString = JSON.serialize(dbList);
                                    // Compress JSON data
                                    byte[] compressedData = compressData(jsonString);
                                    String fileName = sEnquirtyId + "." + "json.gz";
                                    logger.info("Car Old fileName : " + fileName);
                                    String folderName = NStoS3util.S3_FOLDER_PATH_CAR;
                                    String url = s3service.addByteContentWithGzip(compressedData, NStoS3util.BUCKET_NAME_LOGGER_ARCHIVAL, folderName, fileName, "application/gzip");

                                    if (url.equals(AppConstants.FILE_ALREADY_EXIST)) {
                                        try {
//                                        newTempMongoDao.addRows("ReqResLog_Failover", dbList, db);
                                        } catch (Exception e) {
                                            logger.error("Error while adding in motor moveBookLeads , {}", e.getMessage());
                                        }
                                    }
                                    DBObject queryJson = new BasicDBObject("EnquiryId", jsonDocument.get("EnquiryId"));
                                    DBObject setquery = new BasicDBObject("isMigrated", true);
                                    DBObject rowJson = new BasicDBObject("$set", setquery);
                                    newTempMongoDao.updateRow("BookedEnquiry117Temp1", queryJson, rowJson, false, false, db);

                                } catch (Exception e) {
                                    logger.error("Error serializing DBObject: " + e.getMessage());
                                    throw e;
                                }
                            }
                        }
                    }
                }
                enquiryIds = null;
                DBObject queryJson;
                if (motorDeletedId != null && motorDeletedId.size() > 0 && motorDeletedId.getObjectId("_id") != null) {
                    totalMigrated = totalMigrated + dbObjectsList.size();
                    DBObject setquery = new BasicDBObject("movedEnqId", dbObjectsList.get(dbObjectsList.size() - 1).get("EnquiryId"))
                            .append("totalMigrated", totalMigrated)
                            .append("updatedOn", new Date());
                    queryJson = new BasicDBObject("_id", motorDeletedId.getObjectId("_id"));
                    DBObject rowJson = new BasicDBObject("$set", setquery);
                    newArchivalMongoDao.updateRow(NStoS3util.lastUpdatedIdColl, queryJson, rowJson, false, false, NStoS3util.lastUpdatedIdDB);
                }
                queryOnId.put("$gt", dbObjectsList.get(dbObjectsList.size() - 1).get("EnquiryId"));
                queryObject.put("EnquiryId", queryOnId);
                dbObjectsList = newTempMongoDao.getDBObjects("BookedEnquiry117Temp1", queryObject, projection, 0, len, orderObject, db);
            }
        }catch (Exception e) {
            logger.error("Exception caught into moveMotorBookLeads, msg: {}", e.getMessage());
            throw new Exception("Exception cuaght while processing, Car , msg: " + e.getMessage());
        } finally {
            isOldCarMoveS3JobRunning = false;
            isOldCarMoveS3JobStop = false;
        }
    }
    public void moveOldMotorBookLeadsV4(String dbColl, Integer size, int days, int chunkSize) throws Exception {

        logger.info("Inside moveOldMotorBookLeadsV2 , ns: {}, size: {}, timeCheck: {}, days", dbColl, size, days);
        List<DBObject> dbObjectsList = null;
        try {
            String[] nsArray = dbColl.split("\\.");
            String db = nsArray[0];
            String coll = nsArray[1];
            int len = size;
            long totalMigrated = 0;
            DBObject queryForDeletId = new BasicDBObject();
            queryForDeletId.put("coll", "CarLoggerDB.ReqResLog_109_2");
            BasicDBObject motorDeletedId = newArchivalMongoDao.getDBObjectByDbName(NStoS3util.lastUpdatedIdColl, queryForDeletId, NStoS3util.lastUpdatedIdDB);
            DBObject queryOnId = new BasicDBObject();
            //only provide startId at once we are starting the batch.
            if (motorDeletedId != null && motorDeletedId.size() > 0) {
                queryOnId.put("$gt", motorDeletedId.get("movedEnqId"));
                totalMigrated = (long)motorDeletedId.get("totalMigrated");
            }
            queryOnId.put("$lt", 381316144);//new ObjectId(DateUtil.getStringToDate(DateUtil.getFormatedDate(days, DateUtil.DATE_FORMAT_EE_MMM_dd_hh_mm_ss_z_yyyy), DateUtil.DATE_FORMAT_EE_MMM_dd_hh_mm_ss_z_yyyy)));
            DBObject queryObject = new BasicDBObject();
            queryObject.put("EnquiryId", queryOnId);
            BasicDBObject projection = new BasicDBObject("_id", 1).append("EnquiryId", 1).append("isMigrated",1);
            DBObject orderObject = new BasicDBObject("EnquiryId", 1);
            dbObjectsList = newTempMongoDao.getDBObjects("BookedEnquiry117Temp1", queryObject, projection, 0, len, orderObject, db);
            removeDuplicateEnquiryId(dbObjectsList);
            logger.info("isOldCarMoveS3JobStop : {} , isOldCarMoveS3JobRunning: {} ", isOldCarMoveS3JobStop, isOldCarMoveS3JobRunning);
            while (dbObjectsList != null && dbObjectsList.size() > 0 && !isOldCarMoveS3JobStop) {
                logger.info("Car Old migration running");
//                List<Object> listOfIds = new ArrayList<>();
                HashSet<String> enquiryIds = new HashSet<>();
                for (DBObject jsonDocument : dbObjectsList) {
//                    listOfIds.add(jsonDocument.get("_id"));

                    Integer iEnquiryID = null;
                    Long lEnquiryID = null;
                    String sEnquirtyId = null;
                    Boolean isMigrated = (Boolean) jsonDocument.get("isMigrated");
                    if(isMigrated == null || !isMigrated) {
                        if (jsonDocument.get("EnquiryId") instanceof Integer) {
                            iEnquiryID = (Integer) jsonDocument.get("EnquiryId");
                            sEnquirtyId = String.valueOf(iEnquiryID);
                        }
                        if (sEnquirtyId.equals("9999") || sEnquirtyId.equals("999999")) {
                            continue;
                        }
                        if (iEnquiryID != null && iEnquiryID > 0 && !enquiryIds.contains(sEnquirtyId)) {
                            enquiryIds.add(sEnquirtyId);
                            // query to find booked EnquiryId
                            DBObject queryOnEnqId = new BasicDBObject();
                            queryOnEnqId.put("EnquiryID", iEnquiryID);
                            int offset = 0;
                            List<DBObject> dbList = newTempMongoDao.getDBObjects("ReqResLog", queryOnEnqId, offset, chunkSize, null, db);
                            if (dbList.size() > 0) {
                                if (dbList.size() == chunkSize) {
                                    offset = offset + chunkSize;
                                    dbList = getTempOldList(chunkSize, "ReqResLog", queryOnEnqId, offset, db, dbList);
                                }
                            } else {
                                lEnquiryID = Long.valueOf(iEnquiryID);
                                queryOnEnqId.put("EnquiryID", lEnquiryID);
                                dbList = newTempMongoDao.getDBObjects("ReqResLog", queryOnEnqId, offset, chunkSize, null, db);
                                if (dbList.size() > 0) {
                                    if (dbList.size() == chunkSize) {
                                        offset = offset + chunkSize;
                                        dbList = getTempOldList(chunkSize, "ReqResLog", queryOnEnqId, offset, db, dbList);
                                    }
                                } else {
                                    queryOnEnqId.put("EnquiryID", sEnquirtyId);
                                    dbList = newTempMongoDao.getDBObjects("ReqResLog", queryOnEnqId, offset, chunkSize, null, db);
                                    if (dbList.size() > 0) {
                                        if (dbList.size() == chunkSize) {
                                            offset = offset + chunkSize;
                                            dbList = getTempOldList(chunkSize, "ReqResLog", queryOnEnqId, offset, db, dbList);
                                        }
                                    }
                                }
                            }
                            System.out.println("V4 dbList size is  " + dbList.size());
                            String jsonString;
                            if (dbList != null && dbList.size() > 0) {
                                try {
                                    jsonString = JSON.serialize(dbList);
                                    // Compress JSON data
                                    byte[] compressedData = compressData(jsonString);
                                    String fileName = sEnquirtyId + "." + "json.gz";
                                    logger.info("Car Old fileName : " + fileName);
                                    String folderName = NStoS3util.S3_FOLDER_PATH_CAR;
                                    String url = s3service.addByteContentWithGzip(compressedData, NStoS3util.BUCKET_NAME_LOGGER_ARCHIVAL, folderName, fileName, "application/gzip");

                                    if (url.equals(AppConstants.FILE_ALREADY_EXIST)) {
                                        try {
//                                        newTempMongoDao.addRows("ReqResLog_Failover", dbList, db);
                                        } catch (Exception e) {
                                            logger.error("Error while adding in motor moveBookLeads , {}", e.getMessage());
                                        }
                                    }
                                    DBObject queryJson = new BasicDBObject("EnquiryId", jsonDocument.get("EnquiryId"));
                                    DBObject setquery = new BasicDBObject("isMigrated", true);
                                    DBObject rowJson = new BasicDBObject("$set", setquery);
                                    newTempMongoDao.updateRow("BookedEnquiry117Temp1", queryJson, rowJson, false, false, db);

                                } catch (Exception e) {
                                    logger.error("Error serializing DBObject: " + e.getMessage());
                                    throw e;
                                }
                            }
                        }
                    }
                }
                enquiryIds = null;
                DBObject queryJson;
                if (motorDeletedId != null && motorDeletedId.size() > 0 && motorDeletedId.getObjectId("_id") != null) {
                    totalMigrated = totalMigrated + dbObjectsList.size();
                    DBObject setquery = new BasicDBObject("movedEnqId", dbObjectsList.get(dbObjectsList.size() - 1).get("EnquiryId"))
                            .append("totalMigrated", totalMigrated)
                            .append("updatedOn", new Date());
                    queryJson = new BasicDBObject("_id", motorDeletedId.getObjectId("_id"));
                    DBObject rowJson = new BasicDBObject("$set", setquery);
                    newArchivalMongoDao.updateRow(NStoS3util.lastUpdatedIdColl, queryJson, rowJson, false, false, NStoS3util.lastUpdatedIdDB);
                }
                queryOnId.put("$gt", dbObjectsList.get(dbObjectsList.size() - 1).get("EnquiryId"));
                queryObject.put("EnquiryId", queryOnId);
                dbObjectsList = newTempMongoDao.getDBObjects("BookedEnquiry117Temp1", queryObject, projection, 0, len, orderObject, db);
            }
        }catch (Exception e) {
            logger.error("Exception caught into moveMotorBookLeads, msg: {}", e.getMessage());
            throw new Exception("Exception cuaght while processing, Car , msg: " + e.getMessage());
        } finally {
            isOldCarMoveS3JobRunning = false;
            isOldCarMoveS3JobStop = false;
        }
    }
    public void moveOldMotorBookLeadsV5(String dbColl, Integer size, int days, int chunkSize) throws Exception {

        logger.info("Inside moveOldMotorBookLeadsV2 , ns: {}, size: {}, timeCheck: {}, days", dbColl, size, days);
        List<DBObject> dbObjectsList = null;
        try {
            String[] nsArray = dbColl.split("\\.");
            String db = nsArray[0];
            String coll = nsArray[1];
            int len = size;
            long totalMigrated = 0;
            DBObject queryForDeletId = new BasicDBObject();
            queryForDeletId.put("coll", "CarLoggerDB.ReqResLog_109_3");
            BasicDBObject motorDeletedId = newArchivalMongoDao.getDBObjectByDbName(NStoS3util.lastUpdatedIdColl, queryForDeletId, NStoS3util.lastUpdatedIdDB);
            DBObject queryOnId = new BasicDBObject();
            //only provide startId at once we are starting the batch.
            if (motorDeletedId != null && motorDeletedId.size() > 0) {
                queryOnId.put("$gt", motorDeletedId.get("movedEnqId"));
                totalMigrated = (long)motorDeletedId.get("totalMigrated");
            }
            queryOnId.put("$lt", 389368954);//new ObjectId(DateUtil.getStringToDate(DateUtil.getFormatedDate(days, DateUtil.DATE_FORMAT_EE_MMM_dd_hh_mm_ss_z_yyyy), DateUtil.DATE_FORMAT_EE_MMM_dd_hh_mm_ss_z_yyyy)));
            DBObject queryObject = new BasicDBObject();
            queryObject.put("EnquiryId", queryOnId);
            BasicDBObject projection = new BasicDBObject("_id", 1).append("EnquiryId", 1).append("isMigrated",1);
            DBObject orderObject = new BasicDBObject("EnquiryId", 1);
            dbObjectsList = newTempMongoDao.getDBObjects("BookedEnquiry117Temp1", queryObject, projection, 0, len, orderObject, db);
            removeDuplicateEnquiryId(dbObjectsList);
            logger.info("isOldCarMoveS3JobStop : {} , isOldCarMoveS3JobRunning: {} ", isOldCarMoveS3JobStop, isOldCarMoveS3JobRunning);
            while (dbObjectsList != null && dbObjectsList.size() > 0 && !isOldCarMoveS3JobStop) {
                logger.info("Car Old migration running");
//                List<Object> listOfIds = new ArrayList<>();
                HashSet<String> enquiryIds = new HashSet<>();
                for (DBObject jsonDocument : dbObjectsList) {
//                    listOfIds.add(jsonDocument.get("_id"));

                    Integer iEnquiryID = null;
                    Long lEnquiryID = null;
                    String sEnquirtyId = null;
                    Boolean isMigrated = (Boolean) jsonDocument.get("isMigrated");
                    if(isMigrated == null || !isMigrated) {
                        if (jsonDocument.get("EnquiryId") instanceof Integer) {
                            iEnquiryID = (Integer) jsonDocument.get("EnquiryId");
                            sEnquirtyId = String.valueOf(iEnquiryID);
                        }
                        if (sEnquirtyId.equals("9999") || sEnquirtyId.equals("999999")) {
                            continue;
                        }
                        if (iEnquiryID != null && iEnquiryID > 0 && !enquiryIds.contains(sEnquirtyId)) {
                            enquiryIds.add(sEnquirtyId);
                            // query to find booked EnquiryId
                            DBObject queryOnEnqId = new BasicDBObject();
                            queryOnEnqId.put("EnquiryID", iEnquiryID);
                            int offset = 0;
                            List<DBObject> dbList = newTempMongoDao.getDBObjects("ReqResLog", queryOnEnqId, offset, chunkSize, null, db);
                            if (dbList.size() > 0) {
                                if (dbList.size() == chunkSize) {
                                    offset = offset + chunkSize;
                                    dbList = getTempOldList(chunkSize, "ReqResLog", queryOnEnqId, offset, db, dbList);
                                }
                            } else {
                                lEnquiryID = Long.valueOf(iEnquiryID);
                                queryOnEnqId.put("EnquiryID", lEnquiryID);
                                dbList = newTempMongoDao.getDBObjects("ReqResLog", queryOnEnqId, offset, chunkSize, null, db);
                                if (dbList.size() > 0) {
                                    if (dbList.size() == chunkSize) {
                                        offset = offset + chunkSize;
                                        dbList = getTempOldList(chunkSize, "ReqResLog", queryOnEnqId, offset, db, dbList);
                                    }
                                } else {
                                    queryOnEnqId.put("EnquiryID", sEnquirtyId);
                                    dbList = newTempMongoDao.getDBObjects("ReqResLog", queryOnEnqId, offset, chunkSize, null, db);
                                    if (dbList.size() > 0) {
                                        if (dbList.size() == chunkSize) {
                                            offset = offset + chunkSize;
                                            dbList = getTempOldList(chunkSize, "ReqResLog", queryOnEnqId, offset, db, dbList);
                                        }
                                    }
                                }
                            }
                            System.out.println("V5 dbList size is  " + dbList.size());
                            String jsonString;
                            if (dbList != null && dbList.size() > 0) {
                                try {
                                    jsonString = JSON.serialize(dbList);
                                    // Compress JSON data
                                    byte[] compressedData = compressData(jsonString);
                                    String fileName = sEnquirtyId + "." + "json.gz";
                                    logger.info("Car Old fileName : " + fileName);
                                    String folderName = NStoS3util.S3_FOLDER_PATH_CAR;
                                    String url = s3service.addByteContentWithGzip(compressedData, NStoS3util.BUCKET_NAME_LOGGER_ARCHIVAL, folderName, fileName, "application/gzip");

                                    if (url.equals(AppConstants.FILE_ALREADY_EXIST)) {
                                        try {
//                                        newTempMongoDao.addRows("ReqResLog_Failover", dbList, db);
                                        } catch (Exception e) {
                                            logger.error("Error while adding in motor moveBookLeads , {}", e.getMessage());
                                        }
                                    }
                                    DBObject queryJson = new BasicDBObject("EnquiryId", jsonDocument.get("EnquiryId"));
                                    DBObject setquery = new BasicDBObject("isMigrated", true);
                                    DBObject rowJson = new BasicDBObject("$set", setquery);
                                    newTempMongoDao.updateRow("BookedEnquiry117Temp1", queryJson, rowJson, false, false, db);

                                } catch (Exception e) {
                                    logger.error("Error serializing DBObject: " + e.getMessage());
                                    throw e;
                                }
                            }
                        }
                    }
                }
                enquiryIds = null;
                DBObject queryJson;
                if (motorDeletedId != null && motorDeletedId.size() > 0 && motorDeletedId.getObjectId("_id") != null) {
                    totalMigrated = totalMigrated + dbObjectsList.size();
                    DBObject setquery = new BasicDBObject("movedEnqId", dbObjectsList.get(dbObjectsList.size() - 1).get("EnquiryId"))
                            .append("totalMigrated", totalMigrated)
                            .append("updatedOn", new Date());
                    queryJson = new BasicDBObject("_id", motorDeletedId.getObjectId("_id"));
                    DBObject rowJson = new BasicDBObject("$set", setquery);
                    newArchivalMongoDao.updateRow(NStoS3util.lastUpdatedIdColl, queryJson, rowJson, false, false, NStoS3util.lastUpdatedIdDB);
                }
                queryOnId.put("$gt", dbObjectsList.get(dbObjectsList.size() - 1).get("EnquiryId"));
                queryObject.put("EnquiryId", queryOnId);
                dbObjectsList = newTempMongoDao.getDBObjects("BookedEnquiry117Temp1", queryObject, projection, 0, len, orderObject, db);
            }
        }catch (Exception e) {
            logger.error("Exception caught into moveMotorBookLeads, msg: {}", e.getMessage());
            throw new Exception("Exception cuaght while processing, Car , msg: " + e.getMessage());
        } finally {
            isOldCarMoveS3JobRunning = false;
            isOldCarMoveS3JobStop = false;
        }
    }
    public void moveOldMotorBookLeadsV6(String dbColl, Integer size, int days, int chunkSize) throws Exception {

        logger.info("Inside moveOldMotorBookLeadsV2 , ns: {}, size: {}, timeCheck: {}, days", dbColl, size, days);
        List<DBObject> dbObjectsList = null;
        try {
            String[] nsArray = dbColl.split("\\.");
            String db = nsArray[0];
            String coll = nsArray[1];
            int len = size;
            long totalMigrated = 0;
            DBObject queryForDeletId = new BasicDBObject();
            queryForDeletId.put("coll", "CarLoggerDB.ReqResLog_109_4");
            BasicDBObject motorDeletedId = newArchivalMongoDao.getDBObjectByDbName(NStoS3util.lastUpdatedIdColl, queryForDeletId, NStoS3util.lastUpdatedIdDB);
            DBObject queryOnId = new BasicDBObject();
            //only provide startId at once we are starting the batch.
            if (motorDeletedId != null && motorDeletedId.size() > 0) {
                queryOnId.put("$gt", motorDeletedId.get("movedEnqId"));
                totalMigrated = (long)motorDeletedId.get("totalMigrated");
            }
            queryOnId.put("$lt", 397421764);//new ObjectId(DateUtil.getStringToDate(DateUtil.getFormatedDate(days, DateUtil.DATE_FORMAT_EE_MMM_dd_hh_mm_ss_z_yyyy), DateUtil.DATE_FORMAT_EE_MMM_dd_hh_mm_ss_z_yyyy)));
            DBObject queryObject = new BasicDBObject();
            queryObject.put("EnquiryId", queryOnId);
            BasicDBObject projection = new BasicDBObject("_id", 1).append("EnquiryId", 1).append("isMigrated",1);
            DBObject orderObject = new BasicDBObject("EnquiryId", 1);
            dbObjectsList = newTempMongoDao.getDBObjects("BookedEnquiry117Temp1", queryObject, projection, 0, len, orderObject, db);
            removeDuplicateEnquiryId(dbObjectsList);
            logger.info("isOldCarMoveS3JobStop : {} , isOldCarMoveS3JobRunning: {} ", isOldCarMoveS3JobStop, isOldCarMoveS3JobRunning);
            while (dbObjectsList != null && dbObjectsList.size() > 0 && !isOldCarMoveS3JobStop) {
                logger.info("Car Old migration running");
//                List<Object> listOfIds = new ArrayList<>();
                HashSet<String> enquiryIds = new HashSet<>();
                for (DBObject jsonDocument : dbObjectsList) {
//                    listOfIds.add(jsonDocument.get("_id"));

                    Integer iEnquiryID = null;
                    Long lEnquiryID = null;
                    String sEnquirtyId = null;
                    Boolean isMigrated = (Boolean) jsonDocument.get("isMigrated");
                    if(isMigrated == null || !isMigrated) {
                        if (jsonDocument.get("EnquiryId") instanceof Integer) {
                            iEnquiryID = (Integer) jsonDocument.get("EnquiryId");
                            sEnquirtyId = String.valueOf(iEnquiryID);
                        }
                        if (sEnquirtyId.equals("9999") || sEnquirtyId.equals("999999")) {
                            continue;
                        }
                        if (iEnquiryID != null && iEnquiryID > 0 && !enquiryIds.contains(sEnquirtyId)) {
                            enquiryIds.add(sEnquirtyId);
                            // query to find booked EnquiryId
                            DBObject queryOnEnqId = new BasicDBObject();
                            queryOnEnqId.put("EnquiryID", iEnquiryID);
                            int offset = 0;
                            List<DBObject> dbList = newTempMongoDao.getDBObjects("ReqResLog", queryOnEnqId, offset, chunkSize, null, db);
                            if (dbList.size() > 0) {
                                if (dbList.size() == chunkSize) {
                                    offset = offset + chunkSize;
                                    dbList = getTempOldList(chunkSize, "ReqResLog", queryOnEnqId, offset, db, dbList);
                                }
                            } else {
                                lEnquiryID = Long.valueOf(iEnquiryID);
                                queryOnEnqId.put("EnquiryID", lEnquiryID);
                                dbList = newTempMongoDao.getDBObjects("ReqResLog", queryOnEnqId, offset, chunkSize, null, db);
                                if (dbList.size() > 0) {
                                    if (dbList.size() == chunkSize) {
                                        offset = offset + chunkSize;
                                        dbList = getTempOldList(chunkSize, "ReqResLog", queryOnEnqId, offset, db, dbList);
                                    }
                                } else {
                                    queryOnEnqId.put("EnquiryID", sEnquirtyId);
                                    dbList = newTempMongoDao.getDBObjects("ReqResLog", queryOnEnqId, offset, chunkSize, null, db);
                                    if (dbList.size() > 0) {
                                        if (dbList.size() == chunkSize) {
                                            offset = offset + chunkSize;
                                            dbList = getTempOldList(chunkSize, "ReqResLog", queryOnEnqId, offset, db, dbList);
                                        }
                                    }
                                }
                            }
                            System.out.println("V6 dbList size is  " + dbList.size());
                            String jsonString;
                            if (dbList != null && dbList.size() > 0) {
                                try {
                                    jsonString = JSON.serialize(dbList);
                                    // Compress JSON data
                                    byte[] compressedData = compressData(jsonString);
                                    String fileName = sEnquirtyId + "." + "json.gz";
                                    logger.info("Car Old fileName : " + fileName);
                                    String folderName = NStoS3util.S3_FOLDER_PATH_CAR;
                                    String url = s3service.addByteContentWithGzip(compressedData, NStoS3util.BUCKET_NAME_LOGGER_ARCHIVAL, folderName, fileName, "application/gzip");

                                    if (url.equals(AppConstants.FILE_ALREADY_EXIST)) {
                                        try {
//                                        newTempMongoDao.addRows("ReqResLog_Failover", dbList, db);
                                        } catch (Exception e) {
                                            logger.error("Error while adding in motor moveBookLeads , {}", e.getMessage());
                                        }
                                    }
                                    DBObject queryJson = new BasicDBObject("EnquiryId", jsonDocument.get("EnquiryId"));
                                    DBObject setquery = new BasicDBObject("isMigrated", true);
                                    DBObject rowJson = new BasicDBObject("$set", setquery);
                                    newTempMongoDao.updateRow("BookedEnquiry117Temp1", queryJson, rowJson, false, false, db);

                                } catch (Exception e) {
                                    logger.error("Error serializing DBObject: " + e.getMessage());
                                    throw e;
                                }
                            }
                        }
                    }
                }
                enquiryIds = null;
                DBObject queryJson;
                if (motorDeletedId != null && motorDeletedId.size() > 0 && motorDeletedId.getObjectId("_id") != null) {
                    totalMigrated = totalMigrated + dbObjectsList.size();
                    DBObject setquery = new BasicDBObject("movedEnqId", dbObjectsList.get(dbObjectsList.size() - 1).get("EnquiryId"))
                            .append("totalMigrated", totalMigrated)
                            .append("updatedOn", new Date());
                    queryJson = new BasicDBObject("_id", motorDeletedId.getObjectId("_id"));
                    DBObject rowJson = new BasicDBObject("$set", setquery);
                    newArchivalMongoDao.updateRow(NStoS3util.lastUpdatedIdColl, queryJson, rowJson, false, false, NStoS3util.lastUpdatedIdDB);
                }
                queryOnId.put("$gt", dbObjectsList.get(dbObjectsList.size() - 1).get("EnquiryId"));
                queryObject.put("EnquiryId", queryOnId);
                dbObjectsList = newTempMongoDao.getDBObjects("BookedEnquiry117Temp1", queryObject, projection, 0, len, orderObject, db);
            }
        }catch (Exception e) {
            logger.error("Exception caught into moveMotorBookLeads, msg: {}", e.getMessage());
            throw new Exception("Exception cuaght while processing, Car , msg: " + e.getMessage());
        } finally {
            isOldCarMoveS3JobRunning = false;
            isOldCarMoveS3JobStop = false;
        }
    }
    public void moveOldMotorBookLeadsV7(String dbColl, Integer size, int days, int chunkSize) throws Exception {

        logger.info("Inside moveOldMotorBookLeadsV2 , ns: {}, size: {}, timeCheck: {}, days", dbColl, size, days);
        List<DBObject> dbObjectsList = null;
        try {
            String[] nsArray = dbColl.split("\\.");
            String db = nsArray[0];
            String coll = nsArray[1];
            int len = size;
            long totalMigrated = 0;
            DBObject queryForDeletId = new BasicDBObject();
            queryForDeletId.put("coll", "CarLoggerDB.ReqResLog_109_5");
            BasicDBObject motorDeletedId = newArchivalMongoDao.getDBObjectByDbName(NStoS3util.lastUpdatedIdColl, queryForDeletId, NStoS3util.lastUpdatedIdDB);
            DBObject queryOnId = new BasicDBObject();
            //only provide startId at once we are starting the batch.
            if (motorDeletedId != null && motorDeletedId.size() > 0) {
                queryOnId.put("$gt", motorDeletedId.get("movedEnqId"));
                totalMigrated = (long)motorDeletedId.get("totalMigrated");
            }
            queryOnId.put("$lt", 405474574);//new ObjectId(DateUtil.getStringToDate(DateUtil.getFormatedDate(days, DateUtil.DATE_FORMAT_EE_MMM_dd_hh_mm_ss_z_yyyy), DateUtil.DATE_FORMAT_EE_MMM_dd_hh_mm_ss_z_yyyy)));
            DBObject queryObject = new BasicDBObject();
            queryObject.put("EnquiryId", queryOnId);
            BasicDBObject projection = new BasicDBObject("_id", 1).append("EnquiryId", 1).append("isMigrated",1);
            DBObject orderObject = new BasicDBObject("EnquiryId", 1);
            dbObjectsList = newTempMongoDao.getDBObjects("BookedEnquiry117Temp1", queryObject, projection, 0, len, orderObject, db);
            removeDuplicateEnquiryId(dbObjectsList);
            logger.info("isOldCarMoveS3JobStop : {} , isOldCarMoveS3JobRunning: {} ", isOldCarMoveS3JobStop, isOldCarMoveS3JobRunning);
            while (dbObjectsList != null && dbObjectsList.size() > 0 && !isOldCarMoveS3JobStop) {
                logger.info("Car Old migration running");
//                List<Object> listOfIds = new ArrayList<>();
                HashSet<String> enquiryIds = new HashSet<>();
                for (DBObject jsonDocument : dbObjectsList) {
//                    listOfIds.add(jsonDocument.get("_id"));

                    Integer iEnquiryID = null;
                    Long lEnquiryID = null;
                    String sEnquirtyId = null;
                    Boolean isMigrated = (Boolean) jsonDocument.get("isMigrated");
                    if(isMigrated == null || !isMigrated) {
                        if (jsonDocument.get("EnquiryId") instanceof Integer) {
                            iEnquiryID = (Integer) jsonDocument.get("EnquiryId");
                            sEnquirtyId = String.valueOf(iEnquiryID);
                        }
                        if (sEnquirtyId.equals("9999") || sEnquirtyId.equals("999999")) {
                            continue;
                        }
                        if (iEnquiryID != null && iEnquiryID > 0 && !enquiryIds.contains(sEnquirtyId)) {
                            enquiryIds.add(sEnquirtyId);
                            // query to find booked EnquiryId
                            DBObject queryOnEnqId = new BasicDBObject();
                            queryOnEnqId.put("EnquiryID", iEnquiryID);
                            int offset = 0;
                            List<DBObject> dbList = newTempMongoDao.getDBObjects("ReqResLog", queryOnEnqId, offset, chunkSize, null, db);
                            if (dbList.size() > 0) {
                                if (dbList.size() == chunkSize) {
                                    offset = offset + chunkSize;
                                    dbList = getTempOldList(chunkSize, "ReqResLog", queryOnEnqId, offset, db, dbList);
                                }
                            } else {
                                lEnquiryID = Long.valueOf(iEnquiryID);
                                queryOnEnqId.put("EnquiryID", lEnquiryID);
                                dbList = newTempMongoDao.getDBObjects("ReqResLog", queryOnEnqId, offset, chunkSize, null, db);
                                if (dbList.size() > 0) {
                                    if (dbList.size() == chunkSize) {
                                        offset = offset + chunkSize;
                                        dbList = getTempOldList(chunkSize, "ReqResLog", queryOnEnqId, offset, db, dbList);
                                    }
                                } else {
                                    queryOnEnqId.put("EnquiryID", sEnquirtyId);
                                    dbList = newTempMongoDao.getDBObjects("ReqResLog", queryOnEnqId, offset, chunkSize, null, db);
                                    if (dbList.size() > 0) {
                                        if (dbList.size() == chunkSize) {
                                            offset = offset + chunkSize;
                                            dbList = getTempOldList(chunkSize, "ReqResLog", queryOnEnqId, offset, db, dbList);
                                        }
                                    }
                                }
                            }
                            System.out.println("V7 dbList size is  " + dbList.size());
                            String jsonString;
                            if (dbList != null && dbList.size() > 0) {
                                try {
                                    jsonString = JSON.serialize(dbList);
                                    // Compress JSON data
                                    byte[] compressedData = compressData(jsonString);
                                    String fileName = sEnquirtyId + "." + "json.gz";
                                    logger.info("Car Old fileName : " + fileName);
                                    String folderName = NStoS3util.S3_FOLDER_PATH_CAR;
                                    String url = s3service.addByteContentWithGzip(compressedData, NStoS3util.BUCKET_NAME_LOGGER_ARCHIVAL, folderName, fileName, "application/gzip");

                                    if (url.equals(AppConstants.FILE_ALREADY_EXIST)) {
                                        try {
//                                        newTempMongoDao.addRows("ReqResLog_Failover", dbList, db);
                                        } catch (Exception e) {
                                            logger.error("Error while adding in motor moveBookLeads , {}", e.getMessage());
                                        }
                                    }
                                    DBObject queryJson = new BasicDBObject("EnquiryId", jsonDocument.get("EnquiryId"));
                                    DBObject setquery = new BasicDBObject("isMigrated", true);
                                    DBObject rowJson = new BasicDBObject("$set", setquery);
                                    newTempMongoDao.updateRow("BookedEnquiry117Temp1", queryJson, rowJson, false, false, db);

                                } catch (Exception e) {
                                    logger.error("Error serializing DBObject: " + e.getMessage());
                                    throw e;
                                }
                            }
                        }
                    }
                }
                enquiryIds = null;
                DBObject queryJson;
                if (motorDeletedId != null && motorDeletedId.size() > 0 && motorDeletedId.getObjectId("_id") != null) {
                    totalMigrated = totalMigrated + dbObjectsList.size();
                    DBObject setquery = new BasicDBObject("movedEnqId", dbObjectsList.get(dbObjectsList.size() - 1).get("EnquiryId"))
                            .append("totalMigrated", totalMigrated)
                            .append("updatedOn", new Date());
                    queryJson = new BasicDBObject("_id", motorDeletedId.getObjectId("_id"));
                    DBObject rowJson = new BasicDBObject("$set", setquery);
                    newArchivalMongoDao.updateRow(NStoS3util.lastUpdatedIdColl, queryJson, rowJson, false, false, NStoS3util.lastUpdatedIdDB);
                }
                queryOnId.put("$gt", dbObjectsList.get(dbObjectsList.size() - 1).get("EnquiryId"));
                queryObject.put("EnquiryId", queryOnId);
                dbObjectsList = newTempMongoDao.getDBObjects("BookedEnquiry117Temp1", queryObject, projection, 0, len, orderObject, db);
            }
        }catch (Exception e) {
            logger.error("Exception caught into moveMotorBookLeads, msg: {}", e.getMessage());
            throw new Exception("Exception cuaght while processing, Car , msg: " + e.getMessage());
        } finally {
            isOldCarMoveS3JobRunning = false;
            isOldCarMoveS3JobStop = false;
        }
    }
    private void removeDuplicateEnquiryId(List<DBObject> dbObjectsList){
        Iterator<DBObject> itr = dbObjectsList.iterator();
        List<Integer> uniqueEnquiryIdList = new ArrayList<>();
        while (itr.hasNext()) {
            DBObject jsonDocument = itr.next();
            Integer enquiryId = (Integer) jsonDocument.get("EnquiryId");
            if (enquiryId!=null && enquiryId > 0 && !uniqueEnquiryIdList.contains(enquiryId)) {
                uniqueEnquiryIdList.add(enquiryId);
            } else {
                itr.remove();
            }
        }
    }


}
