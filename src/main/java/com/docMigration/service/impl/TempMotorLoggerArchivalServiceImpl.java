package com.docMigration.service.impl;

import com.docMigration.dao.ChatMongoDao;
import com.docMigration.service.S3BucketService;
import com.docMigration.util.AppConstants;
import com.docMigration.util.AppUtil;
import com.docMigration.util.DateUtil;
import com.docMigration.util.NStoS3util;
import com.mongodb.BasicDBObject;
import com.mongodb.DBObject;
import com.mongodb.util.JSON;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.*;

import static com.docMigration.util.CompressionUtil.compressData;

@Service
public class TempMotorLoggerArchivalServiceImpl {
    private final Logger logger = LoggerFactory.getLogger(TempMotorLoggerArchivalServiceImpl.class);
    public static boolean isTempCarMoveS3JobRunning1 = false;
    public static boolean isTempCarMoveS3JobRunning2 = false;
    public static boolean isTempCarMoveS3JobRunning3 = false;
    public static boolean isTempCarMoveS3JobRunning4 = false;
    public static boolean isTempCarMoveS3JobRunning5 = false;
    public static boolean isTempCarMoveS3JobStop1 = false;
    public static boolean isTempCarMoveS3JobStop2 = false;
    public static boolean isTempCarMoveS3JobStop3 = false;
    public static boolean isTempCarMoveS3JobStop4 = false;
    public static boolean isTempCarMoveS3JobStop5 = false;
    public static boolean isTempTWMoveS3JobRunning1 = false;
    public static boolean isTempTWMoveS3JobStop1 = false;
    public static boolean isTempTWMoveS3JobRunning2 = false;
    public static boolean isTempTWMoveS3JobStop2 = false;
    public static boolean isTempTWMoveS3JobRunning3 = false;
    public static boolean isTempTWMoveS3JobStop3 = false;
    public static boolean isPbEventLogS3MoveDataRunning = false;
    public static boolean isPbEventLogS3MoveDataStop = false;


    @Autowired
    @Qualifier("motorLoggerDao")
    private ChatMongoDao motorLoggerDao;
    @Autowired
    @Qualifier("newArchivalMongoDao")
    private ChatMongoDao newArchivalMongoDao;
    @Autowired
    private S3BucketService s3service;
    @Autowired
    @Qualifier("newArchivalMongoDao")
    ChatMongoDao newArchMongoDao;
    @Autowired
    @Qualifier("newTempMongoDao2")
    ChatMongoDao newTempMongoDao;


    public void moveOldMotorBookLeadsV1(String dbColl, Integer size, int days, int chunkSize) throws Exception {

        logger.info("Inside moveOldMotorBookLeadsV2 , ns: {}, size: {}, timeCheck: {}, days", dbColl, size, days);
        List<DBObject> dbObjectsList = null;
        try {
            String[] nsArray = dbColl.split("\\.");
            String db = nsArray[0];
            String coll = nsArray[1];
            int len = size;
            long totalMigrated = 0;
            DBObject queryForDeletId = new BasicDBObject();
            queryForDeletId.put("coll", "CarLoggerDB.ReqResLog_240_1");
            BasicDBObject motorDeletedId = newArchivalMongoDao.getDBObjectByDbName(NStoS3util.lastUpdatedIdColl, queryForDeletId, NStoS3util.lastUpdatedIdDB);
            DBObject queryOnId = new BasicDBObject();
            //only provide startId at once we are starting the batch.
            if (motorDeletedId != null && motorDeletedId.size() > 0) {
                queryOnId.put("$gt", motorDeletedId.get("movedEnqId"));
                totalMigrated = (long)motorDeletedId.get("totalMigrated");
            }
            queryOnId.put("$lt", 61684226);//new ObjectId(DateUtil.getStringToDate(DateUtil.getFormatedDate(days, DateUtil.DATE_FORMAT_EE_MMM_dd_hh_mm_ss_z_yyyy), DateUtil.DATE_FORMAT_EE_MMM_dd_hh_mm_ss_z_yyyy)));
            DBObject queryObject = new BasicDBObject();
            queryObject.put("EnquiryId", queryOnId);
            BasicDBObject projection = new BasicDBObject("_id", 1).append("EnquiryId", 1).append("isMigrated",1);
            DBObject orderObject = new BasicDBObject("EnquiryId", 1);
            dbObjectsList = newTempMongoDao.getDBObjects("BookedEnquiry117Temp1", queryObject, projection, 0, len, orderObject, db);
            removeDuplicateEnquiryId(dbObjectsList, "EnquiryId");
            logger.info("isOldCarMoveS3JobStop : {} , isOldCarMoveS3JobRunning: {} ", isTempCarMoveS3JobStop1, isTempCarMoveS3JobRunning1);
            while (dbObjectsList != null && dbObjectsList.size() > 0 && !isTempCarMoveS3JobStop1) {
                logger.info("Car Old migration running");
//                List<Object> listOfIds = new ArrayList<>();
                HashSet<String> enquiryIds = new HashSet<>();
                for (DBObject jsonDocument : dbObjectsList) {
//                    listOfIds.add(jsonDocument.get("_id"));

                    Integer iEnquiryID = null;
                    Long lEnquiryID = null;
                    String sEnquirtyId = null;
                    Boolean isMigrated = (Boolean) jsonDocument.get("isMigrated");
                    if(isMigrated == null || !isMigrated){
                        if (jsonDocument.get("EnquiryId") instanceof Integer) {
                            iEnquiryID = (Integer) jsonDocument.get("EnquiryId");
                            sEnquirtyId = String.valueOf(iEnquiryID);
                        }
                        if (sEnquirtyId.equals("9999") || sEnquirtyId.equals("999999")) {
                            continue;
                        }
                        if (iEnquiryID != null && iEnquiryID > 0 && !enquiryIds.contains(sEnquirtyId)) {
                            enquiryIds.add(sEnquirtyId);
                            // query to find booked EnquiryId
                            DBObject queryOnEnqId = new BasicDBObject();
                            queryOnEnqId.put("EnquiryID", iEnquiryID);
                            int offset = 0;
                            List<DBObject> dbList = newTempMongoDao.getDBObjects("ReqResLog", queryOnEnqId, offset, chunkSize, null, db);
                            if (dbList.size() > 0) {
                                if (dbList.size() == chunkSize) {
                                    offset = offset + chunkSize;
                                    dbList = getTempOldList(chunkSize, "ReqResLog", queryOnEnqId, offset, db, dbList);
                                }
                            } else {
                                lEnquiryID = Long.valueOf(iEnquiryID);
                                queryOnEnqId.put("EnquiryID", lEnquiryID);
                                dbList = newTempMongoDao.getDBObjects("ReqResLog", queryOnEnqId, offset, chunkSize, null, db);
                                if (dbList.size() > 0) {
                                    if (dbList.size() == chunkSize) {
                                        offset = offset + chunkSize;
                                        dbList = getTempOldList(chunkSize, "ReqResLog", queryOnEnqId, offset, db, dbList);
                                    }
                                } else {
                                    queryOnEnqId.put("EnquiryID", sEnquirtyId);
                                    dbList = newTempMongoDao.getDBObjects("ReqResLog", queryOnEnqId, offset, chunkSize, null, db);
                                    if (dbList.size() > 0) {
                                        if (dbList.size() == chunkSize) {
                                            offset = offset + chunkSize;
                                            dbList = getTempOldList(chunkSize, "ReqResLog", queryOnEnqId, offset, db, dbList);
                                        }
                                    }
                                }
                            }
                            System.out.println("V1 _79_ dbList size is is  " + dbList.size());
                            String jsonString;
                            if (dbList != null && dbList.size() > 0) {
                                try {
                                    jsonString = JSON.serialize(dbList);
                                    // Compress JSON data
                                    byte[] compressedData = compressData(jsonString);
                                    String fileName = sEnquirtyId + "." + "json.gz";
                                    logger.info("Car Old fileName : " + fileName);
                                    String folderName = NStoS3util.S3_FOLDER_PATH_CAR;
                                    String url = s3service.addByteContentWithGzip(compressedData, NStoS3util.BUCKET_NAME_LOGGER_ARCHIVAL, folderName, fileName, "application/gzip");

                                    if (url.equals(AppConstants.FILE_ALREADY_EXIST)) {
                                        try {
//                                        newTempMongoDao.addRows("ReqResLog_Failover", dbList, db);
                                        } catch (Exception e) {
                                            logger.error("Error while adding in motor moveBookLeads , {}", e.getMessage());
                                        }
                                    }
                                    DBObject queryJson = new BasicDBObject("EnquiryId", jsonDocument.get("EnquiryId"));
                                    DBObject setquery = new BasicDBObject("isMigrated", true);
                                    DBObject rowJson = new BasicDBObject("$set", setquery);
                                    newTempMongoDao.updateRow("BookedEnquiry117Temp1", queryJson, rowJson, false, false, db);

                                } catch (Exception e) {
                                    logger.error("Error serializing DBObject: " + e.getMessage());
                                    throw e;
                                }
                            }
                        }
                    }
                }
                enquiryIds = null;
                DBObject queryJson;
                if (motorDeletedId != null && motorDeletedId.size() > 0 && motorDeletedId.getObjectId("_id") != null) {
                    totalMigrated = totalMigrated + dbObjectsList.size();
                    DBObject setquery = new BasicDBObject("movedEnqId", dbObjectsList.get(dbObjectsList.size() - 1).get("EnquiryId"))
                            .append("totalMigrated", totalMigrated)
                            .append("updatedOn", new Date());
                    queryJson = new BasicDBObject("_id", motorDeletedId.getObjectId("_id"));
                    DBObject rowJson = new BasicDBObject("$set", setquery);
                    newArchivalMongoDao.updateRow(NStoS3util.lastUpdatedIdColl, queryJson, rowJson, false, false, NStoS3util.lastUpdatedIdDB);
                }
                queryOnId.put("$gt", dbObjectsList.get(dbObjectsList.size() - 1).get("EnquiryId"));
                queryObject.put("EnquiryId", queryOnId);
                dbObjectsList = newTempMongoDao.getDBObjects("BookedEnquiry117Temp1", queryObject, projection, 0, len, orderObject, db);
            }
        }catch (Exception e) {
            logger.error("Exception caught into moveMotorBookLeads, msg: {}", e.getMessage());
            throw new Exception("Exception cuaght while processing, Car , msg: " + e.getMessage());
        } finally {
            isTempCarMoveS3JobRunning1 = false;
            isTempCarMoveS3JobStop1 = false;
        }
    }

    public void moveOldMotorBookLeadsV2(String dbColl, Integer size, int days, int chunkSize) throws Exception {

        logger.info("Inside moveOldMotorBookLeadsV2 , ns: {}, size: {}, timeCheck: {}, days", dbColl, size, days);
        List<DBObject> dbObjectsList = null;
        try {
            String[] nsArray = dbColl.split("\\.");
            String db = nsArray[0];
            String coll = nsArray[1];
            int len = size;
            long totalMigrated = 0;
            DBObject queryForDeletId = new BasicDBObject();
            queryForDeletId.put("coll", "CarLoggerDB.ReqResLog_240_2");
            BasicDBObject motorDeletedId = newArchivalMongoDao.getDBObjectByDbName(NStoS3util.lastUpdatedIdColl, queryForDeletId, NStoS3util.lastUpdatedIdDB);
            DBObject queryOnId = new BasicDBObject();
            //only provide startId at once we are starting the batch.
            if (motorDeletedId != null && motorDeletedId.size() > 0) {
                queryOnId.put("$gt", motorDeletedId.get("movedEnqId"));
                totalMigrated = (long)motorDeletedId.get("totalMigrated");
            }
            queryOnId.put("$lt", 120829621);//new ObjectId(DateUtil.getStringToDate(DateUtil.getFormatedDate(days, DateUtil.DATE_FORMAT_EE_MMM_dd_hh_mm_ss_z_yyyy), DateUtil.DATE_FORMAT_EE_MMM_dd_hh_mm_ss_z_yyyy)));
            DBObject queryObject = new BasicDBObject();
            queryObject.put("EnquiryId", queryOnId);
            BasicDBObject projection = new BasicDBObject("_id", 1).append("EnquiryId", 1).append("isMigrated",1);
            DBObject orderObject = new BasicDBObject("EnquiryId", 1);
            dbObjectsList = newTempMongoDao.getDBObjects("BookedEnquiry117Temp1", queryObject, projection, 0, len, orderObject, db);
            removeDuplicateEnquiryId(dbObjectsList, "EnquiryId");
            logger.info("isOldCarMoveS3JobStop : {} , isOldCarMoveS3JobRunning: {} ", isTempCarMoveS3JobStop2, isTempCarMoveS3JobRunning2);
            while (dbObjectsList != null && dbObjectsList.size() > 0 && !isTempCarMoveS3JobStop2) {
                logger.info("Car Old migration running");
//                List<Object> listOfIds = new ArrayList<>();
                HashSet<String> enquiryIds = new HashSet<>();
                for (DBObject jsonDocument : dbObjectsList) {
//                    listOfIds.add(jsonDocument.get("_id"));

                    Integer iEnquiryID = null;
                    Long lEnquiryID = null;
                    String sEnquirtyId = null;
                    Boolean isMigrated = (Boolean) jsonDocument.get("isMigrated");
                    if(isMigrated == null || !isMigrated){
                        if (jsonDocument.get("EnquiryId") instanceof Integer) {
                            iEnquiryID = (Integer) jsonDocument.get("EnquiryId");
                            sEnquirtyId = String.valueOf(iEnquiryID);
                        }
                        if (sEnquirtyId.equals("9999") || sEnquirtyId.equals("999999")) {
                            continue;
                        }
                        if (iEnquiryID != null && iEnquiryID > 0 && !enquiryIds.contains(sEnquirtyId)) {
                            enquiryIds.add(sEnquirtyId);
                            // query to find booked EnquiryId
                            DBObject queryOnEnqId = new BasicDBObject();
                            queryOnEnqId.put("EnquiryID", iEnquiryID);
                            int offset = 0;
                            List<DBObject> dbList = newTempMongoDao.getDBObjects("ReqResLog", queryOnEnqId, offset, chunkSize, null, db);
                            if (dbList.size() > 0) {
                                if (dbList.size() == chunkSize) {
                                    offset = offset + chunkSize;
                                    dbList = getTempOldList(chunkSize, "ReqResLog", queryOnEnqId, offset, db, dbList);
                                }
                            } else {
                                lEnquiryID = Long.valueOf(iEnquiryID);
                                queryOnEnqId.put("EnquiryID", lEnquiryID);
                                dbList = newTempMongoDao.getDBObjects("ReqResLog", queryOnEnqId, offset, chunkSize, null, db);
                                if (dbList.size() > 0) {
                                    if (dbList.size() == chunkSize) {
                                        offset = offset + chunkSize;
                                        dbList = getTempOldList(chunkSize, "ReqResLog", queryOnEnqId, offset, db, dbList);
                                    }
                                } else {
                                    queryOnEnqId.put("EnquiryID", sEnquirtyId);
                                    dbList = newTempMongoDao.getDBObjects("ReqResLog", queryOnEnqId, offset, chunkSize, null, db);
                                    if (dbList.size() > 0) {
                                        if (dbList.size() == chunkSize) {
                                            offset = offset + chunkSize;
                                            dbList = getTempOldList(chunkSize, "ReqResLog", queryOnEnqId, offset, db, dbList);
                                        }
                                    }
                                }
                            }
                            System.out.println("V2 _79_ dbList size is is  " + dbList.size());
                            String jsonString;
                            if (dbList != null && dbList.size() > 0) {
                                try {
                                    jsonString = JSON.serialize(dbList);
                                    // Compress JSON data
                                    byte[] compressedData = compressData(jsonString);
                                    String fileName = sEnquirtyId + "." + "json.gz";
                                    logger.info("Car Old fileName : " + fileName);
                                    String folderName = NStoS3util.S3_FOLDER_PATH_CAR;
                                    String url = s3service.addByteContentWithGzip(compressedData, NStoS3util.BUCKET_NAME_LOGGER_ARCHIVAL, folderName, fileName, "application/gzip");

                                    if (url.equals(AppConstants.FILE_ALREADY_EXIST)) {
                                        try {
//                                        newTempMongoDao.addRows("ReqResLog_Failover", dbList, db);
                                        } catch (Exception e) {
                                            logger.error("Error while adding in motor moveBookLeads , {}", e.getMessage());
                                        }
                                    }
                                    DBObject queryJson = new BasicDBObject("EnquiryId", jsonDocument.get("EnquiryId"));
                                    DBObject setquery = new BasicDBObject("isMigrated", true);
                                    DBObject rowJson = new BasicDBObject("$set", setquery);
                                    newTempMongoDao.updateRow("BookedEnquiry117Temp1", queryJson, rowJson, false, false, db);

                                } catch (Exception e) {
                                    logger.error("Error serializing DBObject: " + e.getMessage());
                                    throw e;
                                }
                            }
                        }
                    }
                }
                enquiryIds = null;
                DBObject queryJson;
                if (motorDeletedId != null && motorDeletedId.size() > 0 && motorDeletedId.getObjectId("_id") != null) {
                    totalMigrated = totalMigrated + dbObjectsList.size();
                    DBObject setquery = new BasicDBObject("movedEnqId", dbObjectsList.get(dbObjectsList.size() - 1).get("EnquiryId"))
                            .append("totalMigrated", totalMigrated)
                            .append("updatedOn", new Date());
                    queryJson = new BasicDBObject("_id", motorDeletedId.getObjectId("_id"));
                    DBObject rowJson = new BasicDBObject("$set", setquery);
                    newArchivalMongoDao.updateRow(NStoS3util.lastUpdatedIdColl, queryJson, rowJson, false, false, NStoS3util.lastUpdatedIdDB);
                }
                queryOnId.put("$gt", dbObjectsList.get(dbObjectsList.size() - 1).get("EnquiryId"));
                queryObject.put("EnquiryId", queryOnId);
                dbObjectsList = newTempMongoDao.getDBObjects("BookedEnquiry117Temp1", queryObject, projection, 0, len, orderObject, db);
            }
        }catch (Exception e) {
            logger.error("Exception caught into moveMotorBookLeads, msg: {}", e.getMessage());
            throw new Exception("Exception cuaght while processing, Car , msg: " + e.getMessage());
        } finally {
            isTempCarMoveS3JobRunning2 = false;
            isTempCarMoveS3JobStop2 = false;
        }
    }

    public void moveOldMotorBookLeadsV3(String dbColl, Integer size, int days, int chunkSize) throws Exception {

        logger.info("Inside moveOldMotorBookLeadsV2 , ns: {}, size: {}, timeCheck: {}, days", dbColl, size, days);
        List<DBObject> dbObjectsList = null;
        try {
            String[] nsArray = dbColl.split("\\.");
            String db = nsArray[0];
            String coll = nsArray[1];
            int len = size;
            long totalMigrated = 0;
            DBObject queryForDeletId = new BasicDBObject();
            queryForDeletId.put("coll", "CarLoggerDB.ReqResLog_240_3");
            BasicDBObject motorDeletedId = newArchivalMongoDao.getDBObjectByDbName(NStoS3util.lastUpdatedIdColl, queryForDeletId, NStoS3util.lastUpdatedIdDB);
            DBObject queryOnId = new BasicDBObject();
            //only provide startId at once we are starting the batch.
            if (motorDeletedId != null && motorDeletedId.size() > 0) {
                queryOnId.put("$gt", motorDeletedId.get("movedEnqId"));
                totalMigrated = (long)motorDeletedId.get("totalMigrated");
            }
            queryOnId.put("$lt", 179975016);//new ObjectId(DateUtil.getStringToDate(DateUtil.getFormatedDate(days, DateUtil.DATE_FORMAT_EE_MMM_dd_hh_mm_ss_z_yyyy), DateUtil.DATE_FORMAT_EE_MMM_dd_hh_mm_ss_z_yyyy)));
            DBObject queryObject = new BasicDBObject();
            queryObject.put("EnquiryId", queryOnId);
            BasicDBObject projection = new BasicDBObject("_id", 1).append("EnquiryId", 1).append("isMigrated",1);
            DBObject orderObject = new BasicDBObject("EnquiryId", 1);
            dbObjectsList = newTempMongoDao.getDBObjects("BookedEnquiry117Temp1", queryObject, projection, 0, len, orderObject, db);
            removeDuplicateEnquiryId(dbObjectsList, "EnquiryId");
            logger.info("isOldCarMoveS3JobStop : {} , isOldCarMoveS3JobRunning: {} ", isTempCarMoveS3JobStop3, isTempCarMoveS3JobRunning3);
            while (dbObjectsList != null && dbObjectsList.size() > 0 && !isTempCarMoveS3JobStop3) {
                logger.info("Car Old migration running");
//                List<Object> listOfIds = new ArrayList<>();
                HashSet<String> enquiryIds = new HashSet<>();
                for (DBObject jsonDocument : dbObjectsList) {
//                    listOfIds.add(jsonDocument.get("_id"));

                    Integer iEnquiryID = null;
                    Long lEnquiryID = null;
                    String sEnquirtyId = null;
                    Boolean isMigrated = (Boolean) jsonDocument.get("isMigrated");
                    if(isMigrated == null || !isMigrated){
                        if (jsonDocument.get("EnquiryId") instanceof Integer) {
                            iEnquiryID = (Integer) jsonDocument.get("EnquiryId");
                            sEnquirtyId = String.valueOf(iEnquiryID);
                        }
                        if (sEnquirtyId.equals("9999") || sEnquirtyId.equals("999999")) {
                            continue;
                        }
                        if (iEnquiryID != null && iEnquiryID > 0 && !enquiryIds.contains(sEnquirtyId)) {
                            enquiryIds.add(sEnquirtyId);
                            // query to find booked EnquiryId
                            DBObject queryOnEnqId = new BasicDBObject();
                            queryOnEnqId.put("EnquiryID", iEnquiryID);
                            int offset = 0;
                            List<DBObject> dbList = newTempMongoDao.getDBObjects("ReqResLog", queryOnEnqId, offset, chunkSize, null, db);
                            if (dbList.size() > 0) {
                                if (dbList.size() == chunkSize) {
                                    offset = offset + chunkSize;
                                    dbList = getTempOldList(chunkSize, "ReqResLog", queryOnEnqId, offset, db, dbList);
                                }
                            } else {
                                lEnquiryID = Long.valueOf(iEnquiryID);
                                queryOnEnqId.put("EnquiryID", lEnquiryID);
                                dbList = newTempMongoDao.getDBObjects("ReqResLog", queryOnEnqId, offset, chunkSize, null, db);
                                if (dbList.size() > 0) {
                                    if (dbList.size() == chunkSize) {
                                        offset = offset + chunkSize;
                                        dbList = getTempOldList(chunkSize, "ReqResLog", queryOnEnqId, offset, db, dbList);
                                    }
                                } else {
                                    queryOnEnqId.put("EnquiryID", sEnquirtyId);
                                    dbList = newTempMongoDao.getDBObjects("ReqResLog", queryOnEnqId, offset, chunkSize, null, db);
                                    if (dbList.size() > 0) {
                                        if (dbList.size() == chunkSize) {
                                            offset = offset + chunkSize;
                                            dbList = getTempOldList(chunkSize, "ReqResLog", queryOnEnqId, offset, db, dbList);
                                        }
                                    }
                                }
                            }
                            System.out.println("V3 _79_ dbList size is is  " + dbList.size());
                            String jsonString;
                            if (dbList != null && dbList.size() > 0) {
                                try {
                                    jsonString = JSON.serialize(dbList);
                                    // Compress JSON data
                                    byte[] compressedData = compressData(jsonString);
                                    String fileName = sEnquirtyId + "." + "json.gz";
                                    logger.info("Car Old fileName : " + fileName);
                                    String folderName = NStoS3util.S3_FOLDER_PATH_CAR;
                                    String url = s3service.addByteContentWithGzip(compressedData, NStoS3util.BUCKET_NAME_LOGGER_ARCHIVAL, folderName, fileName, "application/gzip");

                                    if (url.equals(AppConstants.FILE_ALREADY_EXIST)) {
                                        try {
//                                        newTempMongoDao.addRows("ReqResLog_Failover", dbList, db);
                                        } catch (Exception e) {
                                            logger.error("Error while adding in motor moveBookLeads , {}", e.getMessage());
                                        }
                                    }
                                    DBObject queryJson = new BasicDBObject("EnquiryId", jsonDocument.get("EnquiryId"));
                                    DBObject setquery = new BasicDBObject("isMigrated", true);
                                    DBObject rowJson = new BasicDBObject("$set", setquery);
                                    newTempMongoDao.updateRow("BookedEnquiry117Temp1", queryJson, rowJson, false, false, db);

                                } catch (Exception e) {
                                    logger.error("Error serializing DBObject: " + e.getMessage());
                                    throw e;
                                }
                            }
                        }
                    }
                }
                enquiryIds = null;
                DBObject queryJson;
                if (motorDeletedId != null && motorDeletedId.size() > 0 && motorDeletedId.getObjectId("_id") != null) {
                    totalMigrated = totalMigrated + dbObjectsList.size();
                    DBObject setquery = new BasicDBObject("movedEnqId", dbObjectsList.get(dbObjectsList.size() - 1).get("EnquiryId"))
                            .append("totalMigrated", totalMigrated)
                            .append("updatedOn", new Date());
                    queryJson = new BasicDBObject("_id", motorDeletedId.getObjectId("_id"));
                    DBObject rowJson = new BasicDBObject("$set", setquery);
                    newArchivalMongoDao.updateRow(NStoS3util.lastUpdatedIdColl, queryJson, rowJson, false, false, NStoS3util.lastUpdatedIdDB);
                }
                queryOnId.put("$gt", dbObjectsList.get(dbObjectsList.size() - 1).get("EnquiryId"));
                queryObject.put("EnquiryId", queryOnId);
                dbObjectsList = newTempMongoDao.getDBObjects("BookedEnquiry117Temp1", queryObject, projection, 0, len, orderObject, db);
            }
        }catch (Exception e) {
            logger.error("Exception caught into moveMotorBookLeads, msg: {}", e.getMessage());
            throw new Exception("Exception cuaght while processing, Car , msg: " + e.getMessage());
        } finally {
            isTempCarMoveS3JobRunning3 = false;
            isTempCarMoveS3JobStop3 = false;
        }
    }

    public void moveOldMotorBookLeadsV4(String dbColl, Integer size, int days, int chunkSize) throws Exception {

        logger.info("Inside moveOldMotorBookLeadsV2 , ns: {}, size: {}, timeCheck: {}, days", dbColl, size, days);
        List<DBObject> dbObjectsList = null;
        try {
            String[] nsArray = dbColl.split("\\.");
            String db = nsArray[0];
            String coll = nsArray[1];
            int len = size;
            long totalMigrated = 0;
            DBObject queryForDeletId = new BasicDBObject();
            queryForDeletId.put("coll", "CarLoggerDB.ReqResLog_240_4");
            BasicDBObject motorDeletedId = newArchivalMongoDao.getDBObjectByDbName(NStoS3util.lastUpdatedIdColl, queryForDeletId, NStoS3util.lastUpdatedIdDB);
            DBObject queryOnId = new BasicDBObject();
            //only provide startId at once we are starting the batch.
            if (motorDeletedId != null && motorDeletedId.size() > 0) {
                queryOnId.put("$gt", motorDeletedId.get("movedEnqId"));
                totalMigrated = (long)motorDeletedId.get("totalMigrated");
            }
            queryOnId.put("$lt", 239120411);//new ObjectId(DateUtil.getStringToDate(DateUtil.getFormatedDate(days, DateUtil.DATE_FORMAT_EE_MMM_dd_hh_mm_ss_z_yyyy), DateUtil.DATE_FORMAT_EE_MMM_dd_hh_mm_ss_z_yyyy)));
            DBObject queryObject = new BasicDBObject();
            queryObject.put("EnquiryId", queryOnId);
            BasicDBObject projection = new BasicDBObject("_id", 1).append("EnquiryId", 1).append("isMigrated",1);
            DBObject orderObject = new BasicDBObject("EnquiryId", 1);
            dbObjectsList = newTempMongoDao.getDBObjects("BookedEnquiry117Temp1", queryObject, projection, 0, len, orderObject, db);
            removeDuplicateEnquiryId(dbObjectsList, "EnquiryId");
            logger.info("isOldCarMoveS3JobStop : {} , isOldCarMoveS3JobRunning: {} ", isTempCarMoveS3JobStop4, isTempCarMoveS3JobRunning4);
            while (dbObjectsList != null && dbObjectsList.size() > 0 && !isTempCarMoveS3JobStop4) {
                logger.info("Car Old migration running");
//                List<Object> listOfIds = new ArrayList<>();
                HashSet<String> enquiryIds = new HashSet<>();
                for (DBObject jsonDocument : dbObjectsList) {
//                    listOfIds.add(jsonDocument.get("_id"));

                    Integer iEnquiryID = null;
                    Long lEnquiryID = null;
                    String sEnquirtyId = null;
                    Boolean isMigrated = (Boolean) jsonDocument.get("isMigrated");
                    if(isMigrated == null || !isMigrated){
                        if (jsonDocument.get("EnquiryId") instanceof Integer) {
                            iEnquiryID = (Integer) jsonDocument.get("EnquiryId");
                            sEnquirtyId = String.valueOf(iEnquiryID);
                        }
                        if (sEnquirtyId.equals("9999") || sEnquirtyId.equals("999999")) {
                            continue;
                        }
                        if (iEnquiryID != null && iEnquiryID > 0 && !enquiryIds.contains(sEnquirtyId)) {
                            enquiryIds.add(sEnquirtyId);
                            // query to find booked EnquiryId
                            DBObject queryOnEnqId = new BasicDBObject();
                            queryOnEnqId.put("EnquiryID", iEnquiryID);
                            int offset = 0;
                            List<DBObject> dbList = newTempMongoDao.getDBObjects("ReqResLog", queryOnEnqId, offset, chunkSize, null, db);
                            if (dbList.size() > 0) {
                                if (dbList.size() == chunkSize) {
                                    offset = offset + chunkSize;
                                    dbList = getTempOldList(chunkSize, "ReqResLog", queryOnEnqId, offset, db, dbList);
                                }
                            } else {
                                lEnquiryID = Long.valueOf(iEnquiryID);
                                queryOnEnqId.put("EnquiryID", lEnquiryID);
                                dbList = newTempMongoDao.getDBObjects("ReqResLog", queryOnEnqId, offset, chunkSize, null, db);
                                if (dbList.size() > 0) {
                                    if (dbList.size() == chunkSize) {
                                        offset = offset + chunkSize;
                                        dbList = getTempOldList(chunkSize, "ReqResLog", queryOnEnqId, offset, db, dbList);
                                    }
                                } else {
                                    queryOnEnqId.put("EnquiryID", sEnquirtyId);
                                    dbList = newTempMongoDao.getDBObjects("ReqResLog", queryOnEnqId, offset, chunkSize, null, db);
                                    if (dbList.size() > 0) {
                                        if (dbList.size() == chunkSize) {
                                            offset = offset + chunkSize;
                                            dbList = getTempOldList(chunkSize, "ReqResLog", queryOnEnqId, offset, db, dbList);
                                        }
                                    }
                                }
                            }
                            System.out.println("V4 _79_ dbList size is is  " + dbList.size());
                            String jsonString;
                            if (dbList != null && dbList.size() > 0) {
                                try {
                                    jsonString = JSON.serialize(dbList);
                                    // Compress JSON data
                                    byte[] compressedData = compressData(jsonString);
                                    String fileName = sEnquirtyId + "." + "json.gz";
                                    logger.info("Car Old fileName : " + fileName);
                                    String folderName = NStoS3util.S3_FOLDER_PATH_CAR;
                                    String url = s3service.addByteContentWithGzip(compressedData, NStoS3util.BUCKET_NAME_LOGGER_ARCHIVAL, folderName, fileName, "application/gzip");

                                    if (url.equals(AppConstants.FILE_ALREADY_EXIST)) {
                                        try {
//                                        newTempMongoDao.addRows("ReqResLog_Failover", dbList, db);
                                        } catch (Exception e) {
                                            logger.error("Error while adding in motor moveBookLeads , {}", e.getMessage());
                                        }
                                    }
                                    DBObject queryJson = new BasicDBObject("EnquiryId", jsonDocument.get("EnquiryId"));
                                    DBObject setquery = new BasicDBObject("isMigrated", true);
                                    DBObject rowJson = new BasicDBObject("$set", setquery);
                                    newTempMongoDao.updateRow("BookedEnquiry117Temp1", queryJson, rowJson, false, false, db);

                                } catch (Exception e) {
                                    logger.error("Error serializing DBObject: " + e.getMessage());
                                    throw e;
                                }
                            }
                        }
                    }
                }
                enquiryIds = null;
                DBObject queryJson;
                if (motorDeletedId != null && motorDeletedId.size() > 0 && motorDeletedId.getObjectId("_id") != null) {
                    totalMigrated = totalMigrated + dbObjectsList.size();
                    DBObject setquery = new BasicDBObject("movedEnqId", dbObjectsList.get(dbObjectsList.size() - 1).get("EnquiryId"))
                            .append("totalMigrated", totalMigrated)
                            .append("updatedOn", new Date());
                    queryJson = new BasicDBObject("_id", motorDeletedId.getObjectId("_id"));
                    DBObject rowJson = new BasicDBObject("$set", setquery);
                    newArchivalMongoDao.updateRow(NStoS3util.lastUpdatedIdColl, queryJson, rowJson, false, false, NStoS3util.lastUpdatedIdDB);
                }
                queryOnId.put("$gt", dbObjectsList.get(dbObjectsList.size() - 1).get("EnquiryId"));
                queryObject.put("EnquiryId", queryOnId);
                dbObjectsList = newTempMongoDao.getDBObjects("BookedEnquiry117Temp1", queryObject, projection, 0, len, orderObject, db);
            }
        }catch (Exception e) {
            logger.error("Exception caught into moveMotorBookLeads, msg: {}", e.getMessage());
            throw new Exception("Exception cuaght while processing, Car , msg: " + e.getMessage());
        } finally {
            isTempCarMoveS3JobRunning4 = false;
            isTempCarMoveS3JobStop4 = false;
        }
    }

    public void moveOldMotorBookLeadsV5(String dbColl, Integer size, int days, int chunkSize) throws Exception {

        logger.info("Inside moveOldMotorBookLeadsV2 , ns: {}, size: {}, timeCheck: {}, days", dbColl, size, days);
        List<DBObject> dbObjectsList = null;
        try {
            String[] nsArray = dbColl.split("\\.");
            String db = nsArray[0];
            String coll = nsArray[1];
            int len = size;
            long totalMigrated = 0;
            DBObject queryForDeletId = new BasicDBObject();
            queryForDeletId.put("coll", "CarLoggerDB.ReqResLog_240_5");
            BasicDBObject motorDeletedId = newArchivalMongoDao.getDBObjectByDbName(NStoS3util.lastUpdatedIdColl, queryForDeletId, NStoS3util.lastUpdatedIdDB);
            DBObject queryOnId = new BasicDBObject();
            //only provide startId at once we are starting the batch.
            if (motorDeletedId != null && motorDeletedId.size() > 0) {
                queryOnId.put("$gt", motorDeletedId.get("movedEnqId"));
                totalMigrated = (long)motorDeletedId.get("totalMigrated");
            }
            queryOnId.put("$lt", 298265810);//new ObjectId(DateUtil.getStringToDate(DateUtil.getFormatedDate(days, DateUtil.DATE_FORMAT_EE_MMM_dd_hh_mm_ss_z_yyyy), DateUtil.DATE_FORMAT_EE_MMM_dd_hh_mm_ss_z_yyyy)));
            DBObject queryObject = new BasicDBObject();
            queryObject.put("EnquiryId", queryOnId);
            BasicDBObject projection = new BasicDBObject("_id", 1).append("EnquiryId", 1).append("isMigrated",1);
            DBObject orderObject = new BasicDBObject("EnquiryId", 1);
            dbObjectsList = newTempMongoDao.getDBObjects("BookedEnquiry117Temp1", queryObject, projection, 0, len, orderObject, db);
            removeDuplicateEnquiryId(dbObjectsList, "EnquiryId");
            logger.info("isOldCarMoveS3JobStop : {} , isOldCarMoveS3JobRunning: {} ", isTempCarMoveS3JobStop5, isTempCarMoveS3JobRunning5);
            while (dbObjectsList != null && dbObjectsList.size() > 0 && !isTempCarMoveS3JobStop5) {
                logger.info("Car Old migration running");
//                List<Object> listOfIds = new ArrayList<>();
                HashSet<String> enquiryIds = new HashSet<>();
                for (DBObject jsonDocument : dbObjectsList) {
//                    listOfIds.add(jsonDocument.get("_id"));

                    Integer iEnquiryID = null;
                    Long lEnquiryID = null;
                    String sEnquirtyId = null;
                    Boolean isMigrated = (Boolean) jsonDocument.get("isMigrated");
                    if(isMigrated == null || !isMigrated){
                        if (jsonDocument.get("EnquiryId") instanceof Integer) {
                            iEnquiryID = (Integer) jsonDocument.get("EnquiryId");
                            sEnquirtyId = String.valueOf(iEnquiryID);
                        }
                        if (sEnquirtyId.equals("9999") || sEnquirtyId.equals("999999")) {
                            continue;
                        }
                        if (iEnquiryID != null && iEnquiryID > 0 && !enquiryIds.contains(sEnquirtyId)) {
                            enquiryIds.add(sEnquirtyId);
                            // query to find booked EnquiryId
                            DBObject queryOnEnqId = new BasicDBObject();
                            queryOnEnqId.put("EnquiryID", iEnquiryID);
                            int offset = 0;
                            List<DBObject> dbList = newTempMongoDao.getDBObjects("ReqResLog", queryOnEnqId, offset, chunkSize, null, db);
                            if (dbList.size() > 0) {
                                if (dbList.size() == chunkSize) {
                                    offset = offset + chunkSize;
                                    dbList = getTempOldList(chunkSize, "ReqResLog", queryOnEnqId, offset, db, dbList);
                                }
                            } else {
                                lEnquiryID = Long.valueOf(iEnquiryID);
                                queryOnEnqId.put("EnquiryID", lEnquiryID);
                                dbList = newTempMongoDao.getDBObjects("ReqResLog", queryOnEnqId, offset, chunkSize, null, db);
                                if (dbList.size() > 0) {
                                    if (dbList.size() == chunkSize) {
                                        offset = offset + chunkSize;
                                        dbList = getTempOldList(chunkSize, "ReqResLog", queryOnEnqId, offset, db, dbList);
                                    }
                                } else {
                                    queryOnEnqId.put("EnquiryID", sEnquirtyId);
                                    dbList = newTempMongoDao.getDBObjects("ReqResLog", queryOnEnqId, offset, chunkSize, null, db);
                                    if (dbList.size() > 0) {
                                        if (dbList.size() == chunkSize) {
                                            offset = offset + chunkSize;
                                            dbList = getTempOldList(chunkSize, "ReqResLog", queryOnEnqId, offset, db, dbList);
                                        }
                                    }
                                }
                            }
                            System.out.println("V5 _79_ dbList size is is  " + dbList.size());
                            String jsonString;
                            if (dbList != null && dbList.size() > 0) {
                                try {
                                    jsonString = JSON.serialize(dbList);
                                    // Compress JSON data
                                    byte[] compressedData = compressData(jsonString);
                                    String fileName = sEnquirtyId + "." + "json.gz";
                                    logger.info("Car Old fileName : " + fileName);
                                    String folderName = NStoS3util.S3_FOLDER_PATH_CAR;
                                    String url = s3service.addByteContentWithGzip(compressedData, NStoS3util.BUCKET_NAME_LOGGER_ARCHIVAL, folderName, fileName, "application/gzip");

                                    if (url.equals(AppConstants.FILE_ALREADY_EXIST)) {
                                        try {
//                                        newTempMongoDao.addRows("ReqResLog_Failover", dbList, db);
                                        } catch (Exception e) {
                                            logger.error("Error while adding in motor moveBookLeads , {}", e.getMessage());
                                        }
                                    }
                                    DBObject queryJson = new BasicDBObject("EnquiryId", jsonDocument.get("EnquiryId"));
                                    DBObject setquery = new BasicDBObject("isMigrated", true);
                                    DBObject rowJson = new BasicDBObject("$set", setquery);
                                    newTempMongoDao.updateRow("BookedEnquiry117Temp1", queryJson, rowJson, false, false, db);

                                } catch (Exception e) {
                                    logger.error("Error serializing DBObject: " + e.getMessage());
                                    throw e;
                                }
                            }
                        }
                    }
                }
                enquiryIds = null;
                DBObject queryJson;
                if (motorDeletedId != null && motorDeletedId.size() > 0 && motorDeletedId.getObjectId("_id") != null) {
                    totalMigrated = totalMigrated + dbObjectsList.size();
                    DBObject setquery = new BasicDBObject("movedEnqId", dbObjectsList.get(dbObjectsList.size() - 1).get("EnquiryId"))
                            .append("totalMigrated", totalMigrated)
                            .append("updatedOn", new Date());
                    queryJson = new BasicDBObject("_id", motorDeletedId.getObjectId("_id"));
                    DBObject rowJson = new BasicDBObject("$set", setquery);
                    newArchivalMongoDao.updateRow(NStoS3util.lastUpdatedIdColl, queryJson, rowJson, false, false, NStoS3util.lastUpdatedIdDB);
                }
                queryOnId.put("$gt", dbObjectsList.get(dbObjectsList.size() - 1).get("EnquiryId"));
                queryObject.put("EnquiryId", queryOnId);
                dbObjectsList = newTempMongoDao.getDBObjects("BookedEnquiry117Temp1", queryObject, projection, 0, len, orderObject, db);
            }
        }catch (Exception e) {
            logger.error("Exception caught into moveMotorBookLeads, msg: {}", e.getMessage());
            throw new Exception("Exception cuaght while processing, Car , msg: " + e.getMessage());
        } finally {
            isTempCarMoveS3JobRunning5 = false;
            isTempCarMoveS3JobStop5 = false;
        }
    }

    private void removeDuplicateEnquiryId(List<DBObject> dbObjectsList, String uniqueID){
        Iterator<DBObject> itr = dbObjectsList.iterator();
        List<Integer> uniqueEnquiryIdList = new ArrayList<>();
        while (itr.hasNext()) {
            DBObject jsonDocument = itr.next();
            Integer enquiryId = (Integer) jsonDocument.get(uniqueID);
            if (enquiryId!=null && enquiryId > 0 && !uniqueEnquiryIdList.contains(enquiryId)) {
                uniqueEnquiryIdList.add(enquiryId);
            } else {
                itr.remove();
            }
        }
    }
    private List<DBObject> getTempOldList(int chunkSize , String coll, DBObject queryOnrequestId, int offset, String db, List<DBObject> resultList) {

        try {
            List<DBObject> dataList = newTempMongoDao.getDBObjects(coll, queryOnrequestId, offset, chunkSize, null, db);
            while (dataList.size() > 0){
                resultList.addAll(dataList);
                offset += chunkSize;
                dataList = newTempMongoDao.getDBObjects(coll, queryOnrequestId, offset, chunkSize, null, db);
            }
        } catch (Exception e) {
            logger.error("Exception while getList, msg: {}", e.getMessage());
        }
        return resultList;
    }

    public void moveOldTWBookVehicleDetailIdV1(String dbColl, Integer size, int days, int chunkSize) throws Exception {

        logger.info("Inside moveOldTWBookVehicleDetailIdV1 , ns: {}, size: {}, timeCheck: {}, days", dbColl, size, days);
        List<DBObject> dbObjectsList = null;
        try {
            String[] nsArray = dbColl.split("\\.");
            String db = nsArray[0];
            String coll = nsArray[1];
            int len = size;
            long totalMigrated = 0;
            DBObject queryForDeletId = new BasicDBObject();
            queryForDeletId.put("coll", "CarLoggerDB.ReqResTWowheelerLog_240_1");
            BasicDBObject motorDeletedId = newArchivalMongoDao.getDBObjectByDbName(NStoS3util.lastUpdatedIdColl, queryForDeletId, NStoS3util.lastUpdatedIdDB);
            DBObject queryOnId = new BasicDBObject();
            //only provide startId at once we are starting the batch.
            if (motorDeletedId != null && motorDeletedId.size() > 0) {
                queryOnId.put("$gt", motorDeletedId.get("movedVehicleId"));
                totalMigrated = (long)motorDeletedId.get("totalMigrated");
            }
            queryOnId.put("$lt", 60950521);//new ObjectId(DateUtil.getStringToDate(DateUtil.getFormatedDate(days, DateUtil.DATE_FORMAT_EE_MMM_dd_hh_mm_ss_z_yyyy), DateUtil.DATE_FORMAT_EE_MMM_dd_hh_mm_ss_z_yyyy)));
            DBObject queryObject = new BasicDBObject();
            queryObject.put("VehicleDetailId", queryOnId);
            BasicDBObject projection = new BasicDBObject("_id", 1).append("VehicleDetailId", 1).append("isMigrated",1);
            DBObject orderObject = new BasicDBObject("VehicleDetailId", 1);
            dbObjectsList = newTempMongoDao.getDBObjects("BookedEnquiry114Temp1_a", queryObject, projection, 0, len, orderObject, db);
            removeDuplicateEnquiryId(dbObjectsList, "VehicleDetailId");
            logger.info("isTempTWMoveS3JobStop1 : {} , isTempTWMoveS3JobRunning1: {} ", isTempTWMoveS3JobStop1, isTempTWMoveS3JobRunning1);
            while (dbObjectsList != null && dbObjectsList.size() > 0 && !isTempTWMoveS3JobStop1) {
                logger.info("Car Old migration running");
//                List<Object> listOfIds = new ArrayList<>();
                HashSet<String> enquiryIds = new HashSet<>();
                for (DBObject jsonDocument : dbObjectsList) {
//                    listOfIds.add(jsonDocument.get("_id"));

                    Integer iVehicleID = null;
                    Long lVehicleID = null;
                    String sVehicleId = null;
                    Boolean isMigrated = (Boolean) jsonDocument.get("isMigrated");
                    if(isMigrated == null || !isMigrated){
                        if (jsonDocument.get("VehicleDetailId") instanceof Integer) {
                            iVehicleID = (Integer) jsonDocument.get("VehicleDetailId");
                            sVehicleId = String.valueOf(iVehicleID);
                        }
                        if (sVehicleId.equals("9999") || sVehicleId.equals("999999")) {
                            continue;
                        }
                        if (sVehicleId != null && !enquiryIds.contains(sVehicleId)) {
                            enquiryIds.add(sVehicleId);
                            // query to find booked EnquiryId
                            DBObject queryOnEnqId = new BasicDBObject();
                            queryOnEnqId.put("VehicleDetailId", iVehicleID);
                            int offset = 0;
                            List<DBObject> dbList = newTempMongoDao.getDBObjects("ReqResTWowheelerLog", queryOnEnqId, offset, chunkSize, null, db);
                            if (dbList.size() > 0) {
                                if (dbList.size() == chunkSize) {
                                    offset = offset + chunkSize;
                                    dbList = getTempOldList(chunkSize, "ReqResTWowheelerLog", queryOnEnqId, offset, db, dbList);
                                }
                            } else {
                                lVehicleID = Long.valueOf(iVehicleID);
                                queryOnEnqId.put("VehicleDetailId", lVehicleID);
                                dbList = newTempMongoDao.getDBObjects("ReqResTWowheelerLog", queryOnEnqId, offset, chunkSize, null, db);
                                if (dbList.size() > 0) {
                                    if (dbList.size() == chunkSize) {
                                        offset = offset + chunkSize;
                                        dbList = getTempOldList(chunkSize, "ReqResTWowheelerLog", queryOnEnqId, offset, db, dbList);
                                    }
                                } else {
                                    queryOnEnqId.put("VehicleDetailId", sVehicleId);
                                    dbList = newTempMongoDao.getDBObjects("ReqResTWowheelerLog", queryOnEnqId, offset, chunkSize, null, db);
                                    if (dbList.size() > 0) {
                                        if (dbList.size() == chunkSize) {
                                            offset = offset + chunkSize;
                                            dbList = getTempOldList(chunkSize, "ReqResTWowheelerLog", queryOnEnqId, offset, db, dbList);
                                        }
                                    }
                                }
                            }
                            System.out.println("V1 _79 TW_ dbList size is is  " + dbList.size());
                            String jsonString;
                            if (dbList != null && dbList.size() > 0) {
                                try {
                                    jsonString = JSON.serialize(dbList);
                                    // Compress JSON data
                                    byte[] compressedData = compressData(jsonString);
                                    String fileName = "vid_" + sVehicleId + "." + "json.gz";
                                    logger.info("TW V1 Old fileName : " + fileName);
                                    String folderName = NStoS3util.S3_FOLDER_PATH_TW;
                                    String url = s3service.addByteContentWithGzip(compressedData, NStoS3util.BUCKET_NAME_LOGGER_ARCHIVAL, folderName, fileName, "application/gzip");

                                    if (url.equals(AppConstants.FILE_ALREADY_EXIST)) {
                                        try {
//                                        newTempMongoDao.addRows("ReqResLog_Failover", dbList, db);
                                        } catch (Exception e) {
                                            logger.error("Error while adding in motor moveBookLeads , {}", e.getMessage());
                                        }
                                    }
                                    DBObject queryJson = new BasicDBObject("VehicleDetailId", jsonDocument.get("VehicleDetailId"));
                                    DBObject setquery = new BasicDBObject("isMigrated", true);
                                    DBObject rowJson = new BasicDBObject("$set", setquery);
                                    newTempMongoDao.updateRow("BookedEnquiry114Temp1_a", queryJson, rowJson, false, false, db);

                                } catch (Exception e) {
                                    logger.error("Error serializing DBObject: " + e.getMessage());
                                    throw e;
                                }
                            }
                        }
                    }
                }
                enquiryIds = null;
                DBObject queryJson;
                if (motorDeletedId != null && motorDeletedId.size() > 0 && motorDeletedId.getObjectId("_id") != null) {
                    totalMigrated = totalMigrated + dbObjectsList.size();
                    DBObject setquery = new BasicDBObject("movedVehicleId", dbObjectsList.get(dbObjectsList.size() - 1).get("VehicleDetailId"))
                            .append("totalMigrated", totalMigrated)
                            .append("updatedOn", new Date());
                    queryJson = new BasicDBObject("_id", motorDeletedId.getObjectId("_id"));
                    DBObject rowJson = new BasicDBObject("$set", setquery);
                    newArchivalMongoDao.updateRow(NStoS3util.lastUpdatedIdColl, queryJson, rowJson, false, false, NStoS3util.lastUpdatedIdDB);
                }
                queryOnId.put("$gt", dbObjectsList.get(dbObjectsList.size() - 1).get("VehicleDetailId"));
                queryObject.put("VehicleDetailId", queryOnId);
                dbObjectsList = newTempMongoDao.getDBObjects("BookedEnquiry114Temp1_a", queryObject, projection, 0, len, orderObject, db);
            }
        }catch (Exception e) {
            logger.error("Exception caught into moveMotorBookLeads, msg: {}", e.getMessage());
            throw new Exception("Exception cuaght while processing, Car , msg: " + e.getMessage());
        } finally {
            isTempTWMoveS3JobRunning1 = false;
            isTempTWMoveS3JobStop1 = false;
        }
    }

    public void moveOldTWBookVehicleDetailIdV2(String dbColl, Integer size, int days, int chunkSize) throws Exception  {

        logger.info("Inside moveOldTWBookVehicleDetailIdV1 , ns: {}, size: {}, timeCheck: {}, days", dbColl, size, days);
        List<DBObject> dbObjectsList = null;
        try {
            String[] nsArray = dbColl.split("\\.");
            String db = nsArray[0];
            String coll = nsArray[1];
            int len = size;
            long totalMigrated = 0;
            DBObject queryForDeletId = new BasicDBObject();
            queryForDeletId.put("coll", "CarLoggerDB.ReqResTWowheelerLog_240_2");
            BasicDBObject motorDeletedId = newArchivalMongoDao.getDBObjectByDbName(NStoS3util.lastUpdatedIdColl, queryForDeletId, NStoS3util.lastUpdatedIdDB);
            DBObject queryOnId = new BasicDBObject();
            //only provide startId at once we are starting the batch.
            if (motorDeletedId != null && motorDeletedId.size() > 0) {
                queryOnId.put("$gt", motorDeletedId.get("movedVehicleId"));
                totalMigrated = (long)motorDeletedId.get("totalMigrated");
            }
            queryOnId.put("$lt", 64904211);//new ObjectId(DateUtil.getStringToDate(DateUtil.getFormatedDate(days, DateUtil.DATE_FORMAT_EE_MMM_dd_hh_mm_ss_z_yyyy), DateUtil.DATE_FORMAT_EE_MMM_dd_hh_mm_ss_z_yyyy)));
            DBObject queryObject = new BasicDBObject();
            queryObject.put("VehicleDetailId", queryOnId);
            BasicDBObject projection = new BasicDBObject("_id", 1).append("VehicleDetailId", 1).append("isMigrated",1);
            DBObject orderObject = new BasicDBObject("VehicleDetailId", 1);
            dbObjectsList = newTempMongoDao.getDBObjects("BookedEnquiry114Temp1_a", queryObject, projection, 0, len, orderObject, db);
            removeDuplicateEnquiryId(dbObjectsList, "VehicleDetailId");
            logger.info("isTempTWMoveS3JobStop2 : {} , isTempTWMoveS3JobRunning2: {} ", isTempTWMoveS3JobStop2, isTempTWMoveS3JobRunning2);
            while (dbObjectsList != null && dbObjectsList.size() > 0 && !isTempTWMoveS3JobStop2) {
                logger.info("Car Old migration running");
//                List<Object> listOfIds = new ArrayList<>();
                HashSet<String> enquiryIds = new HashSet<>();
                for (DBObject jsonDocument : dbObjectsList) {
//                    listOfIds.add(jsonDocument.get("_id"));

                    Integer iVehicleID = null;
                    Long lVehicleID = null;
                    String sVehicleId = null;
                    Boolean isMigrated = (Boolean) jsonDocument.get("isMigrated");
                    if(isMigrated == null || !isMigrated){
                        if (jsonDocument.get("VehicleDetailId") instanceof Integer) {
                            iVehicleID = (Integer) jsonDocument.get("VehicleDetailId");
                            sVehicleId = String.valueOf(iVehicleID);
                        }
                        if (sVehicleId.equals("9999") || sVehicleId.equals("999999")) {
                            continue;
                        }
                        if (sVehicleId != null && !enquiryIds.contains(sVehicleId)) {
                            enquiryIds.add(sVehicleId);
                            // query to find booked EnquiryId
                            DBObject queryOnEnqId = new BasicDBObject();
                            queryOnEnqId.put("VehicleDetailId", iVehicleID);
                            int offset = 0;
                            List<DBObject> dbList = newTempMongoDao.getDBObjects("ReqResTWowheelerLog", queryOnEnqId, offset, chunkSize, null, db);
                            if (dbList.size() > 0) {
                                if (dbList.size() == chunkSize) {
                                    offset = offset + chunkSize;
                                    dbList = getTempOldList(chunkSize, "ReqResTWowheelerLog", queryOnEnqId, offset, db, dbList);
                                }
                            } else {
                                lVehicleID = Long.valueOf(iVehicleID);
                                queryOnEnqId.put("VehicleDetailId", lVehicleID);
                                dbList = newTempMongoDao.getDBObjects("ReqResTWowheelerLog", queryOnEnqId, offset, chunkSize, null, db);
                                if (dbList.size() > 0) {
                                    if (dbList.size() == chunkSize) {
                                        offset = offset + chunkSize;
                                        dbList = getTempOldList(chunkSize, "ReqResTWowheelerLog", queryOnEnqId, offset, db, dbList);
                                    }
                                } else {
                                    queryOnEnqId.put("VehicleDetailId", sVehicleId);
                                    dbList = newTempMongoDao.getDBObjects("ReqResTWowheelerLog", queryOnEnqId, offset, chunkSize, null, db);
                                    if (dbList.size() > 0) {
                                        if (dbList.size() == chunkSize) {
                                            offset = offset + chunkSize;
                                            dbList = getTempOldList(chunkSize, "ReqResTWowheelerLog", queryOnEnqId, offset, db, dbList);
                                        }
                                    }
                                }
                            }
                            System.out.println("V2 _79 TW_ dbList size is is  " + dbList.size());
                            String jsonString;
                            if (dbList != null && dbList.size() > 0) {
                                try {
                                    jsonString = JSON.serialize(dbList);
                                    // Compress JSON data
                                    byte[] compressedData = compressData(jsonString);
                                    String fileName = "vid_" + sVehicleId + "." + "json.gz";
                                    logger.info("TW V2 Old fileName : " + fileName);
                                    String folderName = NStoS3util.S3_FOLDER_PATH_TW;
                                    String url = s3service.addByteContentWithGzip(compressedData, NStoS3util.BUCKET_NAME_LOGGER_ARCHIVAL, folderName, fileName, "application/gzip");

                                    if (url.equals(AppConstants.FILE_ALREADY_EXIST)) {
                                        try {
//                                        newTempMongoDao.addRows("ReqResLog_Failover", dbList, db);
                                        } catch (Exception e) {
                                            logger.error("Error while adding in motor moveBookLeads , {}", e.getMessage());
                                        }
                                    }
                                    DBObject queryJson = new BasicDBObject("VehicleDetailId", jsonDocument.get("VehicleDetailId"));
                                    DBObject setquery = new BasicDBObject("isMigrated", true);
                                    DBObject rowJson = new BasicDBObject("$set", setquery);
                                    newTempMongoDao.updateRow("BookedEnquiry114Temp1_a", queryJson, rowJson, false, false, db);

                                } catch (Exception e) {
                                    logger.error("Error serializing DBObject: " + e.getMessage());
                                    throw e;
                                }
                            }
                        }
                    }
                }
                enquiryIds = null;
                DBObject queryJson;
                if (motorDeletedId != null && motorDeletedId.size() > 0 && motorDeletedId.getObjectId("_id") != null) {
                    totalMigrated = totalMigrated + dbObjectsList.size();
                    DBObject setquery = new BasicDBObject("movedVehicleId", dbObjectsList.get(dbObjectsList.size() - 1).get("VehicleDetailId"))
                            .append("totalMigrated", totalMigrated)
                            .append("updatedOn", new Date());
                    queryJson = new BasicDBObject("_id", motorDeletedId.getObjectId("_id"));
                    DBObject rowJson = new BasicDBObject("$set", setquery);
                    newArchivalMongoDao.updateRow(NStoS3util.lastUpdatedIdColl, queryJson, rowJson, false, false, NStoS3util.lastUpdatedIdDB);
                }
                queryOnId.put("$gt", dbObjectsList.get(dbObjectsList.size() - 1).get("VehicleDetailId"));
                queryObject.put("VehicleDetailId", queryOnId);
                dbObjectsList = newTempMongoDao.getDBObjects("BookedEnquiry114Temp1_a", queryObject, projection, 0, len, orderObject, db);
            }
        }catch (Exception e) {
            logger.error("Exception caught into moveMotorBookLeads, msg: {}", e.getMessage());
            throw new Exception("Exception cuaght while processing, Car , msg: " + e.getMessage());
        } finally {
            isTempTWMoveS3JobRunning2 = false;
            isTempTWMoveS3JobStop2 = false;
        }
    }

    public void moveOldTWBookVehicleDetailIdV3(String dbColl, Integer size, int days, int chunkSize) throws Exception  {

        logger.info("Inside moveOldTWBookVehicleDetailIdV1 , ns: {}, size: {}, timeCheck: {}, days", dbColl, size, days);
        List<DBObject> dbObjectsList = null;
        try {
            String[] nsArray = dbColl.split("\\.");
            String db = nsArray[0];
            String coll = nsArray[1];
            int len = size;
            long totalMigrated = 0;
            DBObject queryForDeletId = new BasicDBObject();
            queryForDeletId.put("coll", "CarLoggerDB.ReqResTWowheelerLog_240_3");
            BasicDBObject motorDeletedId = newArchivalMongoDao.getDBObjectByDbName(NStoS3util.lastUpdatedIdColl, queryForDeletId, NStoS3util.lastUpdatedIdDB);
            DBObject queryOnId = new BasicDBObject();
            //only provide startId at once we are starting the batch.
            if (motorDeletedId != null && motorDeletedId.size() > 0) {
                queryOnId.put("$gt", motorDeletedId.get("movedVehicleId"));
                totalMigrated = (long)motorDeletedId.get("totalMigrated");
            }
            queryOnId.put("$lt", 68857901);//new ObjectId(DateUtil.getStringToDate(DateUtil.getFormatedDate(days, DateUtil.DATE_FORMAT_EE_MMM_dd_hh_mm_ss_z_yyyy), DateUtil.DATE_FORMAT_EE_MMM_dd_hh_mm_ss_z_yyyy)));
            DBObject queryObject = new BasicDBObject();
            queryObject.put("VehicleDetailId", queryOnId);
            BasicDBObject projection = new BasicDBObject("_id", 1).append("VehicleDetailId", 1).append("isMigrated",1);
            DBObject orderObject = new BasicDBObject("VehicleDetailId", 1);
            dbObjectsList = newTempMongoDao.getDBObjects("BookedEnquiry114Temp1_a", queryObject, projection, 0, len, orderObject, db);
            removeDuplicateEnquiryId(dbObjectsList, "VehicleDetailId");
            logger.info("isTempTWMoveS3JobStop3 : {} , isTempTWMoveS3JobRunning3: {} ", isTempTWMoveS3JobStop3, isTempTWMoveS3JobRunning3);
            while (dbObjectsList != null && dbObjectsList.size() > 0 && !isTempTWMoveS3JobStop3) {
                logger.info("Car Old migration running");
//                List<Object> listOfIds = new ArrayList<>();
                HashSet<String> enquiryIds = new HashSet<>();
                for (DBObject jsonDocument : dbObjectsList) {
//                    listOfIds.add(jsonDocument.get("_id"));

                    Integer iVehicleID = null;
                    Long lVehicleID = null;
                    String sVehicleId = null;
                    Boolean isMigrated = (Boolean) jsonDocument.get("isMigrated");
                    if(isMigrated == null || !isMigrated){
                        if (jsonDocument.get("VehicleDetailId") instanceof Integer) {
                            iVehicleID = (Integer) jsonDocument.get("VehicleDetailId");
                            sVehicleId = String.valueOf(iVehicleID);
                        }
                        if (sVehicleId.equals("9999") || sVehicleId.equals("999999")) {
                            continue;
                        }
                        if (sVehicleId != null && !enquiryIds.contains(sVehicleId)) {
                            enquiryIds.add(sVehicleId);
                            // query to find booked EnquiryId
                            DBObject queryOnEnqId = new BasicDBObject();
                            queryOnEnqId.put("VehicleDetailId", iVehicleID);
                            int offset = 0;
                            List<DBObject> dbList = newTempMongoDao.getDBObjects("ReqResTWowheelerLog", queryOnEnqId, offset, chunkSize, null, db);
                            if (dbList.size() > 0) {
                                if (dbList.size() == chunkSize) {
                                    offset = offset + chunkSize;
                                    dbList = getTempOldList(chunkSize, "ReqResTWowheelerLog", queryOnEnqId, offset, db, dbList);
                                }
                            } else {
                                lVehicleID = Long.valueOf(iVehicleID);
                                queryOnEnqId.put("VehicleDetailId", lVehicleID);
                                dbList = newTempMongoDao.getDBObjects("ReqResTWowheelerLog", queryOnEnqId, offset, chunkSize, null, db);
                                if (dbList.size() > 0) {
                                    if (dbList.size() == chunkSize) {
                                        offset = offset + chunkSize;
                                        dbList = getTempOldList(chunkSize, "ReqResTWowheelerLog", queryOnEnqId, offset, db, dbList);
                                    }
                                } else {
                                    queryOnEnqId.put("VehicleDetailId", sVehicleId);
                                    dbList = newTempMongoDao.getDBObjects("ReqResTWowheelerLog", queryOnEnqId, offset, chunkSize, null, db);
                                    if (dbList.size() > 0) {
                                        if (dbList.size() == chunkSize) {
                                            offset = offset + chunkSize;
                                            dbList = getTempOldList(chunkSize, "ReqResTWowheelerLog", queryOnEnqId, offset, db, dbList);
                                        }
                                    }
                                }
                            }
                            System.out.println("V3 _79 TW_ dbList size is is  " + dbList.size());
                            String jsonString;
                            if (dbList != null && dbList.size() > 0) {
                                try {
                                    jsonString = JSON.serialize(dbList);
                                    // Compress JSON data
                                    byte[] compressedData = compressData(jsonString);
                                    String fileName = "vid_" + sVehicleId + "." + "json.gz";
                                    logger.info("TW V2 Old fileName : " + fileName);
                                    String folderName = NStoS3util.S3_FOLDER_PATH_TW;
                                    String url = s3service.addByteContentWithGzip(compressedData, NStoS3util.BUCKET_NAME_LOGGER_ARCHIVAL, folderName, fileName, "application/gzip");

                                    if (url.equals(AppConstants.FILE_ALREADY_EXIST)) {
                                        try {
//                                        newTempMongoDao.addRows("ReqResLog_Failover", dbList, db);
                                        } catch (Exception e) {
                                            logger.error("Error while adding in motor moveBookLeads , {}", e.getMessage());
                                        }
                                    }
                                    DBObject queryJson = new BasicDBObject("VehicleDetailId", jsonDocument.get("VehicleDetailId"));
                                    DBObject setquery = new BasicDBObject("isMigrated", true);
                                    DBObject rowJson = new BasicDBObject("$set", setquery);
                                    newTempMongoDao.updateRow("BookedEnquiry114Temp1_a", queryJson, rowJson, false, false, db);

                                } catch (Exception e) {
                                    logger.error("Error serializing DBObject: " + e.getMessage());
                                    throw e;
                                }
                            }
                        }
                    }
                }
                enquiryIds = null;
                DBObject queryJson;
                if (motorDeletedId != null && motorDeletedId.size() > 0 && motorDeletedId.getObjectId("_id") != null) {
                    totalMigrated = totalMigrated + dbObjectsList.size();
                    DBObject setquery = new BasicDBObject("movedVehicleId", dbObjectsList.get(dbObjectsList.size() - 1).get("VehicleDetailId"))
                            .append("totalMigrated", totalMigrated)
                            .append("updatedOn", new Date());
                    queryJson = new BasicDBObject("_id", motorDeletedId.getObjectId("_id"));
                    DBObject rowJson = new BasicDBObject("$set", setquery);
                    newArchivalMongoDao.updateRow(NStoS3util.lastUpdatedIdColl, queryJson, rowJson, false, false, NStoS3util.lastUpdatedIdDB);
                }
                queryOnId.put("$gt", dbObjectsList.get(dbObjectsList.size() - 1).get("VehicleDetailId"));
                queryObject.put("VehicleDetailId", queryOnId);
                dbObjectsList = newTempMongoDao.getDBObjects("BookedEnquiry114Temp1_a", queryObject, projection, 0, len, orderObject, db);
            }
        }catch (Exception e) {
            logger.error("Exception caught into moveMotorBookLeads, msg: {}", e.getMessage());
            throw new Exception("Exception cuaght while processing, Car , msg: " + e.getMessage());
        } finally {
            isTempTWMoveS3JobRunning3 = false;
            isTempTWMoveS3JobStop3 = false;
        }
    }

    public void moveRenewalHealthMovePbEventLog(String dbColl, Integer size, int days, Integer chunkSize) {

        logger.info("Inside moveRenewalHealthMovePbEventLog dbColl: {}, days: {}", dbColl, days);
        List<DBObject> dbObjectsList = null;
        try {
            String[] nsArray = dbColl.split("\\.");
            String db = nsArray[0];
            String coll = nsArray[1];;
            int len = size;
            long totalMigrated = 0;
            DBObject queryForDeletId = new BasicDBObject();
            queryForDeletId.put("coll", dbColl);
            BasicDBObject motorDeletedId = newArchivalMongoDao.getDBObjectByDbName(NStoS3util.lastUpdatedIdColl, queryForDeletId, NStoS3util.lastUpdatedIdDB);

            DBObject queryOnId = new BasicDBObject();
            //only provide startId at once we are starting the batch.
            if (motorDeletedId != null && motorDeletedId.size() > 0) {
                queryOnId.put("$gte", motorDeletedId.get("movedId"));
                totalMigrated = (long)motorDeletedId.get("totalMigrated");
            }
            DBObject queryObject = new BasicDBObject();
            DBObject orderObject = new BasicDBObject("_id", 1);
            queryOnId.put("$lt", new ObjectId(DateUtil.getStringToDate(DateUtil.getFormatedDate(days, DateUtil.DATE_FORMAT_EE_MMM_dd_hh_mm_ss_z_yyyy), DateUtil.DATE_FORMAT_EE_MMM_dd_hh_mm_ss_z_yyyy)));
            queryObject.put("_id", queryOnId);
            BasicDBObject projection = new BasicDBObject("_id", 1).append("MatrixLeadID", 1);
            dbObjectsList = newArchivalMongoDao.getDBObjects(coll, queryObject, projection, 0, len, orderObject, db);
            logger.info("isPbEventLogS3MoveDataRunning: {} , isPbEventLogS3MoveDataStop:{}", isPbEventLogS3MoveDataRunning, isPbEventLogS3MoveDataStop);
            while (dbObjectsList != null && dbObjectsList.size() > 0 && !isPbEventLogS3MoveDataStop) {

                List<Object> listOfIds = new ArrayList<>();
                HashSet<String> sMatrixLeadIds = new HashSet<>();
                for (DBObject jsonDocument : dbObjectsList) {
                    listOfIds.add(jsonDocument.get("_id"));
                    Integer matrixLeadId = null;
                    Long lMatrixLeadId = null;
                    String sMatrixLeadId = null;
                    String fileName = null;
                    if(jsonDocument.get("MatrixLeadID") instanceof Integer) {
                        matrixLeadId = (Integer) jsonDocument.get("MatrixLeadID");
                        sMatrixLeadId = String.valueOf(matrixLeadId);
                    }
                    else if(jsonDocument.get("MatrixLeadID") instanceof Long) {
                        lMatrixLeadId = (Long) jsonDocument.get("MatrixLeadID");
                        sMatrixLeadId = String.valueOf(lMatrixLeadId);
                    }
                    else if(jsonDocument.get("MatrixLeadID") instanceof String) {
                        sMatrixLeadId = (String) jsonDocument.get("MatrixLeadID");
                    }
                    List<DBObject> dbList = null;
                    if (sMatrixLeadId != null && sMatrixLeadId.length() > 2 && !sMatrixLeadIds.contains(sMatrixLeadId)) {
                        sMatrixLeadIds.add(sMatrixLeadId);
                        DBObject queryOnLeadId = new BasicDBObject();
                        if (matrixLeadId != null && matrixLeadId > 0) {
                            queryOnLeadId.put("MatrixLeadID", matrixLeadId);
                        } else if (lMatrixLeadId != null && lMatrixLeadId > 0) {
                            queryOnLeadId.put("MatrixLeadID", lMatrixLeadId);
                        } else if (sMatrixLeadId != null && Long.parseLong(sMatrixLeadId) > 0) {
                            queryOnLeadId.put("MatrixLeadID", sMatrixLeadId);
                        }
                        int offset = 0;
                        dbList = newArchivalMongoDao.getDBObjects(coll, queryOnLeadId, offset, chunkSize, null, db);
                        if (dbList.size() == chunkSize) {
                            offset = offset + chunkSize;
                            dbList = getList(chunkSize, coll, queryOnLeadId, offset, db, dbList);
                        }
                        fileName = sMatrixLeadId + "." + "json.gz";
                    }
                    if (dbList != null && dbList.size() > 0) {
                        try {
                            String jsonString = JSON.serialize(dbList);
                            // Compress JSON data
                            byte[] compressedData = compressData(jsonString);
                            //String deCompressedData = decompressData(compressedData);

                            logger.info("PbEventLog fileName : " + fileName);
                            String folderName = NStoS3util.MAP_NS_TO_S3_PATH.get(dbColl);
                            String url = s3service.addByteContentWithGzip(compressedData, NStoS3util.BUCKET_NAME_LOGGER_ARCHIVAL, folderName, fileName, "application/gzip");
                            if (url.equals(AppConstants.FILE_ALREADY_EXIST)) {
                                try {
                                    newArchMongoDao.addRows(coll+"_failOver", dbList, db);
                                } catch (Exception e) {
                                    logger.error("Error while adding in moveRenewalHealthMovePbEventLog , {}", e.getMessage());
                                }
                            }
                            for (DBObject reqIdDoc : dbList) {
                                listOfIds.add(reqIdDoc.get("_id"));
                            }
                        } catch (Exception e) {
                            logger.error("Error serializing DBObject: " + e.getMessage());
                            return;
                        }
                    }
                }
                sMatrixLeadIds = null;
                List<Object> subList = new ArrayList<>();
                int startIndx = 0;
                int lastIndx = 100;
                if (listOfIds != null && listOfIds.size() > 100) {
                    while (startIndx < listOfIds.size()) {
                        if (startIndx + 100 >= listOfIds.size()) {
                            lastIndx = listOfIds.size();
                        }
                        subList = listOfIds.subList(startIndx, lastIndx);
                        DBObject queryFormovedIds = new BasicDBObject();
                        queryFormovedIds.put("_id", new BasicDBObject().append("$in", subList));
                        newArchivalMongoDao.deleteRow(coll, queryFormovedIds, true, db);
                        logger.info("RenewalHealth.PBEventLog deleted Last _Id : {}", subList.get(subList.size() - 1));
                        startIndx = lastIndx;
                        lastIndx = lastIndx + 100;
                        subList = null;
                    }
                } else if (listOfIds != null && listOfIds.size() > 0) {
                    DBObject queryFormovedIds = new BasicDBObject();
                    queryFormovedIds.put("_id", new BasicDBObject().append("$in", listOfIds));
                    newArchivalMongoDao.deleteRow(coll, queryFormovedIds, true, db);
                    logger.info("RenewalHealth.PBEventLog deleted Last _Id : {}", listOfIds.get(listOfIds.size() - 1));
                }

                // nullify after delete all
                listOfIds = null;

                DBObject queryJson;
                if (motorDeletedId != null && motorDeletedId.size() > 0 && motorDeletedId.getObjectId("_id") != null) {
                    totalMigrated = totalMigrated + dbObjectsList.size();
                    DBObject setquery = new BasicDBObject("movedId", dbObjectsList.get(dbObjectsList.size() - 1).get("_id"))
                            .append("totalMigrated", totalMigrated)
                            .append("updatedOn", new Date());
                    queryJson = new BasicDBObject("_id", motorDeletedId.getObjectId("_id"));
                    DBObject rowJson = new BasicDBObject("$set", setquery);
                    newArchivalMongoDao.updateRow(NStoS3util.lastUpdatedIdColl, queryJson, rowJson, false, false, NStoS3util.lastUpdatedIdDB);
                }
                queryOnId.put("$gt", dbObjectsList.get(dbObjectsList.size() - 1).get("_id"));
                queryObject.put("_id", queryOnId);
                dbObjectsList = null;
                dbObjectsList = newArchivalMongoDao.getDBObjects(coll, queryObject, projection, 0, len, orderObject, db);

            }
        }catch (Exception e) {
            logger.error("Exception caught into moveRenewalHealthMovePbEventLog, msg: {}", e.getMessage());
        }finally {
            isPbEventLogS3MoveDataStop = false;
            isPbEventLogS3MoveDataRunning = false;
        }
    }
    private List<DBObject> getList(int chunkSize , String coll, DBObject queryOnrequestId, int offset, String db, List<DBObject> resultList) {

        try {
            List<DBObject> dataList = newArchivalMongoDao.getDBObjects(coll, queryOnrequestId, offset, chunkSize, null, db);
            while (dataList.size() > 0){
                resultList.addAll(dataList);
                offset += chunkSize;
                dataList = newArchivalMongoDao.getDBObjects(coll, queryOnrequestId, offset, chunkSize, null, db);
            }
        } catch (Exception e) {
            logger.error("Exception while getList, msg: {}", e.getMessage());
        }
        return resultList;
    }
}
