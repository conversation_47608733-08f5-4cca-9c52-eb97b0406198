/**
 * 
 */
package com.docMigration.service;

import java.io.IOException;

import com.mongodb.DBObject;

/**
 * <AUTHOR>
 *
 */
public interface DocumentStoreScheduleService {
	
	void migrateFIlesToS3(String coll, String idCount, String startId, String endId, String server) throws IOException, Exception;

	void migrateCollection(String coll, String idCount, String startId) throws Exception;

	void migrateCollectionForChat(String srcColl, String targetColl, String db, String tsStart, String tsEnd, String source,boolean timeCheck, int type);

	void cleanUpForTwoWheeler(Long startingSnNo, Long lastSnNo);

	void deleteNotBookLeadInCarLogger(String startId, String coll, int size, Boolean timeCheck) throws Exception;
	
	void deleteAppNotificationMService(String startId, String coll, int size, Boolean timeCheck) throws Exception;

	void deleteNotBookLeadInTwLogger(String startId, String coll, int size, Boolean timeCheck) throws Exception;

	void cleanUpForTwoWheeler2(String startId, String endId, Integer limit, boolean timeCheck, boolean isNew);

	void markBookedCarLead(String startId, String endId, Integer limit, boolean timeCheck, boolean isNew);

	void cleanUpForCommbox(String dbColl, boolean timeCheck, int years, int type);

	void updateChatUserToken(String db, String source, boolean timeCheck);

	void migrateUaeChatLog(String srcColl, String db, String source,
			boolean timeCheck, String startId);

	void deleteNotBookLeadInCarLoggerV1(String coll, Integer size, Boolean timeCheck, int days, String checkColl,
			String db, DBObject projectObj, DBObject query) throws Exception;

	void migrateUaeJourneyLeadXLog(String dbColl, String source, boolean timeCheck, String startId);
}

