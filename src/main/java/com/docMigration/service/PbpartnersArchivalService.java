/**
 * 
 */
package com.docMigration.service;

import com.mongodb.DBObject;

/**
 * <AUTHOR>
 *
 */

public interface PbpartnersArchivalService {

	void deleteNotBookLeadInCarLogger(String coll, Integer size, Boolean timeCheck, int i, String checkColl,
			String string, DBObject projectObj, DBObject query) throws Exception;

	void moveBookLeads(String coll, Integer size, Boolean timeCheck, String db, int i) throws Exception;

	void deleteNotBookLeadInTwLogger(String startId, String coll, int size, Boolean timeCheck,String db,int days) throws Exception;

	void deleteNotBookLeadInCarLogger(String startId, String coll, int size, Boolean timeCheck,String db,int days) throws Exception;

    void movePbPartnersMotorBookLeads(String dbColl, Integer size, int days, int chunkSize);

	void movePbPartnersTwBookLeads(String dbColl, Integer size, int days, int chunkSize);
}
