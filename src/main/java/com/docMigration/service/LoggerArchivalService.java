/**
 * 
 */
package com.docMigration.service;

import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 *
 */
public interface LoggerArchivalService {

	void moveBookLeads(String coll, Integer size, Boolean timeCheck, String db, int days) throws Exception;

	void twMarkBookedLeads(String startId, String endId, String coll, Boolean timeCheck, String db, int days, int size) throws Exception;

	void cleanReqResLogs(String coll, Integer size, Boolean timeCheck, String db) throws Exception;
	void moveTravelApiLogs(String coll, Integer size, Boolean timeCheck, String db, int i) throws Exception;

	void removeCommV2FilesV2Logs(String coll, Integer size, Boolean timeCheck, String db, int days) throws Exception;

	void movePBCromaV2ToS3(String coll, Integer size, Boolean timeCheck, String db, int i) throws Exception;

	List<Map<String, Object>> getLogFromS3(String folderPath, String fileName) throws IOException;
}

