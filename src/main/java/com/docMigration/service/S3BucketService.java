package com.docMigration.service;

import java.io.File;
import java.io.IOException;
import java.util.List;
import java.util.Map;

import org.springframework.web.multipart.MultipartFile;

import com.amazonaws.AmazonServiceException;
import com.amazonaws.SdkClientException;

public interface S3BucketService {
	String createBucketData(File file, String bucketName, String fileName, String folderName);

	String deleteBucketData(String fileName, String bucketName, String folderName);

	String getfileOrgName(MultipartFile file, String objid, String mimeType, String fileNameSentByBu);
	
	/**
	 * This method is used to upload multipartfile to aws server 
	 * 
	 * @param file
	 * @param bucketName
	 * @param fileName
	 * @param folderName
	 * @return
	 * @throws SdkClientException 
	 * @throws AmazonServiceException 
	 * @throws PGIDataAccessException
	 * @throws IOException
	 */
	String addMultipartContent(MultipartFile file, String bucketName, String fileName, String folderName) throws AmazonServiceException, SdkClientException, IOException;

	String addByteContent(byte[] sd, String bucketName, String folderName, String fileName, String mimeType) throws IOException;

	String addByteContentWithGzip(byte[] fileByte, String bucketName, String folderName, String fileName, String mimeType) throws IOException;

	List<Map<String, Object>> getByteContentWithGzip(String bucketName, String folderName, String fileName) throws IOException;
}
