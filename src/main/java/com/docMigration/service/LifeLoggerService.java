package com.docMigration.service;

public interface LifeLoggerService {
    void moveLifeDocs(String dbColl, Integer size,  boolean timeCheck, int days, int chunkSize);
    void moveLifeDocs(String dbColl, Integer size,  boolean timeCheck, int days, int chunkSize, String lte_id);
    void moveLifeDocsV2(String dbColl, Integer size,  boolean timeCheck, int days, int chunkSize, String lte_id);
    void deleteLifeDocs(String db, Integer size, Boolean timeCheck, int days, int chunkSize);
}
