package com.docMigration.service;

import java.util.concurrent.atomic.AtomicBoolean;
import java.util.*;
import java.text.SimpleDateFormat;

import org.bson.Document;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import com.mongodb.MongoClient;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.model.Filters;
import com.mongodb.client.model.Projections;
import com.mongodb.client.model.UpdateOptions;
import com.docMigration.dao.ChatMongoDao;
import com.docMigration.util.DateUtil;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@Service
public class DeletionService {
    private static final Logger logger = LoggerFactory.getLogger(DeletionService.class);
    private final AtomicBoolean isRunning = new AtomicBoolean(false);
    private final AtomicBoolean cancelRequested = new AtomicBoolean(false);


    private final Set<String> urlSet;

    private static final String DATABASE_NAME = "DocsPoint";
    private static final String COLLECTION_NAME = "DocPushLog";
    private static final String METADATA_DB_NAME = "ArchivalMigrationDB";
    private static final String META_COLLECTION = "deletion_metadata";

    @Autowired
    @Qualifier("commLoggerMongoDao")
    private ChatMongoDao commLoggerMongoDao;

    @Autowired
    @Qualifier("newArchivalMongoDao")
    private ChatMongoDao newArchMongoDao;

    public DeletionService() {
        this.urlSet = new HashSet<>(Arrays.asList(
            "https://vault.policybazaar.com/adv/aadhaar/get",
            "https://vault.policybazaar.com/adv/aadhaar/details"
        ));
    }

    public boolean isJobRunning() {
        return isRunning.get();
    }

    public void requestCancellation() {
        cancelRequested.set(true);
    }

    public void startAsyncDeletion() {
        if (isRunning.compareAndSet(false, true)) {
            Thread deletionThread = new Thread(() -> {
                try {
                    deleteOldData();
                } catch (Exception e) {
                    logger.error("Error during deletion job", e);
                } finally {
                    isRunning.set(false);
                    cancelRequested.set(false);
                }
            });
            deletionThread.setName("Deletion-Thread");
            deletionThread.start();
        }
    }

    private void deleteOldData() throws Exception {
        MongoClient metaMongoClient = newArchMongoDao.getMongoClient();
        MongoClient dbMongoClient = commLoggerMongoDao.getMongoClient();

        MongoCollection<Document> collection = dbMongoClient
            .getDatabase(DATABASE_NAME)
            .getCollection(COLLECTION_NAME);

        MongoCollection<Document> metaCollection = metaMongoClient
            .getDatabase(METADATA_DB_NAME)
            .getCollection(META_COLLECTION);

        Document meta = metaCollection.find(Filters.eq("task", "deleteOldData")).first();
        String startDateStr = meta != null ? meta.getString("startDate") : "2024-11-01T00:00:00";
        
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss");
        sdf.setTimeZone(TimeZone.getTimeZone("UTC"));
        Date startDate = sdf.parse(startDateStr);

        Calendar hourCal = Calendar.getInstance(TimeZone.getTimeZone("UTC"));
        hourCal.setTime(startDate);

        while (hourCal.getTime().before(new Date()) && !cancelRequested.get()) {
            Date hourStart = hourCal.getTime();
            hourCal.add(Calendar.HOUR_OF_DAY, 1);
            Date hourEnd = hourCal.getTime();

            ObjectId startId = DateUtil.convertDateToObjectId(hourStart);
            ObjectId endId = DateUtil.convertDateToObjectId(hourEnd);

            ObjectId lastId = startId; // last processed Id
            boolean moreData = true;

            while (moreData && !cancelRequested.get()) {
                Document query = new Document("_id", new Document("$gt", lastId).append("$lte", endId))
                        .append("$or", Arrays.asList(
                            Filters.eq("MethodName", null),
                            Filters.eq("MethodName", ""),
                            Filters.exists("MethodName", false)
                        ));

                // Bson query = Filters.and(
                //     Filters.gte("_id", startId),
                //     Filters.lt("_id", endId),
                //     Filters.or(
                //         Filters.eq("MethodName", null),
                //         Filters.eq("MethodName", ""),
                //         Filters.exists("MethodName", false)
                //     )
                // );

                // System.out.println("Query: " + query.toBsonDocument(Document.class, MongoClientSettings.getDefaultCodecRegistry()).toJson());

    
                List<Document>docs = new ArrayList<>();
                try {
                    docs = collection.find(query)
                        .projection(Projections.include("_id", "RequestUrl"))
                        .into(new ArrayList<>());

                    System.out.println("docs size: " + docs.size());
                } catch (Exception e) {
                    logger.info("Exception is: " + e.getMessage());
                    try {
                        docs = collection.find(query)
                            .projection(Projections.include("_id", "RequestUrl"))
                            .into(new ArrayList<>());
                    } catch (Exception ex) {
                        logger.info("Exception is: " + ex.getMessage());
                    }
                }
    
                if (docs.isEmpty()) {
                    moreData = false;
                    break;
                }
    
                for (Document doc : docs) {
                    try {
                        lastId = doc.getObjectId("_id");
                        String url = doc.getString("RequestUrl");
                
                        if (url != null && urlSet.contains(url)) {
                            collection.deleteOne(Filters.eq("_id", lastId));
                            logger.info("Deleted doc with ID: {}", lastId.toHexString());
                        }
                    } catch (Exception e) {
                        logger.error("Error processing document with ID: {}", doc.getObjectId("_id"), e);
                    }
                }
    
                logger.info("Processed batch ending at: {}", lastId.toHexString());
            }
    
            try {
                String nextStart = sdf.format(hourEnd);
                metaCollection.updateOne(
                    Filters.eq("task", "deleteOldData"),
                    new Document("$set", new Document("startDate", nextStart)),
                    new UpdateOptions().upsert(true)
                );
                logger.info("Updated metadata for next start time: {}", nextStart);
            } catch (Exception e) {
                logger.error("Failed to update metadata for hour starting: {}", hourStart, e);
            }
    
            logger.info("Completed hour: {}", hourStart);
        }

        logger.info("Deletion job completed.");
    }
}
