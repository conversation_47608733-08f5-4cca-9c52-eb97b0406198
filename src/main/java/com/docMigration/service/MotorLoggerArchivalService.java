package com.docMigration.service;

public interface MotorLoggerArchivalService {
    void moveMotorBookLeads(String dbColl, Integer size, int days, int chunkSize, int startDayForBookingEnqID) throws Exception;

    void moveBookedLeadTwV1(String dbColl, Integer size, int days);

    void moveKycLogs(String dbColl, Integer size, int days, int chunkSize);

    void moveClaimLogs(String dbColl, Integer size, int days, int chunkSize);
    void moveOldMotorBookLeads(String dbColl, Integer size, int days, int chunkSize) throws Exception;

    void moveBookedLeadOldTw(String dbColl, Integer size, int days, int chunkSize) throws Exception;

//    void moveOldMotorKyc(String dbColl, Integer size, int days, int chunkSize) throws Exception;
    void moveTWCoreKycDocsByVehicleId(String dbColl, Integer size, int i, Integer chunkSize);
    void moveMotorKyc(String dbColl, Integer size, int i, int chunkSize) throws Exception;
//    void moveCarEnquiryIdLeadID(int days, int startDay, boolean timeCheck) throws Exception;
    void moveCarCsvToBookedEnquiry();
    void moveTWCsvToBookedEnquiry();
    void moveOldMotorBookLeadsV2(String dbColl, Integer size, int days, int chunkSize) throws Exception;

    void moveOldMotorBookLeadsV3(String dbColl, Integer size, int i, int chunkSize) throws Exception;
    void moveOldMotorBookLeadsV4(String dbColl, Integer size, int i, int chunkSize) throws Exception;
    void moveOldMotorBookLeadsV5(String dbColl, Integer size, int i, int chunkSize) throws Exception;
    void moveOldMotorBookLeadsV6(String dbColl, Integer size, int i, int chunkSize) throws Exception;
    void moveOldMotorBookLeadsV7(String dbColl, Integer size, int i, int chunkSize) throws Exception;

    void moveCarIntegrationLogs(String dbColl, Integer size, int days, Integer twChunkSize);
}
