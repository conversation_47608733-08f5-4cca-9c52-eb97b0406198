/**
 * 
 */
package com.docMigration.bean;

/**
 * <AUTHOR>
 *
 */
public class FieldKey {

	public static final String TIMESTAMP = "ts";
	public static final String TRIP_START_TIME = "st";
	public static final String TRIP_END_TIME = "et";
	public static final String DISTANCE = "dis";
	public static final String CUSTOMER_ID = "cid";
	public static final String MOBILE_NUMBER = "mob";
	public static final String TIMESAMP_AT = "ts_at";
	public static final String OTP = "OTP";
	

/*	public static final String TRIP_DI = "tid";
	public static final String SCORE = "score";
	public static final String AVERAGE_SPEED = "avgs";
	public static final String TIME_TAKEN = "tt";
	public static final String DISTANCE_TRAVELLED = "dt";
	public static final String LAT_LONG = "latlong";
	public static final String DISTANCE_MEASUREMENT_UNIT = "du";
	public static final String SPEED_MEASUREMENT_UNIT = "su";
	public static final String TRIP_STARTED_AT = "TS_AT";
	public static final String TRIP_END_AT = "TE_AT";*/
	public static final String _ID = "_id";
	
	/*public static final String SK_MIN_AVERAGE_SPEED = "minAvgSpeed";
	public static final String SK_MAX_AVERAGE_SPEED = "maxAvgSpeed";
	public static final String SK_AVERAGE_SPEED = "avgSpeed";
	public static final String SK_BADGE = "badge";
	public static final String SK_TOTAL_DISTANCE = "totalDistance";
	public static final String SK_TOTAL_TIME = "totalTime";
	public static final String SK_TOTAL_TRIP = "totalTrip";
	public static final String SK_SCORE = "score";
	public static final String SK_SCORES = "scores";
	public static final String SK_TRIPIDS = "tripids";
	public static final String SK_TRIPID = "tripid";*/
	public static final String SK_START_TIME = "startTime";
	public static final String SK_END_TIME = "endTime";
	public static final String SK_START_DATE = "startDate";
	public static final String SK_END_DATE = "endDate";
	/*public static final String SK_LAST_SIX_MONTHS = "last6Months";
	public static final String SK_LAST_ONE_MONTH = "last1Months";
	public static final String SK_LAST_10_WEEKS = "last10Weeks";*/
	
	public static final String SK_MONTH = "month";
	public static final String SK_YEAR = "year";
	public static final String SK_DATE = "date";
	public static final String SK_COUNTER = "ctr";
	public static final String SK_OK = "ok";
	public static final String SK_MSG = "msg";
	public static final String SK_STATUS = "status";
	public static final String SK_SUCCESS = "success";
	public static final String SK_MSESSAGE = "message";
	public static final String SK_FAILED = "failed";
	public static final String SK_STATUS_CODE = "statusCode";
	public static final String SK_STATUS_MESSAGE = "statusMsg";

	public static final String APP_KEY = "appkey";
	public static final String SECRET_KEY = "secretKey";
	public static final String TEMPLATE_ID = "templateId";
	public static final String TEMPLATE_CODE = "templateCode";
	public static final String PRODUCT_ID = "productId";
	public static final String PROVIDER_ID = "providerId";
	public static final String MSG_RESP = "apiResponse";
	public static final String LEAD_ID = "LeadId";
	
	public static final String IS_SUCCESS = "isSuccess";
	public static final String IS_SKIPPED = "isSkipped";
	public static final String VISIT_ID = "visitId";
	public static final String ENQUIRY_ID = "enquiryId";
	
	public static final String EMAIL_FROM = "from";
	public static final String EMAIL_TO = "to";
	public static final String EMAIL_CC = "cc";
	public static final String EMAIL_BCC = "bcc";
	public static final String EMAIL_SUBJECT = "subject";
	public static final String EMAIL_ATTACHMENT_URLS = "attachment-urls";

	public static final String DOC_URL = "docUrl";

}
