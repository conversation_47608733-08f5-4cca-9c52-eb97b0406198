
/**
 * 
 */
package com.docMigration.bean;

/**
 * <AUTHOR>
 *
 */
public class AppConstants {

	// all the collection name entry are here , for this project
	public static final String COLLECTION_REPO_MISC_REPOSITORY = "docRepoMisc";
	public static final String COLLECTION_REPO_POLICY_REPOSITORY = "docRepoPolicy";
	
	// 
		public static final String COLLECTION_INSURER_PORTAL_DOC = "docRepoInsurerPortal";
		public static final String COLLECTION_AUDIO_DOC = "audioFile";
		public static final String COLLECTION_USER_REVIEW_DOC = "userReviewDoc";
		public static final String COLLECTION_CUSTOMER_PROOF_DOC = "customerProofDoc";
		public static final String COLLECTION_CUSTOMER_PROOF_DOC_AFFLIATE = "customerProofDocAffiliate";
		public static final String COLLECTION_USER_REVIEW_DOC_AFFLIATE = "userReviewDocAffiliate";
		public static final String COLLECTION_DOC_UPLOAD = "docUpload";
		
		public final static String COLLECTION_DOC_REPOSITORY = "docRepo";
		public final static String COLLECTION_PG_DOC_REPOSITORY = "docRepoPG";
		public final static String COLLECTION_DOC_REPOSITORY_AUDIT = "docRepoAudit";
		public static final String COLLECTION_DOC_REPOSITORY_LEAD_MAPPING = "docLeadMapping";


}
