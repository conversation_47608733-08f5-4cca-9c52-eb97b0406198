package com.docMigration.cleanup.entity;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

@Document(collection = "logIdWorked")
public class LogIdWorked {
	
	@Id
	private String id;
	
	@Field("updatedId")
	private String updatedId;
	
	@Field("coll")
	private String coll;

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getUpdatedId() {
		return updatedId;
	}

	public void setUpdatedId(String updatedId) {
		this.updatedId = updatedId;
	}

	public String getColl() {
		return coll;
	}

	public void setColl(String coll) {
		this.coll = coll;
	}

	@Override
	public String toString() {
		return "LogIdWorked [id=" + id + ", updatedId=" + updatedId + ", coll=" + coll + "]";
	}
	

}
