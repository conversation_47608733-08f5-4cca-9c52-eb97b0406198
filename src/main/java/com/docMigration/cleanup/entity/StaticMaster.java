package com.docMigration.cleanup.entity;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

public class StaticMaster {
	private static boolean deleteCarLoggerStop = false;
	
	private static boolean deleteCarLoggerV1Stop = false;
	
	private static boolean moveCarLoggerDataStop = false;

	private static boolean moveS3CarLoggerDataStop = false;

	private static boolean moveS3E2ELoggerDataStop = false;
	private static boolean movePbpS3CarLoggerDataStop = false;
	private static boolean movePbpS3TwLoggerDataStop = false;

	private static boolean moveTLoggerDataStop = false;
	
	private static boolean moveSmeLoggerDataStop = false;
	
	private static boolean deleteTwLoggerStop = false;
	
	private static boolean deleteMserviceStop = false;
	
	private static boolean deleteUaeJourStop = false;
	
	private static int waitForNextDeleteBatchInCar = 1;
	
	private static int waitForNextDeleteBatchInTw = 1;
	private static int waitForNextDeleteE2ELog = 1;

	private static int waitForNextDeleteBatchMService = 1;
	
	private static boolean loggerDeleteJobRunning = false;
	
	private static boolean loggerCarDeleteJobRunning = false;
	
	private static boolean loggerSmeDeleteJobRunning = false;
	
	private static boolean loggerCarMoveJobRunning = false;

	private static boolean loggerClaimMoveS3JobRunning = false;
	private static boolean loggerKYCMoveS3JobRunning = false;

	private static boolean loggerE2EMoveS3JobRunning = false;
	private static boolean pbpLoggerCarMoveS3JobRunning = false;
	private static boolean pbpLoggerTwMoveS3JobRunning = false;


	private static boolean loggerTwoMoveJobRunning = false;
	
	private static boolean loggerSmeMoveJobRunning = false; 
	
	private static boolean loggerCarDeleteV1JobRunning = false;
	
	private static boolean MServiceDeleteJobRunning = false;
	
	private static boolean cleanUpCommBoxrStop = false;
	
	private static boolean moveHomeLoggerDataStop = false;
	
	private static boolean loggerHomeMoveJobRunning = false;
	

	private static boolean deletePbpartnersLogger = false;
	
	private static boolean movePbpartnersLoggerDataStop = false;
	
	private static boolean deletePbpartnersTwLoggerStop = false;
	
	private static boolean deletePbpartnersCarLoggerStop = false;


	private static boolean moveTravelLoggerDataStop = false;
	private static int waitFormoveTravelLoggerData = 1;

	private static boolean deleteIntegrationLoggerStop = false;
	
	private static boolean deleteCommDbLoggerStop = false;
	
	private static boolean deleteDocPointStop = false;
	
	private static boolean isTwMarkBookedLeadsStop = false;

	private static boolean cleanReqResLogStop = false;

	private static boolean syncShortUrl = false;

	private static boolean moveCommV2FileLogsLoggerDataStop = false;
	private static boolean moveCommV2FileLogsRunning = false;
	private static int waitCommV2FileLogsData = 1;
	
	private static List<String> collectionList = Arrays.asList("ReqResTWowheelerLog","ReqResLog", "MServiceDB.App_Notification_Data","LogEntry","LogModel" ,"APILogs", "ApiLog");

	public static boolean isLoggerClaimMoveS3JobRunning() {
		return loggerClaimMoveS3JobRunning;
	}

	public static void setLoggerClaimMoveS3JobRunning(boolean loggerClaimMoveS3JobRunning) {
		StaticMaster.loggerClaimMoveS3JobRunning = loggerClaimMoveS3JobRunning;
	}


	public static boolean isLoggerKYCMoveS3JobRunning() {
		return loggerKYCMoveS3JobRunning;
	}

	public static void setLoggerKYCMoveS3JobRunning(boolean loggerKYCMoveS3JobRunning) {
		StaticMaster.loggerKYCMoveS3JobRunning = loggerKYCMoveS3JobRunning;
	}

	public static boolean isMoveS3E2ELoggerDataStop() {
		return moveS3E2ELoggerDataStop;
	}

	public static void setMoveS3E2ELoggerDataStop(boolean moveS3E2ELoggerDataStop) {
		StaticMaster.moveS3E2ELoggerDataStop = moveS3E2ELoggerDataStop;
	}

	public static boolean isLoggerE2EMoveS3JobRunning() {
		return loggerE2EMoveS3JobRunning;
	}

	public static void setLoggerE2EMoveS3JobRunning(boolean loggerE2EMoveS3JobRunning) {
		StaticMaster.loggerE2EMoveS3JobRunning = loggerE2EMoveS3JobRunning;
	}

	public static boolean isMovePbpS3TwLoggerDataStop() {
		return movePbpS3TwLoggerDataStop;
	}

	public static void setMovePbpS3TwLoggerDataStop(boolean movePbpS3TwLoggerDataStop) {
		StaticMaster.movePbpS3TwLoggerDataStop = movePbpS3TwLoggerDataStop;
	}

	public static boolean isPbpLoggerTwMoveS3JobRunning() {
		return pbpLoggerTwMoveS3JobRunning;
	}

	public static void setPbpLoggerTwMoveS3JobRunning(boolean pbpLoggerTwMoveS3JobRunning) {
		StaticMaster.pbpLoggerTwMoveS3JobRunning = pbpLoggerTwMoveS3JobRunning;
	}

	public static boolean isMovePbpS3CarLoggerDataStop() {
		return movePbpS3CarLoggerDataStop;
	}

	public static void setMovePbpS3CarLoggerDataStop(boolean movePbpS3CarLoggerDataStop) {
		StaticMaster.movePbpS3CarLoggerDataStop = movePbpS3CarLoggerDataStop;
	}

	public static boolean isPbpLoggerCarMoveS3JobRunning() {
		return pbpLoggerCarMoveS3JobRunning;
	}

	public static void setPbpLoggerCarMoveS3JobRunning(boolean pbpLoggerCarMoveS3JobRunning) {
		StaticMaster.pbpLoggerCarMoveS3JobRunning = pbpLoggerCarMoveS3JobRunning;
	}

	public static int getWaitFormoveTravelLoggerData() {
		return waitFormoveTravelLoggerData;
	}

	public static void setWaitFormoveTravelLoggerData(int waitFormoveTravelLoggerData) {
		StaticMaster.waitFormoveTravelLoggerData = waitFormoveTravelLoggerData;
	}

	public static boolean isMoveS3CarLoggerDataStop() {
		return moveS3CarLoggerDataStop;
	}

	public static void setMoveS3CarLoggerDataStop(boolean moveS3CarLoggerDataStop) {
		StaticMaster.moveS3CarLoggerDataStop = moveS3CarLoggerDataStop;
	}

	public static boolean isDeleteUaeJourStop() {
		return deleteUaeJourStop;
	}

	public static void setDeleteUaeJourStop(boolean deleteUaeJourStop) {
		StaticMaster.deleteUaeJourStop = deleteUaeJourStop;
	}

	public static boolean isMoveCommV2FileLogsRunning() {
		return moveCommV2FileLogsRunning;
	}

	public static void setMoveCommV2FileLogsRunning(boolean moveCommV2FileLogsRunning) {
		StaticMaster.moveCommV2FileLogsRunning = moveCommV2FileLogsRunning;
	}

	public static boolean isMoveCommV2FileLogsLoggerDataStop() {
		return moveCommV2FileLogsLoggerDataStop;
	}

	public static void setMoveCommV2FileLogsLoggerDataStop(boolean moveCommV2FileLogsLoggerDataStop) {
		StaticMaster.moveCommV2FileLogsLoggerDataStop = moveCommV2FileLogsLoggerDataStop;
	}

	public static boolean isLoggerCarDeleteJobRunning() {
		return loggerCarDeleteJobRunning;
	}

	public static void setLoggerCarDeleteJobRunning(boolean loggerCarDeleteJobRunning) {
		StaticMaster.loggerCarDeleteJobRunning = loggerCarDeleteJobRunning;
	}

	public static boolean isMServiceDeleteJobRunning() {
		return MServiceDeleteJobRunning;
	}

	public static void setMServiceDeleteJobRunning(boolean mServiceDeleteJobRunning) {
		MServiceDeleteJobRunning = mServiceDeleteJobRunning;
	}

	public static int getWaitForNextDeleteBatchMService() {
		return waitForNextDeleteBatchMService;
	}

	public static void setWaitForNextDeleteBatchMService(int waitForNextDeleteBatchMService) {
		StaticMaster.waitForNextDeleteBatchMService = waitForNextDeleteBatchMService;
	}

	public static boolean isDeleteMserviceStop() {
		return deleteMserviceStop;
	}

	public static void setDeleteMserviceStop(boolean deleteMserviceStop) {
		StaticMaster.deleteMserviceStop = deleteMserviceStop;
	}

	public static List<String> getCollectionList() {
		return collectionList;
	}

	public static boolean isLoggerDeleteJobRunning() {
		return loggerDeleteJobRunning;
	}

	public static void setLoggerDeleteJobRunning(boolean loggerDeleteJobRunning) {
		StaticMaster.loggerDeleteJobRunning = loggerDeleteJobRunning;
	}

	public static int getWaitForNextDeleteBatchInCar() {
		return waitForNextDeleteBatchInCar;
	}

	public static void setWaitForNextDeleteBatchInCar(int waitForNextDeleteBatchInCar) {
		StaticMaster.waitForNextDeleteBatchInCar = waitForNextDeleteBatchInCar;
	}

	public static boolean isDeleteCarLoggerStop() {
		return deleteCarLoggerStop;
	}

	public static void setDeleteCarLoggerStop(boolean deleteCarLoggerStop) {
		StaticMaster.deleteCarLoggerStop = deleteCarLoggerStop;
	}

	public static boolean isDeleteTwLoggerStop() {
		return deleteTwLoggerStop;
	}

	public static void setDeleteTwLoggerStop(boolean deleteTwLoggerStop) {
		StaticMaster.deleteTwLoggerStop = deleteTwLoggerStop;
	}

	public static int getWaitForNextDeleteBatchInTw() {
		return waitForNextDeleteBatchInTw;
	}

	public static void setWaitForNextDeleteBatchInTw(int waitForNextDeleteBatchInTw) {
		StaticMaster.waitForNextDeleteBatchInTw = waitForNextDeleteBatchInTw;
	}

	public static boolean isCleanUpCommBoxrStop() {
		return cleanUpCommBoxrStop;
	}

	public static void setCleanUpCommBoxrStop(boolean cleanUpCommBoxrStop) {
		StaticMaster.cleanUpCommBoxrStop = cleanUpCommBoxrStop;
	}

	public static boolean isDeleteCarLoggerV1Stop() {
		return deleteCarLoggerV1Stop;
	}

	public static void setDeleteCarLoggerV1Stop(boolean deleteCarLoggerV1Stop) {
		StaticMaster.deleteCarLoggerV1Stop = deleteCarLoggerV1Stop;
	}

	public static boolean isLoggerCarDeleteV1JobRunning() {
		return loggerCarDeleteV1JobRunning;
	}

	public static void setLoggerCarDeleteV1JobRunning(boolean loggerCarDeleteV1JobRunning) {
		StaticMaster.loggerCarDeleteV1JobRunning = loggerCarDeleteV1JobRunning;
	}

	public static boolean isMoveCarLoggerDataStop() {
		return moveCarLoggerDataStop;
	}

	public static void setMoveCarLoggerDataStop(boolean moveCarLoggerDataStop) {
		StaticMaster.moveCarLoggerDataStop = moveCarLoggerDataStop;
	}

	public static boolean isLoggerCarMoveJobRunning() {
		return loggerCarMoveJobRunning;
	}

	public static void setLoggerCarMoveJobRunning(boolean loggerCarMoveJobRunning) {
		StaticMaster.loggerCarMoveJobRunning = loggerCarMoveJobRunning;
	}

	public static boolean isMoveTLoggerDataStop() {
		return moveTLoggerDataStop;
	}

	public static void setMoveTLoggerDataStop(boolean moveTLoggerDataStop) {
		StaticMaster.moveTLoggerDataStop = moveTLoggerDataStop;
	}

	public static boolean isLoggerTwoMoveJobRunning() {
		return loggerTwoMoveJobRunning;
	}

	public static void setLoggerTwoMoveJobRunning(boolean loggerTwoMoveJobRunning) {
		StaticMaster.loggerTwoMoveJobRunning = loggerTwoMoveJobRunning;
	}

	public static boolean isLoggerSmeDeleteJobRunning() {
		return loggerSmeDeleteJobRunning;
	}

	public static void setLoggerSmeDeleteJobRunning(boolean loggerSmeDeleteJobRunning) {
		StaticMaster.loggerSmeDeleteJobRunning = loggerSmeDeleteJobRunning;
	}

	public static boolean isMoveSmeLoggerDataStop() {
		return moveSmeLoggerDataStop;
	}

	public static void setMoveSmeLoggerDataStop(boolean moveSmeLoggerDataStop) {
		StaticMaster.moveSmeLoggerDataStop = moveSmeLoggerDataStop;
	}

	public static boolean isLoggerSmeMoveJobRunning() {
		return loggerSmeMoveJobRunning;
	}

	public static void setLoggerSmeMoveJobRunning(boolean loggerSmeMoveJobRunning) {
		StaticMaster.loggerSmeMoveJobRunning = loggerSmeMoveJobRunning;
	}
	
	public static boolean isMoveHomeLoggerDataStop() {
		return moveHomeLoggerDataStop;
	}
	
	public static void setMoveHomeLoggerDataStop(boolean moveHomeLoggerDataStop) {
		StaticMaster.moveHomeLoggerDataStop = moveHomeLoggerDataStop;
	}
	
	public static void setLoggerHomeMoveJobRunning(boolean loggerHomeMoveJobRunning) {
		StaticMaster.loggerHomeMoveJobRunning = loggerHomeMoveJobRunning;
	}
	
	public static boolean isLoggerHomeMoveJobRunning() {
		return loggerHomeMoveJobRunning;
	}

	
	public static boolean isDeletePbpartnerLoggerStop() {
		return deletePbpartnersLogger;
	}

	public static void setDeletePbPartnersLogger(boolean deletePbpartnersLogger) {
		StaticMaster.deletePbpartnersLogger = deletePbpartnersLogger;
	}
	
	public static boolean isMovePbpartnersLoggerDataStop() {
		return movePbpartnersLoggerDataStop;
	}

	public static void setMovePbpartnersLoggerDataStop(boolean movePbpartnersLoggerDataStop) {
		StaticMaster.movePbpartnersLoggerDataStop = movePbpartnersLoggerDataStop;
	}
	public static boolean isDeletePbPartnersCarLoggerStop() {
		return deletePbpartnersCarLoggerStop;
	}

	public static void setDeletePbPartnersCarLoggerStop(boolean deleteCarLoggerStop) {
		StaticMaster.deletePbpartnersCarLoggerStop = deleteCarLoggerStop;
	}

	public static boolean isDeletePbPartnersTwLoggerStop() {
		return deletePbpartnersTwLoggerStop;
	}

	public static void setDeletePbPartnersTwLoggerStop(boolean deletePbpartnersTwLoggerStop) {
		StaticMaster.deletePbpartnersTwLoggerStop = deletePbpartnersTwLoggerStop;
	}

	public static boolean isDeleteIntegrationLoggerStop() {
		return deleteIntegrationLoggerStop;
	}

	public static void setDeleteIntegrationLoggerStop(boolean deleteIntegrationLoggerStop) {
		StaticMaster.deleteIntegrationLoggerStop = deleteIntegrationLoggerStop;
	}


	public static boolean isDeleteDocPointStop() {
		return deleteDocPointStop;
	}

	public static void setDeleteDocPointStop(boolean deleteDocPointStop) {
		StaticMaster.deleteDocPointStop = deleteDocPointStop;
	}

	public static boolean isDeleteCommDbLoggerStop() {
		return deleteCommDbLoggerStop;
	}

	public static void setDeleteCommDbLoggerStop(boolean deleteCommDbLoggerStop) {
		StaticMaster.deleteCommDbLoggerStop = deleteCommDbLoggerStop;
	}
	
	public static boolean isTwMarkBookedLeadsStop() {
		return isTwMarkBookedLeadsStop;

	}
	
	public static boolean isCleanReqResLogStop() {
		return cleanReqResLogStop;
	}

	public static void setCleanReqResLogStop(boolean cleanReqResLogStop) {
		StaticMaster.cleanReqResLogStop = cleanReqResLogStop;
	}

	public static boolean isSyncShortUrlStop() {
		return syncShortUrl;
	}

	public static void setSyncShortUrlStop(boolean syncShortUrl) {
		StaticMaster.syncShortUrl = syncShortUrl;
	}

	public static void setIsTwMarkBookedLeadsStop(boolean isTwMarkBookedLeadsStop) {
		StaticMaster.isTwMarkBookedLeadsStop = isTwMarkBookedLeadsStop;
	}

	public static boolean isMoveTravelLoggerDataStop() {
		return moveTravelLoggerDataStop;
	}

	public static void setMoveTravelLoggerDataStop(boolean moveTravelLoggerDataStop) {
		StaticMaster.moveTravelLoggerDataStop = moveTravelLoggerDataStop;
	}

	public static int getWaitCommV2FileLogsData() {
		return waitCommV2FileLogsData;
	}

	public static void setWaitCommV2FileLogsData(int waitCommV2FileLogsData) {
		StaticMaster.waitCommV2FileLogsData = waitCommV2FileLogsData;
	}

	public static int getWaitForNextDeleteE2ELog() {
		return waitForNextDeleteE2ELog;
	}

	public static void setWaitForNextDeleteE2ELog(int waitForNextDeleteE2ELog) {
		StaticMaster.waitForNextDeleteE2ELog = waitForNextDeleteE2ELog;
	}
}
