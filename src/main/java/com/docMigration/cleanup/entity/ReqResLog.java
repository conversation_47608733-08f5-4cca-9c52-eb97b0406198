package com.docMigration.cleanup.entity;

import java.util.Date;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

@Document(collection = "ReqResLog")
public class ReqResLog {
	
	@Id
	private String id;
	@Field("EnquiryID")
	private long enquiryID;
	@Field("PlanID")
	private long planId;
	@Field("Type")
	private String type;
	@Field("SubType")
	private String subType;
	@Field("XML")
	private String xml;
	@Field("CreatedOn")
	private String createdOn;
	@Field("Date")
	private Date date;

	public long getEnquiryID() {
		return enquiryID;
	}

	public void setEnquiryID(long enquiryID) {
		this.enquiryID = enquiryID;
	}

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public long getPlanId() {
		return planId;
	}

	public void setPlanId(long planId) {
		this.planId = planId;
	}

	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}

	public String getSubType() {
		return subType;
	}

	public void setSubType(String subType) {
		this.subType = subType;
	}

	public String getXml() {
		return xml;
	}

	public void setXml(String xml) {
		this.xml = xml;
	}

	public String getCreatedOn() {
		return createdOn;
	}

	public void setCreatedOn(String createdOn) {
		this.createdOn = createdOn;
	}

	public Date getDate() {
		return date;
	}

	public void setDate(Date date) {
		this.date = date;
	}

}
