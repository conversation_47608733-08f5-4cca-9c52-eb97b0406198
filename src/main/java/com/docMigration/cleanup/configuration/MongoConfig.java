package com.docMigration.cleanup.configuration;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.mongodb.config.AbstractMongoClientConfiguration;
import org.springframework.data.mongodb.repository.config.EnableMongoRepositories;

import com.mongodb.ConnectionString;
import com.mongodb.MongoClientSettings;
import com.mongodb.client.MongoClient;
import com.mongodb.client.MongoClients;
/*
@Configuration
@EnableMongoRepositories("com.docMigration.cleanup.repository")
public class MongoConfig extends AbstractMongoClientConfiguration {


	@Value("${mongo.carlead.host}")
	private String host;
	
	@Value("${mongo.carlead.db}")
	private String dbName;
	
	@Value("${mongo.carlead.port}")
	private int portNo;
	
	@Value("${mongo.carlead.userName}")
	private String userName;
	
	@Value("${mongo.carlead.password}")
	private String password;
	
	@Value("${mongo.connectiontimeout}")
	private int connectionsPerHost;
	
	@Value("${mongo.socketTimeout}")
	private int socketTimeOut;
	
	@Value("${mongo.connectiontimeout}")
	private int connectionTimeOut;
	
	@Value("${mongo.carlead.replicaSetName}")
	private String replicaSetName;
	


    @Override
    protected String getDatabaseName() {
        return dbName;
    }

    @Override
    public MongoClient mongoClient() {
    	StringBuilder builder = new StringBuilder("mongodb://").append(userName).append(":").append(password).append("@")
    			.append(host).append(":").append(portNo).append("/")
    			.append(dbName).append("?socketTimeoutMS=").append(socketTimeOut);
        final ConnectionString connectionString = new ConnectionString(builder.toString());
        final MongoClientSettings mongoClientSettings = MongoClientSettings.builder()
            .applyConnectionString(connectionString)
            .build();
        return MongoClients.create(mongoClientSettings);
    }

}
*/