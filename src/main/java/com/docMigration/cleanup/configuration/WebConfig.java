package com.docMigration.cleanup.configuration;

import javax.sql.DataSource;

import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.jdbc.DataSourceBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;

@Configuration
public class WebConfig {
	 
	 @Bean(name = "dataSource")
	 @ConfigurationProperties(prefix = "spring.datasource")
	 public DataSource dataSource() {
	  return  DataSourceBuilder.create().build();
	 }

	 @Bean(name = "jdbcTemplate")
	 public JdbcTemplate jdbcTemplate(@Qualifier("dataSource") DataSource ds) {
	  return new JdbcTemplate(ds);
	 }
	 
	 @Bean(name = "namedParameterJdbcTemplate")
	 public NamedParameterJdbcTemplate namedParameterJdbcTemplate(@Qualifier("dataSource") DataSource ds) {
	  return new NamedParameterJdbcTemplate(ds);
	 }
	 
	 @Bean(name = "dataSourceRepl")
	 @ConfigurationProperties(prefix = "spring.repldatasource")
	 public DataSource dataSource2() {
	  return  DataSourceBuilder.create().build();
	 }

	 @Bean(name = "jdbcTemplateRepl")
	 public JdbcTemplate jdbcTemplate2(@Qualifier("dataSourceRepl") DataSource ds) {
	  return new JdbcTemplate(ds);
	 }
	@Bean(name = "dataSourceHealth")
	@ConfigurationProperties(prefix = "spring.datasourcehealth")
	public DataSource dataSourceHealth() {
		return  DataSourceBuilder.create().build();
	}
	@Bean(name = "jdbcTemplateHealth")
	public JdbcTemplate jdbcTemplateHealth(@Qualifier("dataSourceHealth") DataSource ds) {
		return new JdbcTemplate(ds);
	}

	@Bean(name = "dataSourceReplHealth")
	@ConfigurationProperties(prefix = "spring.repldatasourcehealth")
	public DataSource dataSourceReplHealth() {
		return  DataSourceBuilder.create().build();
	}
	@Bean(name = "jdbcTemplateReplHealth")
	public JdbcTemplate jdbcTemplateReplHealth(@Qualifier("dataSourceReplHealth") DataSource ds) {
		return new JdbcTemplate(ds);
	}


}
