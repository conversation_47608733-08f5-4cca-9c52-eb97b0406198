package com.docMigration.controller;

import com.docMigration.service.impl.SmeServiceImpl;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

@RestController
public class SmeController {

    private final Logger logger = LoggerFactory.getLogger(BmsLoggerController.class);
    private static final String REQ_HEADER_AUTHKEY = "92kik4i4jn4oi4n3oi4nn4k4l4lk4lknwlk4l4ljk44444klknn4h4jk5k";

    @Autowired
    private SmeServiceImpl smeService;
    @GetMapping(value = "/sme/moveSMELogModelToS3")
    public Map<String, String> moveSMELogModelToS3(@RequestParam(required = false,value = "size") Integer size,
                                                @RequestParam(required = false,value = "stop") Boolean stop,
                                                @RequestParam(value = "ns") String dbColl,
                                                @RequestParam(value="dayBefore", defaultValue = "30") int days,
                                                @RequestParam(value="chunkSize", defaultValue = "100") int chunkSize,
                                                @RequestHeader(value = "authKey") String authKey){

        logger.info("Inside moveSMELogModelToS3 ");
        Map<String, String> result = new HashMap<String, String>();
        // check authKey if not matched then return.
        if(!authKey.equals(REQ_HEADER_AUTHKEY)){
            result.put("error", "Auth key is not valid..");
            return result;
        }
        String[] ns = {"SmeLog.LogModel"};
        if(!Arrays.asList(ns).contains(dbColl)){
            result.put("error", "ns is not whitelisted to proceed further...");
            return result;
        }
        try {

            if(stop && SmeServiceImpl.isMoveSMELogModelJobRunning) {
                SmeServiceImpl.isMoveSMELogModelJobStop = stop;
            }else{
                SmeServiceImpl.isMoveSMELogModelJobStop = false;
            }
            if(size>1000) {
                size = 1000;
            }
            if(days < 90){
                days = 90;
            }
            if(chunkSize>500) {
                chunkSize = 500;
            }
            if (!SmeServiceImpl.isMoveSMELogModelJobRunning) {
                SmeServiceImpl.isMoveSMELogModelJobRunning = true;
                SmeServiceImpl.isMoveSMELogModelJobStop = false;
                smeService.moveSMELogModelToS3(dbColl, size,-days, chunkSize);
                result.put("OK", "1");
                result.put("SK_MSG", "collection migrated");
            }else if(SmeServiceImpl.isMoveSMELogModelJobRunning){
                logger.info("Could not initiate moveSMELogModelToS3 at this time as it is running {} , stop {}    ", new Date(), stop);
                logger.info(" ns {} , size {} ",dbColl, size );
                result.put("OK", "0");
                result.put("SK_MSG", "not initiate moveSMELogModelToS3, " + new Date());
            }
        } catch (Exception e) {
            result.put("OK", "0");
            result.put("SK_MSG", "Error in moveSMELogModelToS3 : ," + e.getMessage());
            logger.error("Error in moveSMELogModelToS3 , {}", e.getMessage());
        }
        logger.info("Exiting moveSMELogModelToS3 ");
        return result;
    }

    @GetMapping(value = "/sme/moveSMELogEntryToS3")
    public Map<String, String> moveSMELogEntryToS3(@RequestParam(required = false,value = "size") Integer size,
                                                   @RequestParam(required = false,value = "stop") Boolean stop,
                                                   @RequestParam(value = "ns") String dbColl,
                                                   @RequestParam(value="dayBefore", defaultValue = "30") int days,
                                                   @RequestParam(value="chunkSize", defaultValue = "100") int chunkSize,
                                                   @RequestHeader(value = "authKey") String authKey){

        logger.info("Inside moveSMELogEntryToS3 ");
        Map<String, String> result = new HashMap<String, String>();
        // check authKey if not matched then return.
        if(!authKey.equals(REQ_HEADER_AUTHKEY)){
            result.put("error", "Auth key is not valid..");
            return result;
        }
        String[] ns = {"SmeLog.LogEntry"};
        if(!Arrays.asList(ns).contains(dbColl)){
            result.put("error", "ns is not whitelisted to proceed further...");
            return result;
        }
        try {

            if(stop && SmeServiceImpl.isMoveSMELogEntryJobRunning) {
                SmeServiceImpl.isMoveSMELogEntryJobStop = stop;
            }else{
                SmeServiceImpl.isMoveSMELogEntryJobStop = false;
            }
            if(size>1000) {
                size = 1000;
            }
            if(days < 90){
                days = 90;
            }
            if(chunkSize>500) {
                chunkSize = 500;
            }
            if (!SmeServiceImpl.isMoveSMELogEntryJobRunning) {
                SmeServiceImpl.isMoveSMELogEntryJobRunning = true;
                SmeServiceImpl.isMoveSMELogEntryJobStop = false;
                smeService.moveSMELogEntryToS3(dbColl, size,-days, chunkSize);
                result.put("OK", "1");
                result.put("SK_MSG", "collection migrated");
            }else if(SmeServiceImpl.isMoveSMELogEntryJobRunning){
                logger.info("Could not initiate moveSMELogEntryToS3 at this time as it is running {} , stop {}    ", new Date(), stop);
                logger.info(" ns {} , size {} ",dbColl, size );
                result.put("OK", "0");
                result.put("SK_MSG", "not initiate moveSMELogEntryToS3, " + new Date());
            }
        } catch (Exception e) {
            result.put("OK", "0");
            result.put("SK_MSG", "Error in moveSMELogEntryToS3 : ," + e.getMessage());
            logger.error("Error in moveSMELogEntryToS3 , {}", e.getMessage());
        }
        logger.info("Exiting moveSMELogEntryToS3 ");
        return result;
    }

    @GetMapping(value = "/sme_nonEB/moveSMELogEntryToS3")
    public Map<String, String> moveSMENonEBLogEntryToS3(@RequestParam(required = false,value = "size") Integer size,
                                                   @RequestParam(required = false,value = "stop") Boolean stop,
                                                   @RequestParam(value = "ns") String dbColl,
                                                   @RequestParam(value="dayBefore", defaultValue = "30") int days,
                                                   @RequestParam(value="chunkSize", defaultValue = "100") int chunkSize,
                                                   @RequestHeader(value = "authKey") String authKey){

        logger.info("Inside moveSMENonEBLogEntryToS3 ");
        Map<String, String> result = new HashMap<String, String>();
        // check authKey if not matched then return.
        if(!authKey.equals(REQ_HEADER_AUTHKEY)){
            result.put("error", "Auth key is not valid..");
            return result;
        }
        String[] ns = {"SmeLog.LogEntry"};
        if(!Arrays.asList(ns).contains(dbColl)){
            result.put("error", "ns is not whitelisted to proceed further...");
            return result;
        }
        try {

            if(stop && SmeServiceImpl.isMoveSMENonEBLogEntryJobRunning) {
                SmeServiceImpl.isMoveSMENonEBLogEntryJobStop = stop;
            }else{
                SmeServiceImpl.isMoveSMENonEBLogEntryJobStop = false;
            }
            if(size>1000) {
                size = 1000;
            }
            if(days < 90){
                days = 90;
            }
            if(chunkSize>500) {
                chunkSize = 500;
            }
            if (!SmeServiceImpl.isMoveSMENonEBLogEntryJobRunning) {
                SmeServiceImpl.isMoveSMENonEBLogEntryJobRunning = true;
                SmeServiceImpl.isMoveSMENonEBLogEntryJobStop = false;
                smeService.moveSMENonEBLogEntryToS3(dbColl, size,-days, chunkSize);
                result.put("OK", "1");
                result.put("SK_MSG", "collection migrated");
            }else if(SmeServiceImpl.isMoveSMENonEBLogEntryJobRunning){
                logger.info("Could not initiate moveSMELogEntryToS3 at this time as it is running {} , stop {}    ", new Date(), stop);
                logger.info(" ns {} , size {} ",dbColl, size );
                result.put("OK", "0");
                result.put("SK_MSG", "not initiate moveSMENonEBLogEntryToS3, " + new Date());
            }
        } catch (Exception e) {
            result.put("OK", "0");
            result.put("SK_MSG", "Error in moveSMENonEBLogEntryToS3 : ," + e.getMessage());
            logger.error("Error in moveSMENonEBLogEntryToS3 , {}", e.getMessage());
        }
        logger.info("Exiting moveSMENonEBLogEntryToS3 ");
        return result;
    }
    @GetMapping(value = "/sme_corpConnect/moveCorpApplicationLogsEntryToS3")
    public Map<String, String> moveCorpApplicationLogsEntryToS3(@RequestParam(required = false,value = "size") Integer size,
                                                        @RequestParam(required = false,value = "stop") Boolean stop,
                                                        @RequestParam(value = "ns") String dbColl,
                                                        @RequestParam(value="dayBefore", defaultValue = "30") int days,
                                                        @RequestParam(value="chunkSize", defaultValue = "100") int chunkSize,
                                                        @RequestHeader(value = "authKey") String authKey){

        logger.info("Inside moveCorpApplicationLogsEntryToS3 ");
        Map<String, String> result = new HashMap<String, String>();
        // check authKey if not matched then return.
        if(!authKey.equals(REQ_HEADER_AUTHKEY)){
            result.put("error", "Auth key is not valid..");
            return result;
        }
        String[] ns = {"CorpConnectLog.CorpApplicationLogs"};
        if(!Arrays.asList(ns).contains(dbColl)){
            result.put("error", "ns is not whitelisted to proceed further...");
            return result;
        }
        try {

            if(stop && SmeServiceImpl.isMoveSMECorpAppLogEntryJobRunning) {
                SmeServiceImpl.isMoveSMECorpAppLogEntryJobStop = stop;
            }else{
                SmeServiceImpl.isMoveSMECorpAppLogEntryJobStop = false;
            }
            if(size>1000) {
                size = 1000;
            }
            if(days < 90){
                days = 90;
            }
            if(chunkSize>500) {
                chunkSize = 500;
            }
            if (!SmeServiceImpl.isMoveSMECorpAppLogEntryJobRunning) {
                SmeServiceImpl.isMoveSMECorpAppLogEntryJobRunning = true;
                SmeServiceImpl.isMoveSMECorpAppLogEntryJobStop = false;
                smeService.moveCorpApplicationLogsEntryToS3(dbColl, size,-days, chunkSize);
                result.put("OK", "1");
                result.put("SK_MSG", "collection migrated");
            }else if(SmeServiceImpl.isMoveSMECorpAppLogEntryJobRunning){
                logger.info("Could not initiate moveCorpConnectLogsEntryToS3 at this time as it is running {} , stop {}    ", new Date(), stop);
                logger.info(" ns {} , size {} ",dbColl, size );
                result.put("OK", "0");
                result.put("SK_MSG", "not initiate moveCorpApplicationLogsEntryToS3, " + new Date());
            }
        } catch (Exception e) {
            result.put("OK", "0");
            result.put("SK_MSG", "Error in moveCorpApplicationLogsEntryToS3 : ," + e.getMessage());
            logger.error("Error in moveCorpApplicationLogsEntryToS3 , {}", e.getMessage());
        }
        logger.info("Exiting moveCorpApplicationLogsEntryToS3 ");
        return result;
    }
    @GetMapping(value = "/sme_corpConnect/moveMongoLogEntryToS3")
    public Map<String, String> moveMongoLogEntryToS3(@RequestParam(required = false,value = "size") Integer size,
                                                                @RequestParam(required = false,value = "stop") Boolean stop,
                                                                @RequestParam(value = "ns") String dbColl,
                                                                @RequestParam(value="dayBefore", defaultValue = "30") int days,
                                                                @RequestParam(value="chunkSize", defaultValue = "100") int chunkSize,
                                                                @RequestHeader(value = "authKey") String authKey){

        logger.info("Inside moveMongoLogEntryToS3 ");
        Map<String, String> result = new HashMap<String, String>();
        // check authKey if not matched then return.
        if(!authKey.equals(REQ_HEADER_AUTHKEY)){
            result.put("error", "Auth key is not valid..");
            return result;
        }
        String[] ns = {"CorpConnectLog.MongoLogEntry"};
        if(!Arrays.asList(ns).contains(dbColl)){
            result.put("error", "ns is not whitelisted to proceed further...");
            return result;
        }
        try {

            if(stop && SmeServiceImpl.isMoveSMEMongoLogEntryJobRunning) {
                SmeServiceImpl.isMoveSMEMongoLogEntryJobStop = stop;
            }else{
                SmeServiceImpl.isMoveSMEMongoLogEntryJobStop = false;
            }
            if(size>1000) {
                size = 1000;
            }
            if(days < 90){
                days = 90;
            }
            if(chunkSize>500) {
                chunkSize = 500;
            }
            if (!SmeServiceImpl.isMoveSMEMongoLogEntryJobRunning) {
                SmeServiceImpl.isMoveSMEMongoLogEntryJobRunning = true;
                SmeServiceImpl.isMoveSMEMongoLogEntryJobStop = false;
                smeService.moveMongoLogEntryToS3(dbColl, size,-days, chunkSize);
                result.put("OK", "1");
                result.put("SK_MSG", "collection migrated");
            }else if(SmeServiceImpl.isMoveSMEMongoLogEntryJobRunning){
                logger.info("Could not initiate moveMongoLogEntryToS3 at this time as it is running {} , stop {}    ", new Date(), stop);
                logger.info(" ns {} , size {} ",dbColl, size );
                result.put("OK", "0");
                result.put("SK_MSG", "not initiate moveMongoLogEntryToS3, " + new Date());
            }
        } catch (Exception e) {
            result.put("OK", "0");
            result.put("SK_MSG", "Error in moveMongoLogEntryToS3 : ," + e.getMessage());
            logger.error("Error in moveMongoLogEntryToS3 , {}", e.getMessage());
        }
        logger.info("Exiting moveMongoLogEntryToS3 ");
        return result;
    }
}
