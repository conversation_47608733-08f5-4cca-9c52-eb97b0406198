package com.docMigration.controller;

import com.docMigration.dao.ChatMongoDao;
import com.docMigration.service.impl.TravelLoggerArchivalServiceImpl;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.stream.Stream;

@RestController
public class TravelLoggerController {
    private final Logger logger = LoggerFactory.getLogger(HealthLoggerController.class);
    private static final String REQ_HEADER_AUTHKEY = "92kik4i4jn4oi4n3oi4nn4k4l4lk4lknwlk4l4ljk44444klknn4h4jk5k";

    @Autowired
    private TravelLoggerArchivalServiceImpl travelLoggerArchivalService;

    @GetMapping(value = "/travel/moveBookedDocsByLeadId")
    public Map<String, String> moveBookedDocsByLeadIdFOrTravel(@RequestParam(required = false,value = "size") Integer size,
                                                         @RequestParam(required = false,value = "waitInSec") Integer waitInSec,
                                                         @RequestParam(required = false,value = "stop", defaultValue = "false") boolean stop,
                                                         @RequestParam(value = "ns") String dbColl,
                                                         @RequestParam(value="dayBefore") int days,
                                                         @RequestParam(value="chunkSize") int chunkSize,
                                                         @RequestParam(required = false,value = "timeCheck") Boolean timeCheck,
                                                         @RequestHeader String authKey){
        logger.info("Inside moveBookedDocsByLeadIdFOrTravel ");
        Map<String, String> responseMap = new HashMap<String, String>();

        try {
            // check authKey if not matched then return.
            if(!authKey.equals(REQ_HEADER_AUTHKEY)){
                responseMap.put("error", "Auth key is not valid..");
                return responseMap;
            }
            String[] ns = {"travelDb.ApiLog"};
            if(!Arrays.asList(ns).contains(dbColl)){
                responseMap.put("error", "ns is not whitelisted to proceed further...");
                return responseMap;
            }

            if(stop && TravelLoggerArchivalServiceImpl.isTravelLogS3JobRunning ) {
                TravelLoggerArchivalServiceImpl.isTravelLogS3JobStop = true;
            } else {
                TravelLoggerArchivalServiceImpl.isTravelLogS3JobStop = false;
            }

            if(size>1000) {
                size = 1000;
            }
            if(chunkSize > 1000) {
                chunkSize = 1000;
            }
            if (!TravelLoggerArchivalServiceImpl.isTravelLogS3JobRunning) {
                TravelLoggerArchivalServiceImpl.isTravelLogS3JobRunning = true;
                TravelLoggerArchivalServiceImpl.isTravelLogS3JobStop = false;
                travelLoggerArchivalService.moveTravelBookLeads(dbColl, size, timeCheck,-days, chunkSize);
                responseMap.put("OK", "1");
                responseMap.put("SK_MSG", "collection migrated");
            }
            else if (TravelLoggerArchivalServiceImpl.isTravelLogS3JobRunning) {
                logger.info(" Travel Job moveBookedDocsByLeadIdFOrTravel migration already running: {} , stop {} , waitInSec {}", new Date(), stop, waitInSec);
                logger.info(" coll {} , startId, {} , size {} ",ns, size );
                responseMap.put("OK", "1");
                responseMap.put("SK_MSG", "Could not initiate migration at this time , " + new Date());
            }
        } catch (Exception e) {
            responseMap.put("OK", "0");
            responseMap.put("SK_MSG", "Error in moveBookedDocsByLeadIdFOrTravel : ," + e.getMessage());
            logger.error("Error in moveBookedDocsByLeadIdFOrTravel , {}", e.getMessage());
        }
        logger.info("Exiting moveBookedDocsByLeadIdFOrTravel ");
        return responseMap;
    }

    @GetMapping(value = "/travel/moveOldBookedDocsByLeadId")
    public Map<String, String> moveOldBookedDocsByLeadId(@RequestParam(required = false,value = "size") Integer size,
                                                               @RequestParam(required = false,value = "stop", defaultValue = "false") boolean stop,
                                                               @RequestParam(value = "ns") String dbColl,
                                                               @RequestParam(value="dayBefore") int days,
                                                               @RequestParam(value="chunkSize") int chunkSize,
                                                               @RequestHeader String authKey){
        logger.info("Inside moveOldBookedDocsByLeadId ");
        Map<String, String> responseMap = new HashMap<String, String>();

        try {
            // check authKey if not matched then return.
            if(!authKey.equals(REQ_HEADER_AUTHKEY)){
                responseMap.put("error", "Auth key is not valid..");
                return responseMap;
            }
            String[] ns = {"travelDb.ApiLog_173"};
            if(!Arrays.asList(ns).contains(dbColl)){
                responseMap.put("error", "ns is not whitelisted to proceed further...");
                return responseMap;
            }

            if(stop && TravelLoggerArchivalServiceImpl.isOldTravelLogS3JobRunning ) {
                TravelLoggerArchivalServiceImpl.isOldTravelLogS3JobStop = true;
            } else {
                TravelLoggerArchivalServiceImpl.isOldTravelLogS3JobStop = false;
            }

            if(size>1000) {
                size = 1000;
            }
            if(chunkSize > 1000) {
                chunkSize = 1000;
            }
            if (!TravelLoggerArchivalServiceImpl.isOldTravelLogS3JobRunning) {
                TravelLoggerArchivalServiceImpl.isOldTravelLogS3JobRunning = true;
                TravelLoggerArchivalServiceImpl.isOldTravelLogS3JobStop = false;
                travelLoggerArchivalService.moveOldTravelBookLeads(dbColl, size,-days, chunkSize);
                responseMap.put("OK", "1");
                responseMap.put("SK_MSG", "collection migrated");
            }
            else if (TravelLoggerArchivalServiceImpl.isOldTravelLogS3JobRunning) {
                logger.info(" Travel Job moveOldBookedDocsByLeadIdForTravel migration already running: {} , stop {} , waitInSec {}", new Date(), stop);
                logger.info(" coll {} , startId, {} , size {} ",ns, size );
                responseMap.put("OK", "1");
                responseMap.put("SK_MSG", "Could not initiate migration at this time , " + new Date());
            }
        } catch (Exception e) {
            responseMap.put("OK", "0");
            responseMap.put("SK_MSG", "Error in moveOldBookedDocsByLeadIdForTravel : ," + e.getMessage());
            logger.error("Error in moveOldBookedDocsByLeadIdForTravel , {}", e.getMessage());
        }
        logger.info("Exiting moveOldBookedDocsByLeadIdForTravel ");
        return responseMap;
    }
}
