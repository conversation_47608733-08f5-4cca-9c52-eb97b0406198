package com.docMigration.controller;

import com.docMigration.service.impl.BmsLoggerServiceImplNew;
import com.docMigration.util.DateUtil;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import java.io.File;

@RestController
@RequestMapping("/bms-new")
public class BmsLoggerControllerNew {
    private final Logger logger = LoggerFactory.getLogger(BmsLoggerControllerNew.class);
    private static final String REQ_HEADER_AUTHKEY = "92kik4i4jn4oi4n3oi4nn4k4l4lk4lknwlk4l4ljk44444klknn4h4jk5k";
    
    @Autowired
    BmsLoggerServiceImplNew bmsLoggerService;

    @GetMapping(value = "/moveNewDocPushLogsToS3")
    public Map<String, String> moveDocPushLogsToS3(@RequestParam(required = false, value = "size") Integer size,
                                                    @RequestParam(required = false, value = "stop") Boolean stop,
                                                    @RequestParam(value = "ns") String dbColl,
                                                    @RequestParam(value = "dayBefore", defaultValue = "30") int days,
                                                    @RequestParam(value = "chunkSize", defaultValue = "100") int chunkSize,
                                                    @RequestParam(value = "authKey") String authKey,
                                                    @RequestParam(value = "movedIdsCollection", defaultValue = "movedIdsLoggerNew") String movedIdsCollection) {

        logger.info("Inside moveDocPushLogsToS3 ");
        Map<String, String> result = new HashMap<>();
        
        if(!authKey.equals(REQ_HEADER_AUTHKEY)){
            result.put("error", "Auth key is not valid..");
            return result;
        }

        List<String> allowedNamespaces = Arrays.asList("DocsPoint.DocPushLog", "CommBox_Logger-DocsPoint.DocPushLog");
        if (!allowedNamespaces.contains(dbColl)) {
            result.put("error", "ns is not whitelisted to proceed further...");
            return result;
        }

        File lockFile = new File("/tmp/moveNewDocPushLogs_" + movedIdsCollection + ".lock");

        try {
            if (Boolean.TRUE.equals(stop)) {
                BmsLoggerServiceImplNew.isMoveDocPushLogsJobStop = true;
                if (lockFile.exists()) {
                    boolean deleted = lockFile.delete();
                    result.put("OK", deleted ? "1" : "0");
                    result.put("SK_MSG", deleted ? "Stopped job and deleting lock file." : "Failed to delete lock file.");
                } else {
                    result.put("OK", "0");
                    result.put("SK_MSG", "No running job found to stop.");
                }
                return result;
            }

            // Check if lock file already exists
            if (lockFile.exists()) {
                logger.info("Lock file exists for {}. Job already running.", movedIdsCollection);
                result.put("OK", "0");
                result.put("SK_MSG", "Job already running for " + movedIdsCollection);
                return result;
            }

            // Create lock file
            boolean created = lockFile.createNewFile();
            if (!created) {
                result.put("OK", "0");
                result.put("SK_MSG", "Failed to create lock file.");
                return result;
            }

            logger.info("Starting job for {} with lock file: {}", movedIdsCollection, lockFile.getAbsolutePath());


            // Limit parameters
            if (size != null && size > 1000) size = 1000;
            if (days < 90) days = 90;
            if (chunkSize > 500) chunkSize = 500;

            // Reset flags before job starts
            BmsLoggerServiceImplNew.isMoveDocPushLogsJobStop = false;

            // Define final local copies
            final Integer finalSize = size;
            final int finalDays = days;
            final int finalChunkSize = chunkSize;
            final String finalDbColl = dbColl;
            final String finalMovedIdsCollection = movedIdsCollection;

            new Thread(() -> {
                try {
                    bmsLoggerService.moveNewDocPushLogsToS3V1(finalDbColl, finalSize, -finalDays, finalChunkSize, finalMovedIdsCollection);
                } catch (Exception e) {
                    logger.error("Error while processing {}: {}", movedIdsCollection, e.getMessage());
                } finally {

                    BmsLoggerServiceImplNew.isMoveDocPushLogsJobStop = false;
                    
                    lockFile.delete();
                }
            }).start();
    
            result.put("OK", "1");
            result.put("SK_MSG", "Job started for " + movedIdsCollection);
        } catch (Exception e) {
            result.put("OK", "0");
            result.put("SK_MSG", "Error in moveDocPushLogsToS3 : ," + e.getMessage());
            logger.error("Error in moveDocPushLogsToS3 , {}", e.getMessage());
        }
        logger.info("Exiting moveDocPushLogsToS3 ");
        return result;
    }

    @GetMapping(value = "/deleteOldCommV2FileLogs")
    public Map<String, String> deleteOldCommV2FileLogs(@RequestParam(required = false, value = "size", defaultValue = "1000") Integer size,
                                                    @RequestParam(required = false, value = "stop") Boolean stop,
                                                    @RequestParam(value = "ns") String dbColl,
                                                    @RequestParam(value = "dayBefore", defaultValue = "30") int days,
                                                    @RequestHeader(value = "authKey") String authKey) {
        logger.info("Inside deleteOldCommV2FileLogs");
        Map<String, String> result = new HashMap<>();

        if (!authKey.equals(REQ_HEADER_AUTHKEY)) {
            result.put("error", "Auth key is not valid.");
            return result;
        }

        List<String> allowedNamespaces = Arrays.asList("communicationDB.CommV2FileLogsv2", "communicationDB_dev.CommV2FileLogs");

        if (!allowedNamespaces.contains(dbColl)) {
            result.put("error", "ns is not whitelisted to proceed further...");
            return result;
        }

        File lockFile = new File("/tmp/deleteOldCommV2FileLogs_" + dbColl.replace(".", "_") + ".lock");

        try {
            if (Boolean.TRUE.equals(stop)) {
                BmsLoggerServiceImplNew.isDeleteOldCommV2LogsJobStop = true;
                if (lockFile.exists()) {
                    boolean deleted = lockFile.delete();
                    result.put("OK", deleted ? "1" : "0");
                    result.put("SK_MSG", deleted ? "Stopped job by deleting lock file." : "Failed to delete lock file.");
                } else {
                    result.put("OK", "0");
                    result.put("SK_MSG", "No running job found to stop.");
                }
                return result;
            }

            if (lockFile.exists()) {
                logger.info("Lock file exists for {}. Job already running.", dbColl);
                result.put("OK", "0");
                result.put("SK_MSG", "Job already running for " + dbColl);
                return result;
            }

            boolean created = lockFile.createNewFile();
            if (!created) {
                result.put("OK", "0");
                result.put("SK_MSG", "Failed to create lock file.");
                return result;
            }

            logger.info("Starting delete job for {} with lock file: {}", dbColl, lockFile.getAbsolutePath());

            if (size > 1000) size = 1000;
            if (days < 30) days = 30;

            // Reset flags before job starts
            BmsLoggerServiceImplNew.isDeleteOldCommV2LogsJobStop = false;

            final String finalDbColl = dbColl;
            final int finalDays = days;
            final int finalSize = size;

            new Thread(() -> {
                try {
                    bmsLoggerService.deleteOldCommV2FileLogs(finalDbColl, finalDays, finalSize);
                } catch (Exception e) {
                    logger.error("Error while deleting {}: {}", dbColl, e.getMessage());
                } finally {

                    BmsLoggerServiceImplNew.isDeleteOldCommV2LogsJobStop = false;

                    lockFile.delete();
                }
            }).start();

            result.put("OK", "1");
            result.put("SK_MSG", "Deletion job started for " + dbColl);
        } catch (Exception e) {
            result.put("OK", "0");
            result.put("SK_MSG", "Error in deleteOldCommV2FileLogs: " + e.getMessage());
            logger.error("Error in deleteOldCommV2FileLogs: {}", e.getMessage(), e);
        }

        return result;
    }
    
    @GetMapping(value = "/moveNewIntegrationExternalAPILogsToS3")
    public Map<String, String> moveNewIntegrationExternalAPILogsToS3(@RequestParam(required = false,value = "size") Integer size,
                                                        @RequestParam(required = false,value = "stop") Boolean stop,
                                                        @RequestParam(value = "ns") String dbColl,
                                                        @RequestParam(value="dayBefore", defaultValue = "30") int days,
                                                        @RequestParam(value="chunkSize", defaultValue = "100") int chunkSize,
                                                        @RequestParam(value = "timeCheck", required = true) boolean timeCheck,
                                                        @RequestParam(value = "authKey") String authKey,
                                                        @RequestParam(value = "movedIdsCollection", defaultValue = "movedIdsLoggerNew") String movedIdsCollection){
        
        logger.info("Inside moveDocsRequestLogsToS3 ");
        Map<String, String> result = new HashMap<String, String>();
        
        if(!authKey.equals(REQ_HEADER_AUTHKEY)){
            result.put("error", "Auth key is not valid..");
            return result;
        }

        List<String> allowedNamespaces = Arrays.asList("Integration.ExternalAPILogs");

        if (!allowedNamespaces.contains(dbColl)) {
            result.put("error", "ns is not whitelisted to proceed further...");
            return result;
        }

        File lockFile = new File("/tmp/moveIntegrationExternalAPILogsToS3_" + movedIdsCollection + ".lock");

        try {
            // STOP logic: if stop=true and lock file exists, delete it
        if (Boolean.TRUE.equals(stop)) {
            BmsLoggerServiceImplNew.isMoveIntegrationExternalAPILogsJobStop = true;
            if (lockFile.exists()) {
                boolean deleted = lockFile.delete();
                result.put("OK", deleted ? "1" : "0");
                result.put("SK_MSG", deleted ? "Stopped job and deleting lock file." : "Failed to delete lock file.");
            } else {
                result.put("OK", "0");
                result.put("SK_MSG", "No running job found to stop.");
            }
            return result;
        }

        // If already running (lock file present)
        if (lockFile.exists()) {
            logger.info("Job already running. Lock file present for {}", movedIdsCollection);
            result.put("OK", "0");
            result.put("SK_MSG", "Job already running for " + movedIdsCollection);
            return result;
        }

        // Create lock file
        if (!lockFile.createNewFile()) {
            result.put("OK", "0");
            result.put("SK_MSG", "Failed to create lock file.");
            return result;
        }

        logger.info("Job triggered with lock file {}", lockFile.getAbsolutePath());

        // Parameter limits
        if (size != null && size > 1000) size = 1000;
        if (days < 90) days = 90;
        if (chunkSize > 500) chunkSize = 500;

        BmsLoggerServiceImplNew.isMoveIntegrationExternalAPILogsJobStop = false;

        // Define final values
        final Integer finalSize = size;
        final int finalDays = days;
        final int finalChunkSize = chunkSize;
        final String finalDbColl = dbColl;
        final String finalMovedIdsCollection = movedIdsCollection;
        final boolean finalTimeCheck = timeCheck;

        // Time check logic
        Date now = new Date();
        Date startTime = DateUtil.setHours(DateUtil.setMinute(DateUtil.setSeconds(new Date(), 0), 0), 7);
        Date endTime = DateUtil.setHours(DateUtil.setMinute(DateUtil.setSeconds(new Date(), 0), 0), 20);

        boolean withinTimeWindow = now.after(startTime) && now.before(endTime);

        if (!finalTimeCheck || !withinTimeWindow) {
            logger.info("Proceeding with execution (timeCheck={}, withinTimeWindow={})", finalTimeCheck, withinTimeWindow);

            new Thread(() -> {
                try {
                    bmsLoggerService.moveNewIntegrationExternalAPILogsToS3(finalDbColl, finalSize, -finalDays, finalChunkSize, finalTimeCheck, finalMovedIdsCollection);

                } catch (Exception e) {
                    logger.error("Error during job execution for {}: {}", finalMovedIdsCollection, e.getMessage());
                } finally {
                    BmsLoggerServiceImplNew.isMoveIntegrationExternalAPILogsJobStop = false;                    lockFile.delete();
                }
            }).start();

            result.put("OK", "1");
            result.put("SK_MSG", "Job started for " + movedIdsCollection);
        } else {
            result.put("OK", "0");
            result.put("SK_MSG", "Job not allowed during restricted time window (7 AM – 8 PM)");
            logger.info("Skipped execution due to restricted time window: {}", now);
        }
        } catch (Exception e) {
            logger.error("Exception in moveIntegrationExternalAPILogsToS3: {}", e.getMessage(), e);
            result.put("OK", "0");
            result.put("SK_MSG", "Error: " + e.getMessage());
        }

        logger.info("Exiting moveIntegrationExternalAPILogsToS3 ");
        return result;
    }

    @GetMapping(value = "/moveNewPIVCLogsToS3")
    public Map<String, String> moveNewPIVCLogsToS3(@RequestParam(required = false, value = "size") Integer size,
                                                @RequestParam(required = false, value = "stop") Boolean stop,
                                                @RequestParam(value = "ns") String dbColl,
                                                @RequestParam(value = "dayBefore", defaultValue = "30") int days,
                                                @RequestParam(value = "chunkSize", defaultValue = "100") int chunkSize,
                                                @RequestParam(value = "authKey") String authKey,
                                                @RequestParam(value = "movedIdsCollection", defaultValue = "movedIdsLoggerNew") String movedIdsCollection) {
        logger.info("Inside movePIVCLogsToS3");
        Map<String, String> result = new HashMap<>();

        // Validate auth key
        if (!authKey.equals(REQ_HEADER_AUTHKEY)) {
            result.put("error", "Auth key is not valid.");
            return result;
        }

        // Whitelisted namespaces
        List<String> allowedNamespaces = Arrays.asList("Integration.PIVCLog");
        if (!allowedNamespaces.contains(dbColl)) {
            result.put("error", "Namespace is not whitelisted.");
            return result;
        }

        File lockFile = new File("/tmp/movePIVCLogsToS3_" + movedIdsCollection + ".lock");

        try {
            // STOP logic: delete lock file to stop the job
            if (Boolean.TRUE.equals(stop)) {
                BmsLoggerServiceImplNew.isMovePIVCLogsJobStop = true;
                if (lockFile.exists()) {
                    boolean deleted = lockFile.delete();
                    result.put("OK", deleted ? "1" : "0");
                    result.put("SK_MSG", deleted ? "Stopped job and deleting lock file." : "Failed to delete lock file.");
                } else {
                    result.put("OK", "0");
                    result.put("SK_MSG", "No running job found to stop.");
                }
                return result;
            }

            // Prevent duplicate job runs
            if (lockFile.exists()) {
                logger.info("Job already running. Lock file exists for {}", movedIdsCollection);
                result.put("OK", "0");
                result.put("SK_MSG", "Job already running for " + movedIdsCollection);
                return result;
            }

            // Create lock file
            boolean created = lockFile.createNewFile();
            if (!created) {
                result.put("OK", "0");
                result.put("SK_MSG", "Failed to create lock file.");
                return result;
            }

            logger.info("Job starting with lock file: {}", lockFile.getAbsolutePath());

            // Limit and sanitize input values
            if (size != null && size > 1000) size = 1000;
            if (days < 100) days = 100;
            if (chunkSize > 500) chunkSize = 500;

            BmsLoggerServiceImplNew.isMovePIVCLogsJobStop = false;

            // Final values to be used inside the thread
            final Integer finalSize = size;
            final int finalDays = days;
            final int finalChunkSize = chunkSize;
            final String finalDbColl = dbColl;
            final String finalMovedIdsCollection = movedIdsCollection;

            new Thread(() -> {
                try {
                    bmsLoggerService.moveNewPIVCLogsToS3(finalDbColl, finalSize, -finalDays, finalChunkSize, finalMovedIdsCollection);

                } catch (Exception e) {
                    logger.error("Error while processing {}: {}", finalMovedIdsCollection, e.getMessage(), e);
                } finally {
                    BmsLoggerServiceImplNew.isMovePIVCLogsJobStop = false;                    lockFile.delete();
                }
            }).start();

            result.put("OK", "1");
            result.put("SK_MSG", "Job started for " + movedIdsCollection);
        } catch (Exception e) {
            logger.error("Exception in movePIVCLogsToS3: {}", e.getMessage(), e);
            result.put("OK", "0");
            result.put("SK_MSG", "Error in movePIVCLogsToS3: " + e.getMessage());
        }

        logger.info("Exiting movePIVCLogsToS3");
        return result;
    }
}