package com.docMigration.controller;

import com.docMigration.service.MotorLoggerArchivalService;
import com.docMigration.service.impl.MotorLoggerArchivalServiceImpl;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * This is a schedular class which will take documents from db and insert in aws s3
 * S3 bucket.
 *
 * <AUTHOR>
 *
 */
@RestController
public class MotorLoggerController {
    private final Logger logger = LoggerFactory.getLogger(MotorLoggerController.class);
    private static final String REQ_HEADER_AUTHKEY = "92kik4i4jn4oi4n3oi4nn4k4l4lk4lknwlk4l4ljk44444klknn4h4jk5k";

    @Autowired
    private MotorLoggerArchivalService motorLoggerArchivalService;

    /**
     *
     * @param size
     * @param stop
     * @param dbColl
     * @param days
     * @param authKey
     * @return
     */
    @GetMapping(value = "/car/moveBookedDocsByEnquiryId")
    public Map<String, String> moveBookedDocsByEnquiryId(@RequestParam(required = false,value = "size", defaultValue = "500") Integer size,
                                                         @RequestParam(required = false,value = "stop", defaultValue = "false") boolean stop,
                                                         @RequestParam(value = "ns") String dbColl,
                                                         @RequestParam(value="dayBefore") int days,
                                                         @RequestParam(value="startDay") int startDayForBookingEnqID,
                                                         @RequestParam(value="chunkSize", defaultValue = "200") int chunkSize,
                                                         @RequestHeader String authKey){
        logger.info("Inside /car/moveBookedDocsByEnquiryId ");
        Map<String, String> responseMap = new HashMap<String, String>();

        try {
            // check authKey if not matched then return.
            if(!authKey.equals(REQ_HEADER_AUTHKEY)){
                responseMap.put("error", "Auth key is not valid..");
                return responseMap;
            }
            String[] ns = {"CarLoggerDB.ReqResLog","CarLoggerDB.ReqResLog"};
            if(!Arrays.asList(ns).contains(dbColl)){
                responseMap.put("error", "ns is not whitelisted to proceed further...");
                return responseMap;
            }
            if(stop && MotorLoggerArchivalServiceImpl.isCarMoveS3JobRunning) {
                MotorLoggerArchivalServiceImpl.isCarMoveS3JobStop = stop;
            } else {
                MotorLoggerArchivalServiceImpl.isCarMoveS3JobStop = false;
            }

            if(size>1000) {
                size = 1000;
            }
            if(chunkSize>300) {
                chunkSize = 300;
            }

            if(days < 10) {
                days = 10;
            }

            // if not running then run the job
            if (!MotorLoggerArchivalServiceImpl.isCarMoveS3JobRunning) {
                MotorLoggerArchivalServiceImpl.isCarMoveS3JobRunning = true;
                MotorLoggerArchivalServiceImpl.isCarMoveS3JobStop = false;
                motorLoggerArchivalService.moveMotorBookLeads(dbColl, size, -days, chunkSize, startDayForBookingEnqID);
                responseMap.put("OK", "1");
                responseMap.put("SK_MSG", "collection migrated, for" + dbColl);
                logger.info("Car job completed for ns: {} , size {} ",dbColl, size );
            }
            else if (MotorLoggerArchivalServiceImpl.isCarMoveS3JobRunning) {
                logger.info(" moveBookedLeadCar migration already running at: {} , for na: {} stop {}  {}", new Date(),dbColl, stop );
                logger.info(" coll {} , startId, {} , size {} ",ns, size );
                responseMap.put("OK", "0");
                responseMap.put("SK_MSG", "Could not initiate migration at this time , " + new Date());
            }
        } catch (Exception e) {
            responseMap.put("OK", "0");
            responseMap.put("SK_MSG", "Error in moveBookedLeadCar : ," + e.getMessage());
            logger.error("Error in Car moveBookedLeadCar , msg: {}", e.getMessage());
        }
        logger.info("completed job car moveBookedLeadCar, at: {} , for ns: ", new Date(), dbColl);
        return responseMap;
    }

    /**
     * This is API is used to move TW document from mongoDB active to S3 location.
     * - first preference if enquiryId , if enquiryId is not greateer than 0 then move by vehicleId
     *
     *  curl:
     *
     * @param size
     * @param stop
     * @param dbColl
     * @param days
     * @param timeCheck
     * @return
     */
    @GetMapping(value = "/tw/moveBookedDocsByEnquiryIdOrVehicleId")
    public Map<String, String> moveBookedDocsByEnquiryIdOrVehicleId(@RequestParam(required = false,value = "size" , defaultValue = "100") Integer size,
                                                                    @RequestParam(required = false,value = "chunkSize" , defaultValue = "200") Integer chunkSize,
                                                   @RequestParam(required = false,value = "stop", defaultValue = "false") boolean stop,
                                                   @RequestParam(value = "ns", defaultValue = "TwoWheelerDB.ReqResTWowheelerLog") String dbColl,
                                                   @RequestParam(value="dayBefore") int days,
                                                   @RequestParam(required = false,value = "timeCheck") Boolean timeCheck,
                                                   @RequestHeader String authKey){
        logger.info("Inside /tw/moveBookedDocsByEnquiryIdOrVehicleId ");
        Map<String, String> result = new HashMap<String, String>();

        // check authKey if not matched then return.
        if(!authKey.equals(REQ_HEADER_AUTHKEY)){
            result.put("error", "Auth key is not valid..");
            return result;
        }
        String[] ns = {"CarLoggerDB.ReqResTWowheelerLog","TwoWheelerDB.ReqResTWowheelerLog"};
        if(!Arrays.asList(ns).contains(dbColl)){
            result.put("error", "ns is not whitelisted to proceed further...");
            return result;
        }
        try {

            // stop job by setting true in this
            if(stop && MotorLoggerArchivalServiceImpl.isTwMoveS3JobRunning) {
                MotorLoggerArchivalServiceImpl.isTwS3MoveDataStop = true;
            } else {
                MotorLoggerArchivalServiceImpl.isTwS3MoveDataStop = false;
            }

            if(chunkSize>500) {
                chunkSize = 500;
            }
            if(days < 10) {
                days = 10;
            }
            if(size>500) {
                size = 500;
            }

            // if not running then start migrattion else stop
            if (!MotorLoggerArchivalServiceImpl.isTwMoveS3JobRunning) {

                MotorLoggerArchivalServiceImpl.isTwS3MoveDataStop = false;
                MotorLoggerArchivalServiceImpl.isTwMoveS3JobRunning = true;

                // start migration.
                motorLoggerArchivalService.moveBookedLeadTwV1(dbColl, size, -days);
                result.put("OK", "1");
                result.put("SK_MSG", "collection migrated");
            } else if (MotorLoggerArchivalServiceImpl.isTwMoveS3JobRunning) {
                logger.info("TW job migration already running, at: {} , for ns: {}, stop {} , {}", new Date(), dbColl, stop);
                result.put("OK", "1");
                result.put("SK_MSG", "TW job already running, so could not initiate migration at this time , " + new Date());
            }

        } catch (Exception e) {
            result.put("OK", "0");
            result.put("SK_MSG", "Error in moveBookedLeadTwV1 : ," + e.getMessage());
            logger.error("Error in moveBookedLeadTwV1 , {}", e.getMessage());
        }
        logger.info("completed for TW job at: {}, for ns:{} ", new Date(), dbColl);
        return result;
    }

    @GetMapping(value = "/kyc/moveBookedDocsByUniqueId")
    public Map<String, String> moveBookedDocsByUniqueId(@RequestParam(required = false,value = "size") Integer size,
                                                         @RequestParam(required = false,value = "stop", defaultValue = "false") boolean stop,
                                                         @RequestParam(value = "ns") String dbColl,
                                                         @RequestParam(value="dayBefore") int days,
                                                         @RequestParam(value="chunkSize", defaultValue = "100") int chunkSize,
                                                         @RequestHeader String authKey){
        logger.info("Inside moveBookedDocsByUniqueId ");
        Map<String, String> responseMap = new HashMap<String, String>();

        try {
            // check authKey if not matched then return.
            if(!authKey.equals(REQ_HEADER_AUTHKEY)){
                responseMap.put("error", "Auth key is not valid..");
                return responseMap;
            }
            String[] ns = {"KYC.KYCCentral"};
            if(!Arrays.asList(ns).contains(dbColl)){
                responseMap.put("error", "ns is not whitelisted to proceed further...");
                return responseMap;
            }

            if(stop && MotorLoggerArchivalServiceImpl.isKYCJobS3MoveRunning) {
                MotorLoggerArchivalServiceImpl.isKYCS3MoveDataStop = true;
            } else {
                MotorLoggerArchivalServiceImpl.isKYCS3MoveDataStop = false;
            }

            if(size>1000) {
                size = 1000;
            }
            if(days < 30) {
                days = 30;
            }
            if(chunkSize >= 100){
                chunkSize = 100;
            }
            if (!MotorLoggerArchivalServiceImpl.isKYCJobS3MoveRunning) {
                MotorLoggerArchivalServiceImpl.isKYCJobS3MoveRunning = true;
                MotorLoggerArchivalServiceImpl.isKYCS3MoveDataStop = false;

                motorLoggerArchivalService.moveKycLogs(dbColl, size, -days, chunkSize);
                responseMap.put("OK", "1");
                responseMap.put("SK_MSG", "collection migrated");
            }
            else if (MotorLoggerArchivalServiceImpl.isKYCJobS3MoveRunning) {
                logger.info("KYC job already running /kyc/moveBookedDocsByUniqueId migration at: {} , stop {} ", new Date(), stop);
                logger.info(" coll {} , startId, {} , size {} ",ns, size );
                responseMap.put("OK", "0");
                responseMap.put("SK_MSG", "Could not initiate migration at this time , " + new Date());
            }
        } catch (Exception e) {
            responseMap.put("OK", "0");
            responseMap.put("SK_MSG", "Error in moveKycLogs : ," + e.getMessage());
            logger.error("Error in KYC job /kyc/moveBookedDocsByUniqueId , msg: {}", e.getMessage());
        } finally {
           // MotorLoggerArchivalServiceImpl.isKYCJobS3MoveRunning = false;
           // MotorLoggerArchivalServiceImpl.isKYCS3MoveDataStop = false;

        }
        logger.info("Exiting /kyc/moveBookedDocsByUniqueId , for ns: ", dbColl);
        return responseMap;
    }

    /**
     *
     * @param size - how many record with projected data need to fetch based on recorded $gt _id
     * @param stop
     * @param dbColl - source ns (dbname.collectionName)  to pull the data and migrate to S3 bucket.
     * @param days  - how many days older data need to migrate by default 30 days older to migrate
     * @param chunkSize - chunkSize is used to fetch how many record at a time by leadId (UniqueId).
     * @param authKey
     * @return
     */
    @GetMapping(value = "/claim/moveDocsByUniqueId")
    public Map<String, String> moveClaimDocsByUniqueId(@RequestParam(required = false,value = "size") Integer size,
                                                        @RequestParam(value = "stop",required = false, defaultValue = "false") boolean stop,
                                                        @RequestParam(value = "ns", defaultValue = "ClaimsDB.claimslogs") String dbColl,
                                                        @RequestParam(value="dayBefore", defaultValue = "30") int days,
                                                        @RequestParam(value="chunkSize", defaultValue = "100") int chunkSize,
                                                        @RequestHeader String authKey){
        logger.info("Inside moveClaimDocsByUniqueId to migrate Claims metadata: ns: {} , dayBefore: {}", dbColl, days);
        Map<String, String> responseMap = new HashMap<String, String>();

        try {
            // check authKey if not matched then return.
            if(!authKey.equals(REQ_HEADER_AUTHKEY)){
                responseMap.put("error", "Auth key is not valid..");
                return responseMap;
            }
            String[] ns = {"ClaimsDB.claimslogs"};
            if(!Arrays.asList(ns).contains(dbColl)){
                responseMap.put("error", "ns is not whitelisted to proceed further...");
                return responseMap;
            }

            if(stop && MotorLoggerArchivalServiceImpl.isClaimMoveS3JobRunning) {
                MotorLoggerArchivalServiceImpl.isClaimMoveS3JobStop = true;
            }else{
                MotorLoggerArchivalServiceImpl.isClaimMoveS3JobStop = false;
            }

            // this size for top level iteration
            if(size>500) {
                size = 500;
            }
            if(days < 30) {
                days = 30;
            }
            // each iteration size for one leadId. ( nested second level document)
            if(chunkSize>100) {
                chunkSize = 100;
            }

            if (!MotorLoggerArchivalServiceImpl.isClaimMoveS3JobRunning) {
                MotorLoggerArchivalServiceImpl.isClaimMoveS3JobRunning = true;
                MotorLoggerArchivalServiceImpl.isClaimMoveS3JobStop = false;

                motorLoggerArchivalService.moveClaimLogs(dbColl, size,-days, chunkSize);
                responseMap.put("ok", "1");
                responseMap.put("SK_MSG", "collection migrated for ns: " + dbColl + ", dayBefore: "+ days);
                logger.info("collection migrated for ns: {} , dayBefore: {}", dbColl, days);
            } else if (MotorLoggerArchivalServiceImpl.isClaimMoveS3JobRunning) {
                logger.info("Claim job already running at: {} , for ns: {}, stop {} ", new Date(), dbColl, stop);
                logger.info(" coll {} , startId, {} , size {} ",ns, size );
                responseMap.put("ok", "1");
                responseMap.put("SK_MSG", "Could not initiate migration of Claim at this time , " + new Date());
            }
        } catch (Exception e) {
            responseMap.put("ok", "0");
            responseMap.put("SK_MSG", "Error in moveKycLogs : ," + e.getMessage());
            logger.error("Error in moveClaim , msg: {}", e.getMessage());
        }
        logger.info("Exiting /claim/moveBookedDocsByUniqueId , for ns: ", dbColl);

        return responseMap;
    }

    @GetMapping(value = "/tw/moveCoreKycDocsByVehicleId")
    public Map<String, String> moveCoreKycDocsByVehicleId(@RequestParam(required = false,value = "size" , defaultValue = "100") Integer size,
                                                                    @RequestParam(required = false,value = "chunkSize" , defaultValue = "200") Integer chunkSize,
                                                                    @RequestParam(required = false,value = "stop", defaultValue = "false") boolean stop,
                                                                    @RequestParam(value = "ns", defaultValue = "TwoWheelerDB.CoreKYC") String dbColl,
                                                                    @RequestParam(value="dayBefore") int days,
                                                                    @RequestHeader String authKey){
        logger.info("Inside /tw/moveCoreKycDocsByVehicleId ");
        Map<String, String> result = new HashMap<String, String>();

        // check authKey if not matched then return.
        if(!authKey.equals(REQ_HEADER_AUTHKEY)){
            result.put("error", "Auth key is not valid..");
            return result;
        }
        String[] ns = {"TwoWheelerDB.CoreKYC"};
        if(!Arrays.asList(ns).contains(dbColl)){
            result.put("error", "ns is not whitelisted to proceed further...");
            return result;
        }
        try {

            // stop job by setting true in this
            if(stop && MotorLoggerArchivalServiceImpl.isTwCoreKycMoveS3JobRunning) {
                MotorLoggerArchivalServiceImpl.isTwoCoreKycS3MoveDataStop = true;
            } else {
                MotorLoggerArchivalServiceImpl.isTwoCoreKycS3MoveDataStop = false;
            }

            if(chunkSize>500) {
                chunkSize = 500;
            }
            if(days < 10) {
                days = 10;
            }
            if(size>500) {
                size = 500;
            }

            // if not running then start migrattion else stop
            if (!MotorLoggerArchivalServiceImpl.isTwCoreKycMoveS3JobRunning) {

                MotorLoggerArchivalServiceImpl.isTwoCoreKycS3MoveDataStop = false;
                MotorLoggerArchivalServiceImpl.isTwCoreKycMoveS3JobRunning = true;

                // start migration.
                motorLoggerArchivalService.moveTWCoreKycDocsByVehicleId(dbColl, size, -days, chunkSize);
                result.put("OK", "1");
                result.put("SK_MSG", "collection migrated");
            } else if (MotorLoggerArchivalServiceImpl.isTwCoreKycMoveS3JobRunning) {
                logger.info("TW job migration already running, at: {} , for ns: {}, stop {} , {}", new Date(), dbColl, stop);
                result.put("OK", "1");
                result.put("SK_MSG", "TW job already running, so could not initiate migration at this time , " + new Date());
            }

        } catch (Exception e) {
            result.put("OK", "0");
            result.put("SK_MSG", "Error in moveCoreKycDocsByVehicleId : ," + e.getMessage());
            logger.error("Error in moveCoreKycDocsByVehicleId , {}", e.getMessage());
        }
        logger.info("completed for TW job at: {}, for ns:{} ", new Date(), dbColl);
        return result;
    }


    @GetMapping(value = "/carTemp/moveBookedDocsByEnquiryId")
    public Map<String, String> moveBookedDocsByEnquiryIdcarTemp(@RequestParam(required = false,value = "size", defaultValue = "500") Integer size,
                                                         @RequestParam(required = false,value = "stop", defaultValue = "false") boolean stop,
                                                         @RequestParam(value = "ns") String dbColl,
                                                         @RequestParam(value="dayBefore") int days,
                                                         @RequestParam(value="chunkSize") int chunkSize,
                                                         @RequestHeader String authKey){
        logger.info("Inside /car/moveBookedDocsByEnquiryId ");
        Map<String, String> responseMap = new HashMap<String, String>();

        try {
            // check authKey if not matched then return.
            if(!authKey.equals(REQ_HEADER_AUTHKEY)){
                responseMap.put("error", "Auth key is not valid..");
                return responseMap;
            }
            String[] ns = {"CarLoggerDB.ReqResLog"};
            if(!Arrays.asList(ns).contains(dbColl)){
                responseMap.put("error", "ns is not whitelisted to proceed further...");
                return responseMap;
            }
            if(stop && MotorLoggerArchivalServiceImpl.isOldCarMoveS3JobRunning) {
                MotorLoggerArchivalServiceImpl.isOldCarMoveS3JobStop = stop;
            } else {
                MotorLoggerArchivalServiceImpl.isOldCarMoveS3JobStop = false;
            }

            if(size>1000) {
                size = 1000;
            }
            if(chunkSize>500) {
                chunkSize = 500;
            }

            // if not running then run the job
            if (!MotorLoggerArchivalServiceImpl.isOldCarMoveS3JobRunning) {
                MotorLoggerArchivalServiceImpl.isOldCarMoveS3JobRunning = true;
                MotorLoggerArchivalServiceImpl.isOldCarMoveS3JobStop = false;
                motorLoggerArchivalService.moveOldMotorBookLeads(dbColl, size, -days, chunkSize);
                responseMap.put("OK", "1");
                responseMap.put("SK_MSG", "collection migrated, for" + dbColl);
                logger.info("Car job completed for ns: {} , size {} ",dbColl, size );
            }
            else if (MotorLoggerArchivalServiceImpl.isOldCarMoveS3JobRunning) {
                logger.info(" moveBookedLeadCar migration already running at: {} , for na: {} stop {}  {}", new Date(),dbColl, stop );
                logger.info(" coll {} , startId, {} , size {} ",ns, size );
                responseMap.put("OK", "0");
                responseMap.put("SK_MSG", "Could not initiate migration at this time , " + new Date());
            }
        } catch (Exception e) {
            responseMap.put("OK", "0");
            responseMap.put("SK_MSG", "Error in moveBookedLeadCar : ," + e.getMessage());
            logger.error("Error in Car moveBookedLeadCar , msg: {}", e.getMessage());
        }
        logger.info("completed job car moveBookedLeadCar, at: {} , for ns: ", new Date(), dbColl);
        return responseMap;
    }

    /**
     * This is API is used to move TW document from mongoDB active to S3 location.
     * - first preference if enquiryId , if enquiryId is not greateer than 0 then move by vehicleId
     *
     *  curl:
     *
     * @param size
     * @param stop
     * @param dbColl
     * @param days
     * @param timeCheck
     * @return
     */
    @GetMapping(value = "/twTemp/moveBookedDocsByEnquiryIdOrVehicleId")
    public Map<String, String> moveBookedDocsByVehicleId(@RequestParam(required = false,value = "size" , defaultValue = "100") Integer size,
                                                                    @RequestParam(required = false,value = "chunkSize" , defaultValue = "100") Integer chunkSize,
                                                                    @RequestParam(required = false,value = "stop", defaultValue = "false") boolean stop,
                                                                    @RequestParam(value = "ns", defaultValue = "TwoWheelerDB.ReqResTWowheelerLog") String dbColl,
                                                                    @RequestParam(value="dayBefore") int days,
                                                                    @RequestParam(required = false,value = "timeCheck") Boolean timeCheck,
                                                                    @RequestHeader String authKey){
        logger.info("Inside /tw/moveBookedDocsByEnquiryIdOrVehicleId ");
        Map<String, String> result = new HashMap<String, String>();

        // check authKey if not matched then return.
        if(!authKey.equals(REQ_HEADER_AUTHKEY)){
            result.put("error", "Auth key is not valid..");
            return result;
        }
        String[] ns = {"TwoWheelerDB.ReqResTWowheelerLog","TwoWheelerDB.ReqResTWowheelerLog_173", "Logger-TwoWheelerDB.ReqResTWowheelerLog"};
        if(!Arrays.asList(ns).contains(dbColl)){
            result.put("error", "ns is not whitelisted to proceed further...");
            return result;
        }
        try {

            // stop job by setting true in this
            if(stop && MotorLoggerArchivalServiceImpl.isOldTwMoveS3JobRunning) {
                MotorLoggerArchivalServiceImpl.isOldTwS3MoveDataStop = true;
            } else {
                MotorLoggerArchivalServiceImpl.isOldTwS3MoveDataStop = false;
            }

            if(chunkSize>500) {
                chunkSize = 500;
            }
            if(size>500) {
                size = 500;
            }

            // if not running then start migrattion else stop
            if (!MotorLoggerArchivalServiceImpl.isOldTwMoveS3JobRunning) {

                MotorLoggerArchivalServiceImpl.isOldTwS3MoveDataStop = false;
                MotorLoggerArchivalServiceImpl.isOldTwMoveS3JobRunning = true;

                // start migration.
                motorLoggerArchivalService.moveBookedLeadOldTw(dbColl, size, -days, chunkSize);
                result.put("OK", "1");
                result.put("SK_MSG", "collection migrated");
            } else if (MotorLoggerArchivalServiceImpl.isOldTwMoveS3JobRunning) {
                logger.info("TW job migration already running, at: {} , for ns: {}, stop {} , {}", new Date(), dbColl, stop);
                result.put("OK", "1");
                result.put("SK_MSG", "TW job already running, so could not initiate migration at this time , " + new Date());
            }

        } catch (Exception e) {
            result.put("OK", "0");
            result.put("SK_MSG", "Error in moveBookedLeadTwV1 : ," + e.getMessage());
            logger.error("Error in moveBookedLeadTwV1 , {}", e.getMessage());
        }
        logger.info("completed for TW job at: {}, for ns:{} ", new Date(), dbColl);
        return result;
    }
    @GetMapping(value = "/carKyc/kycMoveBookedDocsByEnquiryId")
    public Map<String, String> kycMoveBookedDocsByEnquiryId(@RequestParam(required = false,value = "size", defaultValue = "500") Integer size,
                                                               @RequestParam(required = false,value = "stop", defaultValue = "false") boolean stop,
                                                               @RequestParam(value = "ns") String dbColl,
                                                               @RequestParam(value="dayBefore") int days,
                                                               @RequestParam(value="chunkSize") int chunkSize,
                                                               @RequestHeader String authKey){
        logger.info("Inside kycMoveBookedDocsByEnquiryId");
        Map<String, String> responseMap = new HashMap<String, String>();

        try {
            // check authKey if not matched then return.
            if(!authKey.equals(REQ_HEADER_AUTHKEY)){
                responseMap.put("error", "Auth key is not valid..");
                return responseMap;
            }
            String[] ns = {"CarLoggerDB.KycLogs"};
            if(!Arrays.asList(ns).contains(dbColl)){
                responseMap.put("error", "ns is not whitelisted to proceed further...");
                return responseMap;
            }
            if(stop && MotorLoggerArchivalServiceImpl.isKYCCarMoveS3JobRunning) {
                MotorLoggerArchivalServiceImpl.isKYCCarMoveS3JobStop = stop;
            } else {
                MotorLoggerArchivalServiceImpl.isKYCCarMoveS3JobStop = false;
            }
            if(size>500) {
                size = 500;
            }
            if(chunkSize>500) {
                chunkSize = 500;
            }
            if(days < 10) {
                days = 10;
            }

            // if not running then run the job
            if (!MotorLoggerArchivalServiceImpl.isKYCCarMoveS3JobRunning) {
                MotorLoggerArchivalServiceImpl.isKYCCarMoveS3JobRunning = true;
                MotorLoggerArchivalServiceImpl.isKYCCarMoveS3JobStop = false;
                motorLoggerArchivalService.moveMotorKyc(dbColl, size, -days, chunkSize);
                responseMap.put("OK", "1");
                responseMap.put("SK_MSG", "collection migrated, for" + dbColl);
                logger.info("Car job completed for ns: {} , size {} ",dbColl, size );
            }
            else if (MotorLoggerArchivalServiceImpl.isKYCCarMoveS3JobRunning) {
                logger.info(" carKyc migration already running at: {} , for na: {} stop {}  {}", new Date(),dbColl, stop );
                logger.info(" coll {} , startId, {} , size {} ",ns, size );
                responseMap.put("OK", "0");
                responseMap.put("SK_MSG", "Could not initiate migration at this time , " + new Date());
            }
        } catch (Exception e) {
            responseMap.put("OK", "0");
            responseMap.put("SK_MSG", "Error in carKyc : ," + e.getMessage());
            logger.error("Error in Car kycMoveBookedDocsByEnquiryId , msg: {}", e.getMessage());
        }
        logger.info("completed job car kycMoveBookedDocsByEnquiryId, at: {} , for ns: ", new Date(), dbColl);
        return responseMap;
    }
    @GetMapping(value = "/carKyc/moveBookedDocsByEnquiryId_173")
    public Map<String, String> moveBookedDocsByEnquiryIdcarKyc(@RequestParam(required = false,value = "size", defaultValue = "500") Integer size,
                                                                @RequestParam(required = false,value = "stop", defaultValue = "false") boolean stop,
                                                                @RequestParam(value = "ns") String dbColl,
                                                                @RequestParam(value="dayBefore") int days,
                                                                @RequestParam(value="chunkSize") int chunkSize,
                                                                @RequestHeader String authKey){
        logger.info("Inside carKyc");
        Map<String, String> responseMap = new HashMap<String, String>();

        try {
            // check authKey if not matched then return.
            if(!authKey.equals(REQ_HEADER_AUTHKEY)){
                responseMap.put("error", "Auth key is not valid..");
                return responseMap;
            }
            String[] ns = {"CarLoggerDB.KycLogs_173"};
            if(!Arrays.asList(ns).contains(dbColl)){
                responseMap.put("error", "ns is not whitelisted to proceed further...");
                return responseMap;
            }
            if(stop && MotorLoggerArchivalServiceImpl.isOldKYCCarMoveS3JobRunning) {
                MotorLoggerArchivalServiceImpl.isOldCKYCCarMoveS3JobStop = stop;
            } else {
                MotorLoggerArchivalServiceImpl.isOldCKYCCarMoveS3JobStop = false;
            }
            if(size>500) {
                size = 500;
            }
            if(chunkSize>500) {
                chunkSize = 500;
            }

            // if not running then run the job
            if (!MotorLoggerArchivalServiceImpl.isOldKYCCarMoveS3JobRunning) {
                MotorLoggerArchivalServiceImpl.isOldKYCCarMoveS3JobRunning= true;
                MotorLoggerArchivalServiceImpl.isOldCKYCCarMoveS3JobStop = false;
//                motorLoggerArchivalService.moveOldMotorKyc(dbColl, size, -days, chunkSize);
                responseMap.put("OK", "1");
                responseMap.put("SK_MSG", "collection migrated, for" + dbColl);
                logger.info("Car job completed for ns: {} , size {} ",dbColl, size );
            }
            else if (MotorLoggerArchivalServiceImpl.isOldKYCCarMoveS3JobRunning) {
                logger.info(" carKyc migration already running at: {} , for na: {} stop {}  {}", new Date(),dbColl, stop );
                logger.info(" coll {} , startId, {} , size {} ",ns, size );
                responseMap.put("OK", "0");
                responseMap.put("SK_MSG", "Could not initiate migration at this time , " + new Date());
            }
        } catch (Exception e) {
            responseMap.put("OK", "0");
            responseMap.put("SK_MSG", "Error in carKyc : ," + e.getMessage());
            logger.error("Error in Car carKyc , msg: {}", e.getMessage());
        }
        logger.info("completed job car carKyc, at: {} , for ns: ", new Date(), dbColl);
        return responseMap;
    }
    @GetMapping(value = "/testUtil")
    public Map<String, String> testUtil() {
        Map<String, String> resp =new HashMap<>();
        System.out.println("Started");
        try{
//             motorLoggerArchivalService.moveCarCsvToBookedEnquiry();
            resp.put("OK", "1");
            resp.put("test", "ok");
        }catch (Exception e) {
            logger.info("exception in csv file  job, msg:{}",e.getMessage());
            resp.put("OK", "0");
        }
        return resp;
    }
    @GetMapping(value = "/testUtilTW")
    public Map<String, String> testUtilTW() {
        Map<String, String> resp =new HashMap<>();
        System.out.println("Started");
        try{
            motorLoggerArchivalService.moveTWCsvToBookedEnquiry();
            resp.put("OK", "1");
        }catch (Exception e) {
            logger.info("exception in csv file  job, msg:{}",e.getMessage());
            resp.put("OK", "0");
        }
        return resp;
    }

    @GetMapping(value = "/carTemp/moveBookedDocsByEnquiryId/v2")
    public Map<String, String> moveBookedDocsByEnquiryIdcarTempV2(@RequestParam(required = false,value = "size", defaultValue = "500") Integer size,
                                                                @RequestParam(required = false,value = "stop", defaultValue = "false") boolean stop,
                                                                @RequestParam(value = "ns") String dbColl,
                                                                @RequestParam(value="dayBefore") int days,
                                                                @RequestParam(value="chunkSize") int chunkSize,
                                                                @RequestHeader String authKey){
        logger.info("Inside /car/moveBookedDocsByEnquiryIdV2 ");
        Map<String, String> responseMap = new HashMap<String, String>();

        try {
            // check authKey if not matched then return.
            if(!authKey.equals(REQ_HEADER_AUTHKEY)){
                responseMap.put("error", "Auth key is not valid..");
                return responseMap;
            }
            String[] ns = {"CarLoggerDB.ReqResLog","Recovery.ReqResLog"};
            if(!Arrays.asList(ns).contains(dbColl)){
                responseMap.put("error", "ns is not whitelisted to proceed further...");
                return responseMap;
            }
            if(stop && MotorLoggerArchivalServiceImpl.isOldCarMoveS3JobRunning) {
                MotorLoggerArchivalServiceImpl.isOldCarMoveS3JobStop = stop;
            } else {
                MotorLoggerArchivalServiceImpl.isOldCarMoveS3JobStop = false;
            }

            if(size>1000) {
                size = 1000;
            }
            if(chunkSize>500) {
                chunkSize = 500;
            }

            // if not running then run the job
            if (!MotorLoggerArchivalServiceImpl.isOldCarMoveS3JobRunning) {
                MotorLoggerArchivalServiceImpl.isOldCarMoveS3JobRunning = true;
                MotorLoggerArchivalServiceImpl.isOldCarMoveS3JobStop = false;
                motorLoggerArchivalService.moveOldMotorBookLeadsV2(dbColl, size, -days, chunkSize);
                responseMap.put("OK", "1");
                responseMap.put("SK_MSG", "collection migrated, for" + dbColl);
                logger.info("Car job completed for ns: {} , size {} ",dbColl, size );
            }
            else if (MotorLoggerArchivalServiceImpl.isOldCarMoveS3JobRunning) {
                logger.info(" moveBookedLeadCar migration already running at: {} , for na: {} stop {}  {}", new Date(),dbColl, stop );
                logger.info(" coll {} , startId, {} , size {} ",ns, size );
                responseMap.put("OK", "0");
                responseMap.put("SK_MSG", "Could not initiate migration at this time , " + new Date());
            }
        } catch (Exception e) {
            responseMap.put("OK", "0");
            responseMap.put("SK_MSG", "Error in moveBookedLeadCar : ," + e.getMessage());
            logger.error("Error in Car moveBookedLeadCar , msg: {}", e.getMessage());
        }
        logger.info("completed job car moveBookedLeadCar, at: {} , for ns: ", new Date(), dbColl);
        return responseMap;
    }
    @GetMapping(value = "/carTemp/moveBookedDocsByEnquiryId/v3")
    public Map<String, String> moveBookedDocsByEnquiryIdcarTempV3(@RequestParam(required = false,value = "size", defaultValue = "500") Integer size,
                                                                  @RequestParam(required = false,value = "stop", defaultValue = "false") boolean stop,
                                                                  @RequestParam(value = "ns") String dbColl,
                                                                  @RequestParam(value="dayBefore") int days,
                                                                  @RequestParam(value="chunkSize") int chunkSize,
                                                                  @RequestHeader String authKey){
        logger.info("Inside /car/moveBookedDocsByEnquiryIdV2 ");
        Map<String, String> responseMap = new HashMap<String, String>();

        try {
            // check authKey if not matched then return.
            if(!authKey.equals(REQ_HEADER_AUTHKEY)){
                responseMap.put("error", "Auth key is not valid..");
                return responseMap;
            }
            String[] ns = {"CarLoggerDB.ReqResLog","Recovery.ReqResLog"};
            if(!Arrays.asList(ns).contains(dbColl)){
                responseMap.put("error", "ns is not whitelisted to proceed further...");
                return responseMap;
            }
            if(stop && MotorLoggerArchivalServiceImpl.isOldCarMoveS3JobRunning1) {
                MotorLoggerArchivalServiceImpl.isOldCarMoveS3JobStop = stop;
            } else {
                MotorLoggerArchivalServiceImpl.isOldCarMoveS3JobStop = false;
            }

            if(size>1000) {
                size = 1000;
            }
            if(chunkSize>500) {
                chunkSize = 500;
            }

            // if not running then run the job
            if (!MotorLoggerArchivalServiceImpl.isOldCarMoveS3JobRunning1) {
                MotorLoggerArchivalServiceImpl.isOldCarMoveS3JobRunning1 = true;
                MotorLoggerArchivalServiceImpl.isOldCarMoveS3JobStop = false;
                motorLoggerArchivalService.moveOldMotorBookLeadsV3(dbColl, size, -days, chunkSize);
                responseMap.put("OK", "1");
                responseMap.put("SK_MSG", "collection migrated, for" + dbColl);
                logger.info("Car job completed for ns: {} , size {} ",dbColl, size );
            }
            else if (MotorLoggerArchivalServiceImpl.isOldCarMoveS3JobRunning1) {
                logger.info(" moveBookedLeadCar migration already running at: {} , for na: {} stop {}  {}", new Date(),dbColl, stop );
                logger.info(" coll {} , startId, {} , size {} ",ns, size );
                responseMap.put("OK", "0");
                responseMap.put("SK_MSG", "Could not initiate migration at this time , " + new Date());
            }
        } catch (Exception e) {
            responseMap.put("OK", "0");
            responseMap.put("SK_MSG", "Error in moveBookedLeadCar : ," + e.getMessage());
            logger.error("Error in Car moveBookedLeadCar , msg: {}", e.getMessage());
        }
        logger.info("completed job car moveBookedLeadCar, at: {} , for ns: ", new Date(), dbColl);
        return responseMap;
    }
    @GetMapping(value = "/carTemp/moveBookedDocsByEnquiryId/v4")
    public Map<String, String> moveBookedDocsByEnquiryIdcarTempV4(@RequestParam(required = false,value = "size", defaultValue = "500") Integer size,
                                                                  @RequestParam(required = false,value = "stop", defaultValue = "false") boolean stop,
                                                                  @RequestParam(value = "ns") String dbColl,
                                                                  @RequestParam(value="dayBefore") int days,
                                                                  @RequestParam(value="chunkSize") int chunkSize,
                                                                  @RequestHeader String authKey){
        logger.info("Inside /car/moveBookedDocsByEnquiryIdV2 ");
        Map<String, String> responseMap = new HashMap<String, String>();

        try {
            // check authKey if not matched then return.
            if(!authKey.equals(REQ_HEADER_AUTHKEY)){
                responseMap.put("error", "Auth key is not valid..");
                return responseMap;
            }
            String[] ns = {"CarLoggerDB.ReqResLog","Recovery.ReqResLog"};
            if(!Arrays.asList(ns).contains(dbColl)){
                responseMap.put("error", "ns is not whitelisted to proceed further...");
                return responseMap;
            }
            if(stop && MotorLoggerArchivalServiceImpl.isOldCarMoveS3JobRunning2) {
                MotorLoggerArchivalServiceImpl.isOldCarMoveS3JobStop = stop;
            } else {
                MotorLoggerArchivalServiceImpl.isOldCarMoveS3JobStop = false;
            }

            if(size>1000) {
                size = 1000;
            }
            if(chunkSize>500) {
                chunkSize = 500;
            }

            // if not running then run the job
            if (!MotorLoggerArchivalServiceImpl.isOldCarMoveS3JobRunning2) {
                MotorLoggerArchivalServiceImpl.isOldCarMoveS3JobRunning2 = true;
                MotorLoggerArchivalServiceImpl.isOldCarMoveS3JobStop = false;
                motorLoggerArchivalService.moveOldMotorBookLeadsV4(dbColl, size, -days, chunkSize);
                responseMap.put("OK", "1");
                responseMap.put("SK_MSG", "collection migrated, for" + dbColl);
                logger.info("Car job completed for ns: {} , size {} ",dbColl, size );
            }
            else if (MotorLoggerArchivalServiceImpl.isOldCarMoveS3JobRunning2) {
                logger.info(" moveBookedLeadCar migration already running at: {} , for na: {} stop {}  {}", new Date(),dbColl, stop );
                logger.info(" coll {} , startId, {} , size {} ",ns, size );
                responseMap.put("OK", "0");
                responseMap.put("SK_MSG", "Could not initiate migration at this time , " + new Date());
            }
        } catch (Exception e) {
            responseMap.put("OK", "0");
            responseMap.put("SK_MSG", "Error in moveBookedLeadCar : ," + e.getMessage());
            logger.error("Error in Car moveBookedLeadCar , msg: {}", e.getMessage());
        }
        logger.info("completed job car moveBookedLeadCar, at: {} , for ns: ", new Date(), dbColl);
        return responseMap;
    }
    @GetMapping(value = "/carTemp/moveBookedDocsByEnquiryId/v5")
    public Map<String, String> moveBookedDocsByEnquiryIdcarTempV5(@RequestParam(required = false,value = "size", defaultValue = "500") Integer size,
                                                                  @RequestParam(required = false,value = "stop", defaultValue = "false") boolean stop,
                                                                  @RequestParam(value = "ns") String dbColl,
                                                                  @RequestParam(value="dayBefore") int days,
                                                                  @RequestParam(value="chunkSize") int chunkSize,
                                                                  @RequestHeader String authKey){
        logger.info("Inside /car/moveBookedDocsByEnquiryIdV2 ");
        Map<String, String> responseMap = new HashMap<String, String>();

        try {
            // check authKey if not matched then return.
            if(!authKey.equals(REQ_HEADER_AUTHKEY)){
                responseMap.put("error", "Auth key is not valid..");
                return responseMap;
            }
            String[] ns = {"CarLoggerDB.ReqResLog","Recovery.ReqResLog"};
            if(!Arrays.asList(ns).contains(dbColl)){
                responseMap.put("error", "ns is not whitelisted to proceed further...");
                return responseMap;
            }
            if(stop && MotorLoggerArchivalServiceImpl.isOldCarMoveS3JobRunning3) {
                MotorLoggerArchivalServiceImpl.isOldCarMoveS3JobStop = stop;
            } else {
                MotorLoggerArchivalServiceImpl.isOldCarMoveS3JobStop = false;
            }

            if(size>1000) {
                size = 1000;
            }
            if(chunkSize>500) {
                chunkSize = 500;
            }

            // if not running then run the job
            if (!MotorLoggerArchivalServiceImpl.isOldCarMoveS3JobRunning3) {
                MotorLoggerArchivalServiceImpl.isOldCarMoveS3JobRunning3 = true;
                MotorLoggerArchivalServiceImpl.isOldCarMoveS3JobStop = false;
                motorLoggerArchivalService.moveOldMotorBookLeadsV5(dbColl, size, -days, chunkSize);
                responseMap.put("OK", "1");
                responseMap.put("SK_MSG", "collection migrated, for" + dbColl);
                logger.info("Car job completed for ns: {} , size {} ",dbColl, size );
            }
            else if (MotorLoggerArchivalServiceImpl.isOldCarMoveS3JobRunning3) {
                logger.info(" moveBookedLeadCar migration already running at: {} , for na: {} stop {}  {}", new Date(),dbColl, stop );
                logger.info(" coll {} , startId, {} , size {} ",ns, size );
                responseMap.put("OK", "0");
                responseMap.put("SK_MSG", "Could not initiate migration at this time , " + new Date());
            }
        } catch (Exception e) {
            responseMap.put("OK", "0");
            responseMap.put("SK_MSG", "Error in moveBookedLeadCar : ," + e.getMessage());
            logger.error("Error in Car moveBookedLeadCar , msg: {}", e.getMessage());
        }
        logger.info("completed job car moveBookedLeadCar, at: {} , for ns: ", new Date(), dbColl);
        return responseMap;
    }
    @GetMapping(value = "/carTemp/moveBookedDocsByEnquiryId/v6")
    public Map<String, String> moveBookedDocsByEnquiryIdcarTempV6(@RequestParam(required = false,value = "size", defaultValue = "500") Integer size,
                                                                  @RequestParam(required = false,value = "stop", defaultValue = "false") boolean stop,
                                                                  @RequestParam(value = "ns") String dbColl,
                                                                  @RequestParam(value="dayBefore") int days,
                                                                  @RequestParam(value="chunkSize") int chunkSize,
                                                                  @RequestHeader String authKey){
        logger.info("Inside /car/moveBookedDocsByEnquiryIdV2 ");
        Map<String, String> responseMap = new HashMap<String, String>();

        try {
            // check authKey if not matched then return.
            if(!authKey.equals(REQ_HEADER_AUTHKEY)){
                responseMap.put("error", "Auth key is not valid..");
                return responseMap;
            }
            String[] ns = {"CarLoggerDB.ReqResLog","Recovery.ReqResLog"};
            if(!Arrays.asList(ns).contains(dbColl)){
                responseMap.put("error", "ns is not whitelisted to proceed further...");
                return responseMap;
            }
            if(stop && MotorLoggerArchivalServiceImpl.isOldCarMoveS3JobRunning4) {
                MotorLoggerArchivalServiceImpl.isOldCarMoveS3JobStop = stop;
            } else {
                MotorLoggerArchivalServiceImpl.isOldCarMoveS3JobStop = false;
            }

            if(size>1000) {
                size = 1000;
            }
            if(chunkSize>500) {
                chunkSize = 500;
            }

            // if not running then run the job
            if (!MotorLoggerArchivalServiceImpl.isOldCarMoveS3JobRunning4) {
                MotorLoggerArchivalServiceImpl.isOldCarMoveS3JobRunning4 = true;
                MotorLoggerArchivalServiceImpl.isOldCarMoveS3JobStop = false;
                motorLoggerArchivalService.moveOldMotorBookLeadsV6(dbColl, size, -days, chunkSize);
                responseMap.put("OK", "1");
                responseMap.put("SK_MSG", "collection migrated, for" + dbColl);
                logger.info("Car job completed for ns: {} , size {} ",dbColl, size );
            }
            else if (MotorLoggerArchivalServiceImpl.isOldCarMoveS3JobRunning4) {
                logger.info(" moveBookedLeadCar migration already running at: {} , for na: {} stop {}  {}", new Date(),dbColl, stop );
                logger.info(" coll {} , startId, {} , size {} ",ns, size );
                responseMap.put("OK", "0");
                responseMap.put("SK_MSG", "Could not initiate migration at this time , " + new Date());
            }
        } catch (Exception e) {
            responseMap.put("OK", "0");
            responseMap.put("SK_MSG", "Error in moveBookedLeadCar : ," + e.getMessage());
            logger.error("Error in Car moveBookedLeadCar , msg: {}", e.getMessage());
        }
        logger.info("completed job car moveBookedLeadCar, at: {} , for ns: ", new Date(), dbColl);
        return responseMap;
    }
    @GetMapping(value = "/carTemp/moveBookedDocsByEnquiryId/v7")
    public Map<String, String> moveBookedDocsByEnquiryIdcarTempV7(@RequestParam(required = false,value = "size", defaultValue = "500") Integer size,
                                                                  @RequestParam(required = false,value = "stop", defaultValue = "false") boolean stop,
                                                                  @RequestParam(value = "ns") String dbColl,
                                                                  @RequestParam(value="dayBefore") int days,
                                                                  @RequestParam(value="chunkSize") int chunkSize,
                                                                  @RequestHeader String authKey){
        logger.info("Inside /car/moveBookedDocsByEnquiryIdV2 ");
        Map<String, String> responseMap = new HashMap<String, String>();

        try {
            // check authKey if not matched then return.
            if(!authKey.equals(REQ_HEADER_AUTHKEY)){
                responseMap.put("error", "Auth key is not valid..");
                return responseMap;
            }
            String[] ns = {"CarLoggerDB.ReqResLog","Recovery.ReqResLog"};
            if(!Arrays.asList(ns).contains(dbColl)){
                responseMap.put("error", "ns is not whitelisted to proceed further...");
                return responseMap;
            }
            if(stop && MotorLoggerArchivalServiceImpl.isOldCarMoveS3JobRunning5) {
                MotorLoggerArchivalServiceImpl.isOldCarMoveS3JobStop = stop;
            } else {
                MotorLoggerArchivalServiceImpl.isOldCarMoveS3JobStop = false;
            }

            if(size>1000) {
                size = 1000;
            }
            if(chunkSize>500) {
                chunkSize = 500;
            }

            // if not running then run the job
            if (!MotorLoggerArchivalServiceImpl.isOldCarMoveS3JobRunning5) {
                MotorLoggerArchivalServiceImpl.isOldCarMoveS3JobRunning5 = true;
                MotorLoggerArchivalServiceImpl.isOldCarMoveS3JobStop = false;
                motorLoggerArchivalService.moveOldMotorBookLeadsV7(dbColl, size, -days, chunkSize);
                responseMap.put("OK", "1");
                responseMap.put("SK_MSG", "collection migrated, for" + dbColl);
                logger.info("Car job completed for ns: {} , size {} ",dbColl, size );
            }
            else if (MotorLoggerArchivalServiceImpl.isOldCarMoveS3JobRunning5) {
                logger.info(" moveBookedLeadCar migration already running at: {} , for na: {} stop {}  {}", new Date(),dbColl, stop );
                logger.info(" coll {} , startId, {} , size {} ",ns, size );
                responseMap.put("OK", "0");
                responseMap.put("SK_MSG", "Could not initiate migration at this time , " + new Date());
            }
        } catch (Exception e) {
            responseMap.put("OK", "0");
            responseMap.put("SK_MSG", "Error in moveBookedLeadCar : ," + e.getMessage());
            logger.error("Error in Car moveBookedLeadCar , msg: {}", e.getMessage());
        }
        logger.info("completed job car moveBookedLeadCar, at: {} , for ns: ", new Date(), dbColl);
        return responseMap;
    }

}
