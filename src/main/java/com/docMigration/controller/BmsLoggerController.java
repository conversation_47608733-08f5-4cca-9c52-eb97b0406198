package com.docMigration.controller;

import com.docMigration.cleanup.entity.StaticMaster;
import com.docMigration.service.impl.BmsLoggerServiceImpl;
import com.docMigration.util.DateUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import java.io.File;

@RestController
@RequestMapping("/bms")
public class BmsLoggerController {
    private final Logger logger = LoggerFactory.getLogger(BmsLoggerController.class);
    private static final String REQ_HEADER_AUTHKEY = "92kik4i4jn4oi4n3oi4nn4k4l4lk4lknwlk4l4ljk44444klknn4h4jk5k";
    @Autowired
    BmsLoggerServiceImpl bmsLoggerService;

    /**
     * This is to
     * 1. move from CommunicaitonDB.CommV2FileLogs to /S3:CommV2FileLogs and
     * 2. delete from source by _id
     * source: ns=CommunicationDB.CommV2FileLogs
     * target: s3
     * @param size
     * @param stop
     * @param dbColl
     * @param days
     * @return
     *
     * curl --location 'localhost:8090/moveCommV2FileLogsToS3?coll=mytest&size=5&days=0&type=1&ns=communicationDB.CommV2FileLogsv2&stop=false'
     */
    @GetMapping(value = "/moveCommV2FileLogsToS3")
    public Map<String, String> moveCommV2FileLogsToS3(@RequestParam(required = false,value = "size") Integer size,
                                                      @RequestParam(required = false,value = "stop") Boolean stop,
                                                      @RequestParam(value = "ns") String dbColl,
                                                      @RequestParam(value = "jobId") String jobId,
                                                      @RequestParam(value="dayBefore", defaultValue = "30") int days,
                                                      @RequestParam(value="chunkSize", defaultValue = "100") int chunkSize,
                                                      @RequestHeader(value = "authKey") String authKey,
                                                      @RequestParam(value = "movedIdsCollection", defaultValue = "movedIdsLogger") String movedIdsCollection){
        logger.info("Inside moveCommV2FileLogsToS3 ");
        Map<String, String> result = new HashMap<String, String>();
        // check authKey if not matched then return.
        if(!authKey.equals(REQ_HEADER_AUTHKEY)){
            result.put("error", "Auth key is not valid..");
            return result;
        }
        String[] ns = {"communicationDB.CommV2FileLogsv2", "communicationDB_dev.CommV2FileLogs"};
        if(!Arrays.asList(ns).contains(dbColl)){
            result.put("error", "ns is not whitelisted to proceed further...");
            return result;
        }

        File lockFile = new File("/tmp/moveCommV2FileLogsToS3_" + movedIdsCollection + ".lock");

        try {

            if (Boolean.TRUE.equals(stop)) {
                if (lockFile.exists()) {
                    boolean deleted = lockFile.delete();
                    result.put("OK", deleted ? "1" : "0");
                    result.put("SK_MSG", deleted ? "Stopped job by deleting lock file." : "Failed to delete lock file.");
                } else {
                    result.put("OK", "0");
                    result.put("SK_MSG", "No running job found to stop.");
                }
                return result;
            }

            // Check if lock file already exists
            if (lockFile.exists()) {
                logger.info("Lock file exists for {}. Job already running.", movedIdsCollection);
                result.put("OK", "0");
                result.put("SK_MSG", "Job already running for " + movedIdsCollection);
                return result;
            }

            // Create lock file
            boolean created = lockFile.createNewFile();
            if (!created) {
                result.put("OK", "0");
                result.put("SK_MSG", "Failed to create lock file.");
                return result;
            }

            logger.info("Starting job for {} with lock file: {}", movedIdsCollection, lockFile.getAbsolutePath());

            // Limit parameters
            if (size != null && size > 1000) size = 1000;
            if (days < 30) days = 30;
            if (chunkSize > 500) chunkSize = 500;

            // Define final local copies
            final Integer finalSize = size;
            final int finalDays = days;
            final int finalChunkSize = chunkSize;
            final String finalDbColl = dbColl;
            final String finalMovedIdsCollection = movedIdsCollection;
            final String finalJobId = jobId;

            new Thread(() -> {
                try {
                    bmsLoggerService.moveCommV2FileLogsToS3(finalDbColl, finalSize,-finalDays, finalChunkSize, finalJobId, finalMovedIdsCollection);
                } catch (Exception e) {
                    logger.error("Error while processing {}: {}", movedIdsCollection, e.getMessage());
                } finally {
                    boolean deleted = lockFile.delete();
                    if (deleted) {
                        logger.info("Lock file deleted after job completion for {}", movedIdsCollection);
                    } else {
                        logger.warn("Failed to delete lock file for {}", movedIdsCollection);
                    }
                }
            }).start();

            result.put("OK", "1");
            result.put("SK_MSG", "Job started for " + movedIdsCollection);
        } catch (Exception e) {
            result.put("OK", "0");
            result.put("SK_MSG", "Error in moveCommV2FileLogsToS3 : ," + e.getMessage());
            logger.error("Error in moveCommV2FileLogsToS3 , {}", e.getMessage());
        }
        logger.info("Exiting moveCommV2FileLogsToS3 ");
        return result;
    }
    @GetMapping(value = "/moveCommV2FileLogsToS3/v1")
    public Map<String, String> moveCommV2FileLogsToS3V1(@RequestParam(required = false,value = "size") Integer size,
                                                      @RequestParam(required = false,value = "stop") Boolean stop,
                                                      @RequestParam(value = "ns") String dbColl,
                                                      @RequestParam(required = false, value = "lte") String lte_Id,
                                                      @RequestParam(value="dayBefore", defaultValue = "30") int days,
                                                      @RequestParam(value="chunkSize", defaultValue = "100") int chunkSize,
                                                      @RequestHeader(value = "authKey") String authKey){
        logger.info("Inside moveCommV2FileLogsToS3V1 ");
        Map<String, String> result = new HashMap<String, String>();
        // check authKey if not matched then return.
        if(!authKey.equals(REQ_HEADER_AUTHKEY)){
            result.put("error", "Auth key is not valid..");
            return result;
        }
        String[] ns = {"communicationDB.CommV2FileLogsv2", "communicationDB_dev.CommV2FileLogs"};
        if(!Arrays.asList(ns).contains(dbColl)){
            result.put("error", "ns is not whitelisted to proceed further...");
            return result;
        }
        try {

            if(stop && BmsLoggerServiceImpl.isMoveCommV2FileLogsLoggerJobRunningV1) {
                BmsLoggerServiceImpl.isMoveCommV2FileLogsLoggerJobStopV1 = stop;
            }else{
                BmsLoggerServiceImpl.isMoveCommV2FileLogsLoggerJobStopV1 = false;
            }
            if(size>1000) {
                size = 1000;
            }
            if(days < 30){
                days = 30;
            }
            if(chunkSize>500) {
                chunkSize = 500;
            }
            if (!BmsLoggerServiceImpl.isMoveCommV2FileLogsLoggerJobRunningV1) {
                BmsLoggerServiceImpl.isMoveCommV2FileLogsLoggerJobRunningV1 = true;
                BmsLoggerServiceImpl.isMoveCommV2FileLogsLoggerJobStopV1 = false;
                bmsLoggerService.moveCommV2FileLogsToS3V1(dbColl, size,-days, chunkSize, lte_Id);
                result.put("OK", "1");
                result.put("SK_MSG", "collection migrated");
            }else if(BmsLoggerServiceImpl.isMoveCommV2FileLogsLoggerJobRunningV1){
                logger.info("Could not initiate moveCommV2FileLogsToS3 at this time as it is running {} , stop {}    ", new Date(), stop);
                logger.info(" ns {} , size {} ",dbColl, size );
                result.put("OK", "0");
                result.put("SK_MSG", "not initiate moveCommV2FileLogsToS3V1, " + new Date());
            }
        } catch (Exception e) {
            result.put("OK", "0");
            result.put("SK_MSG", "Error in moveCommV2FileLogsToS3V1 : ," + e.getMessage());
            logger.error("Error in moveCommV2FileLogsToS3V1 , {}", e.getMessage());
        }
        logger.info("Exiting moveCommV2FileLogsToS3V1 ");
        return result;
    }

    @GetMapping(value = "/movePIVCLogsToS3")
    public Map<String, String> movePIVCLogsToS3(@RequestParam(required = false,value = "size") Integer size,
                                                      @RequestParam(required = false,value = "stop") Boolean stop,
                                                      @RequestParam(value = "ns") String dbColl,
                                                      @RequestParam(value="dayBefore", defaultValue = "30") int days,
                                                      @RequestParam(value="chunkSize", defaultValue = "100") int chunkSize,
                                                      @RequestParam(value = "authKey") String authKey){
        logger.info("Inside movePIVCLogsToS3 ");
        Map<String, String> result = new HashMap<String, String>();
        // check authKey if not matched then return.
        if(!authKey.equals(REQ_HEADER_AUTHKEY)){
            result.put("error", "Auth key is not valid..");
            return result;
        }
        String[] ns = {"Integration.PIVCLog"};
        if(!Arrays.asList(ns).contains(dbColl)){
            result.put("error", "ns is not whitelisted to proceed further...");
            return result;
        }
        try {

            if(stop && BmsLoggerServiceImpl.isMovePIVCLogsJobRunning) {
                BmsLoggerServiceImpl.isMovePIVCLogsJobStop = stop;
            }else{
                BmsLoggerServiceImpl.isMovePIVCLogsJobStop = false;
            }
            if(size>1000) {
                size = 1000;
            }
            if(days < 100){
                days = 100;
            }
            if(chunkSize>500) {
                chunkSize = 500;
            }
            if (!BmsLoggerServiceImpl.isMovePIVCLogsJobRunning) {
                BmsLoggerServiceImpl.isMovePIVCLogsJobRunning = true;
                BmsLoggerServiceImpl.isMovePIVCLogsJobStop = false;
                bmsLoggerService.movePIVCLogsToS3(dbColl, size,-days, chunkSize);
                result.put("OK", "1");
                result.put("SK_MSG", "collection migrated");
            }else if(BmsLoggerServiceImpl.isMovePIVCLogsJobRunning){
                logger.info("Could not initiate movePIVCLogsToS3 at this time as it is running {} , stop {}    ", new Date(), stop);
                logger.info(" ns {} , size {} ",dbColl, size );
                result.put("OK", "0");
                result.put("SK_MSG", "not initiate movePIVCLogsToS3, " + new Date());
            }
        } catch (Exception e) {
            result.put("OK", "0");
            result.put("SK_MSG", "Error in movePIVCLogsToS3 : ," + e.getMessage());
            logger.error("Error in movePIVCLogsToS3 , {}", e.getMessage());
        }
        logger.info("Exiting movePIVCLogsToS3 ");
        return result;
    }
    @GetMapping(value = "/moveDocPushLogsToS3")
    public Map<String, String> moveDocPushLogsToS3(@RequestParam(required = false,value = "size") Integer size,
                                                @RequestParam(required = false,value = "stop") Boolean stop,
                                                @RequestParam(value = "ns") String dbColl,
                                                @RequestParam(value="dayBefore", defaultValue = "30") int days,
                                                @RequestParam(value="chunkSize", defaultValue = "100") int chunkSize,
                                                @RequestParam(value = "authKey") String authKey,
                                                   @RequestParam(value = "movedIdsCollection", defaultValue = "movedIdsLogger") String movedIdsCollection){
        logger.info("Inside moveDocPushLogsToS3 ");
        Map<String, String> result = new HashMap<String, String>();
        // check authKey if not matched then return.
        if(!authKey.equals(REQ_HEADER_AUTHKEY)){
            result.put("error", "Auth key is not valid..");
            return result;
        }
        String[] ns = {"DocsPoint.DocPushLog","CommBox_Logger-DocsPoint.DocPushLog"};
        if(!Arrays.asList(ns).contains(dbColl)){
            result.put("error", "ns is not whitelisted to proceed further...");
            return result;
        }

        File lockFile = new File("/tmp/moveDocPushLogs_" + movedIdsCollection + ".lock");

        try {

            if (Boolean.TRUE.equals(stop)) {
                if (lockFile.exists()) {
                    boolean deleted = lockFile.delete();
                    result.put("OK", deleted ? "1" : "0");
                    result.put("SK_MSG", deleted ? "Stopped job by deleting lock file." : "Failed to delete lock file.");
                } else {
                    result.put("OK", "0");
                    result.put("SK_MSG", "No running job found to stop.");
                }
                return result;
            }

            // Check if lock file already exists
            if (lockFile.exists()) {
                logger.info("Lock file exists for {}. Job already running.", movedIdsCollection);
                result.put("OK", "0");
                result.put("SK_MSG", "Job already running for " + movedIdsCollection);
                return result;
            }

            // Create lock file
            boolean created = lockFile.createNewFile();
            if (!created) {
                result.put("OK", "0");
                result.put("SK_MSG", "Failed to create lock file.");
                return result;
            }

            logger.info("Starting job for {} with lock file: {}", movedIdsCollection, lockFile.getAbsolutePath());


            // Limit parameters
            if (size != null && size > 1000) size = 1000;
            if (days < 90) days = 90;
            if (chunkSize > 500) chunkSize = 500;

            // Define final local copies
            final Integer finalSize = size;
            final int finalDays = days;
            final int finalChunkSize = chunkSize;
            final String finalDbColl = dbColl;
            final String finalMovedIdsCollection = movedIdsCollection;

            new Thread(() -> {
                try {
                    bmsLoggerService.moveDocPushLogsToS3V1(finalDbColl, finalSize, -finalDays, finalChunkSize, finalMovedIdsCollection);
                } catch (Exception e) {
                    logger.error("Error while processing {}: {}", movedIdsCollection, e.getMessage());
                } finally {
                    boolean deleted = lockFile.delete();
                    if (deleted) {
                        logger.info("Lock file deleted after job completion for {}", movedIdsCollection);
                    } else {
                        logger.warn("Failed to delete lock file for {}", movedIdsCollection);
                    }
                }
            }).start();
    
            result.put("OK", "1");
            result.put("SK_MSG", "Job started for " + movedIdsCollection);
        } catch (Exception e) {
            result.put("OK", "0");
            result.put("SK_MSG", "Error in moveDocPushLogsToS3 : ," + e.getMessage());
            logger.error("Error in moveDocPushLogsToS3 , {}", e.getMessage());
        }
        logger.info("Exiting moveDocPushLogsToS3 ");
        return result;
    }
    @GetMapping(value = "/moveDocPushLogsToS3/v2")
    public Map<String, String> moveDocPushLogsToS3V2(@RequestParam(required = false,value = "size") Integer size,
                                                   @RequestParam(required = false,value = "stop") Boolean stop,
                                                   @RequestParam(value = "ns") String dbColl,
                                                   @RequestParam(value="dayBefore", defaultValue = "30") int days,
                                                   @RequestParam(value="chunkSize", defaultValue = "100") int chunkSize,
                                                     @RequestParam(value = "lte") String lte_id,
                                                   @RequestParam(value = "authKey") String authKey){
        logger.info("Inside moveDocPushLogsToS3 ");
        Map<String, String> result = new HashMap<String, String>();
        // check authKey if not matched then return.
        if(!authKey.equals(REQ_HEADER_AUTHKEY)){
            result.put("error", "Auth key is not valid..");
            return result;
        }
        String[] ns = {"DocsPoint.DocPushLog"};
        if(!Arrays.asList(ns).contains(dbColl)){
            result.put("error", "ns is not whitelisted to proceed further...");
            return result;
        }
        try {

            if(stop && BmsLoggerServiceImpl.isMoveDocPushLogsJobRunningV2) {
                BmsLoggerServiceImpl.isMoveDocPushLogsJobStopV2 = stop;
            }else{
                BmsLoggerServiceImpl.isMoveDocPushLogsJobStopV2 = false;
            }
            if(size>1000) {
                size = 1000;
            }
            if(days < 90){
                days = 90;
            }
            if(chunkSize>500) {
                chunkSize = 500;
            }
            if (!BmsLoggerServiceImpl.isMoveDocPushLogsJobRunningV2) {
                BmsLoggerServiceImpl.isMoveDocPushLogsJobRunningV2 = true;
                BmsLoggerServiceImpl.isMoveDocPushLogsJobStopV2 = false;
                bmsLoggerService.moveDocPushLogsToS3V2(dbColl, size,-days, chunkSize, lte_id);
                result.put("OK", "1");
                result.put("SK_MSG", "collection migrated");
            }else if(BmsLoggerServiceImpl.isMoveDocPushLogsJobRunningV2){
                logger.info("Could not initiate moveDocPushLogsToS3 at this time as it is running {} , stop {}    ", new Date(), stop);
                logger.info(" ns {} , size {} ",dbColl, size );
                result.put("OK", "0");
                result.put("SK_MSG", "not initiate moveDocPushLogsToS3, " + new Date());
            }
        } catch (Exception e) {
            result.put("OK", "0");
            result.put("SK_MSG", "Error in moveDocPushLogsToS3 : ," + e.getMessage());
            logger.error("Error in moveDocPushLogsToS3 , {}", e.getMessage());
        }
        logger.info("Exiting moveDocPushLogsToS3 ");
        return result;
    }
    @GetMapping(value = "/tempMoveDocPushLogsToS3/v1")
    public Map<String, String> tempMoveDocPushLogsToS3V1(@RequestParam(required = false,value = "size") Integer size,
                                                   @RequestParam(required = false,value = "stop") Boolean stop,
                                                   @RequestParam(value = "ns") String dbColl,
                                                   @RequestParam(value="dayBefore", defaultValue = "30") int days,
                                                   @RequestParam(value="chunkSize", defaultValue = "100") int chunkSize,
                                                   @RequestParam(value = "authKey") String authKey){
        logger.info("Inside moveDocPushLogsToS3 ");
        Map<String, String> result = new HashMap<String, String>();
        // check authKey if not matched then return.
        if(!authKey.equals(REQ_HEADER_AUTHKEY)){
            result.put("error", "Auth key is not valid..");
            return result;
        }
        String[] ns = {"DocsPoint.DocPushLog","CommBox_Logger-DocsPoint.DocPushLog"};
        if(!Arrays.asList(ns).contains(dbColl)){
            result.put("error", "ns is not whitelisted to proceed further...");
            return result;
        }
        try {

            if(stop && BmsLoggerServiceImpl.isTempMoveDocPushLogsJobRunning) {
                BmsLoggerServiceImpl.isTempMoveDocPushLogsJobStop = stop;
            }else{
                BmsLoggerServiceImpl.isTempMoveDocPushLogsJobStop = false;
            }
            if(size>1000) {
                size = 1000;
            }
            if(days < 90){
                days = 90;
            }
            if(chunkSize>500) {
                chunkSize = 500;
            }
            if (!BmsLoggerServiceImpl.isTempMoveDocPushLogsJobRunning) {
                BmsLoggerServiceImpl.isTempMoveDocPushLogsJobRunning = true;
                BmsLoggerServiceImpl.isTempMoveDocPushLogsJobStop = false;
                bmsLoggerService.tempMoveDocPushLogsToS3V1(dbColl, size,-days, chunkSize);
                result.put("OK", "1");
                result.put("SK_MSG", "collection migrated");
            }else if(BmsLoggerServiceImpl.isTempMoveDocPushLogsJobRunning){
                logger.info("Could not initiate moveDocPushLogsToS3 at this time as it is running {} , stop {}    ", new Date(), stop);
                logger.info(" ns {} , size {} ",dbColl, size );
                result.put("OK", "0");
                result.put("SK_MSG", "not initiate moveDocPushLogsToS3, " + new Date());
            }
        } catch (Exception e) {
            result.put("OK", "0");
            result.put("SK_MSG", "Error in moveDocPushLogsToS3 : ," + e.getMessage());
            logger.error("Error in moveDocPushLogsToS3 , {}", e.getMessage());
        }
        logger.info("Exiting moveDocPushLogsToS3 ");
        return result;
    }
    @GetMapping(value = "/tempMoveDocPushLogsToS3/v2")
    public Map<String, String> tempMoveDocPushLogsToS3V2(@RequestParam(required = false,value = "size") Integer size,
                                                     @RequestParam(required = false,value = "stop") Boolean stop,
                                                     @RequestParam(value = "ns") String dbColl,
                                                     @RequestParam(value="dayBefore", defaultValue = "30") int days,
                                                     @RequestParam(value="chunkSize", defaultValue = "100") int chunkSize,
                                                     @RequestParam(value = "lte") String lte_id,
                                                     @RequestParam(value = "authKey") String authKey){
        logger.info("Inside moveDocPushLogsToS3 ");
        Map<String, String> result = new HashMap<String, String>();
        // check authKey if not matched then return.
        if(!authKey.equals(REQ_HEADER_AUTHKEY)){
            result.put("error", "Auth key is not valid..");
            return result;
        }
        String[] ns = {"DocsPoint.DocPushLog","CommBox_Logger-DocsPoint.DocPushLog"};
        if(!Arrays.asList(ns).contains(dbColl)){
            result.put("error", "ns is not whitelisted to proceed further...");
            return result;
        }
        try {

            if(stop && BmsLoggerServiceImpl.isTempMoveDocPushLogsJobRunningV2) {
                BmsLoggerServiceImpl.isTempMoveDocPushLogsJobStopV2 = stop;
            }else{
                BmsLoggerServiceImpl.isTempMoveDocPushLogsJobStopV2 = false;
            }
            if(size>1000) {
                size = 1000;
            }
            if(days < 90){
                days = 90;
            }
            if(chunkSize>500) {
                chunkSize = 500;
            }
            if (!BmsLoggerServiceImpl.isTempMoveDocPushLogsJobRunningV2) {
                BmsLoggerServiceImpl.isTempMoveDocPushLogsJobRunningV2 = true;
                BmsLoggerServiceImpl.isTempMoveDocPushLogsJobStopV2 = false;
                bmsLoggerService.tempMoveDocPushLogsToS3V2(dbColl, size,-days, chunkSize, lte_id);
                result.put("OK", "1");
                result.put("SK_MSG", "collection migrated");
            }else if(BmsLoggerServiceImpl.isTempMoveDocPushLogsJobRunningV2){
                logger.info("Could not initiate moveDocPushLogsToS3 at this time as it is running {} , stop {}    ", new Date(), stop);
                logger.info(" ns {} , size {} ",dbColl, size );
                result.put("OK", "0");
                result.put("SK_MSG", "not initiate moveDocPushLogsToS3, " + new Date());
            }
        } catch (Exception e) {
            result.put("OK", "0");
            result.put("SK_MSG", "Error in moveDocPushLogsToS3 : ," + e.getMessage());
            logger.error("Error in moveDocPushLogsToS3 , {}", e.getMessage());
        }
        logger.info("Exiting moveDocPushLogsToS3 ");
        return result;
    }
    @GetMapping(value = "/moveDocsRequestLogsToS3")
    public Map<String, String> moveDocsRequestLogsToS3(@RequestParam(required = false,value = "size") Integer size,
                                                   @RequestParam(required = false,value = "stop") Boolean stop,
                                                   @RequestParam(value = "ns") String dbColl,
                                                   @RequestParam(value="dayBefore", defaultValue = "30") int days,
                                                   @RequestParam(value="chunkSize", defaultValue = "100") int chunkSize,
                                                   @RequestParam(value = "authKey") String authKey){
        logger.info("Inside moveDocsRequestLogsToS3 ");
        Map<String, String> result = new HashMap<String, String>();
        // check authKey if not matched then return.
        if(!authKey.equals(REQ_HEADER_AUTHKEY)){
            result.put("error", "Auth key is not valid..");
            return result;
        }
        String[] ns = {"DocsPoint.RequestLogs"};
        if(!Arrays.asList(ns).contains(dbColl)){
            result.put("error", "ns is not whitelisted to proceed further...");
            return result;
        }
        try {

            if(stop && BmsLoggerServiceImpl.isMoveRequestLogsJobRunning) {
                BmsLoggerServiceImpl.isMoveRequestLogsJobStop= stop;
            }else{
                BmsLoggerServiceImpl.isMoveRequestLogsJobStop = false;
            }
            if(size>1000) {
                size = 1000;
            }
            if(days < 60){
                days = 60;
            }
            if(chunkSize>500) {
                chunkSize = 500;
            }
            if (!BmsLoggerServiceImpl.isMoveRequestLogsJobRunning) {
                BmsLoggerServiceImpl.isMoveRequestLogsJobRunning = true;
                BmsLoggerServiceImpl.isMoveRequestLogsJobStop = false;
                bmsLoggerService.moveRequestLogsLogsToS3(dbColl, size,-days, chunkSize);
                result.put("OK", "1");
                result.put("SK_MSG", "collection migrated");
            }else if(BmsLoggerServiceImpl.isMoveRequestLogsJobRunning){
                logger.info("Could not initiate moveDocsRequestLogsToS3 at this time as it is running {} , stop {}    ", new Date(), stop);
                logger.info(" ns {} , size {} ",dbColl, size );
                result.put("OK", "0");
                result.put("SK_MSG", "not initiate moveDocsRequestLogsToS3, " + new Date());
            }
        } catch (Exception e) {
            result.put("OK", "0");
            result.put("SK_MSG", "Error in moveRequestLogsLogsToS3 : ," + e.getMessage());
            logger.error("Error in moveRequestLogsLogsToS3 , {}", e.getMessage());
        }
        logger.info("Exiting moveRequestLogsLogsToS3 ");
        return result;
    }

    @GetMapping(value = "/moveIntegrationRequestLogsToS3")
    public Map<String, String> moveIntegrationRequestLogsToS3(@RequestParam(required = false,value = "size") Integer size,
                                                       @RequestParam(required = false,value = "stop") Boolean stop,
                                                       @RequestParam(value = "ns") String dbColl,
                                                       @RequestParam(value="dayBefore", defaultValue = "30") int days,
                                                       @RequestParam(value="chunkSize", defaultValue = "100") int chunkSize,
                                                       @RequestParam(value = "authKey") String authKey){
        logger.info("Inside moveIntegrationRequestLogsToS3 ");
        Map<String, String> result = new HashMap<String, String>();
        // check authKey if not matched then return.
        if(!authKey.equals(REQ_HEADER_AUTHKEY)){
            result.put("error", "Auth key is not valid..");
            return result;
        }
        String[] ns = {"Integration.RequestLogs"};
        if(!Arrays.asList(ns).contains(dbColl)){
            result.put("error", "ns is not whitelisted to proceed further...");
            return result;
        }
        try {

            if(stop && BmsLoggerServiceImpl.isMoveIntRequestLogsJobRunning) {
                BmsLoggerServiceImpl.isMoveIntRequestLogsJobStop= stop;
            }else{
                BmsLoggerServiceImpl.isMoveIntRequestLogsJobStop = false;
            }
            if(size>1000) {
                size = 1000;
            }
            if(days < 60){
                days = 60;
            }
            if(chunkSize>500) {
                chunkSize = 500;
            }
            if (!BmsLoggerServiceImpl.isMoveIntRequestLogsJobRunning) {
                BmsLoggerServiceImpl.isMoveIntRequestLogsJobRunning = true;
                BmsLoggerServiceImpl.isMoveIntRequestLogsJobStop = false;
                bmsLoggerService.moveIntegrationRequestLogsLogsToS3(dbColl, size,-days, chunkSize);
                result.put("OK", "1");
                result.put("SK_MSG", "collection migrated");
            }else if(BmsLoggerServiceImpl.isMoveIntRequestLogsJobRunning){
                logger.info("Could not initiate moveIntegrationRequestLogsToS3 at this time as it is running {} , stop {}    ", new Date(), stop);
                logger.info(" ns {} , size {} ",dbColl, size );
                result.put("OK", "0");
                result.put("SK_MSG", "not initiate moveIntegrationRequestLogsToS3, " + new Date());
            }
        } catch (Exception e) {
            result.put("OK", "0");
            result.put("SK_MSG", "Error in moveIntegrationRequestLogsToS3 : ," + e.getMessage());
            logger.error("Error in moveIntegrationRequestLogsToS3 , {}", e.getMessage());
        }
        logger.info("Exiting moveIntegrationRequestLogsToS3 ");
        return result;
    }
    @GetMapping(value = "/moveIntegrationExternalAPILogsToS3")
    public Map<String, String> moveIntegrationExternalAPILogsToS3(@RequestParam(required = false,value = "size") Integer size,
                                                       @RequestParam(required = false,value = "stop") Boolean stop,
                                                       @RequestParam(value = "ns") String dbColl,
                                                       @RequestParam(value="dayBefore", defaultValue = "30") int days,
                                                       @RequestParam(value="chunkSize", defaultValue = "100") int chunkSize,
                                                       @RequestParam(value = "timeCheck", required = true) boolean timeCheck,
                                                       @RequestParam(value = "authKey") String authKey){
        logger.info("Inside moveDocsRequestLogsToS3 ");
        Map<String, String> result = new HashMap<String, String>();
        // check authKey if not matched then return.
        if(!authKey.equals(REQ_HEADER_AUTHKEY)){
            result.put("error", "Auth key is not valid..");
            return result;
        }
        String[] ns = {"Integration.ExternalAPILogs"};
        if(!Arrays.asList(ns).contains(dbColl)){
            result.put("error", "ns is not whitelisted to proceed further...");
            return result;
        }
        try {
            Date todayDate = new Date();
            /* Date endDate = DateUtil.addDays(todayDate, 1); */
            Date startDate = new Date();
            startDate = DateUtil.setHours(startDate, 7);
            startDate = DateUtil.setMinute(startDate, 0);
            startDate = DateUtil.setSeconds(startDate, 0);

            Date endDate = new Date();
            endDate = DateUtil.setHours(endDate, 20);
            endDate = DateUtil.setMinute(endDate, 0);
            endDate = DateUtil.setSeconds(endDate, 0);

            if(stop && BmsLoggerServiceImpl.isMoveIntegrationExternalAPILogsJobRunning) {
                BmsLoggerServiceImpl.isMoveIntegrationExternalAPILogsJobStop= stop;
            }else{
                BmsLoggerServiceImpl.isMoveIntegrationExternalAPILogsJobStop = false;
            }
            if(size>1000) {
                size = 1000;
            }
            if(days < 90){
                days = 90;
            }
            if(chunkSize>500) {
                chunkSize = 500;
            }
            if ((todayDate.before(startDate) || todayDate.after(endDate) || !timeCheck) && !BmsLoggerServiceImpl.isMoveIntegrationExternalAPILogsJobRunning) {
                BmsLoggerServiceImpl.isMoveIntegrationExternalAPILogsJobRunning = true;
                BmsLoggerServiceImpl.isMoveIntegrationExternalAPILogsJobStop = false;
                bmsLoggerService.moveIntegrationExternalAPILogsToS3(dbColl, size,-days, chunkSize, timeCheck);
                result.put("OK", "1");
                result.put("SK_MSG", "collection migrated");
            }else if(BmsLoggerServiceImpl.isMoveIntegrationExternalAPILogsJobRunning){
                logger.info("Could not initiate moveIntegrationExternalAPILogsToS3 at this time as it is running {} , stop {}    ", new Date(), stop);
                logger.info(" ns {} , size {} ",dbColl, size );
                result.put("OK", "0");
                result.put("SK_MSG", "not initiate moveIntegrationExternalAPILogsToS3, " + new Date());
            }
        } catch (Exception e) {
            result.put("OK", "0");
            result.put("SK_MSG", "Error in moveIntegrationExternalAPILogsToS3 : ," + e.getMessage());
            logger.error("Error in moveIntegrationExternalAPILogsToS3 , {}", e.getMessage());
        }
        logger.info("Exiting moveIntegrationExternalAPILogsToS3 ");
        return result;
    }
    @GetMapping(value = "/movePBCromaV2ToS3/v1")
    public Map<String, String> movePBCromaV2ToS3(@RequestParam(required = false,value = "size") Integer size,
                                                 @RequestParam(required = false,value = "dayBefore") Integer days,
                                                 @RequestParam(required = false,value = "chunkSize") Integer chunkSize,
                                                 @RequestParam(required = false,value = "stop") Boolean stop,
                                                 @RequestParam(value = "ns") String dbColl,
                                                 @RequestHeader(value = "authKey") String authKey){
        logger.info("Inside movePBCromaV2ToS3");
        Map<String, String> result = new HashMap<String, String>();
        // check authKey if not matched then return.
        if(!authKey.equals(REQ_HEADER_AUTHKEY)){
            result.put("error", "Auth key is not valid..");
            return result;
        }
        String[] ns = {"Croma.PBCromaV2"};
        if(!Arrays.asList(ns).contains(dbColl)){
            result.put("error", "ns is not whitelisted to proceed further...");
            return result;
        }
        try {

                if (stop && BmsLoggerServiceImpl.isMoveCromaPBCromaV2LogsJobRunning) {
                    BmsLoggerServiceImpl.isMoveCromaPBCromaV2LogsJobStop = stop;
                } else {
                    BmsLoggerServiceImpl.isMoveCromaPBCromaV2LogsJobStop = false;
                }
                if (size > 1000) {
                    size = 1000;
                }
                if (days < 90) {
                    days = 90;
                }
                if (chunkSize > 500) {
                    chunkSize = 500;
                }
                if (!BmsLoggerServiceImpl.isMoveCromaPBCromaV2LogsJobRunning) {
                    BmsLoggerServiceImpl.isMoveCromaPBCromaV2LogsJobRunning = true;
                    BmsLoggerServiceImpl.isMoveCromaPBCromaV2LogsJobStop = false;
                    bmsLoggerService.movePBCromaV2ToS3(dbColl, size, -days, chunkSize);
                    result.put("OK", "1");
                    result.put("SK_MSG", "collection migrated");
                } else if (BmsLoggerServiceImpl.isMoveCromaPBCromaV2LogsJobRunning) {
                    logger.info("Could not initiate movePBCromaV2ToS3 at this time as it is running {} , stop {}    ", new Date(), stop);
                    logger.info(" ns {} , size {} ", dbColl, size);
                    result.put("OK", "0");
                    result.put("SK_MSG", "not initiate movePBCromaV2ToS3, " + new Date());
                }
        } catch (Exception e) {
            result.put("OK", "0");
            result.put("SK_MSG", "Error in movePBCromaV2ToS3 : ," + e.getMessage());
            logger.error("Error in movePBCromaV2ToS3 , {}", e.getMessage());
        }
        logger.info("Exiting movePBCromaV2ToS3 ");
        return result;
    }
    @GetMapping(value = "/movePBCromaV2SalesToS3/v1")
    public Map<String, String> movePBCromaV2SalesToS3(@RequestParam(required = false,value = "size") Integer size,
                                                 @RequestParam(required = false,value = "dayBefore") Integer days,
                                                 @RequestParam(required = false,value = "chunkSize") Integer chunkSize,
                                                 @RequestParam(required = false,value = "stop") Boolean stop,
                                                 @RequestParam(value = "ns") String dbColl,
                                                 @RequestHeader(value = "authKey") String authKey){
        logger.info("Inside movePBCromaV2SalesToS3");
        Map<String, String> result = new HashMap<String, String>();
        // check authKey if not matched then return.
        if(!authKey.equals(REQ_HEADER_AUTHKEY)){
            result.put("error", "Auth key is not valid..");
            return result;
        }
        String[] ns = {"Croma.PBCromav2_Sales"};
        if(!Arrays.asList(ns).contains(dbColl)){
            result.put("error", "ns is not whitelisted to proceed further...");
            return result;
        }
        try {

            if (stop && BmsLoggerServiceImpl.isMoveCromaPBCromaV2SalesLogsJobRunning) {
                BmsLoggerServiceImpl.isMoveCromaPBCromaV2SalesLogsJobStop = stop;
            } else {
                BmsLoggerServiceImpl.isMoveCromaPBCromaV2SalesLogsJobStop = false;
            }
            if (size > 1000) {
                size = 1000;
            }
            if (days < 90) {
                days = 90;
            }
            if (chunkSize > 500) {
                chunkSize = 500;
            }
            if (!BmsLoggerServiceImpl.isMoveCromaPBCromaV2SalesLogsJobRunning) {
                BmsLoggerServiceImpl.isMoveCromaPBCromaV2SalesLogsJobRunning = true;
                BmsLoggerServiceImpl.isMoveCromaPBCromaV2SalesLogsJobStop = false;
                bmsLoggerService.movePBCromaV2_SalesToS3(dbColl, size, -days, chunkSize);
                result.put("OK", "1");
                result.put("SK_MSG", "collection migrated");
            } else if (BmsLoggerServiceImpl.isMoveCromaPBCromaV2SalesLogsJobRunning) {
                logger.info("Could not initiate movePBCromaV2SalesToS3 at this time as it is running {} , stop {}    ", new Date(), stop);
                logger.info(" ns {} , size {} ", dbColl, size);
                result.put("OK", "0");
                result.put("SK_MSG", "not initiate movePBCromaV2SalesToS3, " + new Date());
            }
        } catch (Exception e) {
            result.put("OK", "0");
            result.put("SK_MSG", "Error in movePBCromaV2SalesToS3 : ," + e.getMessage());
            logger.error("Error in movePBCromaV2SalesToS3 , {}", e.getMessage());
        }
        logger.info("Exiting movePBCromaV2SalesToS3 ");
        return result;
    }


    @GetMapping(value = "/moveCommV2FileLogsToS3/v2")
    public Map<String, String> moveCommV2FileLogsToS3V2(@RequestParam(required = false,value = "size") Integer size,
                                                        @RequestParam(required = false,value = "stop") Boolean stop,
                                                        @RequestParam(value = "ns") String dbColl,
                                                        @RequestParam(required = false, value = "lte") String lte_Id,
                                                        @RequestParam(value="dayBefore", defaultValue = "30") int days,
                                                        @RequestParam(value="chunkSize", defaultValue = "100") int chunkSize,
                                                        @RequestHeader(value = "authKey") String authKey){
        logger.info("Inside moveCommV2FileLogsToS3V1 ");
        Map<String, String> result = new HashMap<String, String>();
        // check authKey if not matched then return.
        if(!authKey.equals(REQ_HEADER_AUTHKEY)){
            result.put("error", "Auth key is not valid..");
            return result;
        }
        String[] ns = {"communicationDB.CommV2FileLogsv2", "communicationDB_dev.CommV2FileLogs"};
        if(!Arrays.asList(ns).contains(dbColl)){
            result.put("error", "ns is not whitelisted to proceed further...");
            return result;
        }
        try {

            if(stop && BmsLoggerServiceImpl.isMoveCommV2FileLogsLoggerJobRunningV1) {
                BmsLoggerServiceImpl.isMoveCommV2FileLogsLoggerJobStopV1 = stop;
            }else{
                BmsLoggerServiceImpl.isMoveCommV2FileLogsLoggerJobStopV1 = false;
            }
            if(size>1000) {
                size = 1000;
            }
            if(days < 30){
                days = 30;
            }
            if(chunkSize>500) {
                chunkSize = 500;
            }
            if (!BmsLoggerServiceImpl.isMoveCommV2FileLogsLoggerJobRunningV1) {
                BmsLoggerServiceImpl.isMoveCommV2FileLogsLoggerJobRunningV1 = true;
                BmsLoggerServiceImpl.isMoveCommV2FileLogsLoggerJobStopV1 = false;
                bmsLoggerService.moveCommV2FileLogsToS3V3(dbColl, size,-days, chunkSize, lte_Id);
                result.put("OK", "1");
                result.put("SK_MSG", "collection migrated");
            }else if(BmsLoggerServiceImpl.isMoveCommV2FileLogsLoggerJobRunningV1){
                logger.info("Could not initiate moveCommV2FileLogsToS3 at this time as it is running {} , stop {}    ", new Date(), stop);
                logger.info(" ns {} , size {} ",dbColl, size );
                result.put("OK", "0");
                result.put("SK_MSG", "not initiate moveCommV2FileLogsToS3V1, " + new Date());
            }
        } catch (Exception e) {
            result.put("OK", "0");
            result.put("SK_MSG", "Error in moveCommV2FileLogsToS3V1 : ," + e.getMessage());
            logger.error("Error in moveCommV2FileLogsToS3V1 , {}", e.getMessage());
        }
        logger.info("Exiting moveCommV2FileLogsToS3V1 ");
        return result;
    }

    @GetMapping(value = "runningJobs")
    public Map<String, String> migrationOfLogs( @RequestParam(value = "jobId" , defaultValue = "job1") String jobId ,
                                                @RequestParam(required = false,value = "stop") Boolean stop,
                                                @RequestHeader String authKey){
        logger.info("Inside moveLifeDocs ");
        Map<String, String> responseMap = new HashMap<String, String>();

        try {
            // check authKey if not matched then return.
            if(!authKey.equals(REQ_HEADER_AUTHKEY)){
                responseMap.put("error", "Auth key is not valid..");
                return responseMap;
            }
            String[] ns = {"jobId"};
//            if(!Arrays.asList(ns).contains(jobId)){
//                responseMap.put("error", "ns is not whitelisted to proceed further...");
//                return responseMap;
//            }
            if (!StaticMaster.isMoveS3E2ELoggerDataStop() && !StaticMaster.isLoggerE2EMoveS3JobRunning()) {
                StaticMaster.setLoggerE2EMoveS3JobRunning(true);
                // MongoClient mongoClient = mongoUtil.getMongoClient(ip);
//                bmsLoggerService.migrationOfLogs(jobId);
                responseMap.put("OK", "1");
                responseMap.put("SK_MSG", "collection migrated");
                StaticMaster.setLoggerE2EMoveS3JobRunning(false);
            }
            if (StaticMaster.isLoggerE2EMoveS3JobRunning()) {
                logger.info("Could not initiate moveLifeDocs migration at this time already running: {} , stop {} , waitInSec {}", new Date());
                logger.info(" coll {} , startId, {} , size {} ",ns );
                responseMap.put("OK", "0");
                responseMap.put("SK_MSG", "Could not initiate migration at this time , " + new Date());
            }
        } catch (Exception e) {
            responseMap.put("OK", "0");
            responseMap.put("SK_MSG", "Error in moveLifeDocs : ," + e.getMessage());
            logger.error("Error in moveLifeDocs , {}", e.getMessage());
        } finally {
            StaticMaster.setLoggerE2EMoveS3JobRunning(false);
        }
        logger.info("Exiting moveLifeDocs ");
        return responseMap;
    }


    @GetMapping(value = "/syncShortUrl")
    public Map<String, String> syncShortUrl(@RequestParam(required = false,value = "size") Integer size,
                                               @RequestParam(required = false,value = "waitInSec") Integer waitInSec,
                                               @RequestParam(required = false,value = "stop") Boolean stop,
                                               @RequestParam(value = "coll") String coll,
                                               @RequestParam(value = "db") String db,
                                               @RequestParam(required = false, value = "timeCheck") Boolean timeCheck) {
        logger.info("Inside syncShortUrl ");
        Map<String, String> result = new HashMap<String, String>();
        try {
            Date todayDate = new Date();
            Date startDate = new Date();
            startDate = DateUtil.setHours(startDate, 7);
            startDate = DateUtil.setMinute(startDate, 0);
            startDate = DateUtil.setSeconds(startDate, 0);

            Date endDate = new Date();
            endDate = DateUtil.setHours(endDate, 20);
            endDate = DateUtil.setMinute(endDate, 0);
            endDate = DateUtil.setSeconds(endDate, 0);


            if (stop != null) {
                StaticMaster.setSyncShortUrlStop(stop);
            }

            if (size == null) {
                size = 500;
            }

            logger.info("Today date : {}, start date {} , end date {}", todayDate, startDate, endDate);
            if ((todayDate.before(startDate) || todayDate.after(endDate) || !timeCheck)
                    && !StaticMaster.isSyncShortUrlStop()) {
                bmsLoggerService.syncShortUrl(coll, size, timeCheck, db);
                result.put("OK", "1");
                result.put("SK_MSG", "collection cleaned");
            }
        } catch (Exception e) {
            result.put("OK", "0");
            result.put("SK_MSG", "Error in syncShortUrl : ," + e.getMessage());
            logger.error("Error in syncShortUrl , {}", e.getMessage());
        }
        logger.info("Exiting syncShortUrl ");

        return result;
    }


}
