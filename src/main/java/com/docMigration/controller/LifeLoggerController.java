package com.docMigration.controller;

import com.docMigration.service.LifeLoggerService;
import com.docMigration.service.impl.LifeLoggerServiceImpl;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

@RestController
public class LifeLoggerController {

    private final Logger logger = LoggerFactory.getLogger(LifeLoggerController.class);
    private static final String REQ_HEADER_AUTHKEY = "92kik4i4jn4oi4n3oi4nn4k4l4lk4lknwlk4l4ljk44444klknn4h4jk5k";

    @Autowired
    private LifeLoggerService lifeLoggerService;

    @GetMapping(value = "moveLifeDocsToS3")
    public Map<String, String> moveLifeDocs( @RequestParam(required = false,value = "size") Integer size,
                                             @RequestParam(required = false,value = "stop") Boolean stop,
                                             @RequestParam(value = "ns" , defaultValue = "LifeRenewalLog.LogDataRenewal") String dbColl ,
                                             @RequestParam(value="dayBefore") int days ,
                                             @RequestParam(value="chunkSize") int chunkSize ,
                                             @RequestParam(required = false,value = "timeCheck") Boolean timeCheck,
                                             @RequestHeader String authKey){
        logger.info("Inside moveLifeDocs ");
        Map<String, String> responseMap = new HashMap<String, String>();

        try {
            // check authKey if not matched then return.
            if(!authKey.equals(REQ_HEADER_AUTHKEY)){
                responseMap.put("error", "Auth key is not valid..");
                return responseMap;
            }
            String[] ns = {"LifeRenewalLog.LogDataRenewal"};
            if(!Arrays.asList(ns).contains(dbColl)){
                responseMap.put("error", "ns is not whitelisted to proceed further...");
                return responseMap;
            }

            if(stop && LifeLoggerServiceImpl.isE2ELoggerS3JobRunning ) {
                LifeLoggerServiceImpl.isE2ELoggerS3JobStop = stop;
            }else{
                LifeLoggerServiceImpl.isE2ELoggerS3JobStop = false;
            }
            // As per policy 30 days must be allowed to add in mongoDB
            if(days<30) {
                days = 30;
            }
            if(size>500) {
                size = 500;
            }
            if(chunkSize > 500) {
                chunkSize = 500;
            }
            if (!LifeLoggerServiceImpl.isE2ELoggerS3JobRunning) {
                LifeLoggerServiceImpl.isE2ELoggerS3JobStop = false;
                LifeLoggerServiceImpl.isE2ELoggerS3JobRunning = true;
                lifeLoggerService.moveLifeDocs(dbColl, size, timeCheck, -days, chunkSize);
                responseMap.put("OK", "1");
                responseMap.put("SK_MSG", "collection migrated");
            }
            if (LifeLoggerServiceImpl.isE2ELoggerS3JobRunning) {
                logger.info("Could not initiate moveLifeDocs migration at this time already running: {} , stop {} , waitInSec {}", new Date(), stop);
                logger.info(" coll {} , startId, {} , size {} ",ns, size );
                responseMap.put("OK", "0");
                responseMap.put("SK_MSG", "Could not initiate migration at this time , " + new Date());
            }
        } catch (Exception e) {
            responseMap.put("OK", "0");
            responseMap.put("SK_MSG", "Error in moveLifeDocs : ," + e.getMessage());
            logger.error("Error in moveLifeDocs , {}", e.getMessage());
        }
        logger.info("Exiting moveLifeDocs ");
        return responseMap;
    }
    @GetMapping(value = "moveLifeDocsToS3/v1")
    public Map<String, String> moveLifeDocsV1( @RequestParam(required = false,value = "size") Integer size,
                                             @RequestParam(required = false,value = "stop") Boolean stop,
                                             @RequestParam(value = "ns" , defaultValue = "LifeRenewalLog.LogDataRenewal") String dbColl ,
                                             @RequestParam(value = "lte") String lte_id,
                                             @RequestParam(value="dayBefore") int days ,
                                             @RequestParam(value="chunkSize") int chunkSize ,
                                             @RequestParam(required = false,value = "timeCheck") Boolean timeCheck,
                                             @RequestHeader String authKey){
        logger.info("Inside moveLifeDocsV1 ");
        Map<String, String> responseMap = new HashMap<String, String>();

        try {
            // check authKey if not matched then return.
            if(!authKey.equals(REQ_HEADER_AUTHKEY)){
                responseMap.put("error", "Auth key is not valid..");
                return responseMap;
            }
            String[] ns = {"LifeRenewalLog.LogDataRenewal"};
            if(!Arrays.asList(ns).contains(dbColl)){
                responseMap.put("error", "ns is not whitelisted to proceed further...");
                return responseMap;
            }

            if(stop && LifeLoggerServiceImpl.isE2ELoggerS3JobRunningV1 ) {
                LifeLoggerServiceImpl.isE2ELoggerS3JobStopV1 = stop;
            }else{
                LifeLoggerServiceImpl.isE2ELoggerS3JobStopV1 = false;
            }
            // As per policy 30 days must be allowed to add in mongoDB
            if(days<30) {
                days = 30;
            }
            if(size>500) {
                size = 500;
            }
            if(chunkSize > 500) {
                chunkSize = 500;
            }
            if (!LifeLoggerServiceImpl.isE2ELoggerS3JobRunningV1) {
                LifeLoggerServiceImpl.isE2ELoggerS3JobStopV1 = false;
                LifeLoggerServiceImpl.isE2ELoggerS3JobRunningV1 = true;
                lifeLoggerService.moveLifeDocs(dbColl, size, timeCheck, -days, chunkSize, lte_id);
                responseMap.put("OK", "1");
                responseMap.put("SK_MSG", "collection migrated");
            }
            if (LifeLoggerServiceImpl.isE2ELoggerS3JobRunningV1) {
                logger.info("Could not initiate moveLifeDocs migration at this time already running: {} , stop {} , waitInSec {}", new Date(), stop);
                logger.info(" coll {} , startId, {} , size {} ",ns, size );
                responseMap.put("OK", "0");
                responseMap.put("SK_MSG", "Could not initiate migration at this time , " + new Date());
            }
        } catch (Exception e) {
            responseMap.put("OK", "0");
            responseMap.put("SK_MSG", "Error in moveLifeDocsV1 : ," + e.getMessage());
            logger.error("Error in moveLifeDocsV1 , {}", e.getMessage());
        }
        logger.info("Exiting moveLifeDocsV1 ");
        return responseMap;
    }

    @GetMapping(value = "moveLifeDocsToS3/v2")
    public Map<String, String> moveLifeDocsV2( @RequestParam(required = false,value = "size") Integer size,
                                               @RequestParam(required = false,value = "stop") Boolean stop,
                                               @RequestParam(value = "ns" , defaultValue = "LifeRenewalLog.LogDataRenewal") String dbColl,
                                               @RequestParam(value = "lte") String lte_id,
                                               @RequestParam(value="dayBefore") int days,
                                               @RequestParam(value="chunkSize") int chunkSize ,
                                               @RequestParam(required = false,value = "timeCheck") Boolean timeCheck,
                                               @RequestHeader String authKey){
        logger.info("Inside moveLifeDocsV2 ");
        Map<String, String> responseMap = new HashMap<String, String>();

        try {
            // check authKey if not matched then return.
            if(!authKey.equals(REQ_HEADER_AUTHKEY)){
                responseMap.put("error", "Auth key is not valid..");
                return responseMap;
            }
            String[] ns = {"LifeRenewalLog.LogDataRenewal"};
            if(!Arrays.asList(ns).contains(dbColl)){
                responseMap.put("error", "ns is not whitelisted to proceed further...");
                return responseMap;
            }

            if(stop && LifeLoggerServiceImpl.isE2ELoggerS3JobRunningV2 ) {
                LifeLoggerServiceImpl.isE2ELoggerS3JobStopV2 = stop;
            }else{
                LifeLoggerServiceImpl.isE2ELoggerS3JobStopV2 = false;
            }
            // As per policy 30 days must be allowed to add in mongoDB
            if(days<30) {
                days = 30;
            }
            if(size>500) {
                size = 500;
            }
            if(chunkSize > 500) {
                chunkSize = 500;
            }
            if (!LifeLoggerServiceImpl.isE2ELoggerS3JobRunningV2) {
                LifeLoggerServiceImpl.isE2ELoggerS3JobStopV2 = false;
                LifeLoggerServiceImpl.isE2ELoggerS3JobRunningV2 = true;
                lifeLoggerService.moveLifeDocsV2(dbColl, size, timeCheck, -days, chunkSize, lte_id);
                responseMap.put("OK", "1");
                responseMap.put("SK_MSG", "collection migrated");
            }
            if (LifeLoggerServiceImpl.isE2ELoggerS3JobRunningV2) {
                logger.info("Could not initiate moveLifeDocsV2 migration at this time already running: {} , stop {} , waitInSec {}", new Date(), stop);
                logger.info(" coll {} , startId, {} , size {} ",ns, size );
                responseMap.put("OK", "0");
                responseMap.put("SK_MSG", "Could not initiate migration at this time , " + new Date());
            }
        } catch (Exception e) {
            responseMap.put("OK", "0");
            responseMap.put("SK_MSG", "Error in moveLifeDocsV2 : ," + e.getMessage());
            logger.error("Error in moveLifeDocsV2 , {}", e.getMessage());
        }
        logger.info("Exiting moveLifeDocsV2 ");
        return responseMap;
    }
    @GetMapping(value = "deleteLifeDocs")
    public Map<String, String> deleteLifeDocs( @RequestParam(required = false,value = "size") Integer size,
                                               @RequestParam(required = false,value = "stop") Boolean stop,
                                               @RequestParam(value = "db") String db,
                                               @RequestParam(value="dayBefore") int days,
                                               @RequestParam(value="chunkSize") int chunkSize ,
                                               @RequestParam(required = false,value = "timeCheck") Boolean timeCheck,
                                               @RequestHeader String authKey){
        logger.info("Inside deleteLifeDocs ");
        Map<String, String> responseMap = new HashMap<String, String>();

        try {
            // check authKey if not matched then return.
            if(!authKey.equals(REQ_HEADER_AUTHKEY)){
                responseMap.put("error", "Auth key is not valid..");
                return responseMap;
            }
            if(stop && LifeLoggerServiceImpl.isE2ELoggerDeleteJobRunningV2 ) {
                LifeLoggerServiceImpl.isE2ELoggerDeleteJobStopV2 = stop;
            }else{
                LifeLoggerServiceImpl.isE2ELoggerDeleteJobStopV2 = false;
            }
            // As per policy 30 days must be allowed to add in mongoDB
            if(days<30) {
                days = 30;
            }
            if(size>500) {
                size = 500;
            }
            if(chunkSize > 500) {
                chunkSize = 500;
            }
            if (!LifeLoggerServiceImpl.isE2ELoggerDeleteJobRunningV2) {
                LifeLoggerServiceImpl.isE2ELoggerDeleteJobStopV2 = false;
                LifeLoggerServiceImpl.isE2ELoggerDeleteJobRunningV2 = true;
                lifeLoggerService.deleteLifeDocs(db, size, timeCheck, -days, chunkSize);
                responseMap.put("OK", "1");
                responseMap.put("SK_MSG", "collection migrated");
            }
            if (LifeLoggerServiceImpl.isE2ELoggerDeleteJobRunningV2) {
                logger.info("Could not initiate deleteLifeDocs migration at this time already running: {} , stop {} , waitInSec {}", new Date(), stop);
                logger.info(" coll {} , startId, {} , size {} ",db, size );
                responseMap.put("OK", "0");
                responseMap.put("SK_MSG", "Could not initiate migration at this time , " + new Date());
            }
        } catch (Exception e) {
            responseMap.put("OK", "0");
            responseMap.put("SK_MSG", "Error in deleteLifeDocs : ," + e.getMessage());
            logger.error("Error in moveLifeDocsV2 , {}", e.getMessage());
        }
        logger.info("Exiting moveLifeDocsV2 ");
        return responseMap;
    }
}
