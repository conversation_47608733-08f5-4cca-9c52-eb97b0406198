package com.docMigration.controller;

import com.docMigration.service.impl.HealthLoggerArchivalServiceImpl;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

@RestController
public class HealthLoggerController {
    private final Logger logger = LoggerFactory.getLogger(HealthLoggerController.class);
    private static final String REQ_HEADER_AUTHKEY = "92kik4i4jn4oi4n3oi4nn4k4l4lk4lknwlk4l4ljk44444klknn4h4jk5k";
    @Autowired
    private HealthLoggerArchivalServiceImpl healthLoggerArchivalService;

    @GetMapping(value = "/health/moveBookedDocsByEnquiryId")
    public Map<String, String> moveBookedDocsByEnquiryId(@RequestParam(required = false,value = "size") Integer size,
                                                         @RequestParam(required = false,value = "stop") Boolean stop,
                                                         @RequestParam(value = "ns") String dbColl,
                                                         @RequestParam(value="dayBefore") int days,
                                                         @RequestParam(value="chunkSize") int chunkSize,
                                                         @RequestHeader String authKey){
        logger.info("Inside moveBookedDocsByEnquiryId ");
        Map<String, String> responseMap = new HashMap<String, String>();

        try {
            // check authKey if not matched then return.
            if(!authKey.equals(REQ_HEADER_AUTHKEY)){
                responseMap.put("error", "Auth key is not valid..");
                return responseMap;
            }
            String[] ns = {"ProfileManager.ProposalSLogging"};
            if(!Arrays.asList(ns).contains(dbColl)){
                responseMap.put("error", "ns is not whitelisted to proceed further...");
                return responseMap;
            }

            if(stop && HealthLoggerArchivalServiceImpl.isHealthS3DataMoveJobRunning) {
                HealthLoggerArchivalServiceImpl.isHealthS3DataMoveJobStop = stop;
            } else {
                HealthLoggerArchivalServiceImpl.isHealthS3DataMoveJobStop = stop;
            }

            if(size>1000) {
                size = 1000;
            }
            if(days < 30) {
                days = 30;
            }
            if(chunkSize>1000) {
                chunkSize = 1000;
            }
            if (!HealthLoggerArchivalServiceImpl.isHealthS3DataMoveJobRunning) {
                HealthLoggerArchivalServiceImpl.isHealthS3DataMoveJobRunning = true;
                HealthLoggerArchivalServiceImpl.isHealthS3DataMoveJobStop = false;

                healthLoggerArchivalService.moveHealthBookLeads(dbColl, size,-days, chunkSize);
                responseMap.put("OK", "1");
                responseMap.put("SK_MSG", "collection migrated");
            }
            else if (HealthLoggerArchivalServiceImpl.isHealthS3DataMoveJobRunning) {
                logger.info("Health job moveBookedDocsByEnquiryId migration at this time already running: {} , stop {}", new Date(), stop);
                logger.info(" coll {} , startId, {} , size {} ",ns, size );
                responseMap.put("OK", "0");
                responseMap.put("SK_MSG", "Health Could not initiate migration at this time , " + new Date());
            }
        } catch (Exception e) {
            responseMap.put("OK", "0");
            responseMap.put("SK_MSG", "Error in moveBookedDocsByEnquiryId : ," + e.getMessage());
            logger.error("Error in moveBookedDocsByEnquiryId , {}", e.getMessage());
        }
        logger.info("completing Health migration to S3 ");
        return responseMap;
    }
}
