package com.docMigration.controller;

import com.docMigration.service.impl.MotorLoggerArchivalServiceImpl;
import com.docMigration.service.impl.TempMotorLoggerArchivalServiceImpl;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * This is a schedular class which will take documents from db and insert in aws s3
 * S3 bucket.
 *
 * <AUTHOR>
 *
 */
@RestController
@RequestMapping("/temp_47")
public class TempMotorLoggerController {
    private final Logger logger = LoggerFactory.getLogger(TempMotorLoggerController.class);
    private static final String REQ_HEADER_AUTHKEY = "92kik4i4jn4oi4n3oi4nn4k4l4lk4lknwlk4l4ljk44444klknn4h4jk5k";

    @Autowired
    private TempMotorLoggerArchivalServiceImpl tempMotorLoggerArchivalService;

    @GetMapping(value = "/test")
    public String test(){
        logger.info("Test is ok");
        return "Ok";
    }
    @GetMapping(value = "/moveBookedDocsByEnquiryId/v1")
    public Map<String, String> moveBookedDocsByEnquiryIdcarTempV1(@RequestParam(required = false,value = "size", defaultValue = "500") Integer size,
                                                                  @RequestParam(required = false,value = "stop", defaultValue = "false") boolean stop,
                                                                  @RequestParam(value = "ns") String dbColl,
                                                                  @RequestParam(value="dayBefore") int days,
                                                                  @RequestParam(value="chunkSize") int chunkSize,
                                                                  @RequestHeader String authKey){
        logger.info("Inside /car/moveBookedDocsByEnquiryIdV2 ");
        Map<String, String> responseMap = new HashMap<String, String>();

        try {
            // check authKey if not matched then return.
            if(!authKey.equals(REQ_HEADER_AUTHKEY)){
                responseMap.put("error", "Auth key is not valid..");
                return responseMap;
            }
            String[] ns = {"CarLoggerDB.ReqResLog","Recovery.ReqResLog"};
            if(!Arrays.asList(ns).contains(dbColl)){
                responseMap.put("error", "ns is not whitelisted to proceed further...");
                return responseMap;
            }
            if(stop && TempMotorLoggerArchivalServiceImpl.isTempCarMoveS3JobRunning1) {
                TempMotorLoggerArchivalServiceImpl.isTempCarMoveS3JobStop1 = stop;
            } else {
                TempMotorLoggerArchivalServiceImpl.isTempCarMoveS3JobStop1 = false;
            }

            if(size>1000) {
                size = 1000;
            }
            if(chunkSize>500) {
                chunkSize = 500;
            }

            // if not running then run the job
            if (!TempMotorLoggerArchivalServiceImpl.isTempCarMoveS3JobRunning1) {
                TempMotorLoggerArchivalServiceImpl.isTempCarMoveS3JobRunning1 = true;
                TempMotorLoggerArchivalServiceImpl.isTempCarMoveS3JobStop1 = false;
                tempMotorLoggerArchivalService.moveOldMotorBookLeadsV1(dbColl, size, -days, chunkSize);
                responseMap.put("OK", "1");
                responseMap.put("SK_MSG", "collection migrated, for" + dbColl);
                logger.info("Car job completed for ns: {} , size {} ",dbColl, size );
            }
            else if (TempMotorLoggerArchivalServiceImpl.isTempCarMoveS3JobRunning1) {
                logger.info(" moveBookedLeadCar migration already running at: {} , for na: {} stop {}  {}", new Date(),dbColl, stop );
                logger.info(" coll {} , startId, {} , size {} ",ns, size );
                responseMap.put("OK", "0");
                responseMap.put("SK_MSG", "Could not initiate migration at this time , " + new Date());
            }
        } catch (Exception e) {
            responseMap.put("OK", "0");
            responseMap.put("SK_MSG", "Error in moveBookedLeadCar : ," + e.getMessage());
            logger.error("Error in Car moveBookedLeadCar , msg: {}", e.getMessage());
        }
        logger.info("completed job car moveBookedLeadCar, at: {} , for ns: ", new Date(), dbColl);
        return responseMap;
    }
    @GetMapping(value = "/moveBookedDocsByEnquiryId/v2")
    public Map<String, String> moveBookedDocsByEnquiryIdcarTempV2(@RequestParam(required = false,value = "size", defaultValue = "500") Integer size,
                                                                  @RequestParam(required = false,value = "stop", defaultValue = "false") boolean stop,
                                                                  @RequestParam(value = "ns") String dbColl,
                                                                  @RequestParam(value="dayBefore") int days,
                                                                  @RequestParam(value="chunkSize") int chunkSize,
                                                                  @RequestHeader String authKey){
        logger.info("Inside /car/moveBookedDocsByEnquiryIdV2 ");
        Map<String, String> responseMap = new HashMap<String, String>();

        try {
            // check authKey if not matched then return.
            if(!authKey.equals(REQ_HEADER_AUTHKEY)){
                responseMap.put("error", "Auth key is not valid..");
                return responseMap;
            }
            String[] ns = {"CarLoggerDB.ReqResLog","Recovery.ReqResLog"};
            if(!Arrays.asList(ns).contains(dbColl)){
                responseMap.put("error", "ns is not whitelisted to proceed further...");
                return responseMap;
            }
            if(stop && TempMotorLoggerArchivalServiceImpl.isTempCarMoveS3JobRunning2) {
                TempMotorLoggerArchivalServiceImpl.isTempCarMoveS3JobStop2 = stop;
            } else {
                TempMotorLoggerArchivalServiceImpl.isTempCarMoveS3JobStop2 = false;
            }

            if(size>1000) {
                size = 1000;
            }
            if(chunkSize>500) {
                chunkSize = 500;
            }

            // if not running then run the job
            if (!TempMotorLoggerArchivalServiceImpl.isTempCarMoveS3JobRunning2) {
                TempMotorLoggerArchivalServiceImpl.isTempCarMoveS3JobRunning2 = true;
                TempMotorLoggerArchivalServiceImpl.isTempCarMoveS3JobStop2 = false;
                tempMotorLoggerArchivalService.moveOldMotorBookLeadsV2(dbColl, size, -days, chunkSize);
                responseMap.put("OK", "1");
                responseMap.put("SK_MSG", "collection migrated, for" + dbColl);
                logger.info("Car job completed for ns: {} , size {} ",dbColl, size );
            }
            else if (TempMotorLoggerArchivalServiceImpl.isTempCarMoveS3JobRunning2) {
                logger.info(" moveBookedLeadCar migration already running at: {} , for na: {} stop {}  {}", new Date(),dbColl, stop );
                logger.info(" coll {} , startId, {} , size {} ",ns, size );
                responseMap.put("OK", "0");
                responseMap.put("SK_MSG", "Could not initiate migration at this time , " + new Date());
            }
        } catch (Exception e) {
            responseMap.put("OK", "0");
            responseMap.put("SK_MSG", "Error in moveBookedLeadCar : ," + e.getMessage());
            logger.error("Error in Car moveBookedLeadCar , msg: {}", e.getMessage());
        }
        logger.info("completed job car moveBookedLeadCar, at: {} , for ns: ", new Date(), dbColl);
        return responseMap;
    }
    @GetMapping(value = "/moveBookedDocsByEnquiryId/v3")
    public Map<String, String> moveBookedDocsByEnquiryIdcarTempV3(@RequestParam(required = false,value = "size", defaultValue = "500") Integer size,
                                                                  @RequestParam(required = false,value = "stop", defaultValue = "false") boolean stop,
                                                                  @RequestParam(value = "ns") String dbColl,
                                                                  @RequestParam(value="dayBefore") int days,
                                                                  @RequestParam(value="chunkSize") int chunkSize,
                                                                  @RequestHeader String authKey){
        logger.info("Inside /car/moveBookedDocsByEnquiryIdV2 ");
        Map<String, String> responseMap = new HashMap<String, String>();

        try {
            // check authKey if not matched then return.
            if(!authKey.equals(REQ_HEADER_AUTHKEY)){
                responseMap.put("error", "Auth key is not valid..");
                return responseMap;
            }
            String[] ns = {"CarLoggerDB.ReqResLog","Recovery.ReqResLog"};
            if(!Arrays.asList(ns).contains(dbColl)){
                responseMap.put("error", "ns is not whitelisted to proceed further...");
                return responseMap;
            }
            if(stop && TempMotorLoggerArchivalServiceImpl.isTempCarMoveS3JobRunning3) {
                TempMotorLoggerArchivalServiceImpl.isTempCarMoveS3JobStop3 = stop;
            } else {
                TempMotorLoggerArchivalServiceImpl.isTempCarMoveS3JobStop3 = false;
            }

            if(size>1000) {
                size = 1000;
            }
            if(chunkSize>500) {
                chunkSize = 500;
            }

            // if not running then run the job
            if (!TempMotorLoggerArchivalServiceImpl.isTempCarMoveS3JobRunning3) {
                TempMotorLoggerArchivalServiceImpl.isTempCarMoveS3JobRunning3 = true;
                TempMotorLoggerArchivalServiceImpl.isTempCarMoveS3JobStop3 = false;
                tempMotorLoggerArchivalService.moveOldMotorBookLeadsV3(dbColl, size, -days, chunkSize);
                responseMap.put("OK", "1");
                responseMap.put("SK_MSG", "collection migrated, for" + dbColl);
                logger.info("Car job completed for ns: {} , size {} ",dbColl, size );
            }
            else if (TempMotorLoggerArchivalServiceImpl.isTempCarMoveS3JobRunning3) {
                logger.info(" moveBookedLeadCar migration already running at: {} , for na: {} stop {}  {}", new Date(),dbColl, stop );
                logger.info(" coll {} , startId, {} , size {} ",ns, size );
                responseMap.put("OK", "0");
                responseMap.put("SK_MSG", "Could not initiate migration at this time , " + new Date());
            }
        } catch (Exception e) {
            responseMap.put("OK", "0");
            responseMap.put("SK_MSG", "Error in moveBookedLeadCar : ," + e.getMessage());
            logger.error("Error in Car moveBookedLeadCar , msg: {}", e.getMessage());
        }
        logger.info("completed job car moveBookedLeadCar, at: {} , for ns: ", new Date(), dbColl);
        return responseMap;
    }
    @GetMapping(value = "/moveBookedDocsByEnquiryId/v4")
    public Map<String, String> moveBookedDocsByEnquiryIdcarTempV4(@RequestParam(required = false,value = "size", defaultValue = "500") Integer size,
                                                                  @RequestParam(required = false,value = "stop", defaultValue = "false") boolean stop,
                                                                  @RequestParam(value = "ns") String dbColl,
                                                                  @RequestParam(value="dayBefore") int days,
                                                                  @RequestParam(value="chunkSize") int chunkSize,
                                                                  @RequestHeader String authKey){
        logger.info("Inside /car/moveBookedDocsByEnquiryIdV2 ");
        Map<String, String> responseMap = new HashMap<String, String>();

        try {
            // check authKey if not matched then return.
            if(!authKey.equals(REQ_HEADER_AUTHKEY)){
                responseMap.put("error", "Auth key is not valid..");
                return responseMap;
            }
            String[] ns = {"CarLoggerDB.ReqResLog","Recovery.ReqResLog"};
            if(!Arrays.asList(ns).contains(dbColl)){
                responseMap.put("error", "ns is not whitelisted to proceed further...");
                return responseMap;
            }
            if(stop && TempMotorLoggerArchivalServiceImpl.isTempCarMoveS3JobRunning4) {
                TempMotorLoggerArchivalServiceImpl.isTempCarMoveS3JobStop4 = stop;
            } else {
                TempMotorLoggerArchivalServiceImpl.isTempCarMoveS3JobStop4 = false;
            }

            if(size>1000) {
                size = 1000;
            }
            if(chunkSize>500) {
                chunkSize = 500;
            }

            // if not running then run the job
            if (!TempMotorLoggerArchivalServiceImpl.isTempCarMoveS3JobRunning4) {
                TempMotorLoggerArchivalServiceImpl.isTempCarMoveS3JobRunning4 = true;
                TempMotorLoggerArchivalServiceImpl.isTempCarMoveS3JobStop4 = false;
                tempMotorLoggerArchivalService.moveOldMotorBookLeadsV4(dbColl, size, -days, chunkSize);
                responseMap.put("OK", "1");
                responseMap.put("SK_MSG", "collection migrated, for" + dbColl);
                logger.info("Car job completed for ns: {} , size {} ",dbColl, size );
            }
            else if (TempMotorLoggerArchivalServiceImpl.isTempCarMoveS3JobRunning4) {
                logger.info(" moveBookedLeadCar migration already running at: {} , for na: {} stop {}  {}", new Date(),dbColl, stop );
                logger.info(" coll {} , startId, {} , size {} ",ns, size );
                responseMap.put("OK", "0");
                responseMap.put("SK_MSG", "Could not initiate migration at this time , " + new Date());
            }
        } catch (Exception e) {
            responseMap.put("OK", "0");
            responseMap.put("SK_MSG", "Error in moveBookedLeadCar : ," + e.getMessage());
            logger.error("Error in Car moveBookedLeadCar , msg: {}", e.getMessage());
        }
        logger.info("completed job car moveBookedLeadCar, at: {} , for ns: ", new Date(), dbColl);
        return responseMap;
    }
    @GetMapping(value = "/moveBookedDocsByEnquiryId/v5")
    public Map<String, String> moveBookedDocsByEnquiryIdcarTempV5(@RequestParam(required = false,value = "size", defaultValue = "500") Integer size,
                                                                  @RequestParam(required = false,value = "stop", defaultValue = "false") boolean stop,
                                                                  @RequestParam(value = "ns") String dbColl,
                                                                  @RequestParam(value="dayBefore") int days,
                                                                  @RequestParam(value="chunkSize") int chunkSize,
                                                                  @RequestHeader String authKey){
        logger.info("Inside /car/moveBookedDocsByEnquiryIdV2 ");
        Map<String, String> responseMap = new HashMap<String, String>();

        try {
            // check authKey if not matched then return.
            if(!authKey.equals(REQ_HEADER_AUTHKEY)){
                responseMap.put("error", "Auth key is not valid..");
                return responseMap;
            }
            String[] ns = {"CarLoggerDB.ReqResLog","Recovery.ReqResLog"};
            if(!Arrays.asList(ns).contains(dbColl)){
                responseMap.put("error", "ns is not whitelisted to proceed further...");
                return responseMap;
            }
            if(stop && TempMotorLoggerArchivalServiceImpl.isTempCarMoveS3JobRunning5) {
                TempMotorLoggerArchivalServiceImpl.isTempCarMoveS3JobStop5 = stop;
            } else {
                TempMotorLoggerArchivalServiceImpl.isTempCarMoveS3JobStop5 = false;
            }

            if(size>1000) {
                size = 1000;
            }
            if(chunkSize>500) {
                chunkSize = 500;
            }

            // if not running then run the job
            if (!TempMotorLoggerArchivalServiceImpl.isTempCarMoveS3JobRunning5) {
                TempMotorLoggerArchivalServiceImpl.isTempCarMoveS3JobStop5 = false;
                TempMotorLoggerArchivalServiceImpl.isTempCarMoveS3JobRunning5 = true;
                tempMotorLoggerArchivalService.moveOldMotorBookLeadsV5(dbColl, size, -days, chunkSize);
                responseMap.put("OK", "1");
                responseMap.put("SK_MSG", "collection migrated, for" + dbColl);
                logger.info("Car job completed for ns: {} , size {} ",dbColl, size );
            }
            else if (TempMotorLoggerArchivalServiceImpl.isTempCarMoveS3JobRunning5) {
                logger.info(" moveBookedLeadCar migration already running at: {} , for na: {} stop {}  {}", new Date(),dbColl, stop );
                logger.info(" coll {} , startId, {} , size {} ",ns, size );
                responseMap.put("OK", "0");
                responseMap.put("SK_MSG", "Could not initiate migration at this time , " + new Date());
            }
        } catch (Exception e) {
            responseMap.put("OK", "0");
            responseMap.put("SK_MSG", "Error in moveBookedLeadCar : ," + e.getMessage());
            logger.error("Error in Car moveBookedLeadCar , msg: {}", e.getMessage());
        }
        logger.info("completed job car moveBookedLeadCar, at: {} , for ns: ", new Date(), dbColl);
        return responseMap;
    }

    @GetMapping(value = "/TWmoveBookedDocsByVehicleId/v1")
    public Map<String, String> TWmoveBookedDocsByVehicleID(@RequestParam(required = false,value = "size", defaultValue = "500") Integer size,
                                                                  @RequestParam(required = false,value = "stop", defaultValue = "false") boolean stop,
                                                                  @RequestParam(value = "ns") String dbColl,
                                                                  @RequestParam(value="dayBefore") int days,
                                                                  @RequestParam(value="chunkSize") int chunkSize,
                                                                  @RequestHeader String authKey){
        logger.info("Inside /tw/TWmoveBookedDocsByVehicleID ");
        Map<String, String> responseMap = new HashMap<String, String>();

        try {
            // check authKey if not matched then return.
            if(!authKey.equals(REQ_HEADER_AUTHKEY)){
                responseMap.put("error", "Auth key is not valid..");
                return responseMap;
            }
            String[] ns = {"CarLoggerDB.ReqResTWowheelerLog"};
            if(!Arrays.asList(ns).contains(dbColl)){
                responseMap.put("error", "ns is not whitelisted to proceed further...");
                return responseMap;
            }
            if(stop && TempMotorLoggerArchivalServiceImpl.isTempTWMoveS3JobRunning1) {
                TempMotorLoggerArchivalServiceImpl.isTempTWMoveS3JobStop1 = stop;
            } else {
                TempMotorLoggerArchivalServiceImpl.isTempTWMoveS3JobStop1 = false;
            }

            if(size>1000) {
                size = 1000;
            }
            if(chunkSize>500) {
                chunkSize = 500;
            }

            // if not running then run the job
            if (!TempMotorLoggerArchivalServiceImpl.isTempTWMoveS3JobRunning1) {
                TempMotorLoggerArchivalServiceImpl.isTempTWMoveS3JobStop1 = false;
                TempMotorLoggerArchivalServiceImpl.isTempTWMoveS3JobRunning1 = true;
                tempMotorLoggerArchivalService.moveOldTWBookVehicleDetailIdV1(dbColl, size, -days, chunkSize);
                responseMap.put("OK", "1");
                responseMap.put("SK_MSG", "collection migrated, for" + dbColl);
                logger.info("Car job completed for ns: {} , size {} ",dbColl, size );
            }
            else if (TempMotorLoggerArchivalServiceImpl.isTempTWMoveS3JobRunning1) {
                logger.info(" moveBookedLeadCar migration already running at: {} , for na: {} stop {}  {}", new Date(),dbColl, stop );
                logger.info(" coll {} , startId, {} , size {} ",ns, size );
                responseMap.put("OK", "0");
                responseMap.put("SK_MSG", "Could not initiate migration at this time , " + new Date());
            }
        } catch (Exception e) {
            responseMap.put("OK", "0");
            responseMap.put("SK_MSG", "Error in moveBookedLeadCar : ," + e.getMessage());
            logger.error("Error in Car moveBookedLeadCar , msg: {}", e.getMessage());
        }
        logger.info("completed job car moveBookedLeadCar, at: {} , for ns: ", new Date(), dbColl);
        return responseMap;
    }
    @GetMapping(value = "/TWmoveBookedDocsByVehicleId/v2")
    public Map<String, String> TWmoveBookedDocsByVehicleIDV2(@RequestParam(required = false,value = "size", defaultValue = "500") Integer size,
                                                           @RequestParam(required = false,value = "stop", defaultValue = "false") boolean stop,
                                                           @RequestParam(value = "ns") String dbColl,
                                                           @RequestParam(value="dayBefore") int days,
                                                           @RequestParam(value="chunkSize") int chunkSize,
                                                           @RequestHeader String authKey){
        logger.info("Inside /tw/TWmoveBookedDocsByVehicleID ");
        Map<String, String> responseMap = new HashMap<String, String>();

        try {
            // check authKey if not matched then return.
            if(!authKey.equals(REQ_HEADER_AUTHKEY)){
                responseMap.put("error", "Auth key is not valid..");
                return responseMap;
            }
            String[] ns = {"CarLoggerDB.ReqResTWowheelerLog"};
            if(!Arrays.asList(ns).contains(dbColl)){
                responseMap.put("error", "ns is not whitelisted to proceed further...");
                return responseMap;
            }
            if(stop && TempMotorLoggerArchivalServiceImpl.isTempTWMoveS3JobRunning2) {
                TempMotorLoggerArchivalServiceImpl.isTempTWMoveS3JobStop2 = stop;
            } else {
                TempMotorLoggerArchivalServiceImpl.isTempTWMoveS3JobStop2 = false;
            }

            if(size>1000) {
                size = 1000;
            }
            if(chunkSize>500) {
                chunkSize = 500;
            }

            // if not running then run the job
            if (!TempMotorLoggerArchivalServiceImpl.isTempTWMoveS3JobRunning2) {
                TempMotorLoggerArchivalServiceImpl.isTempTWMoveS3JobStop2 = false;
                TempMotorLoggerArchivalServiceImpl.isTempTWMoveS3JobRunning2 = true;
                tempMotorLoggerArchivalService.moveOldTWBookVehicleDetailIdV2(dbColl, size, -days, chunkSize);
                responseMap.put("OK", "1");
                responseMap.put("SK_MSG", "collection migrated, for" + dbColl);
                logger.info("Car job completed for ns: {} , size {} ",dbColl, size );
            }
            else if (TempMotorLoggerArchivalServiceImpl.isTempTWMoveS3JobRunning2) {
                logger.info(" moveBookedLeadCar migration already running at: {} , for na: {} stop {}  {}", new Date(),dbColl, stop );
                logger.info(" coll {} , startId, {} , size {} ",ns, size );
                responseMap.put("OK", "0");
                responseMap.put("SK_MSG", "Could not initiate migration at this time , " + new Date());
            }
        } catch (Exception e) {
            responseMap.put("OK", "0");
            responseMap.put("SK_MSG", "Error in moveBookedLeadCar : ," + e.getMessage());
            logger.error("Error in Car moveBookedLeadCar , msg: {}", e.getMessage());
        }
        logger.info("completed job car moveBookedLeadCar, at: {} , for ns: ", new Date(), dbColl);
        return responseMap;
    }
    @GetMapping(value = "/TWmoveBookedDocsByVehicleId/v3")
    public Map<String, String> TWmoveBookedDocsByVehicleIDV3(@RequestParam(required = false,value = "size", defaultValue = "500") Integer size,
                                                             @RequestParam(required = false,value = "stop", defaultValue = "false") boolean stop,
                                                             @RequestParam(value = "ns") String dbColl,
                                                             @RequestParam(value="dayBefore") int days,
                                                             @RequestParam(value="chunkSize") int chunkSize,
                                                             @RequestHeader String authKey){
        logger.info("Inside /tw/TWmoveBookedDocsByVehicleID ");
        Map<String, String> responseMap = new HashMap<String, String>();

        try {
            // check authKey if not matched then return.
            if(!authKey.equals(REQ_HEADER_AUTHKEY)){
                responseMap.put("error", "Auth key is not valid..");
                return responseMap;
            }
            String[] ns = {"CarLoggerDB.ReqResTWowheelerLog"};
            if(!Arrays.asList(ns).contains(dbColl)){
                responseMap.put("error", "ns is not whitelisted to proceed further...");
                return responseMap;
            }
            if(stop && TempMotorLoggerArchivalServiceImpl.isTempTWMoveS3JobRunning3) {
                TempMotorLoggerArchivalServiceImpl.isTempTWMoveS3JobStop3 = stop;
            } else {
                TempMotorLoggerArchivalServiceImpl.isTempTWMoveS3JobStop3 = false;
            }

            if(size>1000) {
                size = 1000;
            }
            if(chunkSize>500) {
                chunkSize = 500;
            }

            // if not running then run the job
            if (!TempMotorLoggerArchivalServiceImpl.isTempTWMoveS3JobRunning3) {
                TempMotorLoggerArchivalServiceImpl.isTempTWMoveS3JobStop3 = false;
                TempMotorLoggerArchivalServiceImpl.isTempTWMoveS3JobRunning3 = true;
                tempMotorLoggerArchivalService.moveOldTWBookVehicleDetailIdV3(dbColl, size, -days, chunkSize);
                responseMap.put("OK", "1");
                responseMap.put("SK_MSG", "collection migrated, for" + dbColl);
                logger.info("Car job completed for ns: {} , size {} ",dbColl, size );
            }
            else if (TempMotorLoggerArchivalServiceImpl.isTempTWMoveS3JobRunning3) {
                logger.info(" moveBookedLeadCar migration already running at: {} , for na: {} stop {}  {}", new Date(),dbColl, stop );
                logger.info(" coll {} , startId, {} , size {} ",ns, size );
                responseMap.put("OK", "0");
                responseMap.put("SK_MSG", "Could not initiate migration at this time , " + new Date());
            }
        } catch (Exception e) {
            responseMap.put("OK", "0");
            responseMap.put("SK_MSG", "Error in moveBookedLeadCar : ," + e.getMessage());
            logger.error("Error in Car moveBookedLeadCar , msg: {}", e.getMessage());
        }
        logger.info("completed job car moveBookedLeadCar, at: {} , for ns: ", new Date(), dbColl);
        return responseMap;
    }

    @GetMapping(value = "/renewalHealthMovePbEventLog")
    public Map<String, String> moveRenewalHealthMovePbEventLog(@RequestParam(required = false,value = "size" , defaultValue = "100") Integer size,
                                                          @RequestParam(required = false,value = "chunkSize" , defaultValue = "200") Integer chunkSize,
                                                          @RequestParam(required = false,value = "stop", defaultValue = "false") boolean stop,
                                                          @RequestParam(value = "ns", defaultValue = "TwoWheelerDB.CoreKYC") String dbColl,
                                                          @RequestParam(value="dayBefore") int days,
                                                          @RequestHeader String authKey){
        logger.info("Inside renewalHealthMovePbEventLog");
        Map<String, String> result = new HashMap<String, String>();

        // check authKey if not matched then return.
        if(!authKey.equals(REQ_HEADER_AUTHKEY)){
            result.put("error", "Auth key is not valid..");
            return result;
        }
        String[] ns = {"RenewalHealth.PBEventLog","RenewalHealth.PBEventLog_240","RenewalHealth.PBEventLog_79"};
        if(!Arrays.asList(ns).contains(dbColl)){
            result.put("error", "ns is not whitelisted to proceed further...");
            return result;
        }
        try {

            // stop job by setting true in this
            if(stop && TempMotorLoggerArchivalServiceImpl.isPbEventLogS3MoveDataRunning) {
                TempMotorLoggerArchivalServiceImpl.isPbEventLogS3MoveDataStop = true;
            } else {
                TempMotorLoggerArchivalServiceImpl.isPbEventLogS3MoveDataStop = false;
            }

            if(chunkSize>500) {
                chunkSize = 500;
            }
            if(days < 10) {
                days = 10;
            }
            if(size>500) {
                size = 500;
            }

            // if not running then start migrattion else stop
            if (!TempMotorLoggerArchivalServiceImpl.isPbEventLogS3MoveDataRunning) {

                TempMotorLoggerArchivalServiceImpl.isPbEventLogS3MoveDataStop = false;
                TempMotorLoggerArchivalServiceImpl.isPbEventLogS3MoveDataRunning = true;

                // start migration.
                tempMotorLoggerArchivalService.moveRenewalHealthMovePbEventLog(dbColl, size, -days, chunkSize);
                result.put("OK", "1");
                result.put("SK_MSG", "collection migrated");
            } else if (TempMotorLoggerArchivalServiceImpl.isPbEventLogS3MoveDataRunning) {
                logger.info("TW job migration already running, at: {} , for ns: {}, stop {} , {}", new Date(), dbColl, stop);
                result.put("OK", "1");
                result.put("SK_MSG", "TW job already running, so could not initiate migration at this time , " + new Date());
            }

        } catch (Exception e) {
            result.put("OK", "0");
            result.put("SK_MSG", "Error in renewalHealthMovePbEventLog : ," + e.getMessage());
            logger.error("Error in renewalHealthMovePbEventLog , {}", e.getMessage());
        }
        logger.info("completed for renewalHealthMovePbEventLog job at: {}, for ns:{} ", new Date(), dbColl);
        return result;
    }
       
}
