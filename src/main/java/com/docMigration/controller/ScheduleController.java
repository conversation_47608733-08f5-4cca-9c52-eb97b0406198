/**
 * 
 */
package com.docMigration.controller;

import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.docMigration.bean.AppConstants;
import com.docMigration.cleanup.entity.StaticMaster;
import com.docMigration.service.DocumentStoreScheduleService;
import com.docMigration.service.ScheduleService;
import com.docMigration.service.impl.DocumentStoreScheduleServiceImpl;
import com.docMigration.service.impl.ScheduleServiceImpl;
import com.docMigration.util.DateUtil;

/**
 * This is a schedular class which will take documents from db and insert in aws
 * S3 bucket.
 * 
 * <AUTHOR>
 *
 */
@RestController
@RequestMapping("schedular1")
public class ScheduleController {

	@Autowired
	ScheduleService scheduleService;

	private final Logger logger = LoggerFactory.getLogger(ScheduleController.class);

	public static String[] collections = { AppConstants.COLLECTION_DOC_REPOSITORY,
			AppConstants.COLLECTION_INSURER_PORTAL_DOC, AppConstants.COLLECTION_REPO_MISC_REPOSITORY,
			AppConstants.COLLECTION_PG_DOC_REPOSITORY, AppConstants.COLLECTION_REPO_POLICY_REPOSITORY,
			AppConstants.COLLECTION_DOC_UPLOAD, AppConstants.COLLECTION_CUSTOMER_PROOF_DOC,
			AppConstants.COLLECTION_CUSTOMER_PROOF_DOC_AFFLIATE, AppConstants.COLLECTION_USER_REVIEW_DOC,
			AppConstants.COLLECTION_USER_REVIEW_DOC_AFFLIATE, AppConstants.COLLECTION_AUDIO_DOC };


	@ResponseBody
	@GetMapping(value = "/deleteNotBookedLead")
	public Map<String, String> deleteNotBookedLeadInCarLoggerDb(@RequestParam(required = false, value = "startId") String startId,
			@RequestParam(required = false,value = "size") Integer size,@RequestParam(required = false,value = "waitInSec") Integer waitInSec,
			@RequestParam(required = false,value = "stop") Boolean stop, 
			@RequestParam(value = "coll") String coll, @RequestParam(required = false,value = "timeCheck") Boolean timeCheck) {
		logger.info("Inside deleteNotBookedLeadInCarLoggerDb with ids : {} and {}", startId);
		Map<String, String> result = new HashMap<String, String>();
		try {
			logger.info("Serial no found, startId {}", startId);
			Date todayDate = new Date();
			Date startDate = new Date();
			startDate = DateUtil.setHours(startDate, 7);
			startDate = DateUtil.setMinute(startDate, 0);
			startDate = DateUtil.setSeconds(startDate, 0);

			Date endDate = new Date();
			endDate = DateUtil.setHours(endDate, 20);
			endDate = DateUtil.setMinute(endDate, 0);
			endDate = DateUtil.setSeconds(endDate, 0);

			boolean migrate = false;
			
			if(stop!=null ) {
				ScheduleServiceImpl.stopScheduleTw1 = stop;
			}
			
			if(waitInSec!=null) {
				ScheduleServiceImpl.waitScheduleTw1 = waitInSec;
			}
			
			if(size== null){
				size = 500;
			}			
			
			if(size>1000) {
				logger.info("size is greater than max value 1000");
				size = 1000;
			}
			
			logger.info("Today date : {}, start date {} , end date {}", todayDate, startDate, endDate);
			if ((todayDate.before(startDate) || todayDate.after(endDate) || !timeCheck) && !ScheduleServiceImpl.scheduleTw1Running && StaticMaster.getCollectionList().contains(coll)) {
				ScheduleServiceImpl.scheduleTw1Running =true;
				migrate = true;
				scheduleService.deleteNotBookLeadInTw(startId, coll, size,timeCheck);
				result.put("OK", "1");
				result.put("SK_MSG", "collection migrated");
				ScheduleServiceImpl.scheduleTw1Running = false;
			}
			if (!migrate && ScheduleServiceImpl.scheduleTw1Running) {
				logger.info("Could not initiate migration at this time already running: {} , stop {} , waitInSec {}", new Date(), stop, waitInSec);
				logger.info(" coll {} , startId, {} , size {} ",coll, startId, size );
				result.put("OK", "0");
				result.put("SK_MSG", "Could not initiate migration at this time , " + new Date());
			}else if(!migrate){
				logger.info("Could not initiate migration at this time {} , stop {} , waitInSec {}", new Date(), stop, waitInSec);
				logger.info(" coll {} , startId, {} , size {} ",coll, startId, size );
				result.put("OK", "0");
				result.put("SK_MSG", "not initiate migration, " + new Date());
			}
		} catch (Exception e) {
			ScheduleServiceImpl.scheduleTw1Running = false;
			result.put("OK", "0");
			result.put("SK_MSG", "Error in cleanUpCommBox : ," + e.getMessage());
			logger.error("Error in cleanUpCommBox , {}", e.getMessage());
		}
		logger.info("Exiting cleanUpTwoWheeler ");
		return result;
	}
	
	@ResponseBody
	@GetMapping(value = "/deleteNotBookedLeadCar")
	public Map<String, String> deleteNotBookedLeadInCar(@RequestParam(required = false, value = "startId") String startId,
			@RequestParam(required = false,value = "size") Integer size,@RequestParam(required = false,value = "waitInSec") Integer waitInSec,
			@RequestParam(required = false,value = "stop") Boolean stop, 
			@RequestParam(value = "coll") String coll, @RequestParam(required = false,value = "timeCheck") Boolean timeCheck) {
		logger.info("Inside deleteNotBookedLeadInCar with ids : {} and {}", startId);
		Map<String, String> result = new HashMap<String, String>();
		try {
			logger.info("Serial no found, startId {}", startId);
			Date todayDate = new Date();
			Date startDate = new Date();
			startDate = DateUtil.setHours(startDate, 7);
			startDate = DateUtil.setMinute(startDate, 0);
			startDate = DateUtil.setSeconds(startDate, 0);

			Date endDate = new Date();
			endDate = DateUtil.setHours(endDate, 20);
			endDate = DateUtil.setMinute(endDate, 0);
			endDate = DateUtil.setSeconds(endDate, 0);

			boolean migrate = false;
			
			if(stop!=null ) {
				ScheduleServiceImpl.stopScheduleCar1 = stop;
			}
			
			if(waitInSec!=null) {
				ScheduleServiceImpl.waitScheduleCar1 = waitInSec;
			}
			
			if(size== null){
				size = 500;
			}			
			
			if(size>1000) {
				logger.info("size is greater than max value 1000");
				size = 1000;
			}
			
			logger.info("Today date : {}, start date {} , end date {}", todayDate, startDate, endDate);
			if ((todayDate.before(startDate) || todayDate.after(endDate) || !timeCheck) && !ScheduleServiceImpl.scheduleCar1Running && StaticMaster.getCollectionList().contains(coll)) {
				ScheduleServiceImpl.scheduleCar1Running =true;
				migrate = true;
				scheduleService.deleteNotBookLeadInCar(startId, coll, size,timeCheck);
				result.put("OK", "1");
				result.put("SK_MSG", "collection migrated");
				ScheduleServiceImpl.scheduleCar1Running = false;
			}
			if (!migrate && ScheduleServiceImpl.scheduleCar1Running) {
				logger.info("Could not initiate migration at this time already running: {} , stop {} , waitInSec {}", new Date(), stop, waitInSec);
				logger.info(" coll {} , startId, {} , size {} ",coll, startId, size );
				result.put("OK", "0");
				result.put("SK_MSG", "Could not initiate migration at this time , " + new Date());
			}else if(!migrate){
				logger.info("Could not initiate migration at this time {} , stop {} , waitInSec {}", new Date(), stop, waitInSec);
				logger.info(" coll {} , startId, {} , size {} ",coll, startId, size );
				result.put("OK", "0");
				result.put("SK_MSG", "not initiate migration, " + new Date());
			}
		} catch (Exception e) {
			ScheduleServiceImpl.scheduleCar1Running = false;
			result.put("OK", "0");
			result.put("SK_MSG", "Error in cleanUpCommBox : ," + e.getMessage());
			logger.error("Error in cleanUpCommBox , {}", e.getMessage());
		}
		logger.info("Exiting cleanUpTwoWheeler ");
		return result;
	}
	
}
