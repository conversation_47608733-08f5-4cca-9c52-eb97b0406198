/**
 * 
 */
package com.docMigration.controller;

import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.docMigration.bean.AppConstants;
import com.docMigration.cleanup.entity.StaticMaster;
import com.docMigration.service.DocumentStoreScheduleService;
import com.docMigration.service.impl.DocumentStoreScheduleServiceImpl;
import com.docMigration.util.AppUtil;
import com.docMigration.util.DateUtil;
import com.mongodb.BasicDBObject;
import com.mongodb.DBObject;

/**
 * This is a schedular class which will take documents from db and insert in aws
 * S3 bucket.
 * 
 * <AUTHOR>
 *
 */
@RestController
public class DocumentStoreScheduleController {

	@Autowired
	DocumentStoreScheduleService documentStoreScheduleService;

	private final Logger logger = LoggerFactory.getLogger(DocumentStoreScheduleController.class);

	public static String[] collections = { AppConstants.COLLECTION_DOC_REPOSITORY,
			AppConstants.COLLECTION_INSURER_PORTAL_DOC, AppConstants.COLLECTION_REPO_MISC_REPOSITORY,
			AppConstants.COLLECTION_PG_DOC_REPOSITORY, AppConstants.COLLECTION_REPO_POLICY_REPOSITORY,
			AppConstants.COLLECTION_DOC_UPLOAD, AppConstants.COLLECTION_CUSTOMER_PROOF_DOC,
			AppConstants.COLLECTION_CUSTOMER_PROOF_DOC_AFFLIATE, AppConstants.COLLECTION_USER_REVIEW_DOC,
			AppConstants.COLLECTION_USER_REVIEW_DOC_AFFLIATE, AppConstants.COLLECTION_AUDIO_DOC };

	@ResponseBody
	@GetMapping(value = "/moveToS3")
	public Map<String, String> scheduleFixedDelayTask(@RequestParam("coll") String coll,
			@RequestParam(value = "idCount", required = false) String idCount,
			@RequestParam(value = "timeCheck", required = true) boolean timeCheck,
			@RequestParam(value = "startId", required = false) String startId,
			@RequestParam(value = "endId", required = false) String endId,
			@RequestParam(value = "server", required = true) String server) {

		Map<String, String> result = new HashMap<String, String>();
		try {
			Date todayDate = new Date();
			Date startDate = new Date();
			startDate = DateUtil.setHours(startDate, 7);
			startDate = DateUtil.setMinute(startDate, 0);
			startDate = DateUtil.setSeconds(startDate, 0);

			Date endDate = new Date();
			endDate = DateUtil.setHours(endDate, 20);
			endDate = DateUtil.setMinute(endDate, 0);
			endDate = DateUtil.setSeconds(endDate, 0);

			boolean migrate = false;
			/*
			 * while ((startDate.before(new Date()) && endDate.after(new Date())) ||
			 * !timeCheck) {
			 */
			if (todayDate.before(startDate) || todayDate.after(endDate) || !timeCheck) {
				migrate = true;
				if (Arrays.asList(collections).contains(coll)) {
					logger.info("Collection found, coll {}", coll);
					documentStoreScheduleService.migrateFIlesToS3(coll, idCount, startId, endId, server);
					result.put("OK", "1");
					result.put("SK_MSG", "collection updated");

				} else {
					logger.error("Collection not found, coll {}", coll);
					result.put("OK", "0");
					result.put("SK_MSG", "Collection not found, coll : " + coll);
				}

			}
			if (!migrate) {
				logger.info("Could not initiate migration at this time : {}", new Date());
				result.put("OK", "0");
				result.put("SK_MSG", "Could not initiate migration at this time , " + new Date());
				return result;
			}
		} catch (Exception e) {
			result.put("OK", "0");
			result.put("SK_MSG", "Error in document store schedular : ," + e.getMessage());
			logger.error("Error in document store schedular , {}", e.getMessage());
		}
		logger.info("Exiting scheduleFixedDelayTask ");
		return result;
	}

	@ResponseBody
	@GetMapping(value = "/migrateColl")
	public Map<String, String> migrateColl(@RequestParam("coll") String coll,
			@RequestParam(value = "timeCheck", required = true) boolean timeCheck,
			@RequestParam(value = "startId", required = true) String startId,
			@RequestParam(value = "endId", required = true) String endId) {
		logger.info("Inside migrateColl with ids : {} and {}", startId, endId);
		Map<String, String> result = new HashMap<String, String>();
		try {
			Date todayDate = new Date();
			Date startDate = new Date();
			startDate = DateUtil.setHours(startDate, 7);
			startDate = DateUtil.setMinute(startDate, 0);
			startDate = DateUtil.setSeconds(startDate, 0);

			Date endDate = new Date();
			endDate = DateUtil.setHours(endDate, 20);
			endDate = DateUtil.setMinute(endDate, 0);
			endDate = DateUtil.setSeconds(endDate, 0);

			boolean migrate = false;
			/*
			 * while ((startDate.before(new Date()) && endDate.after(new Date())) ||
			 * !timeCheck) {
			 */
			if (todayDate.before(startDate) || todayDate.after(endDate) || !timeCheck) {
				migrate = true;
				logger.info("Collection found, coll {}", coll);
				documentStoreScheduleService.migrateCollection(coll, startId, endId);
				result.put("OK", "1");
				result.put("SK_MSG", "collection migrated");

			}
			if (!migrate) {
				logger.info("Could not initiate migration at this time : {}", new Date());
				result.put("OK", "0");
				result.put("SK_MSG", "Could not initiate migration at this time , " + new Date());
				return result;
			}
		} catch (Exception e) {
			result.put("OK", "0");
			result.put("SK_MSG", "Error in document store schedular, migrateColl : ," + e.getMessage());
			logger.error("Error in document store schedular , migrateColl , {}", e.getMessage());
		}
		logger.info("Exiting migrateColl ");
		return result;
	}

	@ResponseBody
	@GetMapping(value = "/migrateChatColl")
	public Map<String, String> migrateCollChat(@RequestParam("SrcColl") String SrcColl,
			@RequestParam(value = "targetColl") String targetColl, @RequestParam(value = "Db") String db,
			@RequestParam(value="tsStart", required=false) String tsStart,
			@RequestParam(value = "timeCheck", required = true) boolean timeCheck,
			@RequestParam(required = false,value = "stop") Boolean stop, 
			@RequestParam(value="tsEnd", required=false) String tsEnd,
			@RequestParam(value="source", required=false) String source,
			@RequestParam(required = false,defaultValue = "0") int type) {
		logger.info("Inside migrateColl with ids : {} and {}", SrcColl, targetColl);
		Map<String, String> result = new HashMap<String, String>();
		try {
			Date todayDate = new Date();
			/* Date endDate = DateUtil.addDays(todayDate, 1); */
			Date startDate = new Date();
			startDate = DateUtil.setHours(startDate, 7);
			startDate = DateUtil.setMinute(startDate, 0);
			startDate = DateUtil.setSeconds(startDate, 0);

			Date endDate = new Date();
			endDate = DateUtil.setHours(endDate, 20);
			endDate = DateUtil.setMinute(endDate, 0);
			endDate = DateUtil.setSeconds(endDate, 0);

			boolean migrate = false;
			if (todayDate.before(startDate) || todayDate.after(endDate) || !timeCheck) {
				migrate = true;
				logger.info("Collection found, coll {}", SrcColl);
				documentStoreScheduleService.migrateCollectionForChat(SrcColl, targetColl, db, tsStart, tsEnd,source,timeCheck,type);
				result.put("OK", "1");
				result.put("SK_MSG", "collection migrated");
			}
			if (!migrate) {
				logger.info("Could not initiate migration at this time : {}", new Date());
				result.put("OK", "0");
				result.put("SK_MSG", "Could not initiate migration at this time , " + new Date());
				return result;
			}
		} catch (Exception e) {
			result.put("OK", "0");
			result.put("SK_MSG", "Error in migrateCollChat : ," + e.getMessage());
			logger.error("Error in migrateCollChat , {}", e.getMessage());
		}
		logger.info("Exiting migrateCollChat ");
		return result;
	}
	
	@ResponseBody
	@GetMapping(value = "/migrateUaeChatLog")
	public Map<String, String> migrateUaeChatLog(@RequestParam("SrcColl") String SrcColl,
			@RequestParam(value = "Db") String db,
			@RequestParam(value = "startId", required=false) String startId,
			@RequestParam(value = "timeCheck", required = true) boolean timeCheck,
			@RequestParam(required = false,value = "stop") Boolean stop, 
			@RequestParam(value="source", required=false) String source) {
		logger.info("Inside migrateColl with ids : {} and {}", SrcColl);
		Map<String, String> result = new HashMap<String, String>();
		try {
			Date todayDate = new Date();
			/* Date endDate = DateUtil.addDays(todayDate, 1); */
			Date startDate = new Date();
			startDate = DateUtil.setHours(startDate, 7);
			startDate = DateUtil.setMinute(startDate, 0);
			startDate = DateUtil.setSeconds(startDate, 0);

			Date endDate = new Date();
			endDate = DateUtil.setHours(endDate, 20);
			endDate = DateUtil.setMinute(endDate, 0);
			endDate = DateUtil.setSeconds(endDate, 0);

			boolean migrate = false;
			if (todayDate.before(startDate) || todayDate.after(endDate) || !timeCheck) {
				migrate = true;
				logger.info("Collection found, coll {}", SrcColl);
				documentStoreScheduleService.migrateUaeChatLog(SrcColl, db, source,timeCheck,startId);
				result.put("OK", "1");
				result.put("SK_MSG", "collection migrated");
			}
			if (!migrate) {
				logger.info("Could not initiate migration at this time : {}", new Date());
				result.put("OK", "0");
				result.put("SK_MSG", "Could not initiate migration at this time , " + new Date());
				return result;
			}
		} catch (Exception e) {
			result.put("OK", "0");
			result.put("SK_MSG", "Error in migrateCollChat : ," + e.getMessage());
			logger.error("Error in migrateCollChat , {}", e.getMessage());
		}
		logger.info("Exiting migrateCollChat ");
		return result;
	}
	
	@ResponseBody
	@GetMapping(value = "/updateChatUserToken")
	public Map<String, String> updateChatUserToken(@RequestParam(value = "Db") String db,
			@RequestParam(value = "timeCheck", required = true) boolean timeCheck,
			@RequestParam(required = false,value = "stop") Boolean stop, 
			@RequestParam(value="source", required=false) String source) {
		logger.info("Inside updateChatUserToken with db : {} ", db);
		Map<String, String> result = new HashMap<String, String>();
		try {
			Date todayDate = new Date();
			/* Date endDate = DateUtil.addDays(todayDate, 1); */
			Date startDate = new Date();
			startDate = DateUtil.setHours(startDate, 7);
			startDate = DateUtil.setMinute(startDate, 0);
			startDate = DateUtil.setSeconds(startDate, 0);

			Date endDate = new Date();
			endDate = DateUtil.setHours(endDate, 20);
			endDate = DateUtil.setMinute(endDate, 0);
			endDate = DateUtil.setSeconds(endDate, 0);

			boolean migrate = false;
			if (todayDate.before(startDate) || todayDate.after(endDate) || !timeCheck) {
				migrate = true;
				logger.info("Collection found, db {}", db);
				documentStoreScheduleService.updateChatUserToken(db, source,timeCheck);
				result.put("OK", "1");
				result.put("SK_MSG", "collection migrated");
			}
			if (!migrate) {
				logger.info("Could not initiate migration at this time : {}", new Date());
				result.put("OK", "0");
				result.put("SK_MSG", "Could not initiate migration at this time , " + new Date());
				return result;
			}
		} catch (Exception e) {
			result.put("OK", "0");
			result.put("SK_MSG", "Error in updateChatUserToken : ," + e.getMessage());
			logger.error("Error in updateChatUserToken , {}", e.getMessage());
		}
		logger.info("Exiting updateChatUserToken ");
		return result;
	}

	@ResponseBody
	@GetMapping(value = "/cleanUpTwoWheeler")
	public Map<String, String> cleanUpTwoWheeler(@RequestParam("startingSnNo") String startingSnNo,
			@RequestParam(value = "lastSnNo") String lastSnNo) {
		logger.info("Inside migrateColl with ids : {} and {}", startingSnNo, lastSnNo);
		Map<String, String> result = new HashMap<String, String>();
		try {

			logger.info("Serial no found, startingSnNo {} , lastSnNo {}", startingSnNo, lastSnNo);
			Date todayDate = new Date();
			Date startDate = new Date();
			startDate = DateUtil.setHours(startDate, 7);
			startDate = DateUtil.setMinute(startDate, 0);
			startDate = DateUtil.setSeconds(startDate, 0);

			Date endDate = new Date();
			endDate = DateUtil.setHours(endDate, 20);
			endDate = DateUtil.setMinute(endDate, 0);
			endDate = DateUtil.setSeconds(endDate, 0);

			boolean migrate = false;
			if (todayDate.before(startDate) || todayDate.after(endDate)) {
				migrate = true;
			documentStoreScheduleService.cleanUpForTwoWheeler(Long.valueOf(startingSnNo), Long.valueOf(lastSnNo));
			result.put("OK", "1");
			result.put("SK_MSG", "collection migrated");
			}
			if (!migrate) {
				logger.info("Could not initiate cleanUpTwoWheeler migration at this time : {}", new Date());
				result.put("OK", "0");
				result.put("SK_MSG", "Could not cleanUpTwoWheeler initiate migration at this time , " + new Date());
				return result;
			}
		} catch (Exception e) {
			result.put("OK", "0");
			result.put("SK_MSG", "Error in cleanUpTwoWheeler : ," + e.getMessage());
			logger.error("Error in cleanUpTwoWheeler , {}", e.getMessage());
		}
		logger.info("Exiting cleanUpTwoWheeler ");
		return result;
	}
	
	@ResponseBody
	@GetMapping(value = "/cleanUpTwoWheeler2")
	public Map<String, String> cleanUpTwoWheeler2(@RequestParam(value = "startId", required = false) String startId,
			@RequestParam(value = "timeCheck", required = true) boolean timeCheck, @RequestParam(value = "isNew", required = false) boolean isNew,
			@RequestParam(value = "endId", required = false) String endId,
			@RequestParam(value = "limit", required = true) Integer limit) {
		logger.info("Inside migrateColl with ids : {} and {}", startId, endId);
		Map<String, String> result = new HashMap<String, String>();
		try {

			logger.info("Serial no found, startId {} , lastSnNo {}", startId, endId);
			Date todayDate = new Date();
			Date startDate = new Date();
			startDate = DateUtil.setHours(startDate, 7);
			startDate = DateUtil.setMinute(startDate, 0);
			startDate = DateUtil.setSeconds(startDate, 0);

			Date endDate = new Date();
			endDate = DateUtil.setHours(endDate, 20);
			endDate = DateUtil.setMinute(endDate, 0);
			endDate = DateUtil.setSeconds(endDate, 0);

			boolean migrate = false;
			if (todayDate.before(startDate) || todayDate.after(endDate) || !timeCheck) {
				migrate = true;
			documentStoreScheduleService.cleanUpForTwoWheeler2(startId, endId,limit,timeCheck, isNew);
			result.put("OK", "1");
			result.put("SK_MSG", "collection migrated");
			}
			if (!migrate) {
				logger.info("Could not initiate cleanUpTwoWheeler2 migration at this time : {}", new Date());
				result.put("OK", "0");
				result.put("SK_MSG", "Could not cleanUpTwoWheeler2 initiate migration at this time , " + new Date());
				return result;
			}
		} catch (Exception e) {
			result.put("OK", "0");
			result.put("SK_MSG", "Error in cleanUpTwoWheeler2 : ," + e.getMessage());
			logger.error("Error in cleanUpTwoWheeler2 , {}", e.getMessage());
		}
		logger.info("Exiting cleanUpTwoWheeler2 ");
		return result;
	}
	
	@ResponseBody
	@GetMapping(value = "/markBookedCarLead")
	public Map<String, String> markBookedCarLead(@RequestParam(value = "startId", required = false) String startId,
			@RequestParam(value = "timeCheck", required = true) boolean timeCheck,@RequestParam(value = "isNew", required = false) boolean isNew,
			@RequestParam(value = "endId", required = false) String endId,
			@RequestParam(value = "limit", required = true) Integer limit) {
		logger.info("Inside markBookedCarLead migrateColl with ids : {} and {}", startId, endId);
		Map<String, String> result = new HashMap<String, String>();
		try {

			logger.info("Serial no found, startId {} , lastSnNo {}", startId, endId);
			Date todayDate = new Date();
			Date startDate = new Date();
			startDate = DateUtil.setHours(startDate, 7);
			startDate = DateUtil.setMinute(startDate, 0);
			startDate = DateUtil.setSeconds(startDate, 0);

			Date endDate = new Date();
			endDate = DateUtil.setHours(endDate, 20);
			endDate = DateUtil.setMinute(endDate, 0);
			endDate = DateUtil.setSeconds(endDate, 0);

			boolean migrate = false;
			if (todayDate.before(startDate) || todayDate.after(endDate) || !timeCheck) {
				migrate = true;
			documentStoreScheduleService.markBookedCarLead(startId, endId, limit,timeCheck,isNew);
			result.put("OK", "1");
			result.put("SK_MSG", "collection migrated");
			}
			if (!migrate) {
				logger.info("Could not initiate markBookedCarLead migration at this time : {}", new Date());
				result.put("OK", "0");
				result.put("SK_MSG", "Could not markBookedCarLead initiate migration at this time , " + new Date());
				return result;
			}
		} catch (Exception e) {
			result.put("OK", "0");
			result.put("SK_MSG", "Error in markBookedCarLead : ," + e.getMessage());
			logger.error("Error in markBookedCarLead , {}", e.getMessage());
		}
		logger.info("Exiting markBookedCarLead ");
		return result;
	}
	
	/**
	 * 
	 * @param startId
	 * @param endId
	 * @param timeCheck
	 * @param coll     - collection name - from which collection data need to be moved
	 * @param years    - till (today - years) will be upper limit of data need to be moved
	 * @param type     - to move from commBox to commPrimaryArchival - type = 1 and else commPrimaryArchival to Archival
	 * @param sourceDB - source database name - from where data need to be moved
	 * @param destDB   - destination database name - to where data need to be moved 
	 * @return
	 */
	
	@ResponseBody
	@GetMapping(value = "/cleanUpCommBox")
	public Map<String, String> cleanUpCommBox(@RequestParam(required = false,value = "timeCheck") Boolean timeCheck,
												@RequestParam(value = "coll") String dbColl,
												@RequestParam(required = false,defaultValue  = "3") int years,
												@RequestParam(required = false,defaultValue  = "1") int type) {
		logger.info("Inside cleanUpCommBox with ns : {} years {}", dbColl, years);
		Map<String, String> result = new HashMap<String, String>();
		try {
			String[] ns = {"communicationD.Email_Collection"};
			if(!Arrays.asList(ns).contains(dbColl)){
				result.put("error", "ns is not whitelisted to proceed further...");
				return result;
			}
			Date todayDate = new Date();
			Date startDate = new Date();
			startDate = DateUtil.setHours(startDate, 7);
			startDate = DateUtil.setMinute(startDate, 0);
			startDate = DateUtil.setSeconds(startDate, 0);

			Date endDate = new Date();
			endDate = DateUtil.setHours(endDate, 20);
			endDate = DateUtil.setMinute(endDate, 0);
			endDate = DateUtil.setSeconds(endDate, 0);

			boolean migrate = false;
			if (todayDate.before(startDate) || todayDate.after(endDate) || !timeCheck) {
				migrate = true;
			documentStoreScheduleService.cleanUpForCommbox(dbColl,timeCheck, years, type);
			result.put("OK", "1");
			result.put("SK_MSG", "collection migrated");
			}
			if (!migrate) {
				logger.info("Could not initiate migration at this time : {}", new Date());
				result.put("OK", "0");
				result.put("SK_MSG", "Could not initiate migration at this time , " + new Date());
				return result;
			}
		} catch (Exception e) {
			result.put("OK", "0");
			result.put("SK_MSG", "Error in cleanUpCommBox : ," + e.getMessage());
			logger.error("Error in cleanUpCommBox , {}", e.getMessage());
		}
		logger.info("Exiting cleanUpTwoWheeler ");
		return result;
	}
	
	
	
	@ResponseBody
	@GetMapping(value = "/deleteNotBookedLead")
	public Map<String, String> deleteNotBookedLeadInTwDb(@RequestParam(required = false, value = "startId") String startId,
			@RequestParam(required = false,value = "size") Integer size,@RequestParam(required = false,value = "waitInSec") Integer waitInSec,
			@RequestParam(required = false,value = "stop") Boolean stop, 
			@RequestParam(value = "coll") String coll, @RequestParam(required = false,value = "timeCheck") Boolean timeCheck) {
		logger.info("Inside deleteNotBookedLeadInTwDb with ids : {} and {}", startId);
		Map<String, String> result = new HashMap<String, String>();
		try {
			logger.info("Serial no found, startId {}", startId);
			Date todayDate = new Date();
			Date startDate = new Date();
			startDate = DateUtil.setHours(startDate, 7);
			startDate = DateUtil.setMinute(startDate, 0);
			startDate = DateUtil.setSeconds(startDate, 0);

			Date endDate = new Date();
			endDate = DateUtil.setHours(endDate, 20);
			endDate = DateUtil.setMinute(endDate, 0);
			endDate = DateUtil.setSeconds(endDate, 0);

			boolean migrate = false;
			
			if(stop!=null ) {
				StaticMaster.setDeleteTwLoggerStop(stop);
			}
			
			if(waitInSec!=null) {
				StaticMaster.setWaitForNextDeleteBatchInTw(waitInSec);
			}
			
			if(size== null){
				size = 500;
			}			
			
			if(size>1000) {
				logger.info("size is greater than max value 1000");
				size = 1000;
			}
			
			logger.info("Today date : {}, start date {} , end date {}", todayDate, startDate, endDate);
			if ((todayDate.before(startDate) || todayDate.after(endDate) || !timeCheck) && !StaticMaster.isLoggerDeleteJobRunning() && StaticMaster.getCollectionList().contains(coll)) {
				StaticMaster.setLoggerDeleteJobRunning(true);
				migrate = true;
				documentStoreScheduleService.deleteNotBookLeadInTwLogger(startId, coll, size,timeCheck);
				result.put("OK", "1");
				result.put("SK_MSG", "collection migrated");
				StaticMaster.setLoggerDeleteJobRunning(false);
			}
			if (!migrate && StaticMaster.isLoggerDeleteJobRunning()) {
				logger.info("Could not initiate deleteNotBookedLeadInTwDb migration at this time already running: {} , stop {} , waitInSec {}", new Date(), stop, waitInSec);
				logger.info(" coll {} , startId, {} , size {} ",coll, startId, size );
				result.put("OK", "0");
				result.put("SK_MSG", "Could not initiate migration at this time , " + new Date());
			}else if(!migrate){
				logger.info("Could not initiate deleteNotBookedLeadInTwDb migration at this time {} , stop {} , waitInSec {}", new Date(), stop, waitInSec);
				logger.info(" coll {} , startId, {} , size {} ",coll, startId, size );
				result.put("OK", "0");
				result.put("SK_MSG", "not initiate migration, " + new Date());
			}
		} catch (Exception e) {
			StaticMaster.setLoggerDeleteJobRunning(false);
			result.put("OK", "0");
			result.put("SK_MSG", "Error in deleteNotBookedLeadInTwDb : ," + e.getMessage());
			logger.error("Error in deleteNotBookedLeadInTwDb , {}", e.getMessage());
		}
		logger.info("Exiting cleanUpTwoWheeler ");
		return result;
	}
	
	//@ResponseBody
	//@GetMapping(value = "/deleteNotBookedLeadCar")
	/*public Map<String, String> deleteNotBookedLeadInCar(@RequestParam(required = false, value = "startId") String startId,
			@RequestParam(required = false,value = "size") Integer size,@RequestParam(required = false,value = "waitInSec") Integer waitInSec,
			@RequestParam(required = false,value = "stop") Boolean stop, 
			@RequestParam(value = "coll") String coll, @RequestParam(required = false,value = "timeCheck") Boolean timeCheck) {
		logger.info("Inside deleteNotBookedLeadInCar with ids : {} and {}", startId);
		Map<String, String> result = new HashMap<String, String>();
		try {
			logger.info("Serial no found, startId {}", startId);
			Date todayDate = new Date();
			Date startDate = new Date();
			startDate = DateUtil.setHours(startDate, 7);
			startDate = DateUtil.setMinute(startDate, 0);
			startDate = DateUtil.setSeconds(startDate, 0);

			Date endDate = new Date();
			endDate = DateUtil.setHours(endDate, 20);
			endDate = DateUtil.setMinute(endDate, 0);
			endDate = DateUtil.setSeconds(endDate, 0);

			boolean migrate = false;
			
			if(stop!=null ) {
				StaticMaster.setDeleteCarLoggerStop(stop);
			}
			
			if(waitInSec!=null) {
				StaticMaster.setWaitForNextDeleteBatchInCar(waitInSec);
			}
			
			if(size== null){
				size = 500;
			}			
			
			if(size>1000) {
				logger.info("size is greater than max value 1000");
				size = 1000;
			}
			
			logger.info("Today date : {}, start date {} , end date {}", todayDate, startDate, endDate);
			if ((todayDate.before(startDate) || todayDate.after(endDate) || !timeCheck) && !StaticMaster.isLoggerCarDeleteJobRunning() && StaticMaster.getCollectionList().contains(coll)) {
				StaticMaster.setLoggerCarDeleteJobRunning(true);
				migrate = true;
				documentStoreScheduleService.deleteNotBookLeadInCarLogger(startId, coll, size, timeCheck);
				result.put("OK", "1");
				result.put("SK_MSG", "collection migrated");
				StaticMaster.setLoggerCarDeleteJobRunning(false);
			}
			if (!migrate && StaticMaster.isLoggerCarDeleteJobRunning()) {
				logger.info("Could not initiate deleteNotBookedLeadInCar migration at this time already running: {} , stop {} , waitInSec {}", new Date(), stop, waitInSec);
				logger.info(" coll {} , startId, {} , size {} ",coll, startId, size );
				result.put("OK", "0");
				result.put("SK_MSG", "Could not initiate migration at this time , " + new Date());
			}else if(!migrate){
				logger.info("Could not initiate deleteNotBookedLeadInCar deleteNotBookedLeadInCar at this time {} , stop {} , waitInSec {}", new Date(), stop, waitInSec);
				logger.info(" coll {} , startId, {} , size {} ",coll, startId, size );
				result.put("OK", "0");
				result.put("SK_MSG", "not initiate deleteNotBookedLeadInCar, " + new Date());
			}
		} catch (Exception e) {
			StaticMaster.setLoggerCarDeleteJobRunning(false);
			result.put("OK", "0");
			result.put("SK_MSG", "Error in deleteNotBookedLeadInCar : ," + e.getMessage());
			logger.error("Error in deleteNotBookedLeadInCar , {}", e.getMessage());
		}
		logger.info("Exiting deleteNotBookedLeadInCar ");
		return result;
	}*/

	/**
	 * migrating data from mServiceDB. to same collection in Archival node.
	 *
	 * @param startId
	 * @param size
	 * @param waitInSec
	 * @param stop
	 * @param coll
	 * @param timeCheck
	 * @return
	 */
	@ResponseBody
	@GetMapping(value = "/deleteMserviceColl")
	public Map<String, String> deleteMserviceColl(@RequestParam(required = false, value = "startId") String startId,
			@RequestParam(required = false,value = "size") Integer size,@RequestParam(required = false,value = "waitInSec") Integer waitInSec,
			@RequestParam(required = false,value = "stop") Boolean stop, 
			@RequestParam(value = "coll") String coll, @RequestParam(value = "timeCheck") Boolean timeCheck) {
		logger.info("Inside deleteMserviceColl with ids : {} and {}", startId);
		Map<String, String> result = new HashMap<String, String>();
		try {
			logger.info("Serial no found, startId {}", startId);
			Date todayDate = new Date();
			Date startDate = new Date();
			startDate = DateUtil.setHours(startDate, 7);
			startDate = DateUtil.setMinute(startDate, 0);
			startDate = DateUtil.setSeconds(startDate, 0);

			Date endDate = new Date();
			endDate = DateUtil.setHours(endDate, 20);
			endDate = DateUtil.setMinute(endDate, 0);
			endDate = DateUtil.setSeconds(endDate, 0);

			boolean migrate = false;
			
			if(stop!=null ) {
				StaticMaster.setDeleteMserviceStop(stop);
			}
			
			if(waitInSec!=null) {
				StaticMaster.setWaitForNextDeleteBatchMService(waitInSec);
			}
			
			if(size== null){
				size = 500;
			}			
			
			if(size>1000) {
				logger.info("size is greater than max value 1000");
				size = 1000;
			}
			
			logger.info("Today date : {}, start date {} , end date {}", todayDate, startDate, endDate);
			if ((todayDate.before(startDate) || todayDate.after(endDate) || !timeCheck) && !StaticMaster.isMServiceDeleteJobRunning() && StaticMaster.getCollectionList().contains(coll)) {
				StaticMaster.setMServiceDeleteJobRunning(true);
				migrate = true;
				documentStoreScheduleService.deleteAppNotificationMService(startId, coll, size,timeCheck);
				result.put("OK", "1");
				result.put("SK_MSG", "collection migrated");
				StaticMaster.setMServiceDeleteJobRunning(false);
			}
			if (!migrate && StaticMaster.isMServiceDeleteJobRunning()) {
				logger.info("Could not initiate migration MServiceDeleteJobRunning at this time already running: {} , stop {} , waitInSec {}", new Date(), stop, waitInSec);
				logger.info(" coll {} , startId, {} , size {} ",coll, startId, size );
				result.put("OK", "0");
				result.put("SK_MSG", "Could not initiate migration at this time , " + new Date());
			}else if(!migrate){
				logger.info("Could not initiate migration MServiceDeleteJobRunning at this time {} , stop {} , waitInSec {}", new Date(), stop, waitInSec);
				logger.info(" coll {} , startId, {} , size {} ",coll, startId, size );
				result.put("OK", "0");
				result.put("SK_MSG", "not initiate migration, " + new Date());
			}
		} catch (Exception e) {
			StaticMaster.setMServiceDeleteJobRunning(false);
			result.put("OK", "0");
			result.put("SK_MSG", "Error in deleteMserviceColl : ," + e.getMessage());
			logger.error("Error in deleteMserviceColl , {}", e.getMessage());
		}
		logger.info("Exiting deleteMserviceColl ");
		return result;
	}
	/**
	 * 
	 * @param startId
	 * @param size
	 * @param waitInSec
	 * @param stop
	 * @param coll
	 * @param timeCheck
	 * @param days
	 * @param checkColl
	 * @return
	 */
	@ResponseBody
	@GetMapping(value = "/deleteNotBookedLeadCar/v1")
	public Map<String, String> deleteNotBookedLeadInCarV1(@RequestParam(required = false, value = "startId") String startId,
			@RequestParam(required = false,value = "size") Integer size,@RequestParam(required = false,value = "waitInSec") Integer waitInSec,
			@RequestParam(required = false,value = "stop") Boolean stop, 
			@RequestParam(value = "coll") String coll, @RequestParam(required = false,value = "timeCheck") Boolean timeCheck,
			@RequestParam(value="days") int days,@RequestParam(value="checkColl") String checkColl) {
		logger.info("Inside deleteNotBookedLeadInCarV1 with ids : {} and {}", startId);
		Map<String, String> result = new HashMap<String, String>();
		try {
			logger.info("Serial no found, startId {}", startId);
			Date todayDate = new Date();
			Date startDate = new Date();
			startDate = DateUtil.setHours(startDate, 7);
			startDate = DateUtil.setMinute(startDate, 0);
			startDate = DateUtil.setSeconds(startDate, 0);

			Date endDate = new Date();
			endDate = DateUtil.setHours(endDate, 20);
			endDate = DateUtil.setMinute(endDate, 0);
			endDate = DateUtil.setSeconds(endDate, 0);

			boolean migrate = false;
			
			if(stop!=null ) {
				StaticMaster.setDeleteCarLoggerV1Stop(stop);
			}
			
			if(size== null){
				size = 500;
			}			
			
			if(size>1000) {
				logger.info("size is greater than max value 1000");
				size = 1000;
			}
			
			logger.info("Today date : {}, start date {} , end date {}", todayDate, startDate, endDate);
			if ((todayDate.before(startDate) || todayDate.after(endDate) || !timeCheck) && !StaticMaster.isLoggerCarDeleteJobRunning()) {
				StaticMaster.setLoggerCarDeleteV1JobRunning(true);
				migrate = true;
				DBObject projectObj = new BasicDBObject("_id", 1).append("EnquiryID", 1).append("PlanID", 1);
				DBObject query = new BasicDBObject();
				query.put("enquiryId", "EnquiryID");
				query.put("planid", "PlanID");
				documentStoreScheduleService.deleteNotBookLeadInCarLoggerV1(coll, size, timeCheck,-days,checkColl,"CarLoggerDB",projectObj,query);
				result.put("OK", "1");
				result.put("SK_MSG", "collection migrated");
				StaticMaster.setLoggerCarDeleteV1JobRunning(false);
			}
			if (!migrate && StaticMaster.isLoggerCarDeleteV1JobRunning()) {
				logger.info("Could not initiate deleteNotBookedLeadInCarV1 migration at this time already running: {} , stop {} , waitInSec {}", new Date(), stop, waitInSec);
				logger.info(" coll {} , startId, {} , size {} ",coll, startId, size );
				result.put("OK", "0");
				result.put("SK_MSG", "Could not initiate migration at this time , " + new Date());
			}else if(!migrate){
				logger.info("Could not initiate deleteNotBookedLeadInCarV1 at this time {} , stop {} , waitInSec {}", new Date(), stop, waitInSec);
				logger.info(" coll {} , startId, {} , size {} ",coll, startId, size );
				result.put("OK", "0");
				result.put("SK_MSG", "not initiate deleteNotBookedLeadInCarV1, " + new Date());
			}
		} catch (Exception e) {
			StaticMaster.setLoggerCarDeleteJobRunning(false);
			result.put("OK", "0");
			result.put("SK_MSG", "Error in deleteNotBookedLeadInCarV1 : ," + e.getMessage());
			logger.error("Error in deleteNotBookedLeadInCarV1 , {}", e.getMessage());
		}
		logger.info("Exiting deleteNotBookedLeadInCarV1 ");
		return result;
	}
	
	@GetMapping(value = "/deleteNotBookedLeadSme")
	public Map<String, String> deleteNotBookedLeadSme(@RequestParam(required = false,value = "size") Integer size,@RequestParam(required = false,value = "waitInSec") Integer waitInSec,
			@RequestParam(required = false,value = "stop") Boolean stop, 
			@RequestParam(value = "coll") String coll, @RequestParam(required = false,value = "timeCheck") Boolean timeCheck,
			@RequestParam(value="days") int days,@RequestParam(value="checkColl") String checkColl) {
		logger.info("Inside deleteNotBookedLeadSme with ids : {} and {}");
		Map<String, String> result = new HashMap<String, String>();
		try {
			logger.info("Serial no found, startId {}");
			Date todayDate = new Date();
			Date startDate = new Date();
			startDate = DateUtil.setHours(startDate, 7);
			startDate = DateUtil.setMinute(startDate, 0);
			startDate = DateUtil.setSeconds(startDate, 0);

			Date endDate = new Date();
			endDate = DateUtil.setHours(endDate, 20);
			endDate = DateUtil.setMinute(endDate, 0);
			endDate = DateUtil.setSeconds(endDate, 0);

			boolean migrate = false;
			
			if(stop!=null ) {
				StaticMaster.setDeleteCarLoggerV1Stop(stop);
			}
			
			if(size== null){
				size = 500;
			}			
			
			if(size>1000) {
				logger.info("size is greater than max value 1000");
				size = 1000;
			}
			
			logger.info("Today date : {}, start date {} , end date {}", todayDate, startDate, endDate);
			if ((todayDate.before(startDate) || todayDate.after(endDate) || !timeCheck) && !StaticMaster.isLoggerSmeDeleteJobRunning()) {
				StaticMaster.setLoggerSmeDeleteJobRunning(true);
				migrate = true;
				DBObject projectObj = new BasicDBObject("_id", 1).append("EnquiryID", 1).append("MatrixLeadID", 1);
				DBObject query = new BasicDBObject();
				query.put("EnquiryId", "EnquiryID");	
				documentStoreScheduleService.deleteNotBookLeadInCarLoggerV1(coll, size, timeCheck,-days,checkColl,"SmeLog",projectObj,query);
				result.put("OK", "1");
				result.put("SK_MSG", "collection migrated");
				StaticMaster.setLoggerSmeDeleteJobRunning(false);
			}
			if (!migrate && StaticMaster.isLoggerSmeDeleteJobRunning()) {
				logger.info("Could not initiate deleteNotBookedLeadSme migration at this time already running: {} , stop {} , waitInSec {}", new Date(), stop, waitInSec);
				logger.info(" coll {} , size {} ",coll, size );
				result.put("OK", "0");
				result.put("SK_MSG", "Could not initiate migration at this time , " + new Date());
			}else if(!migrate){
				logger.info("Could not initiate deleteNotBookedLeadSme at this time {} , stop {} , waitInSec {}", new Date(), stop, waitInSec);
				logger.info(" coll {} , size {} ",coll, size );
				result.put("OK", "0");
				result.put("SK_MSG", "not initiate deleteNotBookedLeadSme, " + new Date());
			}
		} catch (Exception e) {
			StaticMaster.setLoggerSmeDeleteJobRunning(false);
			result.put("OK", "0");
			result.put("SK_MSG", "Error in deleteNotBookedLeadSme : ," + e.getMessage());
			logger.error("Error in deleteNotBookedLeadSme , {}", e.getMessage());
		}
		logger.info("Exiting deleteNotBookedLeadSme ");
		return result;
	}
	
	/**
	 * 
	 * @param size
	 * @param waitInSec
	 * @param stop
	 * @param coll
	 * @param timeCheck
	 * @param days
	 * @param checkColl
	 * @return
	 */
	@GetMapping(value = "/deleteNotBookedLeadSmeNonEb")
	public Map<String, String> deleteNotBookedLeadSmeNonEb(@RequestParam(required = false,value = "size") Integer size,@RequestParam(required = false,value = "waitInSec") Integer waitInSec,
			@RequestParam(required = false,value = "stop") Boolean stop, 
			@RequestParam(value = "coll") String coll, @RequestParam(required = false,value = "timeCheck") Boolean timeCheck,
			@RequestParam(value="days") int days,@RequestParam(value="checkColl") String checkColl){
		logger.info("Inside deleteNotBookedLeadSmeNonEb with ids : {} and {}");
		Map<String, String> result = new HashMap<String, String>();
		try {
			logger.info("Serial no found, startId {}");
			Date todayDate = new Date();
			Date startDate = new Date();
			startDate = DateUtil.setHours(startDate, 7);
			startDate = DateUtil.setMinute(startDate, 0);
			startDate = DateUtil.setSeconds(startDate, 0);

			Date endDate = new Date();
			endDate = DateUtil.setHours(endDate, 20);
			endDate = DateUtil.setMinute(endDate, 0);
			endDate = DateUtil.setSeconds(endDate, 0);

			boolean migrate = false;
			
			if(stop!=null ) {
				StaticMaster.setDeleteCarLoggerV1Stop(stop);
			}
			
			if(size== null){
				size = 500;
			}			
			
			if(size>1000) {
				logger.info("size is greater than max value 1000");
				size = 1000;
			}
			
			logger.info("Today date : {}, start date {} , end date {}", todayDate, startDate, endDate);
			if ((todayDate.before(startDate) || todayDate.after(endDate) || !timeCheck)) {
				migrate = true;
				DBObject projectObj = new BasicDBObject("_id", 1).append("EnquiryID", 1).append("MatrixLeadID", 1);
				DBObject query = new BasicDBObject();
				query.put("EnquiryId", "EnquiryID");	
				documentStoreScheduleService.deleteNotBookLeadInCarLoggerV1(coll, size, timeCheck,-days,checkColl,"SMENonEBLog",projectObj,query);
				result.put("OK", "1");
				result.put("SK_MSG", "collection migrated");
			}
			if(!migrate){
				logger.info("Could not initiate deleteNotBookedLeadSmeNonEb at this time {} , stop {} , waitInSec {}", new Date(), stop, waitInSec);
				logger.info(" coll {} , size {} ",coll, size );
				result.put("OK", "0");
				result.put("SK_MSG", "not initiate deleteNotBookedLeadSmeNonEb, " + new Date());
			}
		} catch (Exception e) {
			result.put("OK", "0");
			result.put("SK_MSG", "Error in deleteNotBookedLeadSmeNonEb : ," + e.getMessage());
			logger.error("Error in deleteNotBookedLeadSmeNonEb , {}", e.getMessage());
		}
		logger.info("Exiting deleteNotBookedLeadSmeNonEb ");
		return result;
	}
	
	
	@GetMapping(value = "/deleteNotBookedLeadHome")
	public Map<String, String> deleteNotBookedLeadHome(@RequestParam(required = false,value = "size") Integer size,@RequestParam(required = false,value = "waitInSec") Integer waitInSec,
			@RequestParam(required = false,value = "stop") Boolean stop, 
			@RequestParam(value = "coll") String coll, @RequestParam(required = false,value = "timeCheck") Boolean timeCheck,@RequestParam(value="days") int days,
			@RequestParam(value="checkColl") String checkColl) {
		logger.info("Inside deleteNotBookedLeadSmeNonEb with ids : {} and {}");
		Map<String, String> result = new HashMap<String, String>();
		try {
			logger.info("Serial no found, startId {}");
			Date todayDate = new Date();
			Date startDate = new Date();
			startDate = DateUtil.setHours(startDate, 7);
			startDate = DateUtil.setMinute(startDate, 0);
			startDate = DateUtil.setSeconds(startDate, 0);

			Date endDate = new Date();
			endDate = DateUtil.setHours(endDate, 20);
			endDate = DateUtil.setMinute(endDate, 0);
			endDate = DateUtil.setSeconds(endDate, 0);

			boolean migrate = false;
			
			if(stop!=null ) {
				StaticMaster.setDeleteCarLoggerV1Stop(stop);
			}
			
			if(size== null){
				size = 500;
			}			
			
			if(size>1000) {
				logger.info("size is greater than max value 1000");
				size = 1000;
			}
			
			logger.info("Today date : {}, start date {} , end date {}", todayDate, startDate, endDate);
			if ((todayDate.before(startDate) || todayDate.after(endDate) || !timeCheck)) {
				migrate = true;
				DBObject projectObj = new BasicDBObject("_id", 1).append("EnquiryID", 1).append("MatrixLeadID", 1);
				DBObject query = new BasicDBObject();
				query.put("EnquiryId", "EnquiryID");	
				query.put("MatrixLeadID", "MatrixLeadID");	
				documentStoreScheduleService.deleteNotBookLeadInCarLoggerV1(coll, size, timeCheck,-days,checkColl,"homeLog",projectObj,query);
				result.put("OK", "1");
				result.put("SK_MSG", "collection migrated");
			}
			if(!migrate){
				logger.info("Could not initiate deleteNotBookedLeadHome at this time {} , stop {} , waitInSec {}", new Date(), stop, waitInSec);
				logger.info(" coll {} , size {} ",coll, size );
				result.put("OK", "0");
				result.put("SK_MSG", "not initiate deleteNotBookedLeadHome, " + new Date());
			}
		} catch (Exception e) {
			result.put("OK", "0");
			result.put("SK_MSG", "Error in deleteNotBookedLeadHome : ," + e.getMessage());
			logger.error("Error in deleteNotBookedLeadHome , {}", e.getMessage());
		}
		logger.info("Exiting deleteNotBookedLeadHome ");
		return result;
	}
	
	@GetMapping(value = "/deleteMoveEngage")
	public Map<String, String> deleteMoveEngage(@RequestParam(required = false,value = "size") Integer size,@RequestParam(required = false,value = "waitInSec") Integer waitInSec,
			@RequestParam(required = false,value = "stop") Boolean stop, 
			@RequestParam(value = "coll") String coll, @RequestParam(required = false,value = "timeCheck") Boolean timeCheck,
			@RequestParam(value="db") String db,@RequestParam(value="days") int days,
			@RequestParam(value="checkColl") String checkColl) {
		logger.info("Inside deleteMoveEngage with ids : {} and {}");
		Map<String, String> result = new HashMap<String, String>();
		try {
			logger.info("Serial no found, startId {}");
			Date todayDate = new Date();
			Date startDate = new Date();
			startDate = DateUtil.setHours(startDate, 7);
			startDate = DateUtil.setMinute(startDate, 0);
			startDate = DateUtil.setSeconds(startDate, 0);

			Date endDate = new Date();
			endDate = DateUtil.setHours(endDate, 20);
			endDate = DateUtil.setMinute(endDate, 0);
			endDate = DateUtil.setSeconds(endDate, 0);

			boolean migrate = false;
			
			if(stop!=null ) {
				StaticMaster.setDeleteCarLoggerV1Stop(stop);
			}
			
			if(size== null){
				size = 500;
			}			
			
			if(size>1000) {
				logger.info("size is greater than max value 1000");
				size = 1000;
			}
			
			logger.info("Today date : {}, start date {} , end date {}", todayDate, startDate, endDate);
			if ((todayDate.before(startDate) || todayDate.after(endDate) || !timeCheck)) {
				migrate = true;
				DBObject projectObj = new BasicDBObject("_id", 1).append("EnquiryID", 1).append("MatrixLeadID", 1);
				DBObject query = new BasicDBObject();
				query.put("EnquiryId", "EnquiryID");	
				query.put("MatrixLeadID", "MatrixLeadID");	
				documentStoreScheduleService.deleteNotBookLeadInCarLoggerV1(coll, size, timeCheck,-days,checkColl,db,projectObj,query);
				result.put("OK", "1");
				result.put("SK_MSG", "collection migrated");
			}
			if(!migrate){
				logger.info("Could not initiate deleteMoveEngage at this time {} , stop {} , waitInSec {}", new Date(), stop, waitInSec);
				logger.info(" coll {} , size {} ",coll, size );
				result.put("OK", "0");
				result.put("SK_MSG", "not initiate deleteMoveEngage, " + new Date());
			}
		} catch (Exception e) {
			result.put("OK", "0");
			result.put("SK_MSG", "Error in deleteMoveEngage : ," + e.getMessage());
			logger.error("Error in deleteMoveEngage , {}", e.getMessage());
		}
		logger.info("Exiting deleteMoveEngage ");
		return result;
	}
	
	@GetMapping(value = "/deleteEndorsementLogs")
	public Map<String, String> deleteEndorsementLogs(@RequestParam(required = false,value = "size") Integer size,@RequestParam(required = false,value = "waitInSec") Integer waitInSec,
			@RequestParam(required = false,value = "stop") Boolean stop, 
			@RequestParam(value = "coll") String coll, @RequestParam(required = false,value = "timeCheck") Boolean timeCheck,
			@RequestParam(value="days") int days,@RequestParam(value="checkColl") String checkColl) {
		logger.info("Inside deleteEndorsementLogs with ids : {} and {}");
		Map<String, String> result = new HashMap<String, String>();
		try {
			logger.info("Serial no found, startId {}");
			Date todayDate = new Date();
			Date startDate = new Date();
			startDate = DateUtil.setHours(startDate, 7);
			startDate = DateUtil.setMinute(startDate, 0);
			startDate = DateUtil.setSeconds(startDate, 0);

			Date endDate = new Date();
			endDate = DateUtil.setHours(endDate, 20);
			endDate = DateUtil.setMinute(endDate, 0);
			endDate = DateUtil.setSeconds(endDate, 0);

			boolean migrate = false;
			
			if(stop!=null ) {
				StaticMaster.setDeleteCarLoggerV1Stop(stop);
			}
			
			if(size== null){
				size = 500;
			}			
			
			if(size>1000) {
				logger.info("size is greater than max value 1000");
				size = 1000;
			}
			
			logger.info("Today date : {}, start date {} , end date {}", todayDate, startDate, endDate);
			if ((todayDate.before(startDate) || todayDate.after(endDate) || !timeCheck)) {
				migrate = true;
				DBObject projectObj = new BasicDBObject("_id", 1).append("BookingID", 1);
				DBObject query = new BasicDBObject();
				query.put("BookingID", "BookingID");	
				documentStoreScheduleService.deleteNotBookLeadInCarLoggerV1(coll, size, timeCheck,-days,checkColl,"EndorsementLogs",projectObj,query);
				result.put("OK", "1");
				result.put("SK_MSG", "collection migrated");
			}
			if(!migrate){
				logger.info("Could not initiate deleteEndorsementLogs at this time {} , stop {} , waitInSec {}", new Date(), stop, waitInSec);
				logger.info(" coll {} , size {} ",coll, size );
				result.put("OK", "0");
				result.put("SK_MSG", "not initiate deleteEndorsementLogs, " + new Date());
			}
		} catch (Exception e) {
			result.put("OK", "0");
			result.put("SK_MSG", "Error in deleteEndorsementLogs : ," + e.getMessage());
			logger.error("Error in deleteEndorsementLogs , {}", e.getMessage());
		}
		logger.info("Exiting deleteEndorsementLogs ");
		return result;
	}

	@ResponseBody
	@GetMapping(value = "/migrateUaeJourneyLeadXLog")
	public Map<String, String> migrateUaeJourneyLeadXLog(@RequestParam("ns") String dbColl,
												 @RequestParam(value = "startId", required=false) String startId,
												 @RequestParam(value = "timeCheck", required = true) boolean timeCheck,
												 @RequestParam(required = false,value = "stop") Boolean stop,
												 @RequestParam(value="source", required=false) String source) {
		logger.info("Inside migrateColl with ids : {} and {}", dbColl);
		Map<String, String> result = new HashMap<String, String>();
		try {
			Date todayDate = new Date();
			/* Date endDate = DateUtil.addDays(todayDate, 1); */
			Date startDate = new Date();
			startDate = DateUtil.setHours(startDate, 7);
			startDate = DateUtil.setMinute(startDate, 0);
			startDate = DateUtil.setSeconds(startDate, 0);

			Date endDate = new Date();
			endDate = DateUtil.setHours(endDate, 20);
			endDate = DateUtil.setMinute(endDate, 0);
			endDate = DateUtil.setSeconds(endDate, 0);

			boolean migrate = false;
			if (todayDate.before(startDate) || todayDate.after(endDate) || !timeCheck) {
				migrate = true;
				logger.info("Collection found, coll {}", dbColl);
				documentStoreScheduleService.migrateUaeJourneyLeadXLog(dbColl, source,timeCheck,startId);
				result.put("OK", "1");
				result.put("SK_MSG", "collection migrated");
			}
			if (!migrate) {
				logger.info("Could not initiate migration at this time : {}", new Date());
				result.put("OK", "0");
				result.put("SK_MSG", "Could not initiate migration at this time , " + new Date());
				return result;
			}
		} catch (Exception e) {
			result.put("OK", "0");
			result.put("SK_MSG", "Error in migrateCollChat : ," + e.getMessage());
			logger.error("Error in migrateCollChat , {}", e.getMessage());
		}
		logger.info("Exiting migrateCollChat ");
		return result;
	}
}
