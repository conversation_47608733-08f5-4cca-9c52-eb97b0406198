/**
 * 
 */
package com.docMigration.controller;

import java.util.*;

import com.docMigration.service.impl.LoggerArchivalServiceImpl;
import com.docMigration.util.NStoS3util;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.docMigration.cleanup.entity.StaticMaster;
import com.docMigration.service.LoggerArchivalService;
import com.docMigration.util.DateUtil;

/**
 * This is a schedular class which will take documents from db and insert in aws
 * S3 bucket.
 * 
 * <AUTHOR>
 *
 */
@RestController
public class LoggerArchivalController {

	private final Logger logger = LoggerFactory.getLogger(LoggerArchivalController.class);
	private static final String REQ_HEADER_AUTHKEY = "92kik4i4jn4oi4n3oi4nn4k4l4lk4lknwlk4l4ljk44444klknn4h4jk5k";
	@Autowired
	LoggerArchivalService loggerArchivalService;
	
	@ResponseBody
	@GetMapping(value = "/moveBookedLeadCar")
	public Map<String, String> moveBookedLeadCar(@RequestParam(required = false,value = "size") Integer size,
			@RequestParam(required = false,value = "waitInSec") Integer waitInSec,
			@RequestParam(required = false,value = "stop") Boolean stop, @RequestParam(value = "coll") String coll,
			@RequestParam(value = "db") String db,@RequestParam(value="days") int days,
			@RequestParam(required = false,value = "timeCheck") Boolean timeCheck) {
		logger.info("Inside moveBookedLeadCar ");
		Map<String, String> result = new HashMap<String, String>();
		try {
			Date todayDate = new Date();
			Date startDate = new Date();
			startDate = DateUtil.setHours(startDate, 7);
			startDate = DateUtil.setMinute(startDate, 0);
			startDate = DateUtil.setSeconds(startDate, 0);

			Date endDate = new Date();
			endDate = DateUtil.setHours(endDate, 20);
			endDate = DateUtil.setMinute(endDate, 0);
			endDate = DateUtil.setSeconds(endDate, 0);

			boolean migrate = false;
			
			if(stop!=null ) {
				StaticMaster.setMoveCarLoggerDataStop(stop);
			}
			
			if(size== null){
				size = 500;
			}			
			
			if(size>1000) {
				logger.info("size is greater than max value 1000");
				size = 1000;
			}
			
			logger.info("Today date : {}, start date {} , end date {}", todayDate, startDate, endDate);
			if ((todayDate.before(startDate) || todayDate.after(endDate) || !timeCheck)
					&& !StaticMaster.isMoveCarLoggerDataStop()) {
				StaticMaster.setLoggerCarMoveJobRunning(true);
				migrate = true;
				loggerArchivalService.moveBookLeads(coll, size, timeCheck,db,-days);
				result.put("OK", "1");
				result.put("SK_MSG", "collection migrated");
				StaticMaster.setLoggerCarDeleteV1JobRunning(false);
			}
			if (!migrate && StaticMaster.isLoggerCarMoveJobRunning()) {
				logger.info("Could not initiate moveBookedLeadCar migration at this time already running: {} , stop {} , waitInSec {}", new Date(), stop, waitInSec);
				logger.info(" coll {} , startId, {} , size {} ",coll, size );
				result.put("OK", "0");
				result.put("SK_MSG", "Could not initiate migration at this time , " + new Date());
			}else if(!migrate){
				logger.info("Could not initiate moveBookedLeadCar at this time {} , stop {} , waitInSec {}", new Date(), stop, waitInSec);
				logger.info(" coll {} , size {} ",coll, size );
				result.put("OK", "0");
				result.put("SK_MSG", "not initiate moveBookedLeadCar, " + new Date());
			}
		} catch (Exception e) {
			StaticMaster.setLoggerCarDeleteJobRunning(false);
			result.put("OK", "0");
			result.put("SK_MSG", "Error in moveBookedLeadCar : ," + e.getMessage());
			logger.error("Error in moveBookedLeadCar , {}", e.getMessage());
		}
		logger.info("Exiting deleteNotBookedLeadInCarV1 ");
		return result;
	}
	
	
	@ResponseBody
	@GetMapping(value = "/moveBookedLeadTw")
	public Map<String, String> moveBookedLeadTw(@RequestParam(required = false,value = "size") Integer size,
			@RequestParam(required = false,value = "waitInSec") Integer waitInSec,
			@RequestParam(required = false,value = "stop") Boolean stop, @RequestParam(value = "coll") String coll,
			@RequestParam(value = "db") String db,@RequestParam(value="days") int days,
			@RequestParam(required = false,value = "timeCheck") Boolean timeCheck) {
		logger.info("Inside moveBookedLeadTw ");
		Map<String, String> result = new HashMap<String, String>();
		try {
			Date todayDate = new Date();
			Date startDate = new Date();
			startDate = DateUtil.setHours(startDate, 7);
			startDate = DateUtil.setMinute(startDate, 0);
			startDate = DateUtil.setSeconds(startDate, 0);

			Date endDate = new Date();
			endDate = DateUtil.setHours(endDate, 20);
			endDate = DateUtil.setMinute(endDate, 0);
			endDate = DateUtil.setSeconds(endDate, 0);

			boolean migrate = false;
			
			if(stop!=null ) {
				StaticMaster.setMoveTLoggerDataStop(stop);
			}
			
			if(size== null){
				size = 500;
			}			
			
			if(size>1000) {
				logger.info("size is greater than max value 1000");
				size = 1000;
			}
			
			logger.info("Today date : {}, start date {} , end date {}", todayDate, startDate, endDate);
			if ((todayDate.before(startDate) || todayDate.after(endDate) || !timeCheck)
					&& !StaticMaster.isMoveTLoggerDataStop() ) {
				StaticMaster.setLoggerTwoMoveJobRunning(true);
				migrate = true;
				loggerArchivalService.moveBookLeads(coll, size, timeCheck,db,-days);
				result.put("OK", "1");
				result.put("SK_MSG", "collection migrated");
				StaticMaster.setLoggerTwoMoveJobRunning(false);
			}
			if (!migrate && StaticMaster.isLoggerTwoMoveJobRunning()) {
				logger.info("Could not initiate moveBookedLeadTw migration at this time already running: {} , stop {} , waitInSec {}", new Date(), stop, waitInSec);
				logger.info(" coll {} , startId, {} , size {} ",coll, size );
				result.put("OK", "0");
				result.put("SK_MSG", "Could not initiate migration at this time , " + new Date());
			}else if(!migrate){
				logger.info("Could not initiate moveBookedLeadTw at this time {} , stop {} , waitInSec {}", new Date(), stop, waitInSec);
				logger.info(" coll {} , size {} ",coll, size );
				result.put("OK", "0");
				result.put("SK_MSG", "not initiate moveBookedLeadTw, " + new Date());
			}
		} catch (Exception e) {
			StaticMaster.setLoggerTwoMoveJobRunning(false);
			result.put("OK", "0");
			result.put("SK_MSG", "Error in moveBookedLeadTw : ," + e.getMessage());
			logger.error("Error in moveBookedLeadTw , {}", e.getMessage());
		}
		logger.info("Exiting deleteNotBookedLeadInCarV1 ");
		return result;
	}
	
	@ResponseBody
	@GetMapping(value = "/moveBookedLeadSme")
	public Map<String, String> moveBookedLeadSme(@RequestParam(required = false,value = "size") Integer size,
			@RequestParam(required = false,value = "waitInSec") Integer waitInSec,
			@RequestParam(required = false,value = "stop") Boolean stop, @RequestParam(value = "coll") String coll,
			@RequestParam(value = "db") String db,@RequestParam(value="days") int days,
			@RequestParam(required = false,value = "timeCheck") Boolean timeCheck) {
		logger.info("Inside moveBookedLeadTw ");
		Map<String, String> result = new HashMap<String, String>();
		try {
			Date todayDate = new Date();
			Date startDate = new Date();
			startDate = DateUtil.setHours(startDate, 7);
			startDate = DateUtil.setMinute(startDate, 0);
			startDate = DateUtil.setSeconds(startDate, 0);

			Date endDate = new Date();
			endDate = DateUtil.setHours(endDate, 20);
			endDate = DateUtil.setMinute(endDate, 0);
			endDate = DateUtil.setSeconds(endDate, 0);

			boolean migrate = false;
			
			if(stop!=null ) {
				StaticMaster.setMoveSmeLoggerDataStop(stop);
			}
			
			if(size== null){
				size = 500;
			}			
			
			if(size>1000) {
				logger.info("size is greater than max value 1000");
				size = 1000;
			}
			
			logger.info("Today date : {}, start date {} , end date {}", todayDate, startDate, endDate);
			if ((todayDate.before(startDate) || todayDate.after(endDate) || !timeCheck)
					&& !StaticMaster.isMoveSmeLoggerDataStop()) {
				StaticMaster.setLoggerSmeMoveJobRunning(true);
				migrate = true;
				loggerArchivalService.moveBookLeads(coll, size, timeCheck,db,-days);
				result.put("OK", "1");
				result.put("SK_MSG", "collection migrated");
				StaticMaster.setLoggerSmeMoveJobRunning(false);
			}
			if (!migrate && StaticMaster.isLoggerSmeMoveJobRunning()) {
				logger.info("Could not initiate moveBookedLeadSme migration at this time already running: {} , stop {} , waitInSec {}", new Date(), stop, waitInSec);
				logger.info(" coll {} , startId, {} , size {} ",coll, size );
				result.put("OK", "0");
				result.put("SK_MSG", "Could not initiate migration at this time , " + new Date());
			}else if(!migrate){
				logger.info("Could not initiate moveBookedLeadSme at this time {} , stop {} , waitInSec {}", new Date(), stop, waitInSec);
				logger.info(" coll {} , size {} ",coll, size );
				result.put("OK", "0");
				result.put("SK_MSG", "not initiate moveBookedLeadSme, " + new Date());
			}
		} catch (Exception e) {
			StaticMaster.setLoggerSmeMoveJobRunning(false);
			result.put("OK", "0");
			result.put("SK_MSG", "Error in moveBookedLeadSme : ," + e.getMessage());
			logger.error("Error in moveBookedLeadSme , {}", e.getMessage());
		}
		logger.info("Exiting moveBookedLeadSme ");
		return result;
	}
	
	@ResponseBody
	@GetMapping(value = "/moveBookedLeadHome")
	public Map<String, String> moveBookedLeadHome(@RequestParam(required = false,value = "size") Integer size,
			@RequestParam(required = false,value = "waitInSec") Integer waitInSec,
			@RequestParam(required = false,value = "stop") Boolean stop, @RequestParam(value = "coll") String coll,
			@RequestParam(value = "db") String db,@RequestParam(value="days") int days,
			@RequestParam(required = false,value = "timeCheck") Boolean timeCheck) {
		logger.info("Inside moveBookedLeadHome ");
		Map<String, String> result = new HashMap<String, String>();
		try {
			Date todayDate = new Date();
			Date startDate = new Date();
			startDate = DateUtil.setHours(startDate, 7);
			startDate = DateUtil.setMinute(startDate, 0);
			startDate = DateUtil.setSeconds(startDate, 0);

			Date endDate = new Date();
			endDate = DateUtil.setHours(endDate, 20);
			endDate = DateUtil.setMinute(endDate, 0);
			endDate = DateUtil.setSeconds(endDate, 0);

			boolean migrate = false;
			
			if(stop!=null ) {
				StaticMaster.setMoveCarLoggerDataStop(stop);
			}
			
			if(size== null){
				size = 500;
			}			
			
			if(size>1000) {
				logger.info("size is greater than max value 1000");
				size = 1000;
			}
			
			logger.info("Today date : {}, start date {} , end date {}", todayDate, startDate, endDate);
			if ((todayDate.before(startDate) || todayDate.after(endDate) || !timeCheck)
					&& !StaticMaster.isMoveCarLoggerDataStop()) {
				migrate = true;
				loggerArchivalService.moveBookLeads(coll, size, timeCheck,db,-days);
				result.put("OK", "1");
				result.put("SK_MSG", "collection migrated");
			}else if(!migrate){
				logger.info("Could not initiate moveBookedLeadHome at this time {} , stop {} , waitInSec {}", new Date(), stop, waitInSec);
				logger.info(" coll {} , size {} ",coll, size );
				result.put("OK", "0");
				result.put("SK_MSG", "not initiate moveBookedLeadHome, " + new Date());
			}
		} catch (Exception e) {
			result.put("OK", "0");
			result.put("SK_MSG", "Error in moveBookedLeadHome : ," + e.getMessage());
			logger.error("Error in moveBookedLeadHome , {}", e.getMessage());
		}
		logger.info("Exiting moveBookedLeadHome ");
		return result;
	}
	
	@ResponseBody
	@GetMapping(value = "/moveBookedLeadSmeNonEb")
	public Map<String, String> moveBookedLeadSmeNonEb(@RequestParam(required = false,value = "size") Integer size,
			@RequestParam(required = false,value = "waitInSec") Integer waitInSec,
			@RequestParam(required = false,value = "stop") Boolean stop, @RequestParam(value = "coll") String coll,
			@RequestParam(value = "db") String db,@RequestParam(value="days") int days,
			@RequestParam(required = false,value = "timeCheck") Boolean timeCheck) {
		logger.info("Inside moveBookedLeadSmeNonEb ");
		Map<String, String> result = new HashMap<String, String>();
		try {
			Date todayDate = new Date();
			Date startDate = new Date();
			startDate = DateUtil.setHours(startDate, 7);
			startDate = DateUtil.setMinute(startDate, 0);
			startDate = DateUtil.setSeconds(startDate, 0);

			Date endDate = new Date();
			endDate = DateUtil.setHours(endDate, 20);
			endDate = DateUtil.setMinute(endDate, 0);
			endDate = DateUtil.setSeconds(endDate, 0);

			boolean migrate = false;
			
			if(stop!=null ) {
				StaticMaster.setMoveCarLoggerDataStop(stop);
			}
			
			if(size== null){
				size = 500;
			}			
			
			if(size>1000) {
				logger.info("size is greater than max value 1000");
				size = 1000;
			}
			
			logger.info("Today date : {}, start date {} , end date {}", todayDate, startDate, endDate);
			if ((todayDate.before(startDate) || todayDate.after(endDate) || !timeCheck)
					&& !StaticMaster.isMoveCarLoggerDataStop() ) {
				migrate = true;
				loggerArchivalService.moveBookLeads(coll, size, timeCheck,db,-days);
				result.put("OK", "1");
				result.put("SK_MSG", "collection migrated");
			}else if(!migrate){
				logger.info("Could not initiate moveBookedLeadSmeNonEb at this time {} , stop {} , waitInSec {}", new Date(), stop, waitInSec);
				logger.info(" coll {} , size {} ",coll, size );
				result.put("OK", "0");
				result.put("SK_MSG", "not initiate moveBookedLeadSmeNonEb, " + new Date());
			}
		} catch (Exception e) {
			result.put("OK", "0");
			result.put("SK_MSG", "Error in moveBookedLeadSmeNonEb : ," + e.getMessage());
			logger.error("Error in moveBookedLeadSmeNonEb , {}", e.getMessage());
		}
		logger.info("Exiting moveBookedLeadSmeNonEbs ");
		return result;
	}
	
	@ResponseBody
	@GetMapping(value = "/moveBookedRenewal")
	public Map<String, String> moveBookedRenewal(@RequestParam(required = false,value = "size") Integer size,
			@RequestParam(required = false,value = "waitInSec") Integer waitInSec,
			@RequestParam(required = false,value = "stop") Boolean stop, @RequestParam(value = "coll") String coll,
			@RequestParam(value = "db") String db,@RequestParam(value="days") int days,
			@RequestParam(required = false,value = "timeCheck") Boolean timeCheck) {
		logger.info("Inside moveBookedRenewal ");
		Map<String, String> result = new HashMap<String, String>();
		try {
			Date todayDate = new Date();
			Date startDate = new Date();
			startDate = DateUtil.setHours(startDate, 7);
			startDate = DateUtil.setMinute(startDate, 0);
			startDate = DateUtil.setSeconds(startDate, 0);

			Date endDate = new Date();
			endDate = DateUtil.setHours(endDate, 20);
			endDate = DateUtil.setMinute(endDate, 0);
			endDate = DateUtil.setSeconds(endDate, 0);

			boolean migrate = false;
			
			if(stop!=null ) {
				StaticMaster.setMoveCarLoggerDataStop(stop);
			}
			
			if(size== null){
				size = 500;
			}			
			
			if(size>1000) {
				logger.info("size is greater than max value 1000");
				size = 1000;
			}
			
			logger.info("Today date : {}, start date {} , end date {}", todayDate, startDate, endDate);
			if ((todayDate.before(startDate) || todayDate.after(endDate) || !timeCheck)
					&& !StaticMaster.isMoveCarLoggerDataStop() && StaticMaster.getCollectionList().contains(coll)) {
				migrate = true;
				loggerArchivalService.moveBookLeads(coll, size, timeCheck,db,-days);
				result.put("OK", "1");
				result.put("SK_MSG", "collection migrated");
			}else if(!migrate){
				logger.info("Could not initiate moveBookedRenewal at this time {} , stop {} , waitInSec {}", new Date(), stop, waitInSec);
				logger.info(" coll {} , size {} ",coll, size );
				result.put("OK", "0");
				result.put("SK_MSG", "not initiate moveBookedRenewal, " + new Date());
			}
		} catch (Exception e) {
			result.put("OK", "0");
			result.put("SK_MSG", "Error in moveBookedRenewal : ," + e.getMessage());
			logger.error("Error in moveBookedRenewal , {}", e.getMessage());
		}
		logger.info("Exiting moveBookedRenewal ");
		return result;
	}
	
	@ResponseBody
	@GetMapping(value = "/moveProfileManager")
	public Map<String, String> moveProfileManager(@RequestParam(required = false,value = "size") Integer size,
			@RequestParam(required = false,value = "waitInSec") Integer waitInSec,
			@RequestParam(required = false,value = "stop") Boolean stop, @RequestParam(value = "coll") String coll,
			@RequestParam(value = "db") String db,@RequestParam(value="days") int days,
			@RequestParam(required = false,value = "timeCheck") Boolean timeCheck) {
		logger.info("Inside moveProfileManager ");
		Map<String, String> result = new HashMap<String, String>();
		try {
			Date todayDate = new Date();
			Date startDate = new Date();
			startDate = DateUtil.setHours(startDate, 7);
			startDate = DateUtil.setMinute(startDate, 0);
			startDate = DateUtil.setSeconds(startDate, 0);

			Date endDate = new Date();
			endDate = DateUtil.setHours(endDate, 20);
			endDate = DateUtil.setMinute(endDate, 0);
			endDate = DateUtil.setSeconds(endDate, 0);

			boolean migrate = false;
			
			if(stop!=null ) {
				StaticMaster.setMoveCarLoggerDataStop(stop);
			}
			
			if(size== null){
				size = 500;
			}			
			
			if(size>1000) {
				logger.info("size is greater than max value 1000");
				size = 1000;
			}
			
			logger.info("Today date : {}, start date {} , end date {}", todayDate, startDate, endDate);
			if ((todayDate.before(startDate) || todayDate.after(endDate) || !timeCheck)
					&& !StaticMaster.isMoveCarLoggerDataStop() && StaticMaster.getCollectionList().contains(coll)) {
				migrate = true;
				loggerArchivalService.moveBookLeads(coll, size, timeCheck,db,-days);
				result.put("OK", "1");
				result.put("SK_MSG", "collection migrated");
			}else if(!migrate){
				logger.info("Could not initiate moveProfileManager at this time {} , stop {} , waitInSec {}", new Date(), stop, waitInSec);
				logger.info(" coll {} , size {} ",coll, size );
				result.put("OK", "0");
				result.put("SK_MSG", "not initiate moveProfileManager, " + new Date());
			}
		} catch (Exception e) {
			result.put("OK", "0");
			result.put("SK_MSG", "Error in moveProfileManager : ," + e.getMessage());
			logger.error("Error in moveProfileManager , {}", e.getMessage());
		}
		logger.info("Exiting moveProfileManager ");
		return result;
	}
	
	@ResponseBody
	@GetMapping(value = "/moveTravelDB")
	public Map<String, String> moveTravelDB(@RequestParam(required = false,value = "size") Integer size,
			@RequestParam(required = false,value = "waitInSec") Integer waitInSec,
			@RequestParam(required = false,value = "stop") Boolean stop, @RequestParam(value = "coll") String coll,
			@RequestParam(value = "db") String db,@RequestParam(value="days") int days,
			@RequestParam(required = false,value = "timeCheck") Boolean timeCheck) {
		logger.info("Inside moveTravelDB ");
		Map<String, String> result = new HashMap<String, String>();
		try {
			Date todayDate = new Date();
			Date startDate = new Date();
			startDate = DateUtil.setHours(startDate, 7);
			startDate = DateUtil.setMinute(startDate, 0);
			startDate = DateUtil.setSeconds(startDate, 0);

			Date endDate = new Date();
			endDate = DateUtil.setHours(endDate, 20);
			endDate = DateUtil.setMinute(endDate, 0);
			endDate = DateUtil.setSeconds(endDate, 0);

			boolean migrate = false;
			
			if(stop!=null ) {
				StaticMaster.setMoveCarLoggerDataStop(stop);
			}
			
			if(size== null){
				size = 500;
			}			
			
			if(size>1000) {
				logger.info("size is greater than max value 1000");
				size = 1000;
			}
			
			logger.info("Today date : {}, start date {} , end date {}", todayDate, startDate, endDate);
			if ((todayDate.before(startDate) || todayDate.after(endDate) || !timeCheck)
					&& !StaticMaster.isMoveCarLoggerDataStop() && StaticMaster.getCollectionList().contains(coll)) {
				migrate = true;
				loggerArchivalService.moveBookLeads(coll, size, timeCheck,db,-days);
				result.put("OK", "1");
				result.put("SK_MSG", "collection migrated");
			}else if(!migrate){
				logger.info("Could not initiate moveTravelDB at this time {} , stop {} , waitInSec {}", new Date(), stop, waitInSec);
				logger.info(" coll {} , size {} ",coll, size );
				result.put("OK", "0");
				result.put("SK_MSG", "not initiate moveTravelDB, " + new Date());
			}
		} catch (Exception e) {
			result.put("OK", "0");
			result.put("SK_MSG", "Error in moveTravelDB : ," + e.getMessage());
			logger.error("Error in moveTravelDB , {}", e.getMessage());
		}
		logger.info("Exiting moveTravelDB ");
		return result;
	}
	
	@ResponseBody
	@GetMapping(value = "/moveE2eLogs")
	public Map<String, String> moveE2eLogs(@RequestParam(required = false,value = "size") Integer size,
			@RequestParam(required = false,value = "waitInSec") Integer waitInSec,
			@RequestParam(required = false,value = "stop") Boolean stop, @RequestParam(value = "coll") String coll,
			@RequestParam(value = "db") String db,@RequestParam(value="days") int days,
			@RequestParam(required = false,value = "timeCheck") Boolean timeCheck) {
		logger.info("Inside moveE2eLogs ");
		Map<String, String> result = new HashMap<String, String>();
		try {
			Date todayDate = new Date();
			Date startDate = new Date();
			startDate = DateUtil.setHours(startDate, 7);
			startDate = DateUtil.setMinute(startDate, 0);
			startDate = DateUtil.setSeconds(startDate, 0);

			Date endDate = new Date();
			endDate = DateUtil.setHours(endDate, 20);
			endDate = DateUtil.setMinute(endDate, 0);
			endDate = DateUtil.setSeconds(endDate, 0);

			boolean migrate = false;
			
			if(stop!=null ) {
				StaticMaster.setMoveCarLoggerDataStop(stop);
			}
			
			if(size== null){
				size = 500;
			}			
			
			if(size>1000) {
				logger.info("size is greater than max value 1000");
				size = 1000;
			}
			
			logger.info("Today date : {}, start date {} , end date {}", todayDate, startDate, endDate);
			if ((todayDate.before(startDate) || todayDate.after(endDate) || !timeCheck)
					&& !StaticMaster.isMoveCarLoggerDataStop() ) {
				migrate = true;
				loggerArchivalService.moveBookLeads(coll, size, timeCheck,db,-days);
				result.put("OK", "1");
				result.put("SK_MSG", "collection migrated");
			}else if(!migrate){
				logger.info("Could not initiate moveE2eLogs at this time {} , stop {} , waitInSec {}", new Date(), stop, waitInSec);
				logger.info(" coll {} , size {} ",coll, size );
				result.put("OK", "0");
				result.put("SK_MSG", "not initiate moveE2eLogs, " + new Date());
			}
		} catch (Exception e) {
			result.put("OK", "0");
			result.put("SK_MSG", "Error in moveE2eLogs : ," + e.getMessage());
			logger.error("Error in moveE2eLogs , {}", e.getMessage());
		}
		logger.info("Exiting moveE2eLogs ");
		return result;
	}
	
	@ResponseBody
	@GetMapping(value = "/movePbTermEvent")
	public Map<String, String> movePbTermEvent(@RequestParam(required = false,value = "size") Integer size,
			@RequestParam(required = false,value = "waitInSec") Integer waitInSec,
			@RequestParam(required = false,value = "stop") Boolean stop, @RequestParam(value = "coll") String coll,
			@RequestParam(value = "db") String db,@RequestParam(value="days") int days,
			@RequestParam(required = false,value = "timeCheck") Boolean timeCheck) {
		logger.info("Inside movePbTermEvent ");
		Map<String, String> result = new HashMap<String, String>();
		try {
			Date todayDate = new Date();
			Date startDate = new Date();
			startDate = DateUtil.setHours(startDate, 7);
			startDate = DateUtil.setMinute(startDate, 0);
			startDate = DateUtil.setSeconds(startDate, 0);

			Date endDate = new Date();
			endDate = DateUtil.setHours(endDate, 20);
			endDate = DateUtil.setMinute(endDate, 0);
			endDate = DateUtil.setSeconds(endDate, 0);

			boolean migrate = false;
			
			if(stop!=null ) {
				StaticMaster.setMoveCarLoggerDataStop(stop);
			}
			
			if(size== null){
				size = 500;
			}			
			
			if(size>1000) {
				logger.info("size is greater than max value 1000");
				size = 1000;
			}
			
			logger.info("Today date : {}, start date {} , end date {}", todayDate, startDate, endDate);
			if ((todayDate.before(startDate) || todayDate.after(endDate) || !timeCheck)
					&& !StaticMaster.isMoveCarLoggerDataStop() ) {
				migrate = true;
				loggerArchivalService.moveBookLeads(coll, size, timeCheck,db,-days);
				result.put("OK", "1");
				result.put("SK_MSG", "collection migrated");
			}else if(!migrate){
				logger.info("Could not initiate movePbTermEvent at this time {} , stop {} , waitInSec {}", new Date(), stop, waitInSec);
				logger.info(" coll {} , size {} ",coll, size );
				result.put("OK", "0");
				result.put("SK_MSG", "not initiate movePbTermEvent, " + new Date());
			}
		} catch (Exception e) {
			result.put("OK", "0");
			result.put("SK_MSG", "Error in movePbTermEvent : ," + e.getMessage());
			logger.error("Error in movePbTermEvent , {}", e.getMessage());
		}
		logger.info("Exiting movePbTermEvent ");
		return result;
	}
	
	/**
	 * 
	 * @param size
	 * @param waitInSec
	 * @param stop
	 * @param coll
	 * @param db
	 * @param days
	 * @param timeCheck
	 * @return
	 */
	@ResponseBody
	@GetMapping(value = "/moveEndorsementLogs")
	public Map<String, String> moveEndorsementLogs(@RequestParam(required = false,value = "size") Integer size,
			@RequestParam(required = false,value = "waitInSec") Integer waitInSec,
			@RequestParam(required = false,value = "stop") Boolean stop, @RequestParam(value = "coll") String coll,
			@RequestParam(value = "db") String db,@RequestParam(value="days") int days,
			@RequestParam(required = false,value = "timeCheck") Boolean timeCheck) {
		logger.info("Inside moveEndorsementLogs ");
		Map<String, String> result = new HashMap<String, String>();
		try {
			Date todayDate = new Date();
			Date startDate = new Date();
			startDate = DateUtil.setHours(startDate, 7);
			startDate = DateUtil.setMinute(startDate, 0);
			startDate = DateUtil.setSeconds(startDate, 0);

			Date endDate = new Date();
			endDate = DateUtil.setHours(endDate, 20);
			endDate = DateUtil.setMinute(endDate, 0);
			endDate = DateUtil.setSeconds(endDate, 0);

			boolean migrate = false;
			
			if(stop!=null ) {
				StaticMaster.setMoveCarLoggerDataStop(stop);
			}
			
			if(size== null){
				size = 500;
			}			
			
			if(size>1000) {
				logger.info("size is greater than max value 1000");
				size = 1000;
			}
			
			logger.info("Today date : {}, start date {} , end date {}", todayDate, startDate, endDate);
			if ((todayDate.before(startDate) || todayDate.after(endDate) || !timeCheck)
					&& !StaticMaster.isMoveCarLoggerDataStop()) {
				migrate = true;
				loggerArchivalService.moveBookLeads(coll, size, timeCheck,db,-days);
				result.put("OK", "1");
				result.put("SK_MSG", "collection migrated");
			}else if(!migrate){
				logger.info("Could not initiate moveEndorsementLogs at this time {} , stop {} , waitInSec {}", new Date(), stop, waitInSec);
				logger.info(" coll {} , size {} ",coll, size );
				result.put("OK", "0");
				result.put("SK_MSG", "not initiate moveEndorsementLogs, " + new Date());
			}
		} catch (Exception e) {
			result.put("OK", "0");
			result.put("SK_MSG", "Error in moveEndorsementLogs : ," + e.getMessage());
			logger.error("Error in moveEndorsementLogs , {}", e.getMessage());
		}
		logger.info("Exiting moveEndorsementLogs ");
		return result;
	}
	
	@ResponseBody
	@GetMapping(value = "/moveClaimsLogs")
	public Map<String, String> moveClaimsLogs(@RequestParam(required = false,value = "size") Integer size,
			@RequestParam(required = false,value = "waitInSec") Integer waitInSec,
			@RequestParam(required = false,value = "stop") Boolean stop, @RequestParam(value = "coll") String coll,
			@RequestParam(value = "db") String db,@RequestParam(value="days") int days,
			@RequestParam(required = false,value = "timeCheck") Boolean timeCheck) {
		logger.info("Inside moveClaimsLogs ");
		Map<String, String> result = new HashMap<String, String>();
		try {
			Date todayDate = new Date();
			Date startDate = new Date();
			startDate = DateUtil.setHours(startDate, 7);
			startDate = DateUtil.setMinute(startDate, 0);
			startDate = DateUtil.setSeconds(startDate, 0);

			Date endDate = new Date();
			endDate = DateUtil.setHours(endDate, 20);
			endDate = DateUtil.setMinute(endDate, 0);
			endDate = DateUtil.setSeconds(endDate, 0);

			boolean migrate = false;
			
			if(stop!=null ) {
				StaticMaster.setMoveCarLoggerDataStop(stop);
			}
			
			if(size== null){
				size = 500;
			}			
			
			if(size>1000) {
				logger.info("size is greater than max value 1000");
				size = 1000;
			}
			
			logger.info("Today date : {}, start date {} , end date {}", todayDate, startDate, endDate);
			if ((todayDate.before(startDate) || todayDate.after(endDate) || !timeCheck)
					&& !StaticMaster.isMoveCarLoggerDataStop()) {
				migrate = true;
				loggerArchivalService.moveBookLeads(coll, size, timeCheck,db,-days);
				result.put("OK", "1");
				result.put("SK_MSG", "collection migrated");
			}else if(!migrate){
				logger.info("Could not initiate moveClaimsLogs at this time {} , stop {} , waitInSec {}", new Date(), stop, waitInSec);
				logger.info(" coll {} , size {} ",coll, size );
				result.put("OK", "0");
				result.put("SK_MSG", "not initiate moveClaimsLogs, " + new Date());
			}
		} catch (Exception e) {
			result.put("OK", "0");
			result.put("SK_MSG", "Error in moveClaimsLogs : ," + e.getMessage());
			logger.error("Error in moveClaimsLogs , {}", e.getMessage());
		}
		logger.info("Exiting moveClaimsLogs ");
		return result;
	}
	
	@ResponseBody
	@GetMapping(value = "/moveKycLogs")
	public Map<String, String> moveKycLogs(@RequestParam(required = false,value = "size") Integer size,
			@RequestParam(required = false,value = "waitInSec") Integer waitInSec,
			@RequestParam(required = false,value = "stop") Boolean stop, @RequestParam(value = "coll") String coll,
			@RequestParam(value = "db") String db,@RequestParam(value="days") int days,
			@RequestParam(required = false,value = "timeCheck") Boolean timeCheck) {
		logger.info("Inside moveKycLogs ");
		Map<String, String> result = new HashMap<String, String>();
		try {
			Date todayDate = new Date();
			Date startDate = new Date();
			startDate = DateUtil.setHours(startDate, 7);
			startDate = DateUtil.setMinute(startDate, 0);
			startDate = DateUtil.setSeconds(startDate, 0);

			Date endDate = new Date();
			endDate = DateUtil.setHours(endDate, 20);
			endDate = DateUtil.setMinute(endDate, 0);
			endDate = DateUtil.setSeconds(endDate, 0);

			boolean migrate = false;
			
			if(stop!=null ) {
				StaticMaster.setMoveCarLoggerDataStop(stop);
			}
			
			if(size== null){
				size = 500;
			}			
			
			if(size>1000) {
				logger.info("size is greater than max value 1000");
				size = 1000;
			}
			
			logger.info("Today date : {}, start date {} , end date {}", todayDate, startDate, endDate);
			if ((todayDate.before(startDate) || todayDate.after(endDate) || !timeCheck)
					&& !StaticMaster.isMoveCarLoggerDataStop()) {
				migrate = true;
				loggerArchivalService.moveBookLeads(coll, size, timeCheck,db,-days);
				result.put("OK", "1");
				result.put("SK_MSG", "collection migrated");
			}else if(!migrate){
				logger.info("Could not initiate moveKycLogs at this time {} , stop {} , waitInSec {}", new Date(), stop, waitInSec);
				logger.info(" coll {} , size {} ",coll, size );
				result.put("OK", "0");
				result.put("SK_MSG", "not initiate moveKycLogs, " + new Date());
			}
		} catch (Exception e) {
			result.put("OK", "0");
			result.put("SK_MSG", "Error in moveKycLogs : ," + e.getMessage());
			logger.error("Error in moveKycLogs , {}", e.getMessage());
		}
		logger.info("Exiting moveKycLogs ");
		return result;
	}
	
	
	@ResponseBody
	@GetMapping(value = "/twMarkBookedLeads")
	public Map<String, String> twMarkBookedLeads(@RequestParam(required = false, value = "startId") String startId,
			@RequestParam(required = false, value = "endId") String endId,
			@RequestParam(required = false,value = "timeCheck") Boolean timeCheck,
			@RequestParam(value = "coll") String coll,
			@RequestParam String db,
			@RequestParam(required = false,defaultValue = "0") int days,
			@RequestParam(required = false,defaultValue = "500") int size,
			@RequestParam(required = false,defaultValue = "false") Boolean stop
		
			) {
		logger.info("Inside twMarkBookedLeads with ids : {} and {}", startId, endId);
		Map<String, String> result = new HashMap<String, String>();
		try {

			logger.info("Serial no found, startId {} , endId {}", startId, endId);
			Date todayDate = new Date();
			Date startDate = new Date();
			startDate = DateUtil.setHours(startDate, 7);
			startDate = DateUtil.setMinute(startDate, 0);
			startDate = DateUtil.setSeconds(startDate, 0);

			Date endDate = new Date();
			endDate = DateUtil.setHours(endDate, 20);
			endDate = DateUtil.setMinute(endDate, 0);
			endDate = DateUtil.setSeconds(endDate, 0);
			
			
			StaticMaster.setIsTwMarkBookedLeadsStop(stop);
					
			if(size>1000) {
				logger.info("size is greater than max value 1000");
				size = 1000;
			}
			

			boolean migrate = false;
			if (todayDate.before(startDate) || todayDate.after(endDate) || !timeCheck) {
				migrate = true;
				loggerArchivalService.twMarkBookedLeads(startId,endId,coll,timeCheck, db, days, size);
			result.put("OK", "1");
			result.put("SK_MSG", "collection migrated");
			}
			if (!migrate) {
				logger.info("Could not initiate twMarkBookedLeads at this time : {}", new Date());
				result.put("OK", "0");
				result.put("SK_MSG", "Could not initiate twMarkBookedLeads at this time , " + new Date());
				return result;
			}
		} catch (Exception e) {
			result.put("OK", "0");
			result.put("SK_MSG", "Error in twMarkBookedLeads : ," + e.getMessage());
			logger.error("Error in twMarkBookedLeads , {}", e.getMessage());
		}
		logger.info("Exiting twMarkBookedLeads ");
		return result;
	}

	@ResponseBody
	@GetMapping(value = "/cleanReqResLogs")
	public Map<String, String> cleanReqResLogs(@RequestParam(required = false,value = "size") Integer size,
			@RequestParam(required = false,value = "waitInSec") Integer waitInSec,
			@RequestParam(required = false,value = "stop") Boolean stop, @RequestParam(value = "coll") String coll,
			@RequestParam(value = "db") String db,
			@RequestParam(required = false, value = "timeCheck") Boolean timeCheck) {
		logger.info("Inside cleanReqResLogs ");
		Map<String, String> result = new HashMap<String, String>();
		try {
			Date todayDate = new Date();
			Date startDate = new Date();
			startDate = DateUtil.setHours(startDate, 7);
			startDate = DateUtil.setMinute(startDate, 0);
			startDate = DateUtil.setSeconds(startDate, 0);
	
			Date endDate = new Date();
			endDate = DateUtil.setHours(endDate, 20);
			endDate = DateUtil.setMinute(endDate, 0);
			endDate = DateUtil.setSeconds(endDate, 0);
	
			boolean migrate = false;
	
			if (stop != null) {
				StaticMaster.setCleanReqResLogStop(stop);
			}
	
			if (size == null) {
				size = 500;
			}
	
			logger.info("Today date : {}, start date {} , end date {}", todayDate, startDate, endDate);
			if ((todayDate.before(startDate) || todayDate.after(endDate) || !timeCheck)
					&& !StaticMaster.isCleanReqResLogStop() && StaticMaster.getCollectionList().contains(coll)) {
				migrate = true;
				loggerArchivalService.cleanReqResLogs(coll, size, timeCheck, db);
				result.put("OK", "1");
				result.put("SK_MSG", "collection cleaned");
			} else if (!migrate) {
				logger.info("Could not initiate cleanReqResLogs at this time {} , stop {} , waitInSec {}", new Date(), stop,
						waitInSec);
				logger.info(" coll {} , size {} ", coll, size);
				result.put("OK", "0");
				result.put("SK_MSG", "not initiate cleanReqResLogs, " + new Date());
			}
		} catch (Exception e) {
			result.put("OK", "0");
			result.put("SK_MSG", "Error in cleanReqResLogs : ," + e.getMessage());
			logger.error("Error in cleanReqResLogs , {}", e.getMessage());
		}
		logger.info("Exiting cleanReqResLogs ");
		
		return result;
	}
	@GetMapping(value = "/moveTravelDbOldToTravelDbNew")
	public Map<String, String> moveTravelDbOldToTravelDbNew(@RequestParam(required = false,value = "size") Integer size,
															@RequestParam(required = false,value = "waitInSec") Integer waitInSec,
															@RequestParam(required = false,value = "stop") Boolean stop, @RequestParam(value = "coll") String coll,
															@RequestParam(value = "db") String db,@RequestParam(value="days") int days,
															@RequestParam(required = false,value = "timeCheck") Boolean timeCheck) {
		logger.info("Inside moveTravelDbOldToTravelDbNew ");
		Map<String, String> result = new HashMap<String, String>();
		try {
			Date todayDate = new Date();
			Date startDate = new Date();
			startDate = DateUtil.setHours(startDate, 7);
			startDate = DateUtil.setMinute(startDate, 0);
			startDate = DateUtil.setSeconds(startDate, 0);

			Date endDate = new Date();
			endDate = DateUtil.setHours(endDate, 20);
			endDate = DateUtil.setMinute(endDate, 0);
			endDate = DateUtil.setSeconds(endDate, 0);

			boolean migrate = false;

			if(stop!=null ) {
				StaticMaster.setMoveTravelLoggerDataStop(stop);
			}
			if(waitInSec!=null) {
				StaticMaster.setWaitFormoveTravelLoggerData(waitInSec);
			}
			if(size== null){
				size = 500;
			}

			if(size>1000) {
				logger.info("size is greater than max value 1000");
				size = 1000;
			}

			logger.info("Today date : {}, start date {} , end date {}", todayDate, startDate, endDate);
			if ((todayDate.before(startDate) || todayDate.after(endDate) || !timeCheck)
					&& !StaticMaster.isMoveTravelLoggerDataStop()) {
				migrate = true;
				loggerArchivalService.moveTravelApiLogs(coll, size, timeCheck,db,-days);
				result.put("OK", "1");
				result.put("SK_MSG", "collection migrated");
			}else if(!migrate){
				logger.info("Could not initiate moveTravelDbOldToTravelDbNew at this time {} , stop {} , waitInSec {}", new Date(), stop, waitInSec);
				logger.info(" coll {} , size {} ",coll, size );
				result.put("OK", "0");
				result.put("SK_MSG", "not initiate moveTravelDbOldToTravelDbNew, " + new Date());
			}
		} catch (Exception e) {
			result.put("OK", "0");
			result.put("SK_MSG", "Error in moveTravelDbOldToTravelDbNew : ," + e.getMessage());
			logger.error("Error in moveTravelDbOldToTravelDbNew , {}", e.getMessage());
		}
		logger.info("Exiting moveTravelDB ");
		return result;
	}

	@GetMapping(value = "/moveCommDbOldToCommDbNew")
	public Map<String, String> moveCommDbOldToCommDbNew(@RequestParam(required = false,value = "size") Integer size,
															@RequestParam(required = false,value = "waitInSec") Integer waitInSec,
															@RequestParam(required = false,value = "stop") Boolean stop, @RequestParam(value = "coll") String coll,
															@RequestParam(value = "db") String db,@RequestParam(value="days") int days,
															@RequestParam(required = false,value = "timeCheck") Boolean timeCheck) {
		logger.info("Inside moveCommDbOldToCommDbNew ");
		Map<String, String> result = new HashMap<String, String>();
		try {
			Date todayDate = new Date();
			Date startDate = new Date();
			startDate = DateUtil.setHours(startDate, 7);
			startDate = DateUtil.setMinute(startDate, 0);
			startDate = DateUtil.setSeconds(startDate, 0);

			Date endDate = new Date();
			endDate = DateUtil.setHours(endDate, 20);
			endDate = DateUtil.setMinute(endDate, 0);
			endDate = DateUtil.setSeconds(endDate, 0);

			boolean migrate = false;

			if(stop!=null ) {
				StaticMaster.setMoveCommV2FileLogsLoggerDataStop(stop);
			}
			if(waitInSec!=null) {
				StaticMaster.setWaitFormoveTravelLoggerData(waitInSec);
			}
			if(size== null){
				size = 500;
			}

			if(size>1000) {
				logger.info("size is greater than max value 1000");
				size = 1000;
			}

			logger.info("Today date : {}, start date {} , end date {}", todayDate, startDate, endDate);
			if ((todayDate.before(startDate) || todayDate.after(endDate) || !timeCheck)
					&& !StaticMaster.isMoveCommV2FileLogsLoggerDataStop()) {
				migrate = true;
				loggerArchivalService.removeCommV2FilesV2Logs(coll, size, timeCheck,db,-days);
				result.put("OK", "1");
				result.put("SK_MSG", "collection migrated");
			}else if(!migrate){
				logger.info("Could not initiate moveCommDbOldToCommDbNew at this time {} , stop {} , waitInSec {}", new Date(), stop, waitInSec);
				logger.info(" coll {} , size {} ",coll, size );
				result.put("OK", "0");
				result.put("SK_MSG", "not initiate moveCommDbOldToCommDbNew, " + new Date());
			}
		} catch (Exception e) {
			result.put("OK", "0");
			result.put("SK_MSG", "Error in moveCommDbOldToCommDbNew : ," + e.getMessage());
			logger.error("Error in moveCommDbOldToCommDbNew , {}", e.getMessage());
		}
		logger.info("Exiting moveCommDbOldToCommDbNew ");
		return result;
	}
	@GetMapping(value = "/movePBCromaV2ToS3")
	public Map<String, String> movePBCromaV2ToS3(@RequestParam(required = false,value = "size") Integer size,
														@RequestParam(required = false,value = "waitInSec") Integer waitInSec,
														@RequestParam(required = false,value = "stop") Boolean stop, @RequestParam(value = "coll") String coll,
														@RequestParam(value = "db") String db,@RequestParam(value="days") int days,
														@RequestParam(required = false,value = "timeCheck") Boolean timeCheck) {
		logger.info("Inside movePBCromaV2ToS3 ");
		Map<String, String> result = new HashMap<String, String>();
		try {
			Date todayDate = new Date();
			Date startDate = new Date();
			startDate = DateUtil.setHours(startDate, 7);
			startDate = DateUtil.setMinute(startDate, 0);
			startDate = DateUtil.setSeconds(startDate, 0);

			Date endDate = new Date();
			endDate = DateUtil.setHours(endDate, 20);
			endDate = DateUtil.setMinute(endDate, 0);
			endDate = DateUtil.setSeconds(endDate, 0);

			boolean migrate = false;

			if(stop!=null ) {
				StaticMaster.setMoveCommV2FileLogsLoggerDataStop(stop);
			}
			if(waitInSec!=null) {
				StaticMaster.setWaitFormoveTravelLoggerData(waitInSec);
			}
			if(size== null){
				size = 500;
			}

			if(size>1000) {
				logger.info("size is greater than max value 1000");
				size = 1000;
			}

			logger.info("Today date : {}, start date {} , end date {}", todayDate, startDate, endDate);
			if ((todayDate.before(startDate) || todayDate.after(endDate) || !timeCheck)
					&& !StaticMaster.isMoveCommV2FileLogsLoggerDataStop()) {
				migrate = true;
				loggerArchivalService.movePBCromaV2ToS3(coll, size, timeCheck,db,-days);
				result.put("OK", "1");
				result.put("SK_MSG", "collection migrated");
			}else if(!migrate){
				logger.info("Could not initiate movePBCromaV2ToS3 at this time {} , stop {} , waitInSec {}", new Date(), stop, waitInSec);
				logger.info(" coll {} , size {} ",coll, size );
				result.put("OK", "0");
				result.put("SK_MSG", "not initiate movePBCromaV2ToS3, " + new Date());
			}
		} catch (Exception e) {
			result.put("OK", "0");
			result.put("SK_MSG", "Error in movePBCromaV2ToS3 : ," + e.getMessage());
			logger.error("Error in movePBCromaV2ToS3 , {}", e.getMessage());
		}
		logger.info("Exiting movePBCromaV2ToS3 ");
		return result;
	}

	@GetMapping(value = "/getS3Log")
	public Map<String, Object> getCommV2FileLogsFromS3(@RequestParam(value = "ns") String dbColl,
													   @RequestParam(value="requestId", required = false) String requestId,
													   @RequestParam(value="enquiryId", required = false) String enquiryId,
													   @RequestParam(value="policyNo", required = false) String policyNo,
													   @RequestParam(value="vehicleDetailId", required = false) String vehicleDetailId,
													   @RequestParam(value="product", required = false) String product) {
		logger.info("Inside getCommV2FileLogsFromS3 ");
		Map<String, Object> result = new HashMap<>();
		List<Map<String, Object>> resp = null;
		String[] ns = {"CarLoggerDB.ReqResLog", "communicationDB_dev.CommV2FileLogs","e2eLog.LogDataRenewal"};
		if(!Arrays.asList(ns).contains(dbColl)){
			result.put("error", "ns is not whitelisted to proceed further...");
			return result;
		}
		String[] nsArray = dbColl.split("\\.");
		String db = nsArray[0];
		String coll = nsArray[1];;
		try {
			if(product.equals("car")){
				String folderPath = NStoS3util.MAP_NS_TO_S3_PATH.get(dbColl);
				String fileName = enquiryId + ".json.gz";
				resp = loggerArchivalService.getLogFromS3(folderPath, fileName);
			}
			else if(product.equals("life")){
				String folderPath = NStoS3util.MAP_NS_TO_S3_PATH.get(dbColl);
				String fileName = policyNo + ".json.gz";
				resp = loggerArchivalService.getLogFromS3(folderPath, fileName);
			}
			result.put("Data", resp);
			//result.put("ok", "1");
			//result.put("SK_MSG", "success");
		} catch (Exception e) {
			result.put("OK", "0");
			result.put("SK_MSG", "Error in getCommV2FileLogsFromS3 : ," + e.getMessage());
			logger.error("Error in getCommV2FileLogsFromS3 , {}", e.getMessage());
		}
		logger.info("Exiting getCommV2FileLogsFromS3 ");
		return result;
	}


}
