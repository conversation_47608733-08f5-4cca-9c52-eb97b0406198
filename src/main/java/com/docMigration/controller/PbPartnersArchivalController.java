package com.docMigration.controller;

import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.docMigration.cleanup.entity.StaticMaster;
import com.docMigration.service.impl.PbPartnersArchivalServiceImpl;
import com.docMigration.util.DateUtil;

@RestController
public class PbPartnersArchivalController {
	private final Logger logger = LoggerFactory.getLogger(PbPartnersArchivalController.class);
	
	@Autowired
	PbPartnersArchivalServiceImpl pbPartnersArchivalServiceImpl;
	

	
	/**
	 * 
	 * @param size
	 * @param waitInSec
	 * @param stop
	 * @param coll
	 * @param db
	 * @param days
	 * @param timeCheck
	 * @return
	 */
	@ResponseBody
	@GetMapping(value = "/moveBookedLeadCarPbpartners")
	public Map<String, String> moveBookedLeadCarPbpartners(@RequestParam(required = false,value = "size") Integer size,
			@RequestParam(required = false,value = "waitInSec") Integer waitInSec,
			@RequestParam(required = false,value = "stop") Boolean stop, @RequestParam(value = "coll") String coll,
			@RequestParam(value = "db") String db,@RequestParam(value="days") int days,
			@RequestParam(required = false,value = "timeCheck") Boolean timeCheck) {
		logger.info("Inside moveBookedLeadCarPbpartners ");
		Map<String, String> result = new HashMap<String, String>();
		try {
			Date todayDate = new Date();
			Date startDate = new Date();
			startDate = DateUtil.setHours(startDate, 7);
			startDate = DateUtil.setMinute(startDate, 0);
			startDate = DateUtil.setSeconds(startDate, 0);

			Date endDate = new Date();
			endDate = DateUtil.setHours(endDate, 20);
			endDate = DateUtil.setMinute(endDate, 0);
			endDate = DateUtil.setSeconds(endDate, 0);

			boolean migrate = false;
			
			if(stop!=null ) {
				StaticMaster.setMovePbpartnersLoggerDataStop(stop);
			}
			
			if(size== null){
				size = 500;
			}			
			
			if(size>1000) {
				logger.info("size is greater than max value 1000");
				size = 1000;
			}
			
			logger.info("Today date : {}, start date {} , end date {}", todayDate, startDate, endDate);
			if ((todayDate.before(startDate) || todayDate.after(endDate) || !timeCheck)
					&& !StaticMaster.isMovePbpartnersLoggerDataStop()) {
				migrate = true;
				pbPartnersArchivalServiceImpl.moveBookLeads(coll, size, timeCheck,db,-days);
				result.put("OK", "1");
				result.put("SK_MSG", "collection migrated");
			}else if(!migrate){
				logger.info("Could not initiate moveBookedRenewal at this time {} , stop {} , waitInSec {}", new Date(), stop, waitInSec);
				logger.info(" coll {} , size {} ",coll, size );
				result.put("OK", "0");
				result.put("SK_MSG", "not initiate moveBookedRenewal, " + new Date());
			}
		} catch (Exception e) {
			result.put("OK", "0");
			result.put("SK_MSG", "Error in moveBookedRenewal : ," + e.getMessage());
			logger.error("Error in moveBookedRenewal , {}", e.getMessage());
		}
		logger.info("Exiting moveBookedRenewal ");
		return result;
	}
	
	/**
	 * 
	 * @param startId
	 * @param size
	 * @param waitInSec
	 * @param stop
	 * @param coll
	 * @param timeCheck
	 * @return
	 */
	@ResponseBody
	@GetMapping(value = "/deleteNotBookedPbPartnersTwLead")
	public Map<String, String> deleteNotBookedLeadInTwDb(@RequestParam(required = false, value = "startId") String startId,
			@RequestParam(required = false,value = "size") Integer size,@RequestParam(required = false,value = "waitInSec") Integer waitInSec,
			@RequestParam(required = false,value = "stop") Boolean stop,@RequestParam(value="db") String db,@RequestParam(value="days") int days,
			@RequestParam(value = "coll") String coll, @RequestParam(required = false,value = "timeCheck") Boolean timeCheck) {
		logger.info("Inside deleteNotBookedPbPartnersTwLead with ids : {} and {}", startId);
		Map<String, String> result = new HashMap<String, String>();
		try {
			logger.info("Serial no found, startId {}", startId);
			Date todayDate = new Date();
			Date startDate = new Date();
			startDate = DateUtil.setHours(startDate, 7);
			startDate = DateUtil.setMinute(startDate, 0);
			startDate = DateUtil.setSeconds(startDate, 0);

			Date endDate = new Date();
			endDate = DateUtil.setHours(endDate, 20);
			endDate = DateUtil.setMinute(endDate, 0);
			endDate = DateUtil.setSeconds(endDate, 0);

			boolean migrate = false;
			
			if(stop!=null ) {
				StaticMaster.setDeletePbPartnersTwLoggerStop(stop);
			}
			
			if(waitInSec!=null) {
				StaticMaster.setWaitForNextDeleteBatchInTw(waitInSec);
			}
			
			if(size== null){
				size = 500;
			}			
			
			if(size>1000) {
				logger.info("size is greater than max value 1000");
				size = 1000;
			}
			
			logger.info("Today date : {}, start date {} , end date {}", todayDate, startDate, endDate);
			if ((todayDate.before(startDate) || todayDate.after(endDate) || !timeCheck)) {
				migrate = true;
				pbPartnersArchivalServiceImpl.deleteNotBookLeadInTwLogger(startId, coll, size,timeCheck,db,days);
				result.put("OK", "1");
				result.put("SK_MSG", "collection migrated");
			}
			if(!migrate){
				logger.info("Could not initiate deleteNotBookedPbPartnersTwLead migration at this time {} , stop {} , waitInSec {}", new Date(), stop, waitInSec);
				logger.info(" coll {} , startId, {} , size {} ",coll, startId, size );
				result.put("OK", "0");
				result.put("SK_MSG", "not initiate migration, " + new Date());
			}
		} catch (Exception e) {
			result.put("OK", "0");
			result.put("SK_MSG", "Error in deleteNotBookedPbPartnersTwLead : ," + e.getMessage());
			logger.error("Error in deleteNotBookedPbPartnersTwLead , {}", e.getMessage());
		}
		logger.info("Exiting cleanUpTwoWheeler ");
		return result;
	}
	/**
	 * 
	 * @param startId
	 * @param size
	 * @param waitInSec
	 * @param stop
	 * @param coll
	 * @param timeCheck
	 * @return
	 */
	@ResponseBody
	@GetMapping(value = "/deleteNotBookedPbPartnersLeadCar")
	public Map<String, String> deleteNotBookedLeadInCar(@RequestParam(required = false, value = "startId") String startId,
			@RequestParam(required = false,value = "size") Integer size,@RequestParam(required = false,value = "waitInSec") Integer waitInSec,
			@RequestParam(required = false,value = "stop") Boolean stop,@RequestParam(value="db") String db,@RequestParam(value="days") int days,
			@RequestParam(value = "coll") String coll, @RequestParam(required = false,value = "timeCheck") Boolean timeCheck) {
		logger.info("Inside deleteNotBookedLeadInCar with ids : {} and {}", startId);
		Map<String, String> result = new HashMap<String, String>();
		try {
			logger.info("Serial no found, startId {}", startId);
			Date todayDate = new Date();
			Date startDate = new Date();
			startDate = DateUtil.setHours(startDate, 7);
			startDate = DateUtil.setMinute(startDate, 0);
			startDate = DateUtil.setSeconds(startDate, 0);

			Date endDate = new Date();
			endDate = DateUtil.setHours(endDate, 20);
			endDate = DateUtil.setMinute(endDate, 0);
			endDate = DateUtil.setSeconds(endDate, 0);

			boolean migrate = false;
			
			if(stop!=null ) {
				StaticMaster.setDeletePbPartnersCarLoggerStop(stop);
			}
			
			if(waitInSec!=null) {
				StaticMaster.setWaitForNextDeleteBatchInCar(waitInSec);
			}
			
			if(size== null){
				size = 500;
			}			
			
			if(size>1000) {
				logger.info("size is greater than max value 1000");
				size = 1000;
			}
			
			logger.info("Today date : {}, start date {} , end date {}", todayDate, startDate, endDate);
			if ((todayDate.before(startDate) || todayDate.after(endDate) || !timeCheck)) {
				migrate = true;
				pbPartnersArchivalServiceImpl.deleteNotBookLeadInCarLogger(startId, coll, size, timeCheck,db,days);
				result.put("OK", "1");
				result.put("SK_MSG", "collection migrated");
			}
			if(!migrate){
				logger.info("Could not initiate deleteNotBookedLeadInCar deleteNotBookedLeadInCar at this time {} , stop {} , waitInSec {}", new Date(), stop, waitInSec);
				logger.info(" coll {} , startId, {} , size {} ",coll, startId, size );
				result.put("OK", "0");
				result.put("SK_MSG", "not initiate deleteNotBookedLeadInCar, " + new Date());
			}
		} catch (Exception e) {
			result.put("OK", "0");
			result.put("SK_MSG", "Error in deleteNotBookedLeadInCar : ," + e.getMessage());
			logger.error("Error in deleteNotBookedLeadInCar , {}", e.getMessage());
		}
		logger.info("Exiting deleteNotBookedLeadInCar ");
		return result;
	}
	@ResponseBody
	@GetMapping(value = "car/moveBookedLeadForPbPartner")
	public Map<String, String> moveBookedLeadForPbPartner(@RequestParam(required = false,value = "size") Integer size,
												   @RequestParam(required = false,value = "stop") Boolean stop,
												   @RequestParam(value = "ns") String dbColl,
												   @RequestParam(value="dayBefore") int days,
												   @RequestParam(value="chunkSize") int chunkSize,
												   @RequestParam(required = false,value = "timeCheck") Boolean timeCheck) {
		logger.info("Inside moveBookedLeadCarPbpartnersV1 ");
		Map<String, String> result = new HashMap<String, String>();
		String[] ns = {"CarLoggerDB.ReqResLog","CarLoggerDBpolbk.ReqResLog"};
		if(!Arrays.asList(ns).contains(dbColl)){
			result.put("error", "ns is not whitelisted to proceed further...");
			return result;
		}
		try {
			if(stop && PbPartnersArchivalServiceImpl.isPBPCarLogS3JobRunning) {
				PbPartnersArchivalServiceImpl.isPBPCarLogS3JobStop = stop;
			}else{
				PbPartnersArchivalServiceImpl.isPBPCarLogS3JobStop = false;
			}
			if(size== null){
				size = 500;
			}
			if(size>1000) {
				logger.info("size is greater than max value 1000");
				size = 1000;
			}
			if(chunkSize>500) {
				chunkSize = 500;
			}
			if (!PbPartnersArchivalServiceImpl.isPBPCarLogS3JobRunning) {
				PbPartnersArchivalServiceImpl.isPBPCarLogS3JobRunning = true;
				PbPartnersArchivalServiceImpl.isPBPCarLogS3JobStop = false;
				pbPartnersArchivalServiceImpl.movePbPartnersMotorBookLeads(dbColl, size, -days, chunkSize);
				result.put("OK", "1");
				result.put("SK_MSG", "collection migrated");
			}
			if (PbPartnersArchivalServiceImpl.isPBPCarLogS3JobRunning) {
				logger.info("Could not initiate moveBookedLeadCarPbpartnersV1 migration at this time already running: {} , stop {} , waitInSec {}", new Date(), stop);
				logger.info(" coll {} , startId, {} , size {} ",ns, size );
				result.put("OK", "0");
				result.put("SK_MSG", "Could not initiate migration at this time , " + new Date());
			}
		} catch (Exception e) {
			result.put("OK", "0");
			result.put("SK_MSG", "Error in moveBookedLeadCarPbpartnersV1 : ," + e.getMessage());
			logger.error("Error in moveBookedLeadCarPbpartnersV1 , {}", e.getMessage());
		}
		logger.info("Exiting moveBookedLeadCarPbpartnersV1 ");
		return result;
	}
	@ResponseBody
	@GetMapping(value = "/tw/moveBookedLeadTwPbpartners")
	public Map<String, String> moveBookedLeadTwPbpartnersV1(@RequestParam(required = false,value = "size") Integer size,
															 @RequestParam(required = false,value = "stop") Boolean stop,
															 @RequestParam(value = "ns") String dbColl,
															@RequestParam(value="chunkSize") int chunkSize,
															 @RequestParam(value="dayBefore") int days) {
		logger.info("Inside moveBookedLeadTwPbpartnersV1 ");
		Map<String, String> result = new HashMap<String, String>();
		String[] ns = {"TWLoggerDBpolbk.ReqResTWowheelerLog"};
		if(!Arrays.asList(ns).contains(dbColl)){
			result.put("error", "ns is not whitelisted to proceed further...");
			return result;
		}
		try {
			if(stop && PbPartnersArchivalServiceImpl.isPBPTwLogS3JobRunning) {
				PbPartnersArchivalServiceImpl.isPBPTwLogS3JobStop = stop;
			}else{
				PbPartnersArchivalServiceImpl.isPBPTwLogS3JobStop = false;
			}
			if(size== null){
				size = 500;
			}
			if(size>1000) {
				size = 1000;
			}
			if(chunkSize>500) {
				chunkSize = 500;
			}
			if (!PbPartnersArchivalServiceImpl.isPBPTwLogS3JobRunning) {
				PbPartnersArchivalServiceImpl.isPBPTwLogS3JobRunning = true;
				PbPartnersArchivalServiceImpl.isPBPTwLogS3JobStop = false;
				pbPartnersArchivalServiceImpl.movePbPartnersTwBookLeads(dbColl, size,-days, chunkSize);
				result.put("OK", "1");
				result.put("SK_MSG", "collection migrated");
			}
			if (PbPartnersArchivalServiceImpl.isPBPTwLogS3JobRunning) {
				logger.info("Could not initiate moveBookedLeadTwPbpartnersV1 migration at this time already running: {} , stop {} , waitInSec {}", new Date(), stop);
				logger.info(" coll {} , startId, {} , size {} ",ns, size );
				result.put("OK", "0");
				result.put("SK_MSG", "Could not initiate migration at this time , " + new Date());
			}
		} catch (Exception e) {
			result.put("OK", "0");
			result.put("SK_MSG", "Error in moveBookedLeadTwPbpartnersV1 : ," + e.getMessage());
			logger.error("Error in moveBookedLeadTwPbpartnersV1 , {}", e.getMessage());
		}finally {
			// reset flag to run next time.
			StaticMaster.setPbpLoggerTwMoveS3JobRunning(false);
		}
		logger.info("Exiting moveBookedLeadTwPbpartnersV1 ");
		return result;
	}

	@ResponseBody
	@GetMapping(value = "/tw/moveQuoteDBbpartners")
	public Map<String, String> moveQuoteDBbpartners(@RequestParam(required = false,value = "size") Integer size,
															@RequestParam(required = false,value = "stop") Boolean stop,
															@RequestParam(value = "ns") String dbColl,
															@RequestParam(value="chunkSize") int chunkSize,
															@RequestParam(value="dayBefore") int days) {
		logger.info("Inside moveQuoteDBbpartners ");
		Map<String, String> result = new HashMap<String, String>();
		String[] ns = {"TwoWheelerQuoteDb.policyQuoteResponse"};
		if(!Arrays.asList(ns).contains(dbColl)){
			result.put("error", "ns is not whitelisted to proceed further...");
			return result;
		}
		try {
			if(stop && PbPartnersArchivalServiceImpl.isPBPTwQuoteDBJobRunning) {
				PbPartnersArchivalServiceImpl.isPBPTwQuoteDBJobStop = stop;
			}else{
				PbPartnersArchivalServiceImpl.isPBPTwQuoteDBJobStop = false;
			}
			if(size== null){
				size = 500;
			}
			if(size>1000) {
				size = 1000;
			}
			if(days<180) {
				days = 180;
			}
			if(chunkSize>500) {
				chunkSize = 500;
			}
			if (!PbPartnersArchivalServiceImpl.isPBPTwQuoteDBJobRunning) {
				PbPartnersArchivalServiceImpl.isPBPTwQuoteDBJobRunning = true;
				PbPartnersArchivalServiceImpl.isPBPTwQuoteDBJobStop = false;
				pbPartnersArchivalServiceImpl.movePbPartnersTwQuoteDB(dbColl, size,-days, chunkSize);
				result.put("OK", "1");
				result.put("SK_MSG", "collection migrated");
			}
			if (PbPartnersArchivalServiceImpl.isPBPTwQuoteDBJobRunning) {
				logger.info("Could not initiate moveQuoteDBbpartners migration at this time already running: {} , stop {} , waitInSec {}", new Date(), stop);
				logger.info(" coll {} , startId, {} , size {} ",ns, size );
				result.put("OK", "0");
				result.put("SK_MSG", "Could not initiate migration at this time , " + new Date());
			}
		} catch (Exception e) {
			result.put("OK", "0");
			result.put("SK_MSG", "Error in moveQuoteDBbpartners : ," + e.getMessage());
			logger.error("Error in moveQuoteDBbpartners , {}", e.getMessage());
		}
		logger.info("Exiting moveQuoteDBbpartners ");
		return result;
	}
	@ResponseBody
	@GetMapping(value = "/car/moveQuoteDBbpartners")
	public Map<String, String> moveCarQuoteDBbpartners(@RequestParam(required = false,value = "size") Integer size,
													@RequestParam(required = false,value = "stop") Boolean stop,
													@RequestParam(value = "ns") String dbColl,
													@RequestParam(value="chunkSize") int chunkSize,
													@RequestParam(value="dayBefore") int days) {
		logger.info("Inside moveCarQuoteDBbpartners ");
		Map<String, String> result = new HashMap<String, String>();
		String[] ns = {"motorQuoteDb.policyQuoteResponse"};
		if(!Arrays.asList(ns).contains(dbColl)){
			result.put("error", "ns is not whitelisted to proceed further...");
			return result;
		}
		try {
			if(stop && PbPartnersArchivalServiceImpl.isPBPCarQuoteDBJobRunning) {
				PbPartnersArchivalServiceImpl.isPBPCarQuoteDBJobStop = stop;
			}else{
				PbPartnersArchivalServiceImpl.isPBPCarQuoteDBJobStop = false;
			}
			if(size== null){
				size = 500;
			}
			if(size>1000) {
				size = 1000;
			}
			if(days<180) {
				days = 180;
			}
			if(chunkSize>500) {
				chunkSize = 500;
			}
			if (!PbPartnersArchivalServiceImpl.isPBPCarQuoteDBJobRunning) {
				PbPartnersArchivalServiceImpl.isPBPCarQuoteDBJobRunning = true;
				PbPartnersArchivalServiceImpl.isPBPCarQuoteDBJobStop = false;
				pbPartnersArchivalServiceImpl.movePbPartnersCarQuoteDB(dbColl, size,-days, chunkSize);
				result.put("OK", "1");
				result.put("SK_MSG", "collection migrated");
			}
			if (PbPartnersArchivalServiceImpl.isPBPCarQuoteDBJobRunning) {
				logger.info("Could not initiate moveCarQuoteDBbpartners migration at this time already running: {} , stop {} , waitInSec {}", new Date(), stop);
				logger.info(" coll {} , startId, {} , size {} ",ns, size );
				result.put("OK", "0");
				result.put("SK_MSG", "Could not initiate migration at this time , " + new Date());
			}
		} catch (Exception e) {
			result.put("OK", "0");
			result.put("SK_MSG", "Error in moveCarQuoteDBbpartners : ," + e.getMessage());
			logger.error("Error in moveCarQuoteDBbpartners , {}", e.getMessage());
		}
		logger.info("Exiting moveCarQuoteDBbpartners ");
		return result;
	}

}
