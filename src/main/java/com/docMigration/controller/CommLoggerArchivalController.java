package com.docMigration.controller;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import com.docMigration.service.impl.CommLoggerArchivalServiceImpl;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.docMigration.cleanup.entity.StaticMaster;
import com.docMigration.service.CommLoggerArchivalService;
import com.docMigration.util.DateUtil;

@RestController
public class CommLoggerArchivalController {

private final Logger logger = LoggerFactory.getLogger(CommLoggerArchivalController.class);
	
	@Autowired
	CommLoggerArchivalService commLoggerArchivalService;
	
	@ResponseBody
	@GetMapping(value = "/moveBookedLeadIntegration")
	public Map<String, String> moveBookedLeadIntegration(@RequestParam(required = false,value = "size") Integer size,
			@RequestParam(required = false,value = "waitInSec") Integer waitInSec,
			@RequestParam(required = false,value = "stop") Boolean stop, @RequestParam(value = "coll") String coll,
			@RequestParam(value = "db") String db,
			@RequestParam(value = "days") Integer days ,
			@RequestParam(required = false,value = "timeCheck") Boolean timeCheck) {
		logger.info("Inside moveBookedLeadIntegration");
		Map<String, String> result = new HashMap<String, String>();
		try {
			Date todayDate = new Date();
			Date startDate = new Date();
			startDate = DateUtil.setHours(startDate, 7);
			startDate = DateUtil.setMinute(startDate, 0);
			startDate = DateUtil.setSeconds(startDate, 0);

			Date endDate = new Date();
			endDate = DateUtil.setHours(endDate, 20);
			endDate = DateUtil.setMinute(endDate, 0);
			endDate = DateUtil.setSeconds(endDate, 0);

			boolean migrate = false;
			
			if(stop!=null ) {
				StaticMaster.setDeleteCommDbLoggerStop(stop);
			}
			
			if(size== null){
				size = 500;
			}			
			
			if(size>1000) {
				logger.info("size is greater than max value 1000");
				size = 1000;
			}
			
			logger.info("Today date : {}, start date {} , end date {}", todayDate, startDate, endDate);
			if ((todayDate.before(startDate) || todayDate.after(endDate) || !timeCheck)
					&& !StaticMaster.isDeleteCommDbLoggerStop()) {
				migrate = true;
				boolean isStop = StaticMaster.isDeleteCommDbLoggerStop();
				commLoggerArchivalService.moveBookLeads(coll, size, timeCheck,db,days,isStop, "movedIdsLogger");
				result.put("OK", "1");
				result.put("SK_MSG", "collection migrated");
			}
		     else if(!migrate){
				logger.info("Could not initiate moveBookedLeadCar at this time {} , stop {} , waitInSec {}", new Date(), stop, waitInSec);
				logger.info(" coll {} , size {} ",coll, size );
				result.put("OK", "0");
				result.put("SK_MSG", "not initiate moveBookedLeadCar, " + new Date());
			}
		} catch (Exception e) {
			result.put("OK", "0");
			result.put("SK_MSG", "Error in moveBookedLeadCar : ," + e.getMessage());
			logger.error("Error in moveBookedLeadCar , {}", e.getMessage());
		}
		logger.info("Exiting moveBookedLeadCroma ");
		return result;
	}

	@ResponseBody
	@GetMapping(value = "/moveBookedLeadDocsPoint")
	public Map<String, String> moveBookedLeadDocsPoint(@RequestParam(required = false,value = "size") Integer size,
			@RequestParam(required = false,value = "waitInSec") Integer waitInSec,
			@RequestParam(required = false,value = "stop") Boolean stop, @RequestParam(value = "coll") String coll,
			@RequestParam(value = "db") String db,
			@RequestParam(value = "days") Integer days ,
			@RequestParam(required = false,value = "timeCheck") Boolean timeCheck) {
		logger.info("Inside moveLeadsDocsPoint");
		Map<String, String> result = new HashMap<String, String>();
		try {
			Date todayDate = new Date();
			Date startDate = new Date();
			startDate = DateUtil.setHours(startDate, 7);
			startDate = DateUtil.setMinute(startDate, 0);
			startDate = DateUtil.setSeconds(startDate, 0);

			Date endDate = new Date();
			endDate = DateUtil.setHours(endDate, 20);
			endDate = DateUtil.setMinute(endDate, 0);
			endDate = DateUtil.setSeconds(endDate, 0);

			boolean migrate = false;
			
			if(stop!=null ) {
				StaticMaster.setDeleteCommDbLoggerStop(stop);
			}
			
			if(size== null){
				size = 500;
			}			
			
			if(size>1000) {
				logger.info("size is greater than max value 1000");
				size = 1000;
			}
			
			logger.info("Today date : {}, start date {} , end date {}", todayDate, startDate, endDate);
			if ((todayDate.before(startDate) || todayDate.after(endDate) || !timeCheck)
					&& !StaticMaster.isDeleteCommDbLoggerStop()) {
				migrate = true;
				boolean isStop = StaticMaster.isDeleteCommDbLoggerStop();
				commLoggerArchivalService.moveBookLeads(coll, size, timeCheck,db,days,isStop,"movedIdsLogger");
				result.put("OK", "1");
				result.put("SK_MSG", "collection migrated");
			}
		     else if(!migrate){
				logger.info("Could not initiate moveLeadsDocsPoint at this time {} , stop {} , waitInSec {}", new Date(), stop, waitInSec);
				logger.info(" coll {} , size {} ",coll, size );
				result.put("OK", "0");
				result.put("SK_MSG", "not initiate moveLeadsDocsPoint, " + new Date());
			}
		} catch (Exception e) {
			result.put("OK", "0");
			result.put("SK_MSG", "Error in moveLeadsDocsPoint : ," + e.getMessage());
			logger.error("Error in moveLeadsDocsPoint , {}", e.getMessage());
		}
		logger.info("Exiting moveLeadsDocsPoint ");
		return result;
	}
	
	@ResponseBody
	@GetMapping(value = "/moveCommunicationDB")
	public Map<String, String> moveBookedLeadCommunicationDB(@RequestParam(required = false,value = "size") Integer size,
			@RequestParam(required = false,value = "waitInSec") Integer waitInSec,
			@RequestParam(required = false,value = "stop") Boolean stop, @RequestParam(value = "coll") String coll,
			@RequestParam(value = "db") String db,
			@RequestParam(value = "days") Integer days ,
			@RequestParam(required = false,value = "timeCheck") Boolean timeCheck) {
		logger.info("Inside moveCommunicationDB");
		Map<String, String> result = new HashMap<String, String>();
		try {
			Date todayDate = new Date();
			Date startDate = new Date();
			startDate = DateUtil.setHours(startDate, 7);
			startDate = DateUtil.setMinute(startDate, 0);
			startDate = DateUtil.setSeconds(startDate, 0);

			Date endDate = new Date();
			endDate = DateUtil.setHours(endDate, 20);
			endDate = DateUtil.setMinute(endDate, 0);
			endDate = DateUtil.setSeconds(endDate, 0);

			boolean migrate = false;
			
			if(stop!=null ) {
				StaticMaster.setDeleteCommDbLoggerStop(stop);
			}
			
			if(size== null){
				size = 500;
			}			
			
			if(size>1000) {
				logger.info("size is greater than max value 1000");
				size = 1000;
			}
			
			logger.info("Today date : {}, start date {} , end date {}", todayDate, startDate, endDate);
			if ((todayDate.before(startDate) || todayDate.after(endDate) || !timeCheck)
					&& !StaticMaster.isDeleteCommDbLoggerStop()) {
				migrate = true;
				boolean isStop = StaticMaster.isDeleteCommDbLoggerStop();
				commLoggerArchivalService.moveBookLeads(coll, size, timeCheck,db,days,isStop,"movedIdsLogger");
				result.put("OK", "1");
				result.put("SK_MSG", "collection migrated");
			}
		     else if(!migrate){
				logger.info("Could not initiate moveCommunicationDB at this time {} , stop {} , waitInSec {}", new Date(), stop, waitInSec);
				logger.info(" coll {} , size {} ",coll, size );
				result.put("OK", "0");
				result.put("SK_MSG", "not initiate moveCommunicationDB, " + new Date());
			}
		} catch (Exception e) {
			result.put("OK", "0");
			result.put("SK_MSG", "Error in moveCommunicationDB : ," + e.getMessage());
			logger.error("Error in moveCommunicationDB , {}", e.getMessage());
		}
		logger.info("Exiting moveCommunicationDB ");
		return result;
	}

	@ResponseBody
	@GetMapping(value = "/moveWhatsappChat")
	public Map<String, String> moveWhatsappChat(@RequestParam(required = false,value = "size") Integer size,
															 @RequestParam(required = false,value = "waitInSec") Integer waitInSec,
															 @RequestParam(required = false,value = "stop") Boolean stop,
															 @RequestParam(value = "ns") String dbColl,
															 @RequestParam(value = "days") Integer days ,
															 @RequestParam(required = false,value = "timeCheck") Boolean timeCheck) {
		logger.info("Inside moveWhatsappChat");
		Map<String, String> result = new HashMap<String, String>();
		try {
			Date todayDate = new Date();
			Date startDate = new Date();
			startDate = DateUtil.setHours(startDate, 7);
			startDate = DateUtil.setMinute(startDate, 0);
			startDate = DateUtil.setSeconds(startDate, 0);

			Date endDate = new Date();
			endDate = DateUtil.setHours(endDate, 20);
			endDate = DateUtil.setMinute(endDate, 0);
			endDate = DateUtil.setSeconds(endDate, 0);

			boolean migrate = false;

			if(stop!=null ) {
				CommLoggerArchivalServiceImpl.moveWhatsappIsStop = stop;
			}

			if(size== null){
				size = 500;
			}

			if(size>1000) {
				logger.info("size is greater than max value 1000");
				size = 1000;
			}

			logger.info("Today date : {}, start date {} , end date {}", todayDate, startDate, endDate);
			if ((todayDate.before(startDate) || todayDate.after(endDate) || !timeCheck)
					&& !CommLoggerArchivalServiceImpl.moveWhatsappIsStop) {
				migrate = true;
				boolean isStop = CommLoggerArchivalServiceImpl.moveWhatsappIsStop;
				commLoggerArchivalService.moveWhatsappChat(dbColl, size, timeCheck,days,isStop,"movedIdsLogger");
				result.put("OK", "1");
				result.put("SK_MSG", "collection migrated");
			}
			else if(!migrate){
				logger.info("Could not initiate moveWhatsappChat at this time {} , stop {} , waitInSec {}", new Date(), stop, waitInSec);
				logger.info(" coll {} , size {} ",dbColl, size );
				result.put("OK", "0");
				result.put("SK_MSG", "not initiate moveWhatsappChat, " + new Date());
			}
		} catch (Exception e) {
			result.put("OK", "0");
			result.put("SK_MSG", "Error in moveWhatsappChat : ," + e.getMessage());
			logger.error("Error in moveWhatsappChat , {}", e.getMessage());
		}
		logger.info("Exiting moveWhatsappChat ");
		return result;
	}

}
