package com.docMigration.controller;

import com.docMigration.service.MotorLoggerArchivalService;
import com.docMigration.service.impl.SmeServiceImpl;
import com.docMigration.util.DateUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

@RestController
public class MigrationContoller {

    @Autowired
    private MotorLoggerArchivalService motorService;

    private final Logger logger = LoggerFactory.getLogger(MigrationContoller.class);
    private static final String REQ_HEADER_AUTHKEY = "92kik4i4jn4oi4n3oi4nn4k4l4lk4lknwlk4l4ljk44444klknn4h4jk5k";
    @GetMapping(value = "/moveCarEnquiryIdLeadID")
    public Map<String, String> moveCarEnquiryIdLeadID(@RequestParam(value="dayBefore", defaultValue = "10") int days,
                                                      @RequestParam(required = false, value = "timeCheck") Boolean timeCheck,
                                                      @RequestParam(value = "startDay", defaultValue = "20") int startDay,
                                                      @RequestHeader String authKey){

        logger.info("Inside moveSMELogModelToS3 ");
        Map<String, String> result = new HashMap<String, String>();
        // check authKey if not matched then return.
        if(!authKey.equals(REQ_HEADER_AUTHKEY)){
            result.put("error", "Auth key is not valid..");
            return result;
        }
        Date todayDate = new Date();
        Date startDate = new Date();
        startDate = DateUtil.setHours(startDate, 7);
        startDate = DateUtil.setMinute(startDate, 0);
        startDate = DateUtil.setSeconds(startDate, 0);

        Date endDate = new Date();
        endDate = DateUtil.setHours(endDate, 20);
        endDate = DateUtil.setMinute(endDate, 0);
        endDate = DateUtil.setSeconds(endDate, 0);


        try {
            if (!timeCheck || (todayDate.before(startDate) || todayDate.after(endDate))) {
//                motorService.moveCarEnquiryIdLeadID(days, startDay, timeCheck);
                result.put("OK", "1");
                result.put("SK_MSG", "collection migrated");
            }else { result.put("OK", "0");
                result.put("SK_MSG", "not initiate moveSMELogModelToS3, " + new Date());
            }
        } catch (Exception e) {
            result.put("OK", "0");
            result.put("SK_MSG", "Error in moveSMELogModelToS3 : ," + e.getMessage());
            logger.error("Error in moveSMELogModelToS3 , {}", e.getMessage());
        }
        logger.info("Exiting moveSMELogModelToS3 ");
        return result;
    }

}
