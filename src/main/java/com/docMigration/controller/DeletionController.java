package com.docMigration.controller;

import com.docMigration.service.DeletionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import org.springframework.http.HttpStatus;

@RestController
@RequestMapping("/delete")
public class DeletionController {

    @Autowired
    private DeletionService deletionService;

    private static final String REQ_HEADER_AUTHKEY = "92kik4i4jn4oi4n3oi4nn4k4l4lk4lknwlk4l4ljk44444klknn4h4jk5k";

    @GetMapping("/doc-push-logs")
    public ResponseEntity<String> triggerDeletion(@RequestHeader String authKey) {

        if (!REQ_HEADER_AUTHKEY.equals(authKey)) {
            return ResponseEntity.status(HttpStatus.FORBIDDEN).body("Unauthorized access.");
        }

        if (deletionService.isJobRunning()) {
            return ResponseEntity.ok("Deletion job is already running.");
        }

        deletionService.startAsyncDeletion();
        return ResponseEntity.ok("Deletion job started successfully.");
    }

    @GetMapping("/status")
    public ResponseEntity<String> getDeletionStatus() {
        return ResponseEntity.ok(deletionService.isJobRunning() ? "Running" : "Idle");
    }

    @GetMapping("/cancel")
    public ResponseEntity<String> cancelDeletionJob(@RequestHeader String authKey) {
        if (!authKey.equals(REQ_HEADER_AUTHKEY)) {
            return ResponseEntity.status(HttpStatus.FORBIDDEN).body("Unauthorized access.");
        }

        if (!deletionService.isJobRunning()) {
            return ResponseEntity.ok("No deletion job is currently running.");
        }

        deletionService.requestCancellation();
        return ResponseEntity.ok("Cancellation requested. Job will stop shortly.");
    }
}
