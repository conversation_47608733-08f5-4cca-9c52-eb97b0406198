package com.docMigration.controller;

import com.docMigration.service.PcdLoggerArchivalService;
import com.docMigration.service.impl.MotorLoggerArchivalServiceImpl;
import com.docMigration.service.impl.PcdLoggerArchivalServiceImpl;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

@RestController
public class PcdLoggerController {
    private final Logger logger = LoggerFactory.getLogger(PcdLoggerController.class);
    private static final String REQ_HEADER_AUTHKEY = "92kik4i4jn4oi4n3oi4nn4k4l4lk4lknwlk4l4ljk44444klknn4h4jk5k";
    @Autowired
    private PcdLoggerArchivalService pcdLoggerArchivalService;

    @GetMapping(value = "/pcd/docsByLeadId")
    public Map<String, String> pcdDocsByLeadId(@RequestParam(required = false,value = "size", defaultValue = "500") Integer size,
                                                         @RequestParam(required = false,value = "stop", defaultValue = "false") boolean stop,
                                                         @RequestParam(value = "ns") String dbColl,
                                                         @RequestParam(value="dayBefore") int days,
                                                         @RequestParam(value="chunkSize", defaultValue = "200") int chunkSize,
                                                         @RequestHeader String authKey){
        logger.info("Inside /car/moveBookedDocsByEnquiryId ");
        Map<String, String> responseMap = new HashMap<String, String>();

        try {
            // check authKey if not matched then return.
            if(!authKey.equals(REQ_HEADER_AUTHKEY)){
                responseMap.put("error", "Auth key is not valid..");
                return responseMap;
            }
            String[] ns = {"coreService.pcdLogs"};
            if(!Arrays.asList(ns).contains(dbColl)){
                responseMap.put("error", "ns is not whitelisted to proceed further...");
                return responseMap;
            }
            if(stop && PcdLoggerArchivalServiceImpl.isPCDMoveS3JobRunning) {
                PcdLoggerArchivalServiceImpl.isPCDMoveS3JobStop = stop;
            } else {
                PcdLoggerArchivalServiceImpl.isPCDMoveS3JobStop = false;
            }

            if(size>1000) {
                size = 1000;
            }
            if(chunkSize>300) {
                chunkSize = 300;
            }

            if(days < 365) {
                days = 365;
            }

            // if not running then run the job
            if (!PcdLoggerArchivalServiceImpl.isPCDMoveS3JobRunning) {
                PcdLoggerArchivalServiceImpl.isPCDMoveS3JobRunning = true;
                PcdLoggerArchivalServiceImpl.isPCDMoveS3JobStop = false;
                pcdLoggerArchivalService.movePcdBookLeads(dbColl, size, -days, chunkSize);
                responseMap.put("OK", "1");
                responseMap.put("SK_MSG", "collection migrated, for" + dbColl);
                logger.info("Car job completed for ns: {} , size {} ",dbColl, size );
            }
            else if (PcdLoggerArchivalServiceImpl.isPCDMoveS3JobRunning) {
                logger.info(" moveBookedLeadCar migration already running at: {} , for na: {} stop {}  {}", new Date(),dbColl, stop );
                logger.info(" coll {} , startId, {} , size {} ",ns, size );
                responseMap.put("OK", "0");
                responseMap.put("SK_MSG", "Could not initiate migration at this time , " + new Date());
            }
        } catch (Exception e) {
            responseMap.put("OK", "0");
            responseMap.put("SK_MSG", "Error in moveBookedLeadCar : ," + e.getMessage());
            logger.error("Error in Car moveBookedLeadCar , msg: {}", e.getMessage());
        }
        logger.info("completed job car moveBookedLeadCar, at: {} , for ns: ", new Date(), dbColl);
        return responseMap;
    }

}

