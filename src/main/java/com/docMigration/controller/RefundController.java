package com.docMigration.controller;

import com.docMigration.service.impl.RefundLoggerArchivalServiceImpl;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("refund")
public class RefundController {
    private final Logger logger = LoggerFactory.getLogger(RefundController.class);
    private static final String REQ_HEADER_AUTHKEY = "92kik4i4jn4oi4n3oi4nn4k4l4lk4lknwlk4l4ljk44444klknn4h4jk5k";

    @Autowired
    private RefundLoggerArchivalServiceImpl refundLoggerArchivalService;

    @GetMapping(value = "/moveBookedDocsByLeadId")
    public Map<String, String> moveBookedDocsByLeadId(@RequestParam(required = false,value = "size") Integer size,
                                                         @RequestParam(required = false,value = "stop", defaultValue = "false") boolean stop,
                                                         @RequestParam(value = "ns") String dbColl,
                                                         @RequestParam(value="dayBefore") int days,
                                                         @RequestParam(value="chunkSize", required = false, defaultValue = "200") int chunkSize,
                                                         @RequestHeader String authKey){
        logger.info("Inside refund moveBookedDocsByLeadId ");
        Map<String, String> responseMap = new HashMap<String, String>();

        try {
            // check authKey if not matched then return.
            if(!authKey.equals(REQ_HEADER_AUTHKEY)){
                responseMap.put("error", "Auth key is not valid..");
                return responseMap;
            }
            String[] ns = {"CnRDB.reqResLog"};
            if(!Arrays.asList(ns).contains(dbColl)){
                responseMap.put("error", "ns is not whitelisted to proceed further...");
                return responseMap;
            }

            // set param to stop the job
            if(stop && RefundLoggerArchivalServiceImpl.isRefundS3JobRunning) {
                RefundLoggerArchivalServiceImpl.isRefundS3JobStop = true;
            }else{
                RefundLoggerArchivalServiceImpl.isRefundS3JobStop = false;
            }

            if(size>1000) {
                size = 1000;
            }
            // As per policy 180 days must be allowed to add in mongoDB
            if(days < 180) {
                days = 180;
            }
            if(chunkSize >500) {
                chunkSize = 500;
            }

            if (!RefundLoggerArchivalServiceImpl.isRefundS3JobRunning) {
                RefundLoggerArchivalServiceImpl.isRefundS3JobRunning = true;
                RefundLoggerArchivalServiceImpl.isRefundS3JobStop = false;
                refundLoggerArchivalService.moveRefundBookLeads(dbColl, size,-days, chunkSize);
                responseMap.put("OK", "1");
                responseMap.put("SK_MSG", "collection migrated");
            }else if (RefundLoggerArchivalServiceImpl.isRefundS3JobRunning) {
                logger.info("Refund job already for ns: {}, date: {} , stop {} ",ns, new Date(), stop);
                responseMap.put("ok", "1");
                responseMap.put("SK_MSG", "job is already running, date:  " + new Date());
            }

        } catch (Exception e) {
            responseMap.put("ok", "0");
            responseMap.put("SK_MSG", "Error in refund moveBookedDocsByLeadId  : ," + e.getMessage());
            logger.error("Error in refund moveBookedDocsByLeadId, msg: {}, for ns: {}", e.getMessage(), dbColl);
        }
        logger.info("Exiting refund moveBookedDocsByLeadId  ");
        return responseMap;
    }

    @GetMapping(value = "/moveOldBookedDocsByLeadId_173")
    public Map<String, String> moveOldBookedDocsByLeadId_173(@RequestParam(required = false,value = "size") Integer size,
                                                      @RequestParam(required = false,value = "stop", defaultValue = "false") boolean stop,
                                                      @RequestParam(value = "ns") String dbColl,
                                                      @RequestParam(value="dayBefore") int days,
                                                      @RequestParam(value="chunkSize", required = false, defaultValue = "200") int chunkSize,
                                                      @RequestHeader String authKey){
        logger.info("Inside refund moveBoomoveOldBookedDocsByLeadIdkedDocsByLeadId ");
        Map<String, String> responseMap = new HashMap<String, String>();

        try {
            // check authKey if not matched then return.
            if(!authKey.equals(REQ_HEADER_AUTHKEY)){
                responseMap.put("error", "Auth key is not valid..");
                return responseMap;
            }
            String[] ns = {"CnRDB.reqResLog_173"};
            if(!Arrays.asList(ns).contains(dbColl)){
                responseMap.put("error", "ns is not whitelisted to proceed further...");
                return responseMap;
            }

            // set param to stop the job
            if(stop && RefundLoggerArchivalServiceImpl.isOldRefundS3JobRunning) {
                RefundLoggerArchivalServiceImpl.isOldRefundS3JobStop = true;
            }else{
                RefundLoggerArchivalServiceImpl.isOldRefundS3JobStop = false;
            }

            if(size>1000) {
                size = 1000;
            }
            if(chunkSize >500) {
                chunkSize = 500;
            }

            if (!RefundLoggerArchivalServiceImpl.isOldRefundS3JobRunning) {
                RefundLoggerArchivalServiceImpl.isOldRefundS3JobRunning = true;
                RefundLoggerArchivalServiceImpl.isOldRefundS3JobStop = false;
                refundLoggerArchivalService.moveOldRefundBookLeads(dbColl, size,-days, chunkSize);
                responseMap.put("OK", "1");
                responseMap.put("SK_MSG", "collection migrated");
            }else if (RefundLoggerArchivalServiceImpl.isOldRefundS3JobRunning) {
                logger.info("Refund job already for ns: {}, date: {} , stop {} ",ns, new Date(), stop);
                responseMap.put("ok", "1");
                responseMap.put("SK_MSG", "job is already running, date:  " + new Date());
            }

        } catch (Exception e) {
            responseMap.put("ok", "0");
            responseMap.put("SK_MSG", "Error in refund moveOldBookedDocsByLeadId  : ," + e.getMessage());
            logger.error("Error in refund moveOldBookedDocsByLeadId, msg: {}, for ns: {}", e.getMessage(), dbColl);
        }
        logger.info("Exiting refund moveOldBookedDocsByLeadId  ");
        return responseMap;
    }
}
