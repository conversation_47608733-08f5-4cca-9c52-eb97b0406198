package com.docMigration.util;

import org.apache.http.HttpResponse;
import org.apache.http.client.ClientProtocolException;
import org.apache.http.client.config.CookieSpecs;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Component
public class HttpUtil {
    private static final Logger logger = LoggerFactory.getLogger(HttpUtil.class);
    public static Map<String, Object> post(String uri, String jsonPayload, Map<String, String> header) throws Exception {
        Map<String, Object> responseAttributes = new HashMap<>();
        try (CloseableHttpClient httpclient = HttpClients.custom().setDefaultRequestConfig(RequestConfig.custom().setCookieSpec(CookieSpecs.STANDARD).build()).build()){
            // url with the post data
            HttpPost httpost = new HttpPost(uri);

            // passes the results to a string builder/entity
            StringEntity se = new StringEntity(jsonPayload);

            // sets the post request as the resulting string
            httpost.setEntity(se);

            // sets a request header so the page receving the request will know
            // what to do with it
            if (header != null) {
                for (Map.Entry<String, String> entry : header.entrySet()) {
                    httpost.setHeader(entry.getKey(), entry.getValue());
                }
            }
            httpost.setHeader("Accept", "application/json");
            httpost.setHeader("Content-type", "application/json");
            HttpResponse httpResponse = httpclient.execute(httpost);
            String responseAsString = "";
            int statusCode = 0;
            if (httpResponse != null) {
                responseAsString = EntityUtils.toString(httpResponse.getEntity());
                statusCode = httpResponse.getStatusLine().getStatusCode();
                logger.debug("response String: {}" ,responseAsString);
            } else {
                logger.debug("No response receceived from[ {} ], for payload: {} ", uri, jsonPayload );
            }

            responseAttributes.put("responseBody", responseAsString);
            responseAttributes.put("status", statusCode);
        } catch (ClientProtocolException e) {
            logger.error("ClientProtocolException post() URI=[ {} ], msg: {}" ,uri, e.getMessage(), e);
        } catch (Exception e) {
            logger.error("Exception post() URI=[ {} ] , msg: {} " , uri, e.getMessage(), e);
        }
        return responseAttributes;
    }
}
