package com.docMigration.util;
import com.mongodb.*;
import com.mongodb.MongoClient;
import com.mongodb.client.MongoClients;
import com.mongodb.client.model.DBCollectionRemoveOptions;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.security.cert.Extension;
import java.util.ArrayList;
import java.util.List;

@Component
public class MongoUtil {

    private static final Logger logger = LoggerFactory.getLogger(MongoUtil.class);

    public static void main(String[] args) {
        MongoUtil mongoUtil = new MongoUtil();
        MongoClient mongoClient = null;
        try {

            mongoClient = mongoUtil.getMongoClient("************");
            if (mongoClient != null) {
                logger.debug("connection got");
            } else {

            }
        } catch(Exception e) {

        } finally {
            if(mongoClient != null) {
                mongoClient.close();
            }
        }

    }

    public List<BasicDBObject> getDBObjects(MongoClient mongoClient, String dbName, String collection, DBObject query) {
        DB db = mongoClient.getDB(dbName);
        DBCollection dbCollection = db.getCollection(collection);
        DBCursor cursor = dbCollection.find(query);
        List<BasicDBObject> dbObjects = new ArrayList<BasicDBObject>();

        try {
            if (cursor != null) {
                while (cursor.hasNext()) {
                    BasicDBObject actorObj = (BasicDBObject) cursor.next();
                    dbObjects.add(actorObj);
                }
            }
        } catch (Exception e) {
            logger.error(e.getMessage());
            throw e;
        } finally {
            if (cursor != null) {
                cursor.close();
            }
        }
        return dbObjects;
    }

    public MongoClient getMongoClient(String mongoConnectUrl) {
        MongoClient mongoClient = null;
        try {
            mongoClient = new MongoClient("server");
//            ConnectionString connectionString = new ConnectionString("******************************************************************************************************************");
            ConnectionString connectionString = new ConnectionString(mongoConnectUrl);
            mongoClient = (MongoClient) MongoClients.create(connectionString);
        } catch (Exception e) {
            logger.error("exception in getting mongoClient, msg:{}", e.getMessage());
        }
        return mongoClient;
    }

    public void releaseConnection(MongoClient mongoClient) {
        try{
            if(mongoClient !=null) {
                mongoClient.close();
            }
        }catch (Exception e){

        }
    }
    public BasicDBObject getDBObjectByDbName(MongoClient mongoClient, String collection, DBObject query,String dbName) {
        DB db = mongoClient.getDB(dbName);

        DBCollection dbCollection = db.getCollection(collection);
        BasicDBObject dbObject = (BasicDBObject) dbCollection.findOne(query);
        return dbObject;
    }
    public List<DBObject> getDBObjects(MongoClient mongoClient, String collection, DBObject query, DBObject fieldsToReturn, int offset, int length, DBObject orderBy, String dbName) {
        DB db = (DB) mongoClient.getDatabase(dbName);
        // Tells the nature of sorting on C_AT
		/*DBObject orderBy = new BasicDBObject();
		orderBy.put("C_AT", sortBy);*/
        if (collection == null || query == null) {
            throw new IllegalArgumentException("collectionName and query can't be null");
        }
        DBCollection dbCollection = db.getCollection(collection);

        DBCursor cursor = null;
        if (fieldsToReturn != null) {
            if(length!=0) {
                cursor = dbCollection.find(query, fieldsToReturn).limit(length);
            }else {
                cursor = dbCollection.find(query, fieldsToReturn);
            }
        } else {
            if(length!=0) {
                cursor = dbCollection.find(query, fieldsToReturn).limit(length);
            }else {
                cursor = dbCollection.find(query); // cursor = dbCollection.find(query).sort(orderBy);
            }
        }

        if (orderBy != null) {
            cursor.sort(orderBy);
        }

		/*if (length > 0) {
			cursor.skip(offset).limit(length);

		}*/

        List<DBObject> dbObjects = new ArrayList<DBObject>();
        try {
            if (cursor != null) {
                while (cursor.hasNext()) {
                    //BasicDBObject actorObj = (BasicDBObject) cursor.next();
                    dbObjects.add((BasicDBObject) cursor.next());
                }
            }
        } catch (Exception e) {
            logger.error(e.getMessage());
            throw e;
        } finally {
            if (cursor != null) {
                cursor.close();
            }
        }
        return dbObjects;
    }
    public void deleteRow( MongoClient mongoClient, String collectionName, DBObject queryOnId, boolean useOption, String dbName) throws Exception {
        DB mongoDB = null;
        try {
            mongoDB = (DB) mongoClient.getDatabase(dbName);
            // mongoDB.requestStart();
            DBCollection userColl = mongoDB.getCollection(collectionName);

            DBCollectionRemoveOptions options = new DBCollectionRemoveOptions();
            options.writeConcern(WriteConcern.UNACKNOWLEDGED);
            if(useOption) {
                WriteResult res = userColl.remove(queryOnId, options);
            }else {
                WriteResult res = userColl.remove(queryOnId);
            }

        } catch (MongoException e) {
            logger.error("document not added in Mongo, msg :" + e.getMessage(), e);
            throw e;
        } catch (Exception e) {
            logger.error("document not added Exception , msg :" + e.getMessage(), e);
            throw e;
        } finally {
        }
    }
    public void updateRow(MongoClient mongoClient, String collectionName, DBObject queryJson, DBObject rowJson, boolean upsert, boolean multi, String db) throws Exception {
        DB mongoDB = null;
        WriteConcern concern = WriteConcern.SAFE;
        try {
            mongoDB = (DB) mongoClient.getDatabase(db);
            //mongoDB.requestStart();
            DBCollection userColl = mongoDB.getCollection(collectionName);
            // DBObject dbObject = new BasicDBObject();
            WriteResult res = userColl.update(queryJson, rowJson, upsert, multi, concern);
            /*
             * CommandResult resErr =
             * mongoDB.getLastError(res.getLastConcern()); if (res.getError() !=
             * null) { throw new MongoException(resErr.getErrorMessage()); }
             */
        } catch (MongoException e) {
            logger.error("exception occured while invoking updateRow Mongo into mongo collectionName:" + collectionName + " , msg:" + e.getMessage(), e);
            throw e;
        } catch (Exception e) {
            logger.error("exception occured while invoking updateRow into mongo collectionName:" + collectionName + " , msg:" + e.getMessage(), e);
            throw e;
        } finally {

        }
    }

    public MongoClient getMongoClient(String srcIp, String srcUsername, String srcPwd) {

        MongoClient mongoClient = null;
        try {
            mongoClient = new MongoClient("server");
            StringBuilder stringBuilder = new StringBuilder();
            stringBuilder.append("mongodb://").append(srcUsername).append(":").append(srcPwd).append("@").append(srcIp).append(":27017/?authSource=admin&maxPoolSize=10&socketTimeoutMS=10000");
            ConnectionString connectionString = new ConnectionString(stringBuilder.toString());
//            ConnectionString connectionString = new ConnectionString("******************************************************************************************************************");
            mongoClient = (MongoClient) MongoClients.create(connectionString);
        } catch (Exception e) {
            logger.error("exception in getting mongoClient for IPAddress:{}", srcIp);
        }
        return mongoClient;
    }
}
