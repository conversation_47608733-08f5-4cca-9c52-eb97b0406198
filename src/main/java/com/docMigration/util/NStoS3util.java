package com.docMigration.util;

import com.mongodb.BasicDBObject;
import com.mongodb.DBObject;
import org.apache.commons.io.output.ByteArrayOutputStream;
import org.bson.BSONObject;
import org.bson.types.ObjectId;

import java.io.IOException;
import java.io.ObjectOutputStream;
import java.text.SimpleDateFormat;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;

public class NStoS3util {

    public static final String BUCKET_NAME_LOGGER_ARCHIVAL = "pbmongoarchive";
    public static final String BUCKET_NAME_LOGGER_ARCHIVAL_PB_PARTNER = "pbpmongodbarchive";
    public static final String BUCKET_NAME_SME_LOG = "smemongodbarchive";
    public static final String lastUpdatedIdDB = "ArchivalMigrationDB";
    public static final String lastUpdatedIdColl = "movedIdsLogger";

    // ns to s3 path mapping

    public static  final String S3_FOLDER_PATH_COMM_V2_FILE = "CommV2FileLogs/";
    public static  final String S3_FOLDER_PATH_CAR =  "carBookedLog_CarLoggerDB_ReqResLog/";
    public static  final String S3_FOLDER_PATH_TW =  "twBookedLog_TwoWheelerDB_ReqResTWowheelerLog/";
    public static  final String S3_FOLDER_PATH_CAR_PBPartner =  "pbp_carBookedLog_CarLoggerDBpolbk_ReqResLog/";
    public static  final String S3_FOLDER_PATH_TW_PBPartner =  "pbp_twBookedLog_TWLoggerDBpolbk_ReqResTWowheelerLog/";
    public static  final String S3_FOLDER_PATH_TW_QUOTE_DB_PBPartner =  "pbp_TW_TwoWheelerQuoteDb_policyQuoteResponse/";
    public static  final String S3_FOLDER_PATH_CAR_QUOTE_DB_PBPartner =  "pbp_CAR_motorQuoteDb_policyQuoteResponse/";
    public static  final String S3_FOLDER_PATH_LIFE =  "lifeLog_e2eLog_LogDataRenewal/";
    public static  final String S3_FOLDER_PATH_HEALTH =  "healthBookedLog_ProfileManager_ProposalSLogging/";
    public static  final String S3_FOLDER_PATH_REFUND =  "refundLog_CnRDB_reqResLog/";
    public static  final String S3_FOLDER_PATH_KYC =  "kycLog_KYC_KYCCentral/";
    public static  final String S3_FOLDER_PATH_CLAIM =  "claimLog_ClaimsDB_claimslogs/";
    public static  final String S3_FOLDER_PATH_TRAVEL =  "travelLog_travelDb_ApiLog/";
    public static  final String S3_FOLDER_PATH_PIVC_S3_LOG =  "BMS_Integration_PIVCLog/";
    public static final String S3_FOLDER_PATH_DOCSPOINT_DOCPUSHLOG = "BMS_DocsPoint_DocPushLog/";
    public static final String S3_FOLDER_PATH_DOCSPOINT_REQUESTLOGS = "BMS_DocsPoint_RequestLogs/";
    public static  final String S3_FOLDER_PATH_KYC_LOGS_CAR_S3_LOG =  "carLog_CarLoggerDB_KycLogs/";
    public static final String S3_FOLDER_PATH_TW_CORE_KYC = "twBookedLog_TwoWheelerDB_CoreKYC/";
    public static final String S3_FOLDER_PATH_SME_LOG_MODEL = "SME_SmeLog_LogModel/";
    public static final String S3_FOLDER_PATH_SME_LOG_ENTRY = "SME_SmeLog_LogEntry/";
    public static final String S3_FOLDER_PATH_SME_NON_EB_LOG_ENTRY = "SME_SMENonEBLog_LogEntry/";
    public static final String S3_FOLDER_PATH_BMS_PBCROMA_V2 = "BMS_Croma_PBCromaV2/";
    public static final String S3_FOLDER_PATH_BMS_PBCROMA_V2_SALES = "BMS_Croma_PBCromaV2_sales/";
    private static final String S3_FOLDER_PATH_INT_EXTERNAL_API_LOG = "BMS_Integration_ExternalAPILogs/";
    private static final String S3_FOLDER_PATH_INT_REQUESTLOGS_LOG = "BMS_Integration_RequestLogs/";
    private static final String S3_FOLDER_PATH_RENEWAL_PBEVENTLOG = "RenewalHealth_PBEventLog/";
    private static final String S3_FOLDER_PATH_CS_PCD_LOGS = "CS_PcdLogs/";

    public static Map<String, String> MAP_NS_TO_S3_PATH = new HashMap<String, String>() ;

    static {
        // ns as key and s3 path as value.
        MAP_NS_TO_S3_PATH.put("CarLoggerDBpolbk.ReqResLog", S3_FOLDER_PATH_CAR_PBPartner);
        MAP_NS_TO_S3_PATH.put("TWLoggerDBpolbk.ReqResTWowheelerLog", S3_FOLDER_PATH_TW_PBPartner);
        MAP_NS_TO_S3_PATH.put("e2eLog.LogDataRenewal", S3_FOLDER_PATH_LIFE);
        MAP_NS_TO_S3_PATH.put("LifeRenewalLog.LogDataRenewal", S3_FOLDER_PATH_LIFE);
        MAP_NS_TO_S3_PATH.put("ProfileManager.ProposalSLogging", S3_FOLDER_PATH_HEALTH);
        MAP_NS_TO_S3_PATH.put("CnRDB.reqResLog", S3_FOLDER_PATH_REFUND);
        MAP_NS_TO_S3_PATH.put("ClaimsDB.claimslogs", S3_FOLDER_PATH_CLAIM);
        MAP_NS_TO_S3_PATH.put("KYC.KYCCentral", S3_FOLDER_PATH_KYC);
        MAP_NS_TO_S3_PATH.put("CarLoggerDB.ReqResLog", S3_FOLDER_PATH_CAR);
        MAP_NS_TO_S3_PATH.put("TwoWheelerDB.ReqResTWowheelerLog", S3_FOLDER_PATH_TW);
        MAP_NS_TO_S3_PATH.put("travelDb.ApiLog", S3_FOLDER_PATH_TRAVEL);
        MAP_NS_TO_S3_PATH.put("communicationDB.CommV2FileLogsv2", S3_FOLDER_PATH_COMM_V2_FILE);
        MAP_NS_TO_S3_PATH.put("Integration.PIVCLog", S3_FOLDER_PATH_PIVC_S3_LOG);
        MAP_NS_TO_S3_PATH.put("Integration.ExternalAPILogs", S3_FOLDER_PATH_INT_EXTERNAL_API_LOG);
        MAP_NS_TO_S3_PATH.put("Integration.RequestLogs", S3_FOLDER_PATH_INT_REQUESTLOGS_LOG);
        MAP_NS_TO_S3_PATH.put("CarLoggerDB.KycLogs_173", S3_FOLDER_PATH_KYC_LOGS_CAR_S3_LOG);
        MAP_NS_TO_S3_PATH.put("CarLoggerDB.KycLogs", S3_FOLDER_PATH_KYC_LOGS_CAR_S3_LOG);
        MAP_NS_TO_S3_PATH.put("CarLoggerDB.ReqResLog_173", S3_FOLDER_PATH_CAR);
        MAP_NS_TO_S3_PATH.put("TwoWheelerDB.ReqResTWowheelerLog_173", S3_FOLDER_PATH_TW);
        MAP_NS_TO_S3_PATH.put("CnRDB.reqResLog_173", S3_FOLDER_PATH_REFUND);
        MAP_NS_TO_S3_PATH.put("travelDb.ApiLog_173", S3_FOLDER_PATH_TRAVEL);
        MAP_NS_TO_S3_PATH.put("DocsPoint.DocPushLog", S3_FOLDER_PATH_DOCSPOINT_DOCPUSHLOG);
        MAP_NS_TO_S3_PATH.put("TwoWheelerDB.CoreKYC", S3_FOLDER_PATH_TW_CORE_KYC);
        MAP_NS_TO_S3_PATH.put("SmeLog.LogModel", S3_FOLDER_PATH_SME_LOG_MODEL);
        MAP_NS_TO_S3_PATH.put("SmeLog.LogEntry", S3_FOLDER_PATH_SME_LOG_ENTRY);
        MAP_NS_TO_S3_PATH.put("SMENonEBLog.LogEntry", S3_FOLDER_PATH_SME_NON_EB_LOG_ENTRY);
        MAP_NS_TO_S3_PATH.put("DocsPoint.RequestLogs", S3_FOLDER_PATH_DOCSPOINT_REQUESTLOGS);
        MAP_NS_TO_S3_PATH.put("Croma.PBCromaV2", S3_FOLDER_PATH_BMS_PBCROMA_V2);
        MAP_NS_TO_S3_PATH.put("Croma.PBCromav2_Sales", S3_FOLDER_PATH_BMS_PBCROMA_V2_SALES);
        MAP_NS_TO_S3_PATH.put("TwoWheelerQuoteDb.policyQuoteResponse", S3_FOLDER_PATH_TW_QUOTE_DB_PBPartner);
        MAP_NS_TO_S3_PATH.put("motorQuoteDb.policyQuoteResponse", S3_FOLDER_PATH_CAR_QUOTE_DB_PBPartner);
        MAP_NS_TO_S3_PATH.put("coreService.pcdLogs", S3_FOLDER_PATH_CS_PCD_LOGS);
        MAP_NS_TO_S3_PATH.put("RenewalHealth.PBEventLog", S3_FOLDER_PATH_RENEWAL_PBEVENTLOG);
    }
    public static void main(String[] args) throws IOException {

         List<DBObject> list = new ArrayList<>();

         DBObject obj1 = new BasicDBObject();
         obj1.put("name", "123");
         obj1.put("isExist", true);

        Boolean isObj = (Boolean) obj1.get("isExist");
        System.out.println(isObj);
        if(isObj == null || !isObj){
            System.out.println("true"+ isObj);
        }else{
            System.out.println("false"+ isObj);
        }


//        DBObject obj2 = new BasicDBObject();
//        obj2.put("name", "123");
//        obj2.put("isExist", true);
//
//        DBObject obj3 = new BasicDBObject();
//        obj3.put("name", "123");
//        obj3.put("isExist", true);
//
//        list.add(obj1);
//        list.add(obj2);
//        list.add(obj3);

//        if (list != null && list.size()>0) {
//            HashSet<DBObject> set = new HashSet<>();
//            Iterator<DBObject> itr = list.iterator();
//            while (itr.hasNext()) {
//                DBObject doc = itr.next();
//                if (set.contains(doc)) {
//                    itr.remove();
//                } else {
//                    set.add(doc);
//                }
//            }
//        }
//
//        System.out.println(list);


//        // Input date string
//        String dateString = "2022-12-30T00:00:00.000Z";
//
//        // Parse the input date string to ZonedDateTime
//        DateTimeFormatter formatter = DateTimeFormatter.ISO_ZONED_DATE_TIME;
//        ZonedDateTime date = ZonedDateTime.parse(dateString, formatter);
//        ZonedDateTime nextDate = null;
//
//        // Print the original date
//        System.out.println("Original date: " + date);
//        // Increment the date by 1 day for 3 days and print the results
//        int j=7;
//        String tempfileName ="";
//        date = date.plus(1, ChronoUnit.DAYS);
//        nextDate = date.plus(1, ChronoUnit.DAYS);
//        String lteDate = new ObjectId(Date.from(nextDate.toInstant())).toString();
//
//        System.out.println("#!/bin/bash");
//        System.out.println("LOG_FILE=/opt/script/log/dumpTrackingkyc_239.txt");
//        for (int i = 0; i <= 365; i++) {
//            String fileDate = nextDate.format(DateTimeFormatter.ofPattern("ddMMMyyyy"));
//            String gteDate = lteDate;
//            nextDate = nextDate.plus(1, ChronoUnit.DAYS);
//            lteDate = new ObjectId(Date.from(nextDate.toInstant())).toString();
//            tempfileName = "CarLoggerDB_KycLogs_239_"+fileDate+".gz";
//            String dumpCommand = String.format(
//                    "mongodump --host='************:27017' -u migrationMDB7U -p migrationU8730x --authenticationDatabase admin -d 'CarLoggerDB' --collection KycLogs -q '{\"_id\":{\"$gt\": { \"$oid\" :\"%s\"}, \"$lt\": { \"$oid\" :\"%s\"}}}' --gzip --archive="+tempfileName,
//                    gteDate, lteDate
//            );
//
//            String copyCommand = "aws s3 cp "+tempfileName+" " + "s3://pbmongoarchive/motorLogger/CarLoggerDB/KYC/";
//            String delCommand = String.format("rm -f "+tempfileName);
//            System.out.println();
//            System.out.println("echo \"starting dump $(date) for fileName: " +tempfileName+"_239"+ "\" >> $LOG_FILE");
//            System.out.println(dumpCommand);
//            System.out.println("echo \"completing dump $(date) for fileName: " +tempfileName+"_239"+ "\" >> $LOG_FILE");
//            System.out.println();
//            System.out.println("echo \"copying to S3 fileName: " +tempfileName+"_239"+ "\" >> $LOG_FILE");
//            System.out.println(copyCommand);
//            System.out.println("echo \"completed copying to S3 fileName: " +tempfileName+"_239"+ "\" >> $LOG_FILE");
//            System.out.println();
//            System.out.println("echo \"started removing from disk, fileName: " +tempfileName+"_239"+ "\" >> $LOG_FILE");
//            System.out.println(delCommand);
//            System.out.println("echo \"completed removing from disk, fileName: " +tempfileName+"_239"+ "\" >> $LOG_FILE");
//
//            System.out.println();
//            System.out.println();
//        }
    }
    public static double sizeOf(Object obj) throws java.io.IOException
    {
        ByteArrayOutputStream byteObject = new ByteArrayOutputStream();
        ObjectOutputStream objectOutputStream = new ObjectOutputStream(byteObject);
        objectOutputStream.writeObject(obj);
        objectOutputStream.flush();
        objectOutputStream.close();
        byteObject.close();

        return (byteObject.toByteArray().length)/1024.0;
    }

}
