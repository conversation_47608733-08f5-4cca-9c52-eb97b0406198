package com.docMigration.util;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.util.ArrayList;
import java.util.List;

import org.apache.commons.lang.RandomStringUtils;
import org.apache.commons.lang.StringUtils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * The Class EncodeHashUtil.
 * <AUTHOR>
 *
 */
public class EncodeHashUtil {
	private static final Logger log = LoggerFactory.getLogger(EncodeHashUtil.class);
	
	//private static List<Character> indexToCharTable;
	
	private static List<Character> indexToCharEmailTable;

	static {
		//initializeIndexToCharTable();
		initializeIndexToCharEmailTable();
	}

	private EncodeHashUtil() {
		//initializeIndexToCharTable();
		initializeIndexToCharEmailTable();
	}

	/**
	 * Generate 7 char hash.
	 *
	 * @param inputStr  the input str
	 * @param hashLogic the hash logic
	 * @return the string
	 */
	public static String generateCharHash(String inputStr, int hashLogic) {
		if (hashLogic == 0) {
			return generate7CharHash(inputStr, hashLogic);
		} else if (hashLogic == 1) {
			return generate8CharHash(inputStr, 0);
		} else if (hashLogic == 2) {
			return generate9CharHash(inputStr, 0);
		} else if (hashLogic == 3) {
			return generate10CharHash(inputStr, 0);
		} else {
			return generate10CharHash(inputStr, hashLogic - 3);
		}
	}
	
	public static String generate7CharHash(String inputStr, int hashLogic) {
		if (null != inputStr && !inputStr.isEmpty()) {
			String hashString = sha256Hashing(inputStr.toLowerCase());
			if (StringUtils.isNotEmpty(hashString)) {
				if (hashLogic < hashString.length()) {
					hashString = hashString.substring(0, hashString.length() - hashLogic);
				} else {
					hashString = RandomStringUtils.random(64, true, false);
				}

				char[] t = hashString.toCharArray();
				char[] hash = new char[7];
				for (int i = 0; i < hashString.length(); i++) {
					hash[i % 7] = (char) (hash[i % 7] ^ t[i]);
				}
				for (int i = 0; i < 7; i++) {
					hash[i] = indexToCharEmailTable.get(hash[i] % indexToCharEmailTable.size());
				}

				return new String(hash);

			} else {
				return null;
			}

		} else {
			return inputStr;
		}
	}

	public static String generate8CharHash(String inputStr, int hashLogic) {
		if (null != inputStr && !inputStr.isEmpty()) {
			String hashString = sha256Hashing(inputStr.toLowerCase());
			if (StringUtils.isNotEmpty(hashString)) {

				if (hashLogic < hashString.length()) {
					hashString = hashString.substring(0, hashString.length() - hashLogic);
				} else {
					hashString = RandomStringUtils.random(64, true, false);
				}

				char[] t = hashString.toCharArray();
				char[] hash = new char[8];
				for (int i = 0; i < hashString.length(); i++) {
					hash[i % 8] = (char) (hash[i % 8] ^ t[i]);
				}
				for (int i = 0; i < 8; i++) {
					hash[i] = indexToCharEmailTable.get(hash[i] % indexToCharEmailTable.size());
				}

				return new String(hash);
			} else {
				return null;
			}

		} else {
			return inputStr;
		}
	}
	
	public static String generate9CharHash(String inputStr, int hashLogic) {
		if (null != inputStr && !inputStr.isEmpty()) {
			String hashString = sha256Hashing(inputStr.toLowerCase());
			if (StringUtils.isNotEmpty(hashString)) {

				if (hashLogic < hashString.length()) {
					hashString = hashString.substring(0, hashString.length() - hashLogic);
				} else {
					hashString = RandomStringUtils.random(64, true, false);
				}

				char[] t = hashString.toCharArray();
				char[] hash = new char[9];
				for (int i = 0; i < hashString.length(); i++) {
					hash[i % 9] = (char) (hash[i % 9] ^ t[i]);
				}
				for (int i = 0; i < 9; i++) {
					hash[i] = indexToCharEmailTable.get(hash[i] % indexToCharEmailTable.size());
				}

				return new String(hash);
			} else {
				return null;
			}

		} else {
			return inputStr;
		}
	}
	
	public static String generateCharHashEmail(String inputStr, int hashLogic) {
		if (hashLogic == 0) {
			return generate7CharHash(inputStr, hashLogic);
		} else if (hashLogic == 1) {
			return generate8CharHash(inputStr, 0);
		} else if (hashLogic == 2) {
			return generate9CharHash(inputStr, 0);
		} else if (hashLogic == 3) {
			return generate10CharHash(inputStr, 0);
		} else {
			return generate10CharHash(inputStr, hashLogic - 3);
		}
	}
	
	public static String generate10CharHash(String inputStr, int hashLogic) {

		if (null != inputStr && !inputStr.isEmpty()) {
			String hashString = sha256Hashing(inputStr.toLowerCase());
			if (StringUtils.isNotEmpty(hashString)) {
				if (hashLogic < hashString.length()) {
					hashString = hashString.substring(0, hashString.length() - hashLogic);
				} else {
					hashString = RandomStringUtils.random(64, true, false);
				}

				char[] t = hashString.toCharArray();
				char[] hash = new char[10];
				for (int i = 0; i < hashString.length(); i++) {
					hash[i % 10] = (char) (hash[i % 10] ^ t[i]);
				}
				for (int i = 0; i < 10; i++) {
					hash[i] = indexToCharEmailTable.get(hash[i] % indexToCharEmailTable.size());
				}

				return new String(hash);
			} else {
				return null;
			}

		} else {
			return inputStr;
		}

	}
	
	/*
	 * private static void initializeIndexToCharTable() { // 0->a, 1->b, ..., 25->z,
	 * ..., 52->0, 61->9 indexToCharTable = new ArrayList<>(); for (int i = 0; i <
	 * 26; ++i) { char c = 'a'; c += i; indexToCharTable.add(c); } for (int i = 26;
	 * i < 52; ++i) { char c = 'A'; c += (i - 26); indexToCharTable.add(c); } for
	 * (int i = 52; i < 62; ++i) { char c = '0'; c += (i - 52);
	 * indexToCharTable.add(c); } }
	 */
	
	private static void initializeIndexToCharEmailTable() {
		// 0->a, 1->b, ..., 25->z, ..., 52->0, 61->9
		indexToCharEmailTable = new ArrayList<>();
		for (int i = 0; i < 26; ++i) {
			char c = 'a';
			c += i;
			indexToCharEmailTable.add(c);
		}
		for (int i = 52; i < 62; ++i) {
			char c = '0';
			c += (i - 52);
			indexToCharEmailTable.add(c);
		}
	}
	
	  private static String sha256Hashing(String input) {
			try {
				MessageDigest digest = MessageDigest.getInstance("SHA-256");
				byte[] hash = digest.digest(input.getBytes(StandardCharsets.UTF_8));
				StringBuilder hexString = new StringBuilder();

				for (int i = 0; i < hash.length; i++) {
					String hex = Integer.toHexString(0xff & hash[i]);
					if (hex.length() == 1)
						hexString.append('0');
					hexString.append(hex);
				}

				return hexString.toString();
			} catch (Exception e) {
				log.error("error in hashing :: {}", e.getMessage());
			}
			return null;
		}
}
