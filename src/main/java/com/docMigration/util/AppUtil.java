/**
 * 
 */
package com.docMigration.util;

import java.io.IOException;
import java.security.MessageDigest;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Random;
import java.util.Set;
import java.util.zip.GZIPOutputStream;

import javax.servlet.http.HttpServletRequest;

import org.apache.commons.io.output.ByteArrayOutputStream;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.google.gson.Gson;
import com.mongodb.DBObject;

import org.apache.commons.lang.StringUtils;


/**
 * <AUTHOR>
 *
 */
public class AppUtil {

	private static final Logger logger = LoggerFactory.getLogger(AppUtil.class);

	/** The gson. */
	public static Gson GSON = new Gson();
	
	public static int getInt(String str, int initVal) {

		int i = initVal;
		try {
			i = Integer.parseInt(str);
		} catch (Exception e) {
			// TODO: handle exception
		}
		return i;
	}
	
	/**
	 * Gets trimmed String.
	 *
	 * @param Object which represents the string
	 * @return trimmed string
	 */
	public static String getTrimmedStr(Object strObj){
		boolean isStrObjEmpty = isEmptyStr(strObj);
		if(isStrObjEmpty)
			return "";
		
		String strValue =  String.valueOf(strObj);
		return !isStrObjEmpty ? strValue.trim().replaceAll("\t+"," ").replaceAll("\r+", " ").replaceAll("\n+"," ").replaceAll("  +", " "):"";
	}
	
	public static boolean isEmptyStr(Object strObj) {
		if(strObj == null){
			return true;
		}
		String strValue =  String.valueOf(strObj);
		return StringUtils.isBlank(strValue);
	}

	public static int getInt(String str) {
		int i = 0;
		try {
			i = Integer.parseInt(str);
		} catch (Exception e) {
			// TODO: handle exception
		}
		return i;
	}

	/**
	 * This method is used to merge two <code>DBObject</code> value for excel
	 * sheet processing activities.
	 *
	 * @param reqInput
	 * @param dbInput
	 */
	public static void mergInputValue(DBObject reqInput, DBObject dbInput) {
		String dbKeyName = "";
		String reqKeyName = "";
		Iterator entries = null;
		Entry thisEntry = null;

		try {
			if (reqInput != null && dbInput != null) {
				List<DBObject> dbInputs = (List<DBObject>) dbInput.get("toBeUpdateValues");
				for (DBObject obj : dbInputs) {
					dbKeyName = String.valueOf(obj.get("cellName"));

					entries = reqInput.toMap().entrySet().iterator();
					while (entries.hasNext()) {
						thisEntry = (Entry) entries.next();
						reqKeyName = String.valueOf(thisEntry.getKey());
						if (reqKeyName.equals(dbKeyName)) {
							Object value = thisEntry.getValue();
							logger.debug("changed " + dbKeyName + " from " + obj.get("value") + " to " + value);
							obj.put("value", value);

						}

					}

				}
			}
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			dbKeyName = null;
			reqKeyName = null;
			entries = null;
			thisEntry = null;
		}
	}


	public static Boolean getBoolean(Object obj) {
		Boolean bol = null;
		try {
			bol = obj != null ? obj.equals("1") || obj.equals(1) || obj.equals(true) || obj.equals("true") : null;
		} catch (Exception ignored) {
		}
		return bol;
	}
	
	/**
	 * This method returns random five digit number
	 *
	 * @return
	 */
	public static int fiveDigitUniqueNumberGenerator() {
		Random r = new Random(System.currentTimeMillis());
		return (1 + r.nextInt(2)) * 10000 + r.nextInt(10000);
	}

	public static Integer getInteger(Object strObj) {
		Integer i = null;
		try {
			i = new Integer(String.valueOf(strObj));
		} catch (Exception e) {
			return null;
		}
		return i;
	}

	public static Integer getInteger(String strObj) {
		Integer i = null;
		try {
			i = new Integer(strObj);
		} catch (Exception e) {
			return null;
		}
		return i;
	}

	public static long getLong(String str) {
		long i = 0L;
		try {
			i = Long.parseLong(str);
		} catch (Exception e) {
			// TODO: handle exception
		}
		return i;
	}

	public static Long getLong(Object obj) {
		Long l = null;
		try {
			l = new Long(String.valueOf(obj));
		} catch (Exception ignored) {
		}
		return l;
	}
	
	public static double getDouble(String str) {
		double i = 0;
		try {
			i = Double.parseDouble(str);
		} catch (Exception e) {
			// TODO: handle exception
		}
		return i;
	}

	public static String getEncryptedPassword(String password) throws Exception {
		logger.info(" executing getEncryptedPassword ");
		MessageDigest md = MessageDigest.getInstance("MD5");
		md.update(password.getBytes());

		byte byteData[] = md.digest();

		StringBuffer encrypPwd = new StringBuffer();

		for (int i = 0; i < byteData.length; i++) {
			encrypPwd.append(Integer.toString((byteData[i] & 0xff) + 0x100, 16).substring(1));
		}
		return encrypPwd.toString();
	}

	public static boolean isNotBlank(String val) {
		boolean retVal = false;
		if (val != null && val.trim().length() > 0) {
			retVal = true;
		}
		return retVal;
	}

	public static boolean isValidDate(String dateToValidate, String dateFromat) {
		if (dateToValidate == null) {
			return false;
		}
		SimpleDateFormat sdf = new SimpleDateFormat(dateFromat);
		sdf.setLenient(false);
		try {
			Date date = sdf.parse(dateToValidate);
		} catch (ParseException e) {
			e.printStackTrace();
			return false;
		}
		return true;
	}

	public static boolean isListNotBlank(List list) {
		boolean retValue = false;
		if (list != null && list.size() > 0) {
			retValue = true;
		}
		return retValue;
	}

	/**
	 * This method calculate age based on the date
	 *
	 * @return
	 */
	public static int calculateAge(Date date) {
		int age = 0;
		Calendar dob = Calendar.getInstance();
		dob.setTime(date);
		Calendar today = Calendar.getInstance();
		age = today.get(Calendar.YEAR) - dob.get(Calendar.YEAR);
		return age;
	}

	public static boolean isSetNotBlank(Set set) {
		boolean retValue = false;
		if (set != null && set.size() > 0) {
			retValue = true;
		}
		return retValue;
	}

	public static boolean isInteger(String str) {
		try {
			Integer.parseInt(str);
			return true;
		} catch (NumberFormatException nfe) {
			return false;
		}
	}

	public static String getBaseUrl(HttpServletRequest request) {
		StringBuilder builder = new StringBuilder();
		builder.append(request.getScheme())
				.append("://")
				.append(request.getServerName())
				.append("http".equals(request.getScheme()) && request.getServerPort() == 80 || "https".equals(request.getScheme()) && request.getServerPort() == 443 ? "" : ":"
						+ request.getServerPort()).append(request.getContextPath());
		return builder.toString();

	}

	public static Integer getAge(String dob,String format) {
		Integer age;
		Date dateOfBirth =DateUtil.getStringToDate(dob,format);
		dob=DateUtil.parseDate(dateOfBirth,DateUtil.DATE_FORMAT_DD_MM_YYYY);
		int dateDOB = getInteger(dob.substring(0, 2));
		int monthDOB =getInteger(dob.substring(3, 5));
		int yearDOB = getInteger(dob.substring(6, 10));

		// CALCULATE THE CURRENT YEAR, MONTH AND DAY
		// INTO SEPERATE VARIABLES
		DateFormat dateFormat = new SimpleDateFormat("yyyy");
		java.util.Date date = new java.util.Date();
		int thisYear = getInteger(dateFormat.format(date));

		dateFormat = new SimpleDateFormat("MM");
		date = new java.util.Date();
		int thisMonth = getInteger(dateFormat.format(date));

		dateFormat = new SimpleDateFormat("dd");
		date = new java.util.Date();
		int thisDay = getInteger(dateFormat.format(date));

		age = thisYear - yearDOB;

		if (thisMonth < monthDOB) {
			age = age - 1;
		}

		if (thisMonth == monthDOB && thisDay < dateDOB) {
			age = age - 1;
		}
		return age;
	}

	public static byte[] compressData(String data) {
		ByteArrayOutputStream outputStream = null;
		GZIPOutputStream gzipOutputStream = null;
		try {
			outputStream = new ByteArrayOutputStream();
			gzipOutputStream = new GZIPOutputStream(outputStream);
			gzipOutputStream.write(data.getBytes());
			gzipOutputStream.close();
			return outputStream.toByteArray();
		} catch (Exception e) {
			e.printStackTrace();
			return null;
		} finally {
			if(outputStream != null) {
				try {
					outputStream.close();
				} catch (IOException e) {
				}
			}
			if(gzipOutputStream != null) {
				try {
					gzipOutputStream.close();
				} catch (IOException e) {
				}
			}
		}
	}
	
	/**
	 * This method is used to build respMap for logger4Dashboard.
	 * 
	 * @param reqDate
	 * @param respDate
	 * @param methodName
	 * @param reqText
	 * @param resText
	 * @param trackId
	 * @param error
	 * @param ip
	 * @return
	 */
	public static Map<String, Object> buildLoggerMap(long startTime, String methodName, String reqText, String resText, String trackId, String error, String ip ) {
		
		Map<String, Object> map = new HashMap<String, Object>(12);
		
		try {
			//  create  map to async call (using service api provided by jitendra)
			
			map.put("Requesttime", "/Date(" + (startTime + ")/" ));
			map.put("Responsetime", "/Date(" + System.currentTimeMillis() + ")/");
			map.put("TrackingID", trackId);
			map.put("Method", methodName);
			map.put("ResponseText", resText);
			map.put("Exception", error);
	        map.put("RequestText", reqText);
	        map.put("IP", ip);
		} catch(Exception e) {
			
		}
		
		return map;
	}
	public static String incrementFilenameNumber(String filename) {
		// Regular expression to match the pattern "_<number>.json.gz"
		String regex = "_(\\d+)\\.json\\.gz";

		// Create a pattern and matcher
		java.util.regex.Pattern pattern = java.util.regex.Pattern.compile(regex);
		java.util.regex.Matcher matcher = pattern.matcher(filename);

		// Check if the pattern matches
		if (matcher.find()) {
			// Extract the number from the matched group
			String numberStr = matcher.group(1);
			int number = Integer.parseInt(numberStr);

			// Increment the number
			int incrementedNumber = number + 1;

			// Create the new filename with the incremented number
			String incrementedFilename = filename.replace("_" + number + ".json.gz", "_" + incrementedNumber + ".json.gz");
			return incrementedFilename;
		} else {
			// If the pattern does not match, return the original filename
			return filename;
		}
	}
	
	public static  void main(String[] arg) {
		long t = System.currentTimeMillis();
		System.out.println(t);
	}

}
