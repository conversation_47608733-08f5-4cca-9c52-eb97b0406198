package com.docMigration.util;

import org.apache.commons.io.output.ByteArrayOutputStream;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.util.zip.GZIPInputStream;
import java.util.zip.GZIPOutputStream;

public class CompressionUtil {

    public static byte[] compressData(String data) {
        ByteArrayOutputStream outputStream = null;
        GZIPOutputStream gzipOutputStream = null;
        try {
            outputStream = new ByteArrayOutputStream();
            gzipOutputStream = new GZIPOutputStream(outputStream);
            gzipOutputStream.write(data.getBytes());
            gzipOutputStream.close();
            return outputStream.toByteArray();
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        } finally {
            if(outputStream != null) {
                try {
                    outputStream.close();
                } catch (IOException e) {
                }
            }
            if(gzipOutputStream != null) {
                try {
                    gzipOutputStream.close();
                } catch (IOException e) {
                }
            }
        }
    }
    public static String decompressData(byte[] compressedData) {
        try {
            // Create a GZIP input stream to decompress the data
            ByteArrayInputStream inputStream = new ByteArrayInputStream(compressedData);
            GZIPInputStream gzipInputStream = new GZIPInputStream(inputStream);

            // Read decompressed data into a byte array output stream
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            byte[] buffer = new byte[1024];
            int bytesRead;
            while ((bytesRead = gzipInputStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, bytesRead);
            }

            // Close streams
            gzipInputStream.close();
            outputStream.close();

            // Convert decompressed byte array to string
            return outputStream.toString("UTF-8");
        } catch (IOException e) {
            e.printStackTrace();
            return null;
        }
    }
}
