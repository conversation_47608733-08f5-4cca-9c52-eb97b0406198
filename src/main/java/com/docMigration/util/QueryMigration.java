package com.docMigration.util;

/*
 * <AUTHOR> <PERSON><PERSON><PERSON>
 * Objective - To put migration queries
 */

public interface QueryMigration {

	String GET_EMAIL_DETAIL = "select Email,encEmail from customer.EmailDetail where Email in (:email) and encEmail is not null";

	String GET_MOBILE_DETAIL = "select MobileNo,encMobile from customer.MobileDetail where MobileNo in (:mobile) and encMobile is not null";

	String GET_MOBILE_DETAIL_FROM_PB_NETWORK = "select mobileNo,encMobile from customer.mobileDetailsPbNetwork where MobileNo in (:mobile) and encMobile is not null";

	String GET_EMAIL_DETAIL_FROM_PB_NETWORK = "select email,encEmail from customer.emailDetailsPbNetwork where email in (:email) and encEmail is not null";

	String ADD_MOBILE_IN_PB_NETWORK = "insert into customer.mobileDetailsPbNetwork (mobileNo,encMobile,createdOn,createdBy,isActive,updatedOn,updatedBy) "
			+ "SELECT ?,?,GetDate(),?,1,GetDate(),? "
			+ "where not exists (select top 1 mdpn.mobileNo from Customer.mobileDetailsPbNetwork mdpn (nolock) where mdpn.mobileNo=?) ";

	String ADD_EMAIL_IN_PB_NETWORK = "insert into customer.emailDetailsPbNetwork (email,encEmail ,createdOn,createdBy,isActive,updatedOn,updatedBy) "
			+ "SELECT ?,?,GetDate(),?,1,GetDate(),? "
			+ "where not exists (select top 1 email from Customer.emailDetailsPbNetwork (nolock) where email = ? )";
	
	String GET_EMAIL_CUST_DETAIL = "SELECT top(?) e.CustId as CustomerId ,e.email, e.encEmail, e.IsPrimary, e.IsActive from productDB.Customer.EmailDetail e(nolock) "
			+ "where e.CustId > ? order by e.CustId asc ";
	
	String GET_EMAIL_NETWORK_DETAIL = "SELECT top(?) e.id, e.email, e.encEmail  , e.IsActive from productDB.Customer.emailDetailsPbNetwork e(nolock) "
			+ "where e.id > ? order by e.id asc";
	
	String GET_MOBILE_CUST_DETAIL = "select top (?) CustId, MobileNo, encMobile, CountryID from productDB.Customer.MobileDetail m(nolock) "
			+ " where MobileNo >= ? and isActive = 1 order by MobileNo asc ";
	
	String GET_MOBILE_NETWORK_DETAIL = "select top (?) MobileNo, encMobile from Customer.mobileDetailsPbNetwork m(nolock) " 
			 + " where MobileNo >= ? and isActive = 1 order by MobileNo asc ";
	
	String GET_CUST_DETAIL = "SELECT top (?) r.CustomerId , r.MobileNo as regMobile ,m.MobileNo, m.CountryID, m.IsPrimary as mIsPrimary, m.encMobile, "
			+ " null as Email, null as encEmail , null as IsPrimary , r.Email as regEmail "
			+ " from ProductDB.Customer.Registration r(nolock) "
			+ " left join ProductDB.Customer.MobileDetail m(nolock) on m.CustId = r.CustomerId and m.IsActive = 1 and m.IsPrimary = 1 "
			+ " where r.CustomerId > ? "
			+ " UNION ALL "
			+ " SELECT top(?) r.CustomerId ,r.MobileNo as regMobile ,  null as MobileNo, null as CountryID, null as mIsPrimary, null as encMobile, "
			+ " e.Email, e.encEmail , e.IsPrimary,  r.Email as regEmail "
			+ " from ProductDB.Customer.Registration r(nolock) "
			+ " left join ProductDB.Customer.EmailDetail e(nolock) on e.CustId = r.CustomerId and e.IsActive = 1 and e.IsPrimary = 1 "
			+ " where r.CustomerId > ? "
			+ " order by r.CustomerId asc ";
	
	String GET_CUST_MOB_IDS_DETAIL = "SELECT top(?) CustMobId,  encMobile ,MobileNo  from Customer.MobileDetail md (nolock) where "
			+ " CustMobId > ? order by CustMobId ASC ";
	
	String GET_CUST_NETWORK_MOB_IDS_DETAIL = "SELECT top(?) id as CustMobId,  encMobile , mobileNo as MobileNo  from Customer.mobileDetailsPbNetwork md (nolock) where "
			+ " id > ? order by id ASC ";
	
	String GET_CUST_MOB_ID_ENC_MOBILE_DETAIL = "SELECT encMobile , MobileNo from customer.MobileDetail md (nolock) where encMobile = ?";
	
	String UPDATE_CUST_ENC_MOBILE_ALL_FOR_MOBILE = "update customer.mobileDetail set encMobile = :encMobile, updatedOn = :updatedOn, updatedBy=:updatedBy where mobileNo=:mobileNo ";
	
	String UPDATE_PB_NETWORK_ENC_MOBILE_ALL_FOR_MOBILE = "update customer.mobileDetailsPbNetwork set encMobile = :encMobile, updatedOn = :updatedOn, updatedBy =:updatedBy where mobileNo =:mobileNo ";
	
	String GET_CUST_EMAIL_IDS_DETAIL = "SELECT top(?) CustEmailId ,  encEmail, Email from Customer.EmailDetail (nolock) where "
			+ " CustEmailId > ? order by CustEmailId ASC ";
	
	String GET_CUST_EMAIL_PB_NETWORK_IDS_DETAIL = "SELECT top(?) id as CustEmailId,  encEmail , email as Email from Customer.emailDetailsPbNetwork (nolock) where " 
			 + " id > ? order by id ASC" ;
	
	String GET_CUST_MBL_PB_NETWORK_IDS_DETAIL = "SELECT top(?) id as CustMobId,  encMobile , mobileNo  from Customer.mobileDetailsPbNetwork (nolock) where "
			+ " id > ? order by id ASC" ;
	
	String GET_CUST_MBL_IDS_DETAIL = "SELECT top(?) CustMobId ,  encMobile , MobileNo from Customer.MobileDetail md (nolock)  where "
			+ " CustMobId > ? order by CustMobId ASC ";
	
	String GET_CUST_EMAIL_ID_ENC_EMAIL_DETAIL = "SELECT encEmail , Email from customer.EmailDetail (nolock) where encEmail = ?";
	
	String UPDATE_CUST_ENC_EMAIL_ALL_FOR_EMAIL = "update customer.EmailDetail set encEmail = :encEmail, updatedOn = :updatedOn, updatedBy=:updatedBy where Email =:email ";
	
	String UPDATE_CUST_ENC_MBL_ALL_FOR_MBL = "update customer.MobileDetail set encMobile = :encMobile, updatedOn = :updatedOn, updatedBy=:updatedBy where MobileNo =:MobileNo ";
	
	String UPDATE_PB_NETWORK_ENC_EMAIL_ALL_FOR_EMAIL =  "update customer.emailDetailsPbNetwork set encEmail = :encEmail, updatedOn = :updatedOn, updatedBy=:updatedBy where Email =:email";
	
	String UPDATE_PB_NETWORK_ENC_MBL_ALL_FOR_MBL =  "update customer.mobileDetailsPbNetwork set encMobile = :encMobile, updatedOn = :updatedOn, updatedBy=:updatedBy where mobileNo =:mobileNo";
	
	String UPDATE_CUST_ENC_MOBILE_ALL_FOR_MOBILE_BULK = "update customer.mobileDetail set encMobile = ?, updatedOn = ?, updatedBy= ? where mobileNo=? ";
	
	String UPDATE_CUST_ENC_EMAIL_ALL_FOR_MOBILE_BULK = "update customer.EmailDetail set encEmail = ?, updatedOn = ?, updatedBy=? where Email =?";
	
	String GET_COMM_RESPONSE_BY_RANGE = "select emailResponseId, email, timeStampDt, eventType, e.isActive,ev.id as evenId "
			+ "from PBCROMA.MTX.CommunicationResponseEmail e(nolock) "
			+ " inner join customer.M_CommEventType ev on ev.[type] = e.EventType "
			+ "where EmailResponseId >= ? order by EmailResponseId ASC OFFSET 0 ROWS FETCH next ? ROWS ONLY";
	
	String GET_MOB_COMM_RESPONSE_BY_RANGE = "select SMSResponseId, crl.MobileNo , LastSubmitTime, DLRStatus, e.IsActive from PBCROMA.MTX.CommunicationResponseSMS e(nolock)"
			+ " inner join PBCroma.MTX.CommunicationRequestLog crl (nolock) on e.RequestId =crl.RequestId"
			+ " where SMSResponseId >= ? and e.isActive = 1 order by SMSResponseId ASC OFFSET 0 ROWS FETCH next ? ROWS ONLY";
	
	String GET_MAX_CUST_ID = "SELECT MAX(CustomerId) from Customer.Registration r ";
	
	String UPDATE_COMM_RESPONSE_FRM_MONGO_BY_EMAIL = "update e set e.eventId = :eventId, e.sentTime = :sentTime, UpdatedOn =getDate(), UpdatedBy =99"
			+ " from Customer.EmailDetail e "
			+ " where e.CustEmailId =:CustEmailId";
	
	String GET_EMAIL_DETAIL_BY_RANGE = "SELECT CustEmailId , Email from customer.EmailDetail pd (nolock) "
			+ "where CustEmailId >= ? and IsActive  = 1 order by CustEmailId ASC OFFSET 0 ROWS FETCH next ? ROWS ONLY";

}
