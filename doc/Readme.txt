for local: refresh token API:
https://apiqa.policybazaar.com/cs/access/getAccessKey

curl --location 'localhost:8090/car/moveBookedDocsByEnquiryId?size=50&dayBefore=30&ns=CarLoggerDB.ReqResLog&stop=false' \
--header 'authKey: 92kik4i4jn4oi4n3oi4nn4k4l4lk4lknwlk4l4ljk44444klknn4h4jk5k'

curl --location 'localhost:8090/getS3Log?ns=CarLoggerDB.ReqResLog&enquiryId=567678848&product=car'


for life ----------------

curl --location 'localhost:8090/getS3Log?ns=e2eLog.LogDataRenewal&policyNo=145576575&product=life'

curl --location 'localhost:8090/moveLifeDocsToS3?timeCheck=false&dayBefore=0&stop=false&ns=e2eLog.LogDataRenewal&size=5&=92kik4i4jn4oi4n3oi4nn4k4l4lk4lknwlk4l4ljk44444klknn4h4jk5k' \
--header 'authKey: 92kik4i4jn4oi4n3oi4nn4k4l4lk4lknwlk4l4ljk44444klknn4h4jk5k'

---------

--- get for TW
curl --location 'mongolog.policybazaar.com/log/getS3Log?product=tw&vehicleDetailId=134711444'

--- get for Car
curl --location 'mongolog.policybazaar.com/log/getS3Log?product=car&enquiryId=587293256'

--- get for Health
curl --location 'mongolog.policybazaar.com/log/getS3Log?product=health&enquiryId=392356781'

--- get for Kyc
curl --location 'mongolog.policybazaar.com/log/getS3Log?product=kyc&uniqueId=535479668&uniqueType=LeadId'

--- get for Claim
curl --location 'mongolog.policybazaar.com/log/getS3Log?product=claim&uniqueId=535479668&uniqueType=LeadId'


--- migration for Car
curl --location 'localhost:8090/car/moveBookedDocsByEnquiryId?timeCheck=false&dayBefore=30&ns=CarLoggerDB.ReqResLog&stop=false&size=50' \
--header 'authKey: 92kik4i4jn4oi4n3oi4nn4k4l4lk4lknwlk4l4ljk44444klknn4h4jk5k'

--- migration for Tw
curl --location 'localhost:8090/tw/moveBookedDocsByEnquiryIdOrVehicleId?size=20&dayBefore=30&ns=TwoWheelerDB.ReqResTWowheelerLog&stop=false' \
--header 'authKey: 92kik4i4jn4oi4n3oi4nn4k4l4lk4lknwlk4l4ljk44444klknn4h4jk5k'

--- migration for Life
curl --location 'localhost:8090/moveLifeDocsToS3?timeCheck=false&dayBefore=0&stop=false&ns=e2eLog.LogDataRenewal&size=1' \
--header 'authKey: 92kik4i4jn4oi4n3oi4nn4k4l4lk4lknwlk4l4ljk44444klknn4h4jk5k'

--- migration for CAR PbPartner
curl --location 'localhost:8090/car/moveBookedLeadForPbPartner?size=20&timeCheck=false&dayBefore=30&ns=CarLoggerDBpolbk.ReqResLog&stop=false&chunkSize=200' \
--header 'authKey: 92kik4i4jn4oi4n3oi4nn4k4l4lk4lknwlk4l4ljk44444klknn4h4jk5k'

--- migration for TW PbPartner
curl --location 'localhost:8090/tw/moveBookedLeadTwPbpartners?size=20&timeCheck=false&dayBefore=30&ns=CarLoggerDBpolbk.ReqResTWowheelerLog&stop=false&chunkSize=200' \
--header 'authKey: 92kik4i4jn4oi4n3oi4nn4k4l4lk4lknwlk4l4ljk44444klknn4h4jk5k'

--- migration for Kyc
curl --location 'localhost:8090/kyc/moveBookedDocsByUniqueId?timeCheck=false&dayBefore=30&ns=KYC.KYCCentral&stop=false&size=5' \
--header 'authKey: 92kik4i4jn4oi4n3oi4nn4k4l4lk4lknwlk4l4ljk44444klknn4h4jk5k'

--- migration for Claim
curl --location 'localhost:8090/claim/moveDocsByUniqueId?timeCheck=false&dayBefore=30&ns=ClaimsDB.claimslogs&stop=false&size=50' \
--header 'authKey: 92kik4i4jn4oi4n3oi4nn4k4l4lk4lknwlk4l4ljk44444klknn4h4jk5k'

--- migration for BMS : CommV2FileLogsV2
curl --location 'localhost:8090/bms/moveCommV2FileLogsToS3?size=50&dayBefore=30&ns=communicationDB.CommV2FileLogsv2&stop=false&chunkSize=200' \
--header 'authKey: 92kik4i4jn4oi4n3oi4nn4k4l4lk4lknwlk4l4ljk44444klknn4h4jk5k'

--- migration for BMS : CommV2FileLogsV2/v1
curl --location 'localhost:8090/bms/moveCommV2FileLogsToS3/v1?size=50&timeCheck=false&days=30&type=1&ns=communicationDB.CommV2FileLogsv2&stop=false&lte=666ecd030000000000000000' \
--header 'authKey: 92kik4i4jn4oi4n3oi4nn4k4l4lk4lknwlk4l4ljk44444klknn4h4jk5k'

--- migration for Bms PIVCLog Logs
curl --location 'localhost:8090/bms/movePIVCLogsToS3?size=50&dayBefore=100&ns=Integration.PIVCLog&stop=false&authKey=92kik4i4jn4oi4n3oi4nn4k4l4lk4lknwlk4l4ljk44444klknn4h4jk5k&chunkSize=200'

--- migration for Bms DocPushLogs Logs
curl --location 'localhost:8090/bms/moveDocPushLogsToS3?size=50&dayBefore=90&ns=DocsPoint.DocPushLog&stop=false&authKey=92kik4i4jn4oi4n3oi4nn4k4l4lk4lknwlk4l4ljk44444klknn4h4jk5k&chunkSize=200'

--- migration for Bms RequestLogs Logs
curl --location 'localhost:8090/bms/moveDocsRequestLogsToS3?size=50&dayBefore=100&ns=DocsPoint.RequestLogs&stop=false&authKey=92kik4i4jn4oi4n3oi4nn4k4l4lk4lknwlk4l4ljk44444klknn4h4jk5k&chunkSize=200'
--- migration for Travel
curl --location 'localhost:8090/travel/moveBookedDocsByLeadId?timeCheck=false&dayBefore=30&ns=travelDb.ApiLog&stop=false&size=5' \
--header 'authKey: 92kik4i4jn4oi4n3oi4nn4k4l4lk4lknwlk4l4ljk44444klknn4h4jk5k'

--- migration for Car Kyc New Logs
curl --location 'localhost:8090/carKyc/kycMoveBookedDocsByEnquiryId?timeCheck=false&dayBefore=30&chunkSize=500&ns=CarLoggerDB.KycLogs&stop=false&size=50' \
--header 'authKey: 92kik4i4jn4oi4n3oi4nn4k4l4lk4lknwlk4l4ljk44444klknn4h4jk5k'

--- migration for TW Old Logs at ...40.173
curl --location 'localhost:8090/twTemp/moveBookedDocsByEnquiryIdOrVehicleId?size=20&timeCheck=false&dayBefore=15&ns=TwoWheelerDB.ReqResTWowheelerLog_173&stop=false&chunkSize=200' \
--header 'authKey: 92kik4i4jn4oi4n3oi4nn4k4l4lk4lknwlk4l4ljk44444klknn4h4jk5k'

--- migration for Motor Old Logs at ...40.173
curl --location 'localhost:8090/carTemp/moveBookedDocsByEnquiryId?timeCheck=false&dayBefore=30&chunkSize=100&ns=CarLoggerDB.ReqResLog_173&stop=false&size=50' \
--header 'authKey: 92kik4i4jn4oi4n3oi4nn4k4l4lk4lknwlk4l4ljk44444klknn4h4jk5k'

--- migration for Refund Old Logs at ...40.173
curl --location 'localhost:8090/refund/moveOldBookedDocsByLeadId_173?timeCheck=false&dayBefore=30&ns=CnRDB.reqResLog_173&stop=true&chunkSize=200&size=5' \
--header 'authKey: 92kik4i4jn4oi4n3oi4nn4k4l4lk4lknwlk4l4ljk44444klknn4h4jk5k'

--- migration for Car Kyc Old Logs at ...40.173
curl --location 'localhost:8090/carTemp/moveBookedDocsByEnquiryId?timeCheck=false&dayBefore=30&ns=CarLoggerDB.ReqResLog_173&stop=false&size=5' \
--header 'authKey: 92kik4i4jn4oi4n3oi4nn4k4l4lk4lknwlk4l4ljk44444klknn4h4jk5k'

--- migration for Travel Old Logs at ...40.173
curl --location 'localhost:8090/travel/moveOldBookedDocsByLeadId?timeCheck=false&dayBefore=1&chunkSize=200&ns=travelDb.ApiLog_173&stop=false&size=50' \
--header 'authKey: 92kik4i4jn4oi4n3oi4nn4k4l4lk4lknwlk4l4ljk44444klknn4h4jk5k'



--- csv file read:
curl --location 'localhost:8090/testUtil'

Required commands:

nohup java -jar -Xms1024m -Xmx4096m springMoveToS3Proj.jar --spring.config.location=file:///opt/props/application-live.properties nohup.out 2>&1

nohup java -jar springMoveToS3Proj.jar --spring.config.location=file:///opt/props/application-live.properties nohup.out 2>&1

