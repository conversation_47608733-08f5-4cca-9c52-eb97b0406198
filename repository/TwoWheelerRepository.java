package com.policybazaar.cleanup.repository;

import java.util.List;

import org.springframework.data.mongodb.repository.MongoRepository;

import com.policybazaar.cleanup.entity.ReqResTWowheelerLog;

public interface TwoWheelerRepository extends MongoRepository<ReqResTWowheelerLog, String> {

//	@Query(value = "{'errorCode': 500}", count = true)
//	public Long countFetchedDocumentsForCategory(Long errorCode);
	List<ReqResTWowheelerLog> findByvehicleDetailId(Long vehicleDetailId);

}